// Load environment variables from multiple sources
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // Load from system environment

module.exports = {
  apps: [{
    name: 'rtb-exchange',
    script: 'server.js',
    instances: 24, // Optimal for 32-core CPU (leave cores for system/DB)
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '6G', // Increased for better caching performance
    env: {
      // Pass through ALL environment variables from .env files
      ...process.env,

      // Only override critical production settings
      NODE_ENV: 'production',
      PORT: 3102, // All instances will attempt to listen on this port (Node.js cluster handles sharing)

      // High-Performance Node.js Settings for 18 instances
      NODE_OPTIONS: '--max-old-space-size=5120 --max-semi-space-size=256', // 5GB heap + optimized GC
    },
    exp_backoff_restart_delay: 100,
    // listen_timeout: 5000, // This listen_timeout is for PM2 to wait for the 'listening' event
    kill_timeout: 3000,
    merge_logs: true,
    error_file: './logs/rtb-err.log',
    out_file: './logs/rtb-out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

    wait_ready: true, // Important: PM2 waits for process.send('ready')
    listen_timeout: 10000, // PM2's timeout for app to be 'ready' (increased for safety)
    instance_var: 'INSTANCE_ID', // Makes INSTANCE_ID env var available to each instance
    exec_interpreter: 'node',

    // load_balancing: 'round_robin', // Node.js cluster's default is usually round-robin for connection distribution

    // Graceful Shutdown
    shutdown_with_message: true,
    autorestart: true,

    // Cluster Management
    // increment_var : 'PORT' // ---- REMOVED THIS LINE ----
  }]
};