{"name": "global-ads-media", "version": "1.3.4", "private": true, "scripts": {"dev": "PORT=3102 next dev", "start:single": "PORT=3102 next start", "build": "next build", "start": "npx pm2 start ecosystem.config.js", "stop": "npx pm2 stop ecosystem.config.js", "reload": "npx pm2 reload ecosystem.config.js", "monitor": "npx pm2 monit", "logs": "npx pm2 logs", "lint": "next lint", "create-db": "tsx src/scripts/create-database.ts", "init-db": "tsx src/scripts/init-db.ts", "seed-data": "tsx src/scripts/seed-test-data.ts", "setup-db": "npm run create-db && npm run init-db", "update-db-email": "tsx src/scripts/update-database-email-verification.ts", "cleanup-screenshots": "tsx src/scripts/cleanup-screenshots.ts", "cleanup-monitoring": "tsx src/scripts/cleanup-monitoring-data.ts", "setup-mv-ttl": "tsx src/scripts/setup-materialized-views-ttl.ts", "setup-production": "tsx src/scripts/admin.ts", "init-queues": "tsx src/scripts/init-queues.ts", "monitor:queues": "watch -n 2 'curl -s http://localhost:3102/api/admin/queue-stats | jq .'", "health:check": "curl -s http://localhost:3102/api/admin/queue-health | jq .", "health:simple": "curl -s http://localhost:3102/api/admin/queue-health?format=simple", "fallback:process": "curl -X POST -H 'Content-Type: application/json' -d '{\"action\":\"process-fallback\"}' http://localhost:3102/api/admin/queue-health", "fallback:status": "curl -X POST -H 'Content-Type: application/json' -d '{\"action\":\"get-fallback-status\"}' http://localhost:3102/api/admin/queue-health", "health:init": "tsx src/scripts/init-health-monitor.ts", "health:test": "tsx src/scripts/init-health-monitor.ts --test", "health:monitor": "watch -n 5 'curl -s http://localhost:3102/api/admin/health-monitor?type=summary | jq .'", "health:dashboard": "echo 'Health Dashboard: http://localhost:3102/admin/health-monitor'", "optimize": "tsx src/scripts/startup-optimization.ts", "start:optimized": "npm run optimize && npm run start", "test:env": "tsx src/scripts/test-env.ts", "test:rabbitmq": "tsx src/scripts/test-rabbitmq.ts"}, "dependencies": {"@clickhouse/client": "^1.11.1", "@heroicons/react": "^2.2.0", "@stripe/stripe-js": "^4.10.0", "@types/amqplib": "^0.10.7", "amqplib": "^0.10.8", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "country-state-city": "^3.2.1", "dotenv": "^16.5.0", "esbuild-register": "^3.6.0", "fast-xml-parser": "^5.2.3", "ioredis": "^5.6.1", "maxmind": "^4.3.25", "mongodb": "^6.16.0", "next": "15.1.8", "next-auth": "5.0.0-beta.25", "nodemailer": "^6.10.1", "pm2": "^6.0.6", "puppeteer": "^24.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "redis": "^5.1.1", "redis-client": "^0.3.5", "stripe": "^17.4.0", "ua-parser-js": "^2.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/estree": "^1.0.7", "@types/json-schema": "^7.0.15", "@types/json5": "^2.2.0", "@types/node": "^20.17.57", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/puppeteer": "^7.0.4", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/webidl-conversions": "^7.0.3", "@types/whatwg-url": "^13.0.0", "@types/yauzl": "^2.10.3", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.7.0", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}