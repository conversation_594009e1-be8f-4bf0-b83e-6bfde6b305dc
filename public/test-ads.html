<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Ads - Global Ads Media</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .ad-container {
            border: 2px dashed #ccc;
            padding: 10px;
            margin: 10px 0;
            min-height: 50px;
        }
        h1, h2 {
            color: #333;
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Ad Testing Page</h1>
    <div style="background: #e7f3ff; border: 1px solid #0066cc; padding: 15px; margin: 20px 0; border-radius: 5px;">
        <strong>🎉 New Implementation:</strong> Now using iframe with srcdoc (like your working platform) for better ad delivery and security!
    </div>

    <div class="test-section">
        <h2>Banner Ad Test (Zone 230763) - 300x250</h2>
        <div class="ad-container">
            <p>300x250 Banner ad should appear below:</p>
            <div id="banner-test" style="width: 300px; height: 250px; border: 1px dashed #999; margin: 10px 0;">
                <!-- Ad will be inserted here by the script -->
            </div>
        </div>
        <div class="debug" id="banner-debug">Loading 300x250 banner ad...</div>
    </div>

    <div class="test-section">
        <h2>Popup Ad Test (Zone 230764)</h2>
        <div class="ad-container">
            <p>Popup ad will trigger on user interaction:</p>
            <button onclick="triggerPopup()">Click to trigger popup</button>
            <div id="popup-test"></div>
        </div>
        <div class="debug" id="popup-debug">Popup ready (click button above)</div>
    </div>

    <div class="test-section">
        <h2>Manual 300x250 Ad Tag Test</h2>
        <div class="ad-container">
            <p>Manual 300x250 ad container:</p>
            <div id="gam-zone-manual" style="width: 300px; height: 250px; border: 2px solid #007bff; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                <span style="color: #666;">Manual 300x250 Ad Container</span>
            </div>
            <button onclick="loadManualAd()">Load Manual Ad</button>
        </div>
        <div class="debug" id="manual-debug">Click button to load manual ad</div>
    </div>

    <div class="test-section">
        <h2>Publisher Ad Tag Examples</h2>
        <div class="ad-container">
            <h3>300x250 Banner Ad Tag:</h3>
            <div class="debug" style="background: #e9ecef; padding: 15px; margin: 10px 0;">
                <strong>Copy this code to your website:</strong><br><br>
                <code>&lt;script src="https://test.globaladsmedia.us/api/js/adtag.js?zone_id=230763&amp;format=banner&amp;size=300x250"&gt;&lt;/script&gt;</code>
            </div>

            <h3>Popup Ad Tag:</h3>
            <div class="debug" style="background: #e9ecef; padding: 15px; margin: 10px 0;">
                <strong>Copy this code to your website:</strong><br><br>
                <code>&lt;script src="https://test.globaladsmedia.us/api/js/adtag.js?zone_id=230764&amp;format=popup"&gt;&lt;/script&gt;</code>
            </div>

            <h3>Manual Container Method:</h3>
            <div class="debug" style="background: #e9ecef; padding: 15px; margin: 10px 0;">
                <strong>1. Create a container:</strong><br>
                <code>&lt;div id="gam-zone-230763" style="width:300px;height:250px;"&gt;&lt;/div&gt;</code><br><br>
                <strong>2. Add the script:</strong><br>
                <code>&lt;script src="https://test.globaladsmedia.us/api/js/adtag.js?zone_id=230763&amp;format=banner&amp;size=300x250"&gt;&lt;/script&gt;</code>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Direct API Test</h2>
        <div class="ad-container">
            <button onclick="testDirectAPI()">Test Direct API Call</button>
            <div id="api-result"></div>
        </div>
        <div class="debug" id="api-debug">Click button to test API</div>
    </div>

    <!-- Load banner ad -->
    <script src="https://test.globaladsmedia.us/api/js/adtag.js?zone_id=230763&format=banner&size=300x250"></script>

    <!-- Load popup ad -->
    <script src="https://test.globaladsmedia.us/api/js/adtag.js?zone_id=230764&format=popup"></script>

    <script>
        // Debug logging
        console.log('Test page loaded');

        // Check if GlobalAdsMedia is loaded
        setTimeout(() => {
            if (window.GlobalAdsMedia) {
                document.getElementById('banner-debug').innerHTML = 'GlobalAdsMedia loaded: ' + JSON.stringify(Object.keys(window.GlobalAdsMedia));

                // Check zones
                if (window.GlobalAdsMedia.zones) {
                    document.getElementById('banner-debug').innerHTML += '<br>Zones: ' + JSON.stringify(window.GlobalAdsMedia.zones);
                }
            } else {
                document.getElementById('banner-debug').innerHTML = 'ERROR: GlobalAdsMedia not loaded';
            }
        }, 2000);

        // Check for ad containers every 2 seconds
        setInterval(() => {
            const containers = document.querySelectorAll('[id^="gam-zone-"]');
            console.log('Found ad containers:', containers.length);
            containers.forEach(container => {
                console.log('Container:', container.id, 'Content:', container.innerHTML.substring(0, 100));
            });
        }, 2000);

        function triggerPopup() {
            // Simulate user interaction for popup
            if (window.GlobalAdsMedia && window.GlobalAdsMedia.userInteracted) {
                document.getElementById('popup-debug').innerHTML = 'User interaction detected, popup should trigger';
            } else {
                document.getElementById('popup-debug').innerHTML = 'Triggering user interaction...';
                // Trigger click event to enable popups
                document.dispatchEvent(new Event('click'));
            }
        }

        function loadManualAd() {
            document.getElementById('manual-debug').innerHTML = 'Loading manual 300x250 ad...';

            fetch('https://test.globaladsmedia.us/api/serve?zone_id=230763&format=banner&size=300x250')
                .then(response => response.json())
                .then(data => {
                    if (data.ad && data.ad.html) {
                        document.getElementById('gam-zone-manual').innerHTML = data.ad.html;
                        document.getElementById('manual-debug').innerHTML = 'Manual ad loaded successfully! Campaign: ' + data.ad.campaign_id;
                    } else {
                        document.getElementById('gam-zone-manual').innerHTML = '<div style="color: red; text-align: center;">No ad available</div>';
                        document.getElementById('manual-debug').innerHTML = 'No ad returned from API';
                    }
                })
                .catch(error => {
                    document.getElementById('gam-zone-manual').innerHTML = '<div style="color: red; text-align: center;">Error loading ad</div>';
                    document.getElementById('manual-debug').innerHTML = 'Manual ad load failed: ' + error.message;
                });
        }

        function testDirectAPI() {
            document.getElementById('api-debug').innerHTML = 'Testing API...';

            fetch('https://test.globaladsmedia.us/api/serve?zone_id=230763&format=banner&size=300x250')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    document.getElementById('api-debug').innerHTML = 'API test successful';
                })
                .catch(error => {
                    document.getElementById('api-result').innerHTML = 'Error: ' + error.message;
                    document.getElementById('api-debug').innerHTML = 'API test failed: ' + error.message;
                });
        }

        // Monitor ad loading
        setInterval(() => {
            const bannerContainer = document.getElementById('gam-zone-230763');
            const popupContainer = document.getElementById('gam-zone-230764');

            if (bannerContainer) {
                document.getElementById('banner-debug').innerHTML += '<br>Banner container found: ' + bannerContainer.innerHTML.substring(0, 100) + '...';
            }

            if (popupContainer) {
                document.getElementById('popup-debug').innerHTML += '<br>Popup container found: ' + popupContainer.innerHTML.substring(0, 100) + '...';
            }
        }, 3000);
    </script>
</body>
</html>
