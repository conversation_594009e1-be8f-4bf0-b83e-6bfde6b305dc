<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTB Ad Serving Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .ad-zone {
            border: 2px dashed #ddd;
            padding: 20px;
            margin: 15px 0;
            background: #fafafa;
            text-align: center;
            min-height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .info-box {
            background: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3e0;
            padding: 15px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
        .success-box {
            background: #e8f5e8;
            padding: 15px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎯 RTB Platform - Ad Serving & Tracking Test</h1>
    
    <div class="info-box">
        <h3>📋 Test Overview</h3>
        <p>This page tests the complete ad serving flow with impression and click tracking:</p>
        <ol>
            <li><strong>Ad Request:</strong> Calls <code>/api/serve</code> with zone parameters</li>
            <li><strong>Auction:</strong> Runs auction between local campaigns and DSP partners</li>
            <li><strong>Ad Serving:</strong> Returns ad markup with tracking URLs</li>
            <li><strong>Impression Tracking:</strong> Records impression when ad is displayed</li>
            <li><strong>Click Tracking:</strong> Redirects through <code>/api/click</code> for tracking</li>
        </ol>
    </div>

    <div class="warning-box">
        <h3>⚠️ Prerequisites</h3>
        <ul>
            <li>Make sure you have active zones in your database</li>
            <li>Ensure you have campaigns or DSP partners configured</li>
            <li>Check that ClickHouse is running and accessible</li>
            <li>Open browser Developer Tools to monitor network requests</li>
        </ul>
    </div>

    <div class="test-container">
        <h3>🎯 Banner Ad Test (300x250)</h3>
        <div>
            <label>Zone ID: <input type="number" id="bannerZoneId" value="1" min="1"></label>
            <button onclick="loadBannerAd()">Load Banner Ad</button>
            <button onclick="clearBannerAd()">Clear</button>
        </div>
        <div id="bannerAdZone" class="ad-zone">
            <p>Banner ad will appear here</p>
        </div>
        <div id="bannerLog" class="log"></div>
    </div>

    <div class="test-container">
        <h3>📱 Native Ad Test</h3>
        <div>
            <label>Zone ID: <input type="number" id="nativeZoneId" value="2" min="1"></label>
            <button onclick="loadNativeAd()">Load Native Ad</button>
            <button onclick="clearNativeAd()">Clear</button>
        </div>
        <div id="nativeAdZone" class="ad-zone">
            <p>Native ad will appear here</p>
        </div>
        <div id="nativeLog" class="log"></div>
    </div>

    <div class="test-container">
        <h3>🚀 Popup Ad Test</h3>
        <div>
            <label>Zone ID: <input type="number" id="popupZoneId" value="3" min="1"></label>
            <button onclick="loadPopupAd()">Load Popup Ad</button>
            <button onclick="clearPopupAd()">Clear</button>
        </div>
        <div id="popupAdZone" class="ad-zone">
            <p>Popup ad info will appear here</p>
        </div>
        <div id="popupLog" class="log"></div>
    </div>

    <div class="success-box">
        <h3>✅ What to Check</h3>
        <ul>
            <li><strong>Network Tab:</strong> Look for requests to <code>/api/serve</code></li>
            <li><strong>Ad Display:</strong> Verify ads are displayed with proper formatting</li>
            <li><strong>Click Tracking:</strong> Click on ads and verify they go through <code>/api/click</code></li>
            <li><strong>Database:</strong> Check <code>impressions</code> and <code>clicks</code> tables in ClickHouse</li>
            <li><strong>Console Logs:</strong> Monitor for any JavaScript errors</li>
        </ul>
    </div>

    <script>
        function log(elementId, message) {
            const logElement = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function loadBannerAd() {
            const zoneId = document.getElementById('bannerZoneId').value;
            const adZone = document.getElementById('bannerAdZone');
            
            log('bannerLog', `Loading banner ad for zone ${zoneId}...`);
            
            try {
                const response = await fetch(`/api/serve?zone_id=${zoneId}&format=banner&size=300x250`);
                const data = await response.json();
                
                log('bannerLog', `Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.ad && data.ad.html) {
                    adZone.innerHTML = data.ad.html;
                    log('bannerLog', `✅ Banner ad loaded successfully`);
                    log('bannerLog', `Source: ${data.ad.source}, CPM: $${data.ad.cpm}, Campaign: ${data.ad.campaign_id}`);
                } else {
                    adZone.innerHTML = '<p style="color: red;">No ad available</p>';
                    log('bannerLog', `❌ No ad returned`);
                }
            } catch (error) {
                log('bannerLog', `❌ Error: ${error.message}`);
                adZone.innerHTML = '<p style="color: red;">Error loading ad</p>';
            }
        }

        async function loadNativeAd() {
            const zoneId = document.getElementById('nativeZoneId').value;
            const adZone = document.getElementById('nativeAdZone');
            
            log('nativeLog', `Loading native ad for zone ${zoneId}...`);
            
            try {
                const response = await fetch(`/api/serve?zone_id=${zoneId}&format=native`);
                const data = await response.json();
                
                log('nativeLog', `Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.ad && data.ad.html) {
                    adZone.innerHTML = data.ad.html;
                    log('nativeLog', `✅ Native ad loaded successfully`);
                    log('nativeLog', `Source: ${data.ad.source}, CPM: $${data.ad.cpm}, Campaign: ${data.ad.campaign_id}`);
                } else {
                    adZone.innerHTML = '<p style="color: red;">No ad available</p>';
                    log('nativeLog', `❌ No ad returned`);
                }
            } catch (error) {
                log('nativeLog', `❌ Error: ${error.message}`);
                adZone.innerHTML = '<p style="color: red;">Error loading ad</p>';
            }
        }

        async function loadPopupAd() {
            const zoneId = document.getElementById('popupZoneId').value;
            const adZone = document.getElementById('popupAdZone');
            
            log('popupLog', `Loading popup ad for zone ${zoneId}...`);
            
            try {
                const response = await fetch(`/api/serve?zone_id=${zoneId}&format=popup`);
                const data = await response.json();
                
                log('popupLog', `Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.ad) {
                    adZone.innerHTML = `
                        <div>
                            <h4>Popup Ad Ready</h4>
                            <p><strong>Landing URL:</strong> ${data.ad.landing_url}</p>
                            <p><strong>Source:</strong> ${data.ad.source}</p>
                            <p><strong>CPM:</strong> $${data.ad.cpm}</p>
                            <button onclick="window.open('${data.ad.landing_url}', '_blank')">Test Popup</button>
                        </div>
                    `;
                    log('popupLog', `✅ Popup ad loaded successfully`);
                } else {
                    adZone.innerHTML = '<p style="color: red;">No ad available</p>';
                    log('popupLog', `❌ No ad returned`);
                }
            } catch (error) {
                log('popupLog', `❌ Error: ${error.message}`);
                adZone.innerHTML = '<p style="color: red;">Error loading ad</p>';
            }
        }

        function clearBannerAd() {
            document.getElementById('bannerAdZone').innerHTML = '<p>Banner ad will appear here</p>';
            document.getElementById('bannerLog').innerHTML = '';
        }

        function clearNativeAd() {
            document.getElementById('nativeAdZone').innerHTML = '<p>Native ad will appear here</p>';
            document.getElementById('nativeLog').innerHTML = '';
        }

        function clearPopupAd() {
            document.getElementById('popupAdZone').innerHTML = '<p>Popup ad info will appear here</p>';
            document.getElementById('popupLog').innerHTML = '';
        }

        // Log page load
        console.log('RTB Ad Serving Test Page Loaded');
        console.log('Open Network tab to monitor ad serving requests');
    </script>
</body>
</html>
