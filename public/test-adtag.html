<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Ad Tag Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .ad-container {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            background: #f9f9f9;
            text-align: center;
        }
        .ad-container h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
            margin: 10px 0;
            overflow-x: auto;
        }
        .section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Universal Ad Tag Test Page</h1>
    <p>This page demonstrates the unified JavaScript ad tag system that handles all ad formats automatically.</p>

    <!-- Banner Ad Test -->
    <div class="section">
        <h2>1. Banner Ad (300x250)</h2>
        <p>Standard banner ad that supports both image and JS tag campaigns:</p>

        <div class="code-block">
&lt;script src="http://localhost:3000/api/js/adtag.js?zone_id=YOUR_ZONE_ID&format=banner&size=300x250"&gt;&lt;/script&gt;
        </div>

        <div class="ad-container">
            <h3>Banner Ad Zone</h3>
            <!-- Single script - container created automatically -->
            <script src="http://localhost:3000/api/js/adtag.js?zone_id=1&format=banner&size=300x250"></script>
        </div>
    </div>

    <!-- Native Ad Test -->
    <div class="section">
        <h2>2. Native Ad with Configuration</h2>
        <p>Native ad with optional grid layout and text styling:</p>

        <div class="code-block">
&lt;script&gt;
// Configure before loading the ad tag
GlobalAdsMedia.configure('YOUR_ZONE_ID', {
  gridLayout: true,        // Enable grid layout
  textStyle: 'bold'        // 'default', 'bold', 'italic'
});
&lt;/script&gt;
&lt;script src="http://localhost:3000/api/js/adtag.js?zone_id=YOUR_ZONE_ID&format=native"&gt;&lt;/script&gt;
        </div>

        <div class="ad-container">
            <h3>Native Ad Zone</h3>
            <script>
            // Configure native ad
            window.GlobalAdsMedia = window.GlobalAdsMedia || {};
            GlobalAdsMedia.configure = GlobalAdsMedia.configure || function() {};
            GlobalAdsMedia.configure('2', {
                gridLayout: true,
                textStyle: 'bold'
            });
            </script>
            <!-- Single script - container created automatically -->
            <script src="http://localhost:3000/api/js/adtag.js?zone_id=2&format=native"></script>
        </div>
    </div>

    <!-- In-Page Push Test -->
    <div class="section">
        <h2>3. In-Page Push Notification</h2>
        <p>Push notification that appears as an overlay with configurable position:</p>

        <div class="code-block">
&lt;script&gt;
// Configure before loading the ad tag
GlobalAdsMedia.configure('YOUR_ZONE_ID', {
  position: 'bottom-right', // 'top-left', 'top-right', 'bottom-left', 'bottom-right'
  autoClose: true,         // Auto close notification
  closeDelay: 5000         // Close after 5 seconds
});
&lt;/script&gt;
&lt;script src="http://localhost:3000/api/js/adtag.js?zone_id=YOUR_ZONE_ID&format=in_page_push"&gt;&lt;/script&gt;
        </div>

        <div class="ad-container">
            <h3>Push Notification Zone</h3>
            <script>
            // Configure push notification
            window.GlobalAdsMedia = window.GlobalAdsMedia || {};
            GlobalAdsMedia.configure = GlobalAdsMedia.configure || function() {};
            GlobalAdsMedia.configure('3', {
                position: 'bottom-right',
                autoClose: true,
                closeDelay: 8000
            });
            </script>
            <!-- Single script - container created automatically -->
            <script src="http://localhost:3000/api/js/adtag.js?zone_id=3&format=in_page_push"></script>
        </div>
    </div>

    <!-- Popup Ad Test -->
    <div class="section">
        <h2>4. Popup Ad (User Interaction Required)</h2>
        <p>Popup ad that only triggers after user interaction (browser compliance):</p>

        <div class="code-block">
&lt;script src="http://localhost:3000/api/js/adtag.js?zone_id=YOUR_ZONE_ID&format=popup"&gt;&lt;/script&gt;
        </div>

        <div class="ad-container">
            <h3>Popup Ad Zone</h3>
            <p>Click anywhere on this page first, then the popup will trigger.</p>
            <!-- Single script - container created automatically -->
            <script src="http://localhost:3000/api/js/adtag.js?zone_id=4&format=popup"></script>
        </div>

        <h3>Direct Popup Link</h3>
        <p>For direct popup traffic, use this link format:</p>
        <div class="code-block">
http://localhost:3000/api/serve?zone_id=YOUR_ZONE_ID&format=popup&redirect=1
        </div>
        <p><a href="http://localhost:3000/api/serve?zone_id=4&format=popup&redirect=1" target="_blank">Test Direct Popup Link</a></p>
    </div>

    <!-- Multiple Ads Test -->
    <div class="section">
        <h2>5. Multiple Ads on Same Page</h2>
        <p>The system can handle multiple ad zones on the same page:</p>

        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
            <div class="ad-container" style="flex: 1; min-width: 250px;">
                <h4>Banner 728x90</h4>
                <!-- Single script - container created automatically -->
                <script src="http://localhost:3000/api/js/adtag.js?zone_id=5&format=banner&size=728x90"></script>
            </div>

            <div class="ad-container" style="flex: 1; min-width: 250px;">
                <h4>Banner 300x250</h4>
                <!-- Single script - container created automatically -->
                <script src="http://localhost:3000/api/js/adtag.js?zone_id=6&format=banner&size=300x250"></script>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Features of the Universal Ad Tag</h2>
        <ul>
            <li><strong>True Single Script:</strong> One script tag - no separate div needed</li>
            <li><strong>Auto Container Creation:</strong> Automatically creates and positions ad containers</li>
            <li><strong>Auto-Detection:</strong> Automatically detects and renders the correct ad format</li>
            <li><strong>JS Tag Support:</strong> Safely executes advertiser JavaScript in sandboxed iframes</li>
            <li><strong>Configurable:</strong> Publishers can customize behavior for native and push ads</li>
            <li><strong>Browser Compliant:</strong> Popup ads only trigger after user interaction</li>
            <li><strong>Direct Popup Links:</strong> Support for direct popup traffic routing</li>
            <li><strong>Multiple Zones:</strong> Support for multiple ad zones on the same page</li>
            <li><strong>Error Handling:</strong> Graceful fallbacks when ads fail to load</li>
            <li><strong>Responsive:</strong> Adapts to different screen sizes and containers</li>
        </ul>
    </div>

    <script>
        // Add some interactivity to demonstrate popup compliance
        document.addEventListener('click', function() {
            console.log('User interaction detected - popups can now be triggered');
        });
    </script>
</body>
</html>
