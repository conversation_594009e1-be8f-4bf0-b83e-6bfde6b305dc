(function() {
  'use strict';

  // Get the current script element
  var currentScript = document.currentScript || (function() {
    var scripts = document.getElementsByTagName('script');
    return scripts[scripts.length - 1];
  })();

  // Extract parameters from script attributes
  var zoneId = currentScript.getAttribute('data-zone-id');
  var format = currentScript.getAttribute('data-format') || 'banner';
  var size = currentScript.getAttribute('data-size') || '300x250';

  if (!zoneId) {
    console.error('Global Ads Media: zone-id is required');
    return;
  }

  // Create container for the ad
  var adContainer = document.createElement('div');
  adContainer.id = 'gam-ad-' + zoneId + '-' + Date.now();
  adContainer.style.cssText = 'display: block; margin: 0; padding: 0;';

  // Insert container after the script
  currentScript.parentNode.insertBefore(adContainer, currentScript.nextSibling);

  // Build API URL
  var apiUrl = (currentScript.src.indexOf('localhost') !== -1 ? 'http://localhost:3000' : 'https://globaladsmedia.com') + 
               '/api/serve?zone_id=' + encodeURIComponent(zoneId) + 
               '&format=' + encodeURIComponent(format) + 
               '&size=' + encodeURIComponent(size) +
               '&ref=' + encodeURIComponent(document.referrer) +
               '&url=' + encodeURIComponent(window.location.href);

  // Function to load ad
  function loadAd() {
    // Create JSONP callback
    var callbackName = 'gamCallback' + Date.now() + Math.floor(Math.random() * 1000);
    
    window[callbackName] = function(response) {
      try {
        if (response && response.ad) {
          if (response.ad.type === 'house' || response.ad.html) {
            adContainer.innerHTML = response.ad.html;
            
            // Set container size for banner ads
            if (format === 'banner' && size) {
              var dimensions = size.split('x');
              if (dimensions.length === 2) {
                adContainer.style.width = dimensions[0] + 'px';
                adContainer.style.height = dimensions[1] + 'px';
              }
            }
          }
        } else {
          // No ad available
          adContainer.style.display = 'none';
        }
      } catch (e) {
        console.error('Global Ads Media: Error processing ad response', e);
        adContainer.style.display = 'none';
      }
      
      // Cleanup
      document.head.removeChild(script);
      delete window[callbackName];
    };

    // Create script element for JSONP
    var script = document.createElement('script');
    script.src = apiUrl + '&callback=' + callbackName;
    script.onerror = function() {
      console.error('Global Ads Media: Failed to load ad');
      adContainer.style.display = 'none';
      delete window[callbackName];
    };
    
    document.head.appendChild(script);
  }

  // Load ad when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadAd);
  } else {
    loadAd();
  }

})();
