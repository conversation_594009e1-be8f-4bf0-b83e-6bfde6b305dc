#!/bin/bash

# ClickHouse Data Migration Script
# From: **************:8123 -> To: localhost:8123

OLD_HOST="**************"
OLD_PORT="8123"
NEW_HOST="localhost"
NEW_PORT="8123"
USER="default"
PASSWORD="gamuschdb171288"
DATABASE="global_ads_media"

echo "🚀 Starting ClickHouse Data Migration..."
echo "From: $OLD_HOST:$OLD_PORT -> To: $NEW_HOST:$NEW_PORT"
echo "Database: $DATABASE"
echo ""

# Function to execute query on old database
query_old() {
    clickhouse-client --host $OLD_HOST --port 9000 --user $USER --password $PASSWORD --query "$1"
}

# Function to execute query on new database
query_new() {
    clickhouse-client --host $NEW_HOST --port 9000 --user $USER --password $PASSWORD --query "$1"
}

# Function to migrate table data
migrate_table() {
    local table=$1
    local select_columns="$2"
    
    echo "📊 Migrating table: $table"
    
    # Get count from old database
    old_count=$(query_old "SELECT COUNT(*) FROM $DATABASE.$table")
    echo "  Old database records: $old_count"
    
    if [ "$old_count" -eq 0 ]; then
        echo "  ✅ No data to migrate for $table"
        return
    fi
    
    # Clear existing data in new database
    echo "  🧹 Clearing existing data in new database..."
    query_new "TRUNCATE TABLE $DATABASE.$table" 2>/dev/null || echo "  ⚠️  Table might not exist or already empty"
    
    # Export data from old database and import to new
    echo "  📤 Exporting data from old database..."
    if [ -n "$select_columns" ]; then
        clickhouse-client --host $OLD_HOST --port 9000 --user $USER --password $PASSWORD --query "SELECT $select_columns FROM $DATABASE.$table FORMAT JSONEachRow" > "/tmp/${table}_data.json"
    else
        clickhouse-client --host $OLD_HOST --port 9000 --user $USER --password $PASSWORD --query "SELECT * FROM $DATABASE.$table FORMAT JSONEachRow" > "/tmp/${table}_data.json"
    fi
    
    # Check if export was successful
    if [ ! -s "/tmp/${table}_data.json" ]; then
        echo "  ❌ Export failed or no data found"
        return
    fi
    
    echo "  📥 Importing data to new database..."
    cat "/tmp/${table}_data.json" | clickhouse-client --host $NEW_HOST --port 9000 --user $USER --password $PASSWORD --query "INSERT INTO $DATABASE.$table FORMAT JSONEachRow"
    
    # Verify migration
    new_count=$(query_new "SELECT COUNT(*) FROM $DATABASE.$table")
    echo "  New database records: $new_count"
    
    if [ "$old_count" -eq "$new_count" ]; then
        echo "  ✅ Migration successful for $table"
    else
        echo "  ❌ Migration failed for $table (count mismatch)"
    fi
    
    # Cleanup
    rm -f "/tmp/${table}_data.json"
    echo ""
}

# Start migration
echo "🔄 Starting table migrations..."
echo ""

# 1. Users table (exclude extra columns from old schema)
migrate_table "users" "id, email, password, full_name, address, city, zip, state, country, role, status, balance, email_verified, email_verification_token, created_at, updated_at"

# 2. Websites table
migrate_table "websites"

# 3. Ad Zones table
migrate_table "ad_zones"

# 4. Campaigns table
migrate_table "campaigns"

# 5. Email Settings table
migrate_table "email_settings"

# 6. Email Templates table
migrate_table "email_templates"

# 7. Partner Endpoints table
migrate_table "partner_endpoints"

# 8. Platform Settings table
migrate_table "platform_settings"

echo "🎉 Migration completed!"
echo ""
echo "📋 Summary:"
for table in users websites ad_zones campaigns email_settings email_templates partner_endpoints platform_settings; do
    count=$(query_new "SELECT COUNT(*) FROM $DATABASE.$table" 2>/dev/null || echo "0")
    echo "  $table: $count records"
done
