#!/bin/bash

# ClickHouse System Logs TTL Setup Script
# Sets TTL of 3 hours for all system log tables

PASSWORD="gamuschdb171288"

echo "🕒 Setting up ClickHouse System Logs TTL (3 hours)..."
echo "This will automatically clean up log data older than 3 hours"
echo ""

# Function to execute ClickHouse query
execute_query() {
    local query="$1"
    local description="$2"
    
    echo "📝 $description"
    echo "   Query: $query"
    
    if clickhouse-client --password $PASSWORD --query "$query"; then
        echo "   ✅ Success"
    else
        echo "   ❌ Failed"
    fi
    echo ""
}

# System log tables with their timestamp columns
declare -A LOG_TABLES=(
    ["system.query_log"]="event_time"
    ["system.part_log"]="event_time"
    ["system.text_log"]="event_time"
    ["system.trace_log"]="event_time"
    ["system.metric_log"]="event_time"
    ["system.asynchronous_metric_log"]="event_time"
    ["system.asynchronous_insert_log"]="event_time"
    ["system.error_log"]="event_time"
    ["system.processors_profile_log"]="event_time"
    ["system.latency_log"]="event_time"
)

echo "🔧 Configuring TTL for system log tables..."
echo ""

# Set TTL for each system log table
for table in "${!LOG_TABLES[@]}"; do
    timestamp_col="${LOG_TABLES[$table]}"
    
    # Check if table exists and get its current structure
    table_exists=$(clickhouse-client --password $PASSWORD --query "EXISTS TABLE $table" 2>/dev/null)
    
    if [ "$table_exists" = "1" ]; then
        echo "📊 Processing table: $table"
        
        # Try to modify TTL (this might fail for system tables, which is expected)
        execute_query "ALTER TABLE $table MODIFY TTL $timestamp_col + INTERVAL 3 HOUR DELETE" \
                     "Setting TTL for $table (3 hours)"
    else
        echo "⚠️  Table $table does not exist, skipping..."
        echo ""
    fi
done

echo "🎯 Setting up configuration-based TTL for system tables..."
echo ""

# Create configuration file for system table TTL
cat > /tmp/system_logs_ttl.xml << 'EOF'
<clickhouse>
    <!-- System logs TTL configuration - 3 hours -->
    <query_log>
        <database>system</database>
        <table>query_log</table>
        <partition_by>toYYYYMM(event_date)</partition_by>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </query_log>
    
    <part_log>
        <database>system</database>
        <table>part_log</table>
        <partition_by>toYYYYMM(event_date)</partition_by>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </part_log>
    
    <text_log>
        <database>system</database>
        <table>text_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </text_log>
    
    <trace_log>
        <database>system</database>
        <table>trace_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </trace_log>
    
    <metric_log>
        <database>system</database>
        <table>metric_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </metric_log>
    
    <asynchronous_metric_log>
        <database>system</database>
        <table>asynchronous_metric_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </asynchronous_metric_log>
    
    <asynchronous_insert_log>
        <database>system</database>
        <table>asynchronous_insert_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </asynchronous_insert_log>
    
    <error_log>
        <database>system</database>
        <table>error_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </error_log>
    
    <processors_profile_log>
        <database>system</database>
        <table>processors_profile_log</table>
        <ttl>event_time + INTERVAL 3 HOUR DELETE</ttl>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </processors_profile_log>
</clickhouse>
EOF

echo "📁 Installing system logs TTL configuration..."
if cp /tmp/system_logs_ttl.xml /etc/clickhouse-server/config.d/system_logs_ttl.xml; then
    echo "✅ Configuration file installed successfully"
else
    echo "❌ Failed to install configuration file"
fi

echo ""
echo "🔄 Restarting ClickHouse to apply TTL configuration..."
if systemctl restart clickhouse-server; then
    echo "✅ ClickHouse restarted successfully"
else
    echo "❌ Failed to restart ClickHouse"
    exit 1
fi

echo ""
echo "⏳ Waiting for ClickHouse to start..."
sleep 10

echo ""
echo "🧹 Cleaning up existing old data..."
for table in "${!LOG_TABLES[@]}"; do
    timestamp_col="${LOG_TABLES[$table]}"
    
    # Clean up data older than 3 hours
    execute_query "DELETE FROM $table WHERE $timestamp_col < now() - INTERVAL 3 HOUR" \
                 "Cleaning old data from $table"
done

echo ""
echo "✅ ClickHouse System Logs TTL Setup Complete!"
echo ""
echo "📋 Summary:"
echo "   • TTL set to 3 hours for all system log tables"
echo "   • Automatic cleanup will run periodically"
echo "   • Old data has been cleaned up"
echo "   • Configuration saved to /etc/clickhouse-server/config.d/system_logs_ttl.xml"
echo ""
echo "🔍 To verify TTL settings:"
echo "   clickhouse-client --password $PASSWORD --query \"SHOW CREATE TABLE system.query_log\""

# Cleanup
rm -f /tmp/system_logs_ttl.xml
