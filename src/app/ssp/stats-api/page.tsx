'use client';

import React, { useState, useEffect } from 'react';
import {
  KeyIcon,
  DocumentDuplicateIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  PlusIcon,
  CodeBracketIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface ApiKey {
  id: number;
  apiKey: string;
  apiSecret: string;
  keyName: string;
  keyType: 'dsp' | 'ssp' | 'admin';
  permissions: string[];
  rateLimitPerHour: number;
  rateLimitPerDay: number;
  totalRequests: number;
  lastUsedAt: string;
  status: 'active' | 'suspended' | 'revoked';
  createdAt: string;
  description: string;
}

export default function SspStatsApiPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showSecrets, setShowSecrets] = useState<{ [key: string]: boolean }>({});
  const [selectedTab, setSelectedTab] = useState<'keys' | 'docs'>('keys');

  // Form state
  const [formData, setFormData] = useState({
    keyName: '',
    keyType: 'ssp' as 'dsp' | 'ssp',
    permissions: ['stats:read'],
    partnerId: '',
    rateLimitPerHour: 1000,
    rateLimitPerDay: 10000,
    description: '',
  });

  useEffect(() => {
    fetchApiKeys();
  }, []);

  const fetchApiKeys = async () => {
    try {
      const response = await fetch('/api/admin/api-keys?key_type=ssp');
      const result = await response.json();
      if (result.success) {
        setApiKeys(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch API keys:', error);
    } finally {
      setLoading(false);
    }
  };

  const createApiKey = async () => {
    try {
      const response = await fetch('/api/admin/api-keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          partnerId: parseInt(formData.partnerId) || 0,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setApiKeys([result.data, ...apiKeys]);
        setShowCreateForm(false);
        setFormData({
          keyName: '',
          keyType: 'ssp',
          permissions: ['stats:read'],
          partnerId: '',
          rateLimitPerHour: 1000,
          rateLimitPerDay: 10000,
          description: '',
        });
        alert('API key created successfully! Make sure to copy the secret now.');
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to create API key:', error);
      alert('Failed to create API key');
    }
  };

  const revokeApiKey = async (apiKey: string) => {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/api-keys?api_key=${apiKey}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        setApiKeys(apiKeys.filter(key => key.apiKey !== apiKey));
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to revoke API key:', error);
      alert('Failed to revoke API key');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  const toggleSecretVisibility = (keyId: number) => {
    setShowSecrets(prev => ({
      ...prev,
      [keyId]: !prev[keyId],
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl">Loading SSP Stats API...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">SSP Stats API</h1>
          <p className="mt-2 text-gray-600">Manage API keys and access documentation for SSP statistics</p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setSelectedTab('keys')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === 'keys'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <KeyIcon className="w-5 h-5 inline mr-2" />
                API Keys
              </button>
              <button
                onClick={() => setSelectedTab('docs')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedTab === 'docs'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <CodeBracketIcon className="w-5 h-5 inline mr-2" />
                Documentation
              </button>
            </nav>
          </div>
        </div>

        {selectedTab === 'keys' && (
          <div>
            {/* Create API Key Button */}
            <div className="mb-6">
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Create New API Key
              </button>
            </div>

            {/* Create Form Modal */}
            {showCreateForm && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 w-full max-w-md">
                  <h3 className="text-lg font-semibold mb-4">Create New SSP API Key</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Key Name</label>
                      <input
                        type="text"
                        value={formData.keyName}
                        onChange={(e) => setFormData({ ...formData, keyName: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2"
                        placeholder="My SSP API Key"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Partner ID</label>
                      <input
                        type="number"
                        value={formData.partnerId}
                        onChange={(e) => setFormData({ ...formData, partnerId: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2"
                        placeholder="SSP Partner ID"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Permissions</label>
                      <select
                        multiple
                        value={formData.permissions}
                        onChange={(e) => setFormData({ 
                          ...formData, 
                          permissions: Array.from(e.target.selectedOptions, option => option.value)
                        })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2"
                      >
                        <option value="stats:read">Stats Read</option>
                        <option value="reports:read">Reports Read</option>
                        <option value="billing:read">Billing Read</option>
                      </select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Hourly Limit</label>
                        <input
                          type="number"
                          value={formData.rateLimitPerHour}
                          onChange={(e) => setFormData({ ...formData, rateLimitPerHour: parseInt(e.target.value) })}
                          className="w-full border border-gray-300 rounded-md px-3 py-2"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Daily Limit</label>
                        <input
                          type="number"
                          value={formData.rateLimitPerDay}
                          onChange={(e) => setFormData({ ...formData, rateLimitPerDay: parseInt(e.target.value) })}
                          className="w-full border border-gray-300 rounded-md px-3 py-2"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full border border-gray-300 rounded-md px-3 py-2"
                        rows={3}
                        placeholder="Optional description"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      onClick={() => setShowCreateForm(false)}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={createApiKey}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                      Create API Key
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* API Keys List */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Your SSP API Keys</h2>
              </div>
              <div className="divide-y divide-gray-200">
                {apiKeys.length === 0 ? (
                  <div className="px-6 py-12 text-center">
                    <KeyIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No API keys</h3>
                    <p className="mt-1 text-sm text-gray-500">Get started by creating your first API key.</p>
                  </div>
                ) : (
                  apiKeys.map((key) => (
                    <div key={key.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <h3 className="text-sm font-medium text-gray-900">{key.keyName}</h3>
                            <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                              key.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {key.status}
                            </span>
                          </div>
                          <div className="mt-1 text-sm text-gray-500">
                            Created: {new Date(key.createdAt).toLocaleDateString()} • 
                            Requests: {key.totalRequests.toLocaleString()} • 
                            Permissions: {key.permissions.join(', ')}
                          </div>
                          
                          {/* API Key */}
                          <div className="mt-2 flex items-center space-x-2">
                            <span className="text-xs text-gray-500">API Key:</span>
                            <code className="bg-gray-100 px-2 py-1 rounded text-xs">{key.apiKey}</code>
                            <button
                              onClick={() => copyToClipboard(key.apiKey)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <DocumentDuplicateIcon className="w-4 h-4" />
                            </button>
                          </div>

                          {/* API Secret */}
                          <div className="mt-1 flex items-center space-x-2">
                            <span className="text-xs text-gray-500">API Secret:</span>
                            <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                              {showSecrets[key.id] ? key.apiSecret : '***hidden***'}
                            </code>
                            <button
                              onClick={() => toggleSecretVisibility(key.id)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              {showSecrets[key.id] ? <EyeSlashIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
                            </button>
                            {showSecrets[key.id] && (
                              <button
                                onClick={() => copyToClipboard(key.apiSecret)}
                                className="text-blue-600 hover:text-blue-800"
                              >
                                <DocumentDuplicateIcon className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => revokeApiKey(key.apiKey)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'docs' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <ChartBarIcon className="w-6 h-6 mr-2 text-green-600" />
              SSP Stats API Documentation
            </h2>

            <div className="prose max-w-none">
              <p className="text-gray-600 mb-4">
                The SSP Stats API provides comprehensive statistics and reporting for your SSP inventory.
                Access real-time and historical data to optimize your revenue and fill rates.
              </p>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="w-5 h-5 text-green-600 mr-2" />
                  <span className="font-medium text-green-800">Base URL:</span>
                </div>
                <code className="block mt-2 bg-white px-3 py-2 rounded border">
                  {typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com'}/api/v1/stats
                </code>
              </div>

              <h3 className="text-lg font-semibold mb-3">Authentication</h3>
              <p className="mb-4">Include your SSP API key in the request headers:</p>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
{`Authorization: Bearer YOUR_SSP_API_KEY
# or
X-API-Key: YOUR_SSP_API_KEY`}
              </pre>

              <h3 className="text-lg font-semibold mb-3 mt-6">Example Request</h3>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
{`curl -X GET "https://your-domain.com/api/v1/stats?start_date=2024-01-01&end_date=2024-01-31" \\
  -H "Authorization: Bearer YOUR_SSP_API_KEY" \\
  -H "Content-Type: application/json"`}
              </pre>

              <h3 className="text-lg font-semibold mb-3 mt-6">SSP-Specific Response</h3>
              <p className="mb-4">SSP API keys return revenue-focused metrics:</p>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
{`{
  "success": true,
  "data": {
    "summary": {
      "total_impressions": 2500000,
      "total_revenue": 5000.00,
      "avg_rpm": "2.0000",
      "fill_rate": "85.5"
    },
    "time_series": [
      {
        "time_period": "2024-01-01",
        "impressions": 100000,
        "revenue": 200.00,
        "avg_rpm": 2.00,
        "fill_rate": 85.5
      }
    ],
    "top_websites": [
      {
        "website_id": 456,
        "impressions": 500000,
        "revenue": 1000.00,
        "avg_rpm": 2.00
      }
    ],
    "geographic_breakdown": [
      {
        "country": "US",
        "impressions": 1500000,
        "revenue": 3000.00
      }
    ]
  },
  "meta": {
    "api_key_type": "ssp",
    "partner_id": 456
  }
}`}
              </pre>

              <h3 className="text-lg font-semibold mb-3 mt-6">SSP Query Parameters</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm border border-gray-200 rounded-lg">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-4 border-b">Parameter</th>
                      <th className="text-left py-3 px-4 border-b">Type</th>
                      <th className="text-left py-3 px-4 border-b">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="py-3 px-4"><code>start_date</code></td>
                      <td className="py-3 px-4">string</td>
                      <td className="py-3 px-4">Start date (YYYY-MM-DD). Default: 7 days ago</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4"><code>end_date</code></td>
                      <td className="py-3 px-4">string</td>
                      <td className="py-3 px-4">End date (YYYY-MM-DD). Default: today</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4"><code>website_id</code></td>
                      <td className="py-3 px-4">integer</td>
                      <td className="py-3 px-4">Filter by specific website</td>
                    </tr>
                    <tr className="border-b">
                      <td className="py-3 px-4"><code>granularity</code></td>
                      <td className="py-3 px-4">string</td>
                      <td className="py-3 px-4">hour, day, week, month. Default: day</td>
                    </tr>
                    <tr>
                      <td className="py-3 px-4"><code>format</code></td>
                      <td className="py-3 px-4">string</td>
                      <td className="py-3 px-4">json or csv. Default: json</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <h3 className="text-lg font-semibold mb-3 mt-6">SSP Metrics Explained</h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li><strong>Revenue:</strong> Total publisher revenue earned</li>
                  <li><strong>RPM:</strong> Revenue per thousand impressions</li>
                  <li><strong>Fill Rate:</strong> Percentage of ad requests filled</li>
                  <li><strong>Top Websites:</strong> Best performing inventory sources</li>
                  <li><strong>Geographic Data:</strong> Revenue breakdown by country</li>
                </ul>
              </div>

              <h3 className="text-lg font-semibold mb-3 mt-6">Rate Limits</h3>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <ul className="list-disc list-inside text-gray-700">
                  <li>Default: 1,000 requests per hour, 10,000 requests per day</li>
                  <li>Rate limits are per API key</li>
                  <li>Exceeded limits return HTTP 429 status</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
