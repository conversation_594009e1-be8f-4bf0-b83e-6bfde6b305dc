'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';
import { formatNumber, formatCurrency } from '@/lib/format-utils';

interface StatData {
  date?: string;
  partner_id?: number;
  partner_name?: string;
  country?: string;
  os?: string;
  browser?: string;
  device_type?: string;
  total_requests?: number;
  total_wins?: number;
  total_revenue?: number;
  win_rate?: number;
  avg_win_price?: number;
}

// Helper function to format currency with variable decimal places
const formatCurrencyWithDecimals = (amount: number, decimals: number = 2): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(amount);
};

export default function SSPStatistics() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  }>({ key: '', direction: 'desc' });

  const [filters, setFilters] = useState({
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    dateTo: new Date().toISOString().split('T')[0],
    groupBy: 'day',
  });

  const handleSort = (key: string) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  const getSortedData = (data: any[]) => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Convert string numbers to actual numbers for sorting
      if (typeof aValue === 'string' && !isNaN(parseFloat(aValue))) {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }

      if (aValue === bValue) return 0;
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  const SortIcon = ({ columnKey }: { columnKey: string }) => {
    if (sortConfig.key !== columnKey) {
      return (
        <svg className="w-3 h-3 ml-1.5" fill="currentColor" viewBox="0 0 320 512">
          <path d="M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm238-64H41c-21.4 0-32.1-25.9-17-41L143 64c9.4-9.4 24.6-9.4 33.9 0l119 119c15.1 15.1 4.4 41-17 41z"/>
        </svg>
      );
    }
    return sortConfig.direction === 'asc' ? (
      <svg className="w-3 h-3 ml-1.5" fill="currentColor" viewBox="0 0 320 512">
        <path d="M279 224H41c-21.4 0-32.1-25.9-17-41L143 64c9.4-9.4 24.6-9.4 33.9 0l119 119c15.1 15.1 4.4 41-17 41z"/>
      </svg>
    ) : (
      <svg className="w-3 h-3 ml-1.5" fill="currentColor" viewBox="0 0 320 512">
        <path d="M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41z"/>
      </svg>
    );
  };

  useEffect(() => {
    fetchStatistics();
  }, [filters]);

  const fetchStatistics = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        date_from: filters.dateFrom,
        date_to: filters.dateTo,
        group_by: filters.groupBy,
      });

      const response = await fetch(`/api/ssp/statistics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data.data || {});
      } else {
        console.error('Failed to fetch SSP statistics:', response.status, response.statusText);
        setStats({});
      }
    } catch (error) {
      console.error('Failed to fetch SSP statistics:', error);
      setStats({});
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const refreshData = () => {
    fetchStatistics();
  };

  if (!session || session.user?.role !== 'ssp') {
    return <div>Access denied</div>;
  }

  // Calculate totals using only detailed breakdown data as source of truth
  const rawTotals = stats.totals || {};
  let totalRequests = 0;
  let totalWins = 0;
  let totalRevenue = 0;
  let avgWinRate = 0;

  // Calculate from the appropriate breakdown data
  if (filters.groupBy === 'day' && stats.daily_breakdown && stats.daily_breakdown.length > 0) {
    totalRequests = stats.daily_breakdown.reduce((sum: number, day: any) => sum + (parseInt(day.total_requests) || 0), 0);
    totalWins = stats.daily_breakdown.reduce((sum: number, day: any) => sum + (parseInt(day.total_wins) || 0), 0);
    totalRevenue = stats.daily_breakdown.reduce((sum: number, day: any) => sum + (parseFloat(day.total_revenue) || 0), 0);
    avgWinRate = totalRequests > 0 ? (totalWins / totalRequests) * 100 : 0;
  } else if (filters.groupBy !== 'day' && stats[`${filters.groupBy}_breakdown`] && stats[`${filters.groupBy}_breakdown`].length > 0) {
    const breakdownData = stats[`${filters.groupBy}_breakdown`];
    totalRequests = breakdownData.reduce((sum: number, item: any) => sum + (parseInt(item.total_requests) || 0), 0);
    totalWins = breakdownData.reduce((sum: number, item: any) => sum + (parseInt(item.total_wins) || 0), 0);
    totalRevenue = breakdownData.reduce((sum: number, item: any) => sum + (parseFloat(item.total_revenue) || 0), 0);
    avgWinRate = totalRequests > 0 ? (totalWins / totalRequests) * 100 : 0;
  } else {
    // Fallback to API totals if no breakdown data
    totalRequests = parseInt(rawTotals.total_requests) || 0;
    totalWins = parseInt(rawTotals.total_wins) || 0;
    totalRevenue = parseFloat(rawTotals.total_revenue) || 0;
    avgWinRate = parseFloat(rawTotals.avg_win_rate) || 0;
  }

  const totals = {
    total_requests: totalRequests,
    total_wins: totalWins,
    total_revenue: totalRevenue,
    avg_win_rate: avgWinRate
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">SSP Statistics</h1>
            <p className="mt-2 text-gray-600">Track your inventory performance and revenue</p>
          </div>
          <button
            onClick={refreshData}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Filters</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Group By</label>
                <select
                  value={filters.groupBy}
                  onChange={(e) => handleFilterChange('groupBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="day">Day</option>
                  <option value="endpoint">Endpoint</option>
                  <option value="country">Country</option>
                  <option value="os">OS</option>
                  <option value="browser">Browser</option>
                  <option value="device">Device</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Requests</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.total_requests || 0)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Wins</div>
            <div className="text-2xl font-bold text-green-600">{formatNumber(totals.total_wins || 0)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Win Rate</div>
            <div className="text-2xl font-bold text-blue-600">{(totals.avg_win_rate || 0).toFixed(2)}%</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Revenue</div>
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(totals.total_revenue || 0)}</div>
          </div>
        </div>


        {/* Charts */}
        {filters.groupBy === 'day' && stats.daily_breakdown && stats.daily_breakdown.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Requests & Wins Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats.daily_breakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="total_requests" stroke="#3B82F6" name="Requests" />
                  <Line type="monotone" dataKey="total_wins" stroke="#10B981" name="Wins" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats.wins_breakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="total_revenue" fill="#10B981" name="Revenue ($)" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {filters.groupBy !== 'day' && stats[`${filters.groupBy}_breakdown`] && stats[`${filters.groupBy}_breakdown`].length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{filters.groupBy.charAt(0).toUpperCase() + filters.groupBy.slice(1)} Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats[`${filters.groupBy}_breakdown`].slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey={
                    filters.groupBy === 'endpoint' ? 'partner_name' :
                    filters.groupBy === 'device' ? 'device_type' :
                    filters.groupBy
                  } 
                  angle={-45} 
                  textAnchor="end" 
                  height={100} 
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#3B82F6" name="Wins" />
                <Bar dataKey="total_revenue" fill="#10B981" name="Revenue ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Statistics Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Detailed Statistics</h2>
          </div>
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Loading statistics...</p>
              </div>
            ) : (
              <div className="p-6">
                {filters.groupBy === 'day' && stats.daily_breakdown && stats.daily_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('date')}
                        >
                          Date
                          <SortIcon columnKey="date" />
                        </th>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('total_requests')}
                        >
                          Requests
                          <SortIcon columnKey="total_requests" />
                        </th>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('total_wins')}
                        >
                          Wins
                          <SortIcon columnKey="total_wins" />
                        </th>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('win_rate')}
                        >
                          Win Rate
                          <SortIcon columnKey="win_rate" />
                        </th>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('total_revenue')}
                        >
                          Total Revenue
                          <SortIcon columnKey="total_revenue" />
                        </th>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('avg_win_price')}
                        >
                          Avg Win Price
                          <SortIcon columnKey="avg_win_price" />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.daily_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.date}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_requests || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{(stat.win_rate || 0).toFixed(2)}%</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_revenue || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrencyWithDecimals(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy !== 'day' && stats[`${filters.groupBy}_breakdown`] && stats[`${filters.groupBy}_breakdown`].length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort(filters.groupBy === 'endpoint' ? 'partner_name' : 
                                                  filters.groupBy === 'device' ? 'device_type' : 
                                                  filters.groupBy)}
                        >
                          {filters.groupBy === 'endpoint' ? 'Endpoint' :
                           filters.groupBy === 'device' ? 'Device Type' :
                           filters.groupBy.charAt(0).toUpperCase() + filters.groupBy.slice(1)}
                          <SortIcon columnKey={filters.groupBy === 'endpoint' ? 'partner_name' : 
                                              filters.groupBy === 'device' ? 'device_type' : 
                                              filters.groupBy} />
                        </th>
                        {(filters.groupBy === 'day' || filters.groupBy === 'endpoint') && (
                          <>
                            <th 
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                              onClick={() => handleSort('total_requests')}
                            >
                              Requests
                              <SortIcon columnKey="total_requests" />
                            </th>
                          </>
                        )}
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('total_wins')}
                        >
                          Wins
                          <SortIcon columnKey="total_wins" />
                        </th>
                        {(filters.groupBy === 'day' || filters.groupBy === 'endpoint') && (
                          <th 
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                            onClick={() => handleSort('win_rate')}
                          >
                            Win Rate
                            <SortIcon columnKey="win_rate" />
                          </th>
                        )}
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('total_revenue')}
                        >
                          Total Revenue
                          <SortIcon columnKey="total_revenue" />
                        </th>
                        <th 
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 flex items-center"
                          onClick={() => handleSort('avg_win_price')}
                        >
                          Avg Win Price
                          <SortIcon columnKey="avg_win_price" />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats[`${filters.groupBy}_breakdown`]).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {filters.groupBy === 'endpoint' ? stat.partner_name :
                             filters.groupBy === 'device' ? stat.device_type :
                             filters.groupBy === 'country' ? stat.country :
                             filters.groupBy === 'os' ? stat.os :
                             filters.groupBy === 'browser' ? stat.browser : ''}
                          </td>
                          {(filters.groupBy === 'day' || filters.groupBy === 'endpoint') && (
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatNumber(stat.total_requests || 0)}
                            </td>
                          )}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatNumber(stat.total_wins || 0)}
                          </td>
                          {(filters.groupBy === 'day' || filters.groupBy === 'endpoint') && (
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {(stat.win_rate || 0).toFixed(2)}%
                            </td>
                          )}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(stat.total_revenue || 0)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrencyWithDecimals(stat.avg_win_price || 0, 6)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {(!stats.daily_breakdown && 
                  !stats.endpoint_breakdown && 
                  !stats.country_breakdown && 
                  !stats.os_breakdown && 
                  !stats.browser_breakdown && 
                  !stats.device_breakdown) && (
                  <div className="text-center py-12">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                    <p className="mt-1 text-sm text-gray-500">Statistics will appear here once you have inventory activity.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
