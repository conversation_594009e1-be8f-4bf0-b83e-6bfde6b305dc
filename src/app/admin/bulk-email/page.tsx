'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface User {
  id: number;
  email: string;
  full_name: string;
  role: string;
}

export default function BulkEmailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);

  // Form state
  const [recipientType, setRecipientType] = useState<'all' | 'types' | 'specific'>('types');
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['advertiser']);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [filterRole, setFilterRole] = useState('all');

  // Result state
  const [result, setResult] = useState<any>(null);

  // Email templates
  const emailTemplates = [
    {
      name: 'Platform Update',
      subject: 'Important Platform Update - Global Ads Media',
      message: 'We are excited to announce a new update to our platform that will enhance your advertising experience.\n\nKey improvements include:\n• Enhanced targeting options\n• Improved reporting dashboard\n• Better campaign optimization tools\n\nThese updates are now live and ready for you to use. If you have any questions, please don\'t hesitate to contact our support team.\n\nThank you for being a valued partner!'
    },
    {
      name: 'Maintenance Notice',
      subject: 'Scheduled Maintenance - Global Ads Media',
      message: 'We will be performing scheduled maintenance on our platform to improve performance and add new features.\n\nMaintenance Details:\n• Date: [DATE]\n• Time: [TIME]\n• Duration: Approximately 2 hours\n• Impact: Platform will be temporarily unavailable\n\nWe apologize for any inconvenience and appreciate your understanding. All campaigns will resume automatically once maintenance is complete.'
    },
    {
      name: 'Payment Reminder',
      subject: 'Payment Reminder - Global Ads Media',
      message: 'This is a friendly reminder regarding your account balance.\n\nPlease review your account and ensure sufficient funds are available for your campaigns to continue running smoothly.\n\nIf you have any questions about billing or need assistance with payments, our support team is here to help.\n\nThank you for your attention to this matter.'
    },
    {
      name: 'Welcome Message',
      subject: 'Welcome to {{platform_name}}, {{user_name}}!',
      message: 'Dear {{user_name}},\n\nWelcome to {{platform_name}}! We\'re thrilled to have you join our advertising platform as a {{user_role}}.\n\nYour account details:\n• Email: {{user_email}}\n• Role: {{user_role}}\n• Current Balance: {{account_balance}}\n• Join Date: {{current_date}}\n\nTo get started:\n• Complete your profile setup\n• Review our platform guidelines\n• Contact {{support_email}} if you need assistance\n\nOur team is here to help you succeed. We look forward to a successful partnership!'
    }
  ];

  useEffect(() => {
    if (status === 'loading') return;
    if (!session || session.user?.role !== 'admin') {
      router.push('/login');
      return;
    }
    fetchUsers();
  }, [session, status, router]);

  useEffect(() => {
    if (filterRole === 'all') {
      setFilteredUsers(users);
    } else {
      setFilteredUsers(users.filter(user => user.role === filterRole));
    }
  }, [users, filterRole]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/bulk-email');
      const data = await response.json();
      setUsers(data.users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleTypeChange = (type: string) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter(t => t !== type));
    } else {
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  const handleUserChange = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  const handleTemplateSelect = (template: typeof emailTemplates[0]) => {
    setSubject(template.subject);
    setMessage(template.message);
  };

  const insertPlaceholder = (placeholder: string) => {
    const textarea = document.getElementById('message') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newMessage = message.substring(0, start) + placeholder + message.substring(end);
      setMessage(newMessage);

      // Set cursor position after inserted placeholder
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
      }, 0);
    } else {
      // Fallback: append to end
      setMessage(message + placeholder);
    }
  };

  // Available placeholders based on recipient type
  const getAvailablePlaceholders = () => {
    const commonPlaceholders = [
      { placeholder: '{{user_name}}', description: 'User\'s full name', example: 'John Smith' },
      { placeholder: '{{user_email}}', description: 'User\'s email address', example: '<EMAIL>' },
      { placeholder: '{{user_role}}', description: 'User\'s role', example: 'Advertiser' },
      { placeholder: '{{platform_name}}', description: 'Platform name', example: 'Global Ads Media' },
      { placeholder: '{{current_date}}', description: 'Current date', example: 'December 15, 2024' },
      { placeholder: '{{support_email}}', description: 'Support email', example: '<EMAIL>' },
    ];

    const roleSpecificPlaceholders: { [key: string]: any[] } = {
      advertiser: [
        { placeholder: '{{account_balance}}', description: 'Current account balance', example: '$1,250.00' },
        { placeholder: '{{active_campaigns}}', description: 'Number of active campaigns', example: '5' },
        { placeholder: '{{total_spent}}', description: 'Total amount spent', example: '$5,430.25' },
        { placeholder: '{{last_login}}', description: 'Last login date', example: 'December 10, 2024' },
      ],
      publisher: [
        { placeholder: '{{account_balance}}', description: 'Current account balance', example: '$850.00' },
        { placeholder: '{{active_websites}}', description: 'Number of active websites', example: '3' },
        { placeholder: '{{total_earned}}', description: 'Total amount earned', example: '$3,200.50' },
        { placeholder: '{{pending_payout}}', description: 'Pending payout amount', example: '$450.00' },
      ],
      dsp: [
        { placeholder: '{{account_balance}}', description: 'Current account balance', example: '$15,000.00' },
        { placeholder: '{{api_requests}}', description: 'Monthly API requests', example: '125,000' },
        { placeholder: '{{win_rate}}', description: 'Auction win rate', example: '23.5%' },
      ],
      ssp: [
        { placeholder: '{{account_balance}}', description: 'Current account balance', example: '$8,500.00' },
        { placeholder: '{{inventory_requests}}', description: 'Monthly inventory requests', example: '85,000' },
        { placeholder: '{{fill_rate}}', description: 'Inventory fill rate', example: '78.2%' },
      ]
    };

    let availablePlaceholders = [...commonPlaceholders];

    // Add role-specific placeholders based on selected recipient types
    if (recipientType === 'types' && selectedTypes.length > 0) {
      selectedTypes.forEach(type => {
        if (roleSpecificPlaceholders[type]) {
          availablePlaceholders.push(...roleSpecificPlaceholders[type]);
        }
      });
    } else if (recipientType === 'all') {
      // Add all role-specific placeholders for "all users"
      Object.values(roleSpecificPlaceholders).forEach(placeholders => {
        availablePlaceholders.push(...placeholders);
      });
    }

    // Remove duplicates
    const uniquePlaceholders = availablePlaceholders.filter((placeholder, index, self) =>
      index === self.findIndex(p => p.placeholder === placeholder.placeholder)
    );

    return uniquePlaceholders;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/bulk-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipient_types: recipientType === 'types' ? selectedTypes : [],
          subject,
          message,
          send_to_all: recipientType === 'all',
          specific_users: recipientType === 'specific' ? selectedUsers : [],
        }),
      });

      const data = await response.json();
      setResult(data);

      if (response.ok) {
        // Reset form on success
        setSubject('');
        setMessage('');
        setSelectedUsers([]);
      }
    } catch (error) {
      console.error('Error sending bulk email:', error);
      setResult({ message: 'Failed to send emails' });
    } finally {
      setLoading(false);
    }
  };

  const userTypeOptions = [
    { value: 'advertiser', label: 'Advertisers', color: 'bg-blue-100 text-blue-800' },
    { value: 'publisher', label: 'Publishers', color: 'bg-green-100 text-green-800' },
    { value: 'dsp', label: 'DSPs', color: 'bg-purple-100 text-purple-800' },
    { value: 'ssp', label: 'SSPs', color: 'bg-orange-100 text-orange-800' },
  ];

  const getRecipientCount = () => {
    if (recipientType === 'all') {
      return users.length;
    } else if (recipientType === 'types') {
      return users.filter(user => selectedTypes.includes(user.role)).length;
    } else {
      return selectedUsers.length;
    }
  };

  if (status === 'loading') {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Bulk Email</h1>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Recipient Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Recipients
              </label>

              <div className="space-y-4">
                {/* All Users Option */}
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recipientType"
                    value="all"
                    checked={recipientType === 'all'}
                    onChange={(e) => setRecipientType(e.target.value as any)}
                    className="mr-3"
                  />
                  <span className="text-sm">Send to all users ({users.length} users)</span>
                </label>

                {/* User Types Option */}
                <div>
                  <label className="flex items-center mb-3">
                    <input
                      type="radio"
                      name="recipientType"
                      value="types"
                      checked={recipientType === 'types'}
                      onChange={(e) => setRecipientType(e.target.value as any)}
                      className="mr-3"
                    />
                    <span className="text-sm">Send to specific user types</span>
                  </label>

                  {recipientType === 'types' && (
                    <div className="ml-6 grid grid-cols-2 gap-3">
                      {userTypeOptions.map((option) => (
                        <label key={option.value} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedTypes.includes(option.value)}
                            onChange={() => handleTypeChange(option.value)}
                            className="mr-2"
                          />
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${option.color}`}>
                            {option.label} ({users.filter(u => u.role === option.value).length})
                          </span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>

                {/* Specific Users Option */}
                <div>
                  <label className="flex items-center mb-3">
                    <input
                      type="radio"
                      name="recipientType"
                      value="specific"
                      checked={recipientType === 'specific'}
                      onChange={(e) => setRecipientType(e.target.value as any)}
                      className="mr-3"
                    />
                    <span className="text-sm">Send to specific users</span>
                  </label>

                  {recipientType === 'specific' && (
                    <div className="ml-6">
                      <div className="mb-3">
                        <select
                          value={filterRole}
                          onChange={(e) => setFilterRole(e.target.value)}
                          className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                        >
                          <option value="all">All Roles</option>
                          {userTypeOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3">
                        {filteredUsers.map((user) => (
                          <label key={user.id} className="flex items-center mb-2">
                            <input
                              type="checkbox"
                              checked={selectedUsers.includes(user.id.toString())}
                              onChange={() => handleUserChange(user.id.toString())}
                              className="mr-2"
                            />
                            <span className="text-sm">
                              {user.full_name} ({user.email}) -
                              <span className={`ml-1 px-1 py-0.5 rounded text-xs ${
                                userTypeOptions.find(opt => opt.value === user.role)?.color || 'bg-gray-100'
                              }`}>
                                {user.role}
                              </span>
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-3 text-sm text-gray-600">
                <strong>Recipients: {getRecipientCount()} users</strong>
              </div>
            </div>

            {/* Email Templates */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Quick Templates (Optional)
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {emailTemplates.map((template, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleTemplateSelect(template)}
                    className="text-left p-3 border border-gray-200 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
                  >
                    <div className="font-medium text-sm text-gray-900">{template.name}</div>
                    <div className="text-xs text-gray-500 mt-1 truncate">{template.subject}</div>
                  </button>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Click a template to auto-fill the subject and message fields.
              </p>
            </div>

            {/* Subject */}
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                Subject *
              </label>
              <input
                type="text"
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                required
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter email subject"
              />
            </div>

            {/* Message */}
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Message *
              </label>
              <textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                required
                rows={8}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your message here..."
              />
              <p className="text-xs text-gray-500 mt-1">
                The message will be formatted with proper styling and platform branding.
              </p>
            </div>

            {/* Dynamic Placeholders */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Available Placeholders
              </label>
              <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                <p className="text-xs text-gray-600 mb-3">
                  Click any placeholder below to insert it into your message. These will be automatically replaced with actual user data when emails are sent.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                  {getAvailablePlaceholders().map((item, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => insertPlaceholder(item.placeholder)}
                      className="text-left p-2 bg-white border border-gray-200 rounded hover:border-blue-500 hover:bg-blue-50 transition-colors group"
                      title={`Example: ${item.example}`}
                    >
                      <div className="font-mono text-xs text-blue-600 group-hover:text-blue-700">
                        {item.placeholder}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {item.description}
                      </div>
                      <div className="text-xs text-gray-400 mt-1 italic">
                        e.g., {item.example}
                      </div>
                    </button>
                  ))}
                </div>

                {getAvailablePlaceholders().length === 6 && (
                  <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
                    💡 <strong>Tip:</strong> Select specific user types above to see role-specific placeholders (account balance, campaigns, etc.)
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading || getRecipientCount() === 0}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Sending...' : `Send Email to ${getRecipientCount()} Recipients`}
              </button>
            </div>
          </form>

          {/* Result */}
          {result && (
            <div className={`mt-6 p-4 rounded-md ${
              result.success_count > 0 ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <h3 className={`font-medium ${
                result.success_count > 0 ? 'text-green-800' : 'text-red-800'
              }`}>
                Email Results
              </h3>
              <div className={`mt-2 text-sm ${
                result.success_count > 0 ? 'text-green-700' : 'text-red-700'
              }`}>
                {result.success_count !== undefined ? (
                  <>
                    <p>✅ Successfully sent: {result.success_count} emails</p>
                    {result.failure_count > 0 && (
                      <p>❌ Failed to send: {result.failure_count} emails</p>
                    )}
                    <p>📧 Total recipients: {result.total_recipients}</p>
                  </>
                ) : (
                  <p>{result.message}</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
