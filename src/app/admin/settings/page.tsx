'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

export default function AdminSettings() {
  const { data: session } = useSession();

  const [settings, setSettings] = useState({
    platformName: 'Global Ads Media',
    publisherRevenueShare: '20',
    minimumPayout: '100',
    minimumDepositStripe: '100',
    minimumDepositPaypal: '100',
    minimumCpmBanner: '0.50',
    minimumCpmNative: '0.75',
    minimumCpmInPagePush: '0.25',
    minimumCpmPopup: '0.10',
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (session?.user?.role === 'admin') {
      fetchSettings();
    }
  }, [session]);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const data = await response.json();

        // Map database keys to frontend keys
        setSettings({
          platformName: data.platform_name || 'Global Ads Media',
          publisherRevenueShare: data.publisher_revenue_share || '20',
          minimumPayout: data.minimum_payout || '100',
          minimumDepositStripe: data.minimum_deposit_stripe || '100',
          minimumDepositPaypal: data.minimum_deposit_paypal || '100',
          minimumCpmBanner: data.minimum_cpm_banner || '0.50',
          minimumCpmNative: data.minimum_cpm_native || '0.75',
          minimumCpmInPagePush: data.minimum_cpm_in_page_push || '0.25',
          minimumCpmPopup: data.minimum_cpm_popup || '0.10',
        });
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setSuccessMessage('');

    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setSuccessMessage('Settings updated successfully');
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        const error = await response.json();
        console.error('Failed to update settings:', error.message);
      }
    } catch (error) {
      console.error('Error updating settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
          <p className="mt-2 text-gray-600">Configure global platform settings</p>
        </div>

        <div className="bg-white rounded-lg shadow">
          <form onSubmit={handleSubmit} className="p-6 space-y-8">
            {successMessage && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <p className="text-sm text-green-600">{successMessage}</p>
              </div>
            )}

            {/* Platform Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Information</h3>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="platformName" className="block text-sm font-medium text-gray-700">
                    Platform Name
                  </label>
                  <input
                    type="text"
                    id="platformName"
                    name="platformName"
                    value={settings.platformName}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Revenue Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="publisherRevenueShare" className="block text-sm font-medium text-gray-700">
                    Publisher Revenue Share (%)
                  </label>
                  <input
                    type="number"
                    step="1"
                    min="0"
                    max="100"
                    id="publisherRevenueShare"
                    name="publisherRevenueShare"
                    value={settings.publisherRevenueShare}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-sm text-gray-500">Percentage of revenue shared with publishers</p>
                </div>
                <div>
                  <label htmlFor="minimumPayout" className="block text-sm font-medium text-gray-700">
                    Minimum Payout ($)
                  </label>
                  <input
                    type="number"
                    step="1"
                    min="1"
                    id="minimumPayout"
                    name="minimumPayout"
                    value={settings.minimumPayout}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-sm text-gray-500">Minimum amount for publisher payouts</p>
                </div>
              </div>
            </div>

            {/* Deposit Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Minimum Deposit Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="minimumDepositStripe" className="block text-sm font-medium text-gray-700">
                    Stripe Minimum Deposit ($)
                  </label>
                  <input
                    type="number"
                    step="1"
                    min="1"
                    id="minimumDepositStripe"
                    name="minimumDepositStripe"
                    value={settings.minimumDepositStripe}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="minimumDepositPaypal" className="block text-sm font-medium text-gray-700">
                    PayPal Minimum Deposit ($)
                  </label>
                  <input
                    type="number"
                    step="1"
                    min="1"
                    id="minimumDepositPaypal"
                    name="minimumDepositPaypal"
                    value={settings.minimumDepositPaypal}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Minimum Bid Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Minimum CPM Bid Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="minimumCpmBanner" className="block text-sm font-medium text-gray-700">
                    Banner Ads ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    id="minimumCpmBanner"
                    name="minimumCpmBanner"
                    value={settings.minimumCpmBanner}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="minimumCpmNative" className="block text-sm font-medium text-gray-700">
                    Native Ads ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    id="minimumCpmNative"
                    name="minimumCpmNative"
                    value={settings.minimumCpmNative}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="minimumCpmInPagePush" className="block text-sm font-medium text-gray-700">
                    In-Page Push ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    id="minimumCpmInPagePush"
                    name="minimumCpmInPagePush"
                    value={settings.minimumCpmInPagePush}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label htmlFor="minimumCpmPopup" className="block text-sm font-medium text-gray-700">
                    Popup Ads ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    id="minimumCpmPopup"
                    name="minimumCpmPopup"
                    value={settings.minimumCpmPopup}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isSaving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
