'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface SMTPSettings {
  host: string;
  port: number;
  username: string;
  password: string;
  security: string;
  from_email: string;
  from_name: string;
}

export default function AdminEmailSettings() {
  const { data: session } = useSession();
  const [settings, setSettings] = useState<SMTPSettings>({
    host: '',
    port: 587,
    username: '',
    password: '',
    security: 'starttls',
    from_email: '',
    from_name: 'Global Ads Media',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');
  const [showPassword, setShowPassword] = useState(false);
  const [errorDetails, setErrorDetails] = useState<Record<string, unknown> | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/email-settings');
      if (response.ok) {
        const data = await response.json();
        // Keep password visible for easier management
        setSettings(data);
      }
    } catch (error) {
      console.error('Failed to fetch email settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/email-settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setMessage('Email settings saved successfully');
        setMessageType('success');
      } else {
        const data = await response.json();
        setMessage(data.message || 'Failed to save settings');
        setMessageType('error');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      setMessage('An error occurred while saving settings');
      setMessageType('error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    setMessage('');
    setErrorDetails(null);

    // Validate required fields
    if (!settings.host || !settings.username || !settings.password || !settings.from_email) {
      setMessage('Please fill in all required fields (Host, Username, Password, From Email)');
      setMessageType('error');
      setIsTesting(false);
      return;
    }

    try {
      const response = await fetch('/api/admin/email-settings/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('✅ Email connection test successful!');
        setMessageType('success');
        setErrorDetails(null);
      } else {
        setMessage(`❌ Test failed: ${data.message}`);
        setMessageType('error');
        setErrorDetails(data);
      }
    } catch (error) {
      console.error('SMTP test failed:', error);
      setMessage('❌ Test failed: Network error');
      setMessageType('error');
      setErrorDetails(null);
    } finally {
      setIsTesting(false);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Email Settings</h1>
          <p className="mt-2 text-gray-600">Configure SMTP settings for platform email notifications</p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            messageType === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <p className={`text-sm ${messageType === 'success' ? 'text-green-600' : 'text-red-600'}`}>
              {message}
            </p>
          </div>
        )}

        {/* Enhanced Error Details - Only show for detailed SMTP errors */}
        {errorDetails && messageType === 'error' && errorDetails.code && errorDetails.troubleshooting && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-red-800">SMTP Connection Failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p><strong>Error Code:</strong> {errorDetails.code}</p>
                  {errorDetails.error && <p><strong>Details:</strong> {errorDetails.error}</p>}
                </div>

                {errorDetails.troubleshooting && errorDetails.troubleshooting.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-red-800">Troubleshooting Steps:</h4>
                    <ul className="mt-2 list-disc list-inside text-sm text-red-700 space-y-1">
                      {errorDetails.troubleshooting.map((step: string, index: number) => (
                        <li key={index}>{step}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {errorDetails.testedConfig && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-red-800">Tested Configuration:</h4>
                    <div className="mt-2 text-sm text-red-700 bg-red-100 p-3 rounded">
                      <p><strong>Host:</strong> {errorDetails.testedConfig.host}</p>
                      <p><strong>Port:</strong> {errorDetails.testedConfig.port}</p>
                      <p><strong>Security:</strong> {errorDetails.testedConfig.security}</p>
                      <p><strong>Username:</strong> {errorDetails.testedConfig.username}</p>
                    </div>
                  </div>
                )}

                {errorDetails.commonSolutions && errorDetails.commonSolutions.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-red-800">Common Solutions:</h4>
                    <ul className="mt-2 list-disc list-inside text-sm text-red-700 space-y-1">
                      {errorDetails.commonSolutions.map((solution: string, index: number) => (
                        <li key={index}>{solution}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* SMTP Configuration */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">SMTP Configuration</h2>
          </div>
          <form onSubmit={handleSave} className="p-6 space-y-6">
            {/* Server Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">SMTP Host</label>
                <input
                  type="text"
                  value={settings.host}
                  onChange={(e) => setSettings(prev => ({ ...prev, host: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="smtp.gmail.com"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Port</label>
                <input
                  type="number"
                  value={settings.port}
                  onChange={(e) => setSettings(prev => ({ ...prev, port: parseInt(e.target.value) }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="587"
                  required
                />
              </div>
            </div>

            {/* Authentication */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Username</label>
                <input
                  type="text"
                  value={settings.username}
                  onChange={(e) => setSettings(prev => ({ ...prev, username: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Password</label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={settings.password}
                    onChange={(e) => setSettings(prev => ({ ...prev, password: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="App password or SMTP password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Use App Password for Gmail/Outlook if 2FA is enabled
                </p>
              </div>
            </div>

            {/* Security */}
            <div>
              <label className="block text-sm font-medium text-gray-700">Security</label>
              <select
                value={settings.security}
                onChange={(e) => setSettings(prev => ({ ...prev, security: e.target.value }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="starttls">STARTTLS (Recommended)</option>
                <option value="ssl">SSL</option>
                <option value="tls">TLS</option>
                <option value="none">None (Not Recommended)</option>
              </select>
              <p className="mt-1 text-xs text-gray-500">
                STARTTLS (port 587) is recommended for most providers. SSL (port 465) for legacy systems.
              </p>
            </div>

            {/* From Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">From Email</label>
                <input
                  type="email"
                  value={settings.from_email}
                  onChange={(e) => setSettings(prev => ({ ...prev, from_email: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">From Name</label>
                <input
                  type="text"
                  value={settings.from_name}
                  onChange={(e) => setSettings(prev => ({ ...prev, from_name: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Global Ads Media"
                  required
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={handleTestConnection}
                disabled={isTesting || !settings.host || !settings.username}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isTesting ? 'Testing...' : 'Test Connection'}
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isSaving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </form>
        </div>

        {/* Email Templates Section */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">Email Templates</h2>
            <button
              onClick={() => window.location.href = '/admin/email-templates'}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Manage Templates
            </button>
          </div>
          <div className="p-6">
            <p className="text-sm text-gray-600">
              Configure email templates for account verification, campaign approvals, payment notifications, and more.
            </p>
          </div>
        </div>

        {/* Common SMTP Providers */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Common SMTP Providers</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900">Gmail</h4>
              <p className="text-gray-600">Host: smtp.gmail.com, Port: 587, Security: STARTTLS</p>
              <p className="text-xs text-gray-500">Requires App Password if 2FA enabled</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Outlook/Hotmail</h4>
              <p className="text-gray-600">Host: smtp-mail.outlook.com, Port: 587, Security: STARTTLS</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">SendGrid</h4>
              <p className="text-gray-600">Host: smtp.sendgrid.net, Port: 587, Security: STARTTLS</p>
              <p className="text-xs text-gray-500">Use API key as password</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Mailgun</h4>
              <p className="text-gray-600">Host: smtp.mailgun.org, Port: 587, Security: STARTTLS</p>
              <p className="text-xs text-gray-500">Use domain-specific credentials</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
