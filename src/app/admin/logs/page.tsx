'use client';

import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  ArrowPathIcon,
  TrashIcon,
  ClockIcon,
  ServerIcon,
  FolderIcon,
} from '@heroicons/react/24/outline';

interface LogStats {
  [category: string]: {
    size: number;
    lastModified: Date;
    age: number;
  };
}

interface LogStatus {
  logDirectory: string;
  rotationInterval: number;
  totalLogFiles: number;
  totalSize: number;
  totalSizeFormatted: string;
  logStats: LogStats;
  nextRotationTimes: { [key: string]: Date };
  availableLoggers: string[];
  systemInfo: {
    nodeVersion: string;
    platform: string;
    uptime: number;
    memoryUsage: any;
  };
}

export default function LogsPage() {
  const [logStatus, setLogStatus] = useState<LogStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchLogStatus();
    const interval = setInterval(fetchLogStatus, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchLogStatus = async () => {
    try {
      const response = await fetch('/api/admin/logs/status');
      const result = await response.json();
      if (result.success) {
        setLogStatus(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch log status:', error);
    } finally {
      setLoading(false);
    }
  };

  const executeAction = async (action: string, category?: string) => {
    setActionLoading(true);
    try {
      const response = await fetch('/api/admin/logs/status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, category }),
      });
      
      const result = await response.json();
      if (result.success) {
        alert(result.message);
        await fetchLogStatus();
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to execute action:', error);
      alert('Failed to execute action');
    } finally {
      setActionLoading(false);
    }
  };

  const formatAge = (ageMs: number): string => {
    const minutes = Math.floor(ageMs / (60 * 1000));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl">Loading log status...</div>
      </div>
    );
  }

  if (!logStatus) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Failed to load log status</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <DocumentTextIcon className="w-8 h-8 mr-3 text-blue-600" />
            Log Management
          </h1>
          <p className="mt-2 text-gray-600">Monitor and manage RTB platform log files with hourly rotation</p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <FolderIcon className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Files</h3>
                <p className="text-2xl font-bold text-blue-600">{logStatus.totalLogFiles}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <ServerIcon className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Size</h3>
                <p className="text-2xl font-bold text-green-600">{logStatus.totalSizeFormatted}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <ClockIcon className="w-8 h-8 text-yellow-500 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Rotation</h3>
                <p className="text-2xl font-bold text-yellow-600">{logStatus.rotationInterval}h</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <DocumentTextIcon className="w-8 h-8 text-purple-500 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Loggers</h3>
                <p className="text-2xl font-bold text-purple-600">{logStatus.availableLoggers.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
          <div className="flex space-x-4">
            <button
              onClick={() => executeAction('rotate_all')}
              disabled={actionLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <ArrowPathIcon className="w-4 h-4 mr-2" />
              {actionLoading ? 'Processing...' : 'Rotate All Logs'}
            </button>
            
            <button
              onClick={() => executeAction('cleanup_old')}
              disabled={actionLoading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center"
            >
              <TrashIcon className="w-4 h-4 mr-2" />
              Cleanup Old Files
            </button>
            
            <button
              onClick={fetchLogStatus}
              disabled={loading}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center"
            >
              <ArrowPathIcon className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {/* Log Files Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Log Files Status</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Logger Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    File Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Age
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Modified
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Next Rotation
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(logStatus.logStats).map(([category, stats]) => (
                  <tr key={category}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <DocumentTextIcon className="w-5 h-5 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">{category}.log</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatBytes(stats.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatAge(stats.age)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(stats.lastModified).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {logStatus.nextRotationTimes[category] ? 
                        new Date(logStatus.nextRotationTimes[category]).toLocaleString() : 
                        'N/A'
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => executeAction('rotate_single', category)}
                        disabled={actionLoading}
                        className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                      >
                        Rotate
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* System Info */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Log Directory:</p>
              <p className="text-sm font-mono text-gray-900">{logStatus.logDirectory}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Node.js Version:</p>
              <p className="text-sm font-mono text-gray-900">{logStatus.systemInfo.nodeVersion}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Platform:</p>
              <p className="text-sm font-mono text-gray-900">{logStatus.systemInfo.platform}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Uptime:</p>
              <p className="text-sm font-mono text-gray-900">{Math.floor(logStatus.systemInfo.uptime / 3600)}h {Math.floor((logStatus.systemInfo.uptime % 3600) / 60)}m</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
