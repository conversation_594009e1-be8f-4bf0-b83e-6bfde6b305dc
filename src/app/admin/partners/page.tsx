'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Country, State } from 'country-state-city';

const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']; // Define globally

interface PartnerEndpoint {
  id: number;
  name: string;
  type: 'dsp' | 'ssp';
  endpoint_url: string;
  status: string;
  targeting: Targeting | null;
  created_at: string;
  updated_at: string;
  protocol: 'openrtb' | 'xml';
  openrtb_version: string;
  api_key: string;
  timeout_ms: number;
  qps_limit: number;
  revenue_share: number;
  seat_id: string;
  test_mode: boolean;
  auth_type: string;
  auth_credentials: any;
  user_id: number;
  user_name: string;
  user_email: string;
  auction_type?: 'first_price' | 'second_price';
  bid_price_format?: 'cpm' | 'cpv' | 'cpc' | 'ecpm';
}

interface Targeting {
  ad_formats: string[];
  geo: {
    countries: string[];
    states: string[];
  };
  device: {
    devices: string[];
    os: string[];
    browsers: string[];
    connection_types: string[];
  };
  whitelist: {
    publishers: string[];
    websites: string[];
    zones: string[];
  };
  blacklist: {
    publishers: string[];
    websites: string[];
    zones: string[];
  };
  excluded_partners: string[];
  hourly_schedule: boolean[];
  daily_schedule: boolean[];
}

interface PartnerUser {
  id: number;
  full_name: string;
  email: string;
  role: 'dsp' | 'ssp';
  status: string;
  created_at: string;
}

export default function AdminPartners() {
  const { data: session } = useSession();
  const router = useRouter();
  const [endpoints, setEndpoints] = useState<PartnerEndpoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingEndpoint, setEditingEndpoint] = useState<PartnerEndpoint | null>(null);
  const [filter, setFilter] = useState('all');
  const [testingEndpoint, setTestingEndpoint] = useState<number | null>(null);
  const [testResult, setTestResult] = useState<any>(null);

  useEffect(() => {
    fetchEndpoints();
  }, []);

  const fetchEndpoints = async () => {
    try {
      const response = await fetch('/api/admin/partners');
      if (response.ok) {
        const data = await response.json();
        setEndpoints(data);
      }
    } catch (error) {
      console.error('Failed to fetch endpoints:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (endpoint: PartnerEndpoint) => {
    setEditingEndpoint(endpoint);
    setShowEditModal(true);
  };

  const handleDelete = async (endpoint: PartnerEndpoint) => {
    if (!confirm(`Are you sure you want to delete "${endpoint.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/partners?id=${endpoint.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchEndpoints();
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to delete endpoint');
      }
    } catch (error) {
      alert('An error occurred. Please try again.');
    }
  };

  const handleTest = async (endpoint: PartnerEndpoint) => {
    setTestingEndpoint(endpoint.id);
    setTestResult(null);

    try {
      const response = await fetch('/api/admin/partners/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ endpoint_id: endpoint.id }),
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult(data);
      } else {
        const data = await response.json();
        setTestResult({
          message: 'Test failed',
          test_result: {
            success: false,
            error: data.message || 'Unknown error',
          },
        });
      }
    } catch (error) {
      setTestResult({
        message: 'Test failed',
        test_result: {
          success: false,
          error: 'Network error',
        },
      });
    } finally {
      setTestingEndpoint(null);
    }
  };

  const handleToggleStatus = async (endpoint: PartnerEndpoint) => {
    const newStatus = endpoint.status === 'active' ? 'inactive' : 'active';

    try {
      const response = await fetch('/api/admin/partners', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: endpoint.id,
          status: newStatus
        }),
      });

      if (response.ok) {
        fetchEndpoints();
      } else {
        alert('Failed to update endpoint status');
      }
    } catch (error) {
      alert('An error occurred while updating the endpoint status');
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const filteredEndpoints = endpoints.filter(endpoint => {
    if (filter === 'all') return true;
    return endpoint.type === filter;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Partner Endpoints</h1>
            <p className="mt-2 text-gray-600">Manage DSP and SSP integration endpoints</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Endpoint
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {['all', 'dsp', 'ssp'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setFilter(tab)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                    filter === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab === 'all' ? 'All Endpoints' : `${tab.toUpperCase()} Endpoints`}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Endpoints List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {filter === 'all' ? 'All Endpoints' : `${filter.toUpperCase()} Endpoints`} ({filteredEndpoints.length})
            </h2>
          </div>
          <div className="overflow-x-auto">
            {filteredEndpoints.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No endpoints found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filter === 'all' ? 'Partner endpoints will appear here.' : `No ${filter.toUpperCase()} endpoints found.`}
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Protocol</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint URL</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEndpoints.map((endpoint) => (
                    <tr key={endpoint.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{endpoint.name}</div>
                        {endpoint.seat_id && (
                          <div className="text-xs text-gray-500">Seat: {endpoint.seat_id}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{endpoint.user_name || 'User ID: ' + endpoint.user_id}</div>
                        <div className="text-xs text-gray-500">{endpoint.user_email || 'No email available'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full uppercase ${
                          endpoint.type === 'dsp' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {endpoint.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full uppercase ${
                          endpoint.protocol === 'openrtb' ? 'bg-purple-100 text-purple-800' : 'bg-orange-100 text-orange-800'
                        }`}>
                          {endpoint.protocol}
                        </span>
                        {endpoint.protocol === 'openrtb' && (
                          <div className="text-xs text-gray-500 mt-1">v{endpoint.openrtb_version}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2 max-w-xs">
                          <div className="flex-1 min-w-0">
                            <input
                              type="text"
                              value={endpoint.endpoint_url}
                              readOnly
                              className="w-full text-sm text-gray-900 bg-transparent border-none p-0 focus:ring-0 cursor-text"
                              style={{
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden'
                              }}
                            />
                          </div>
                          <button
                            onClick={() => navigator.clipboard.writeText(endpoint.endpoint_url)}
                            className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 focus:outline-none"
                            title="Copy URL"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        </div>
                        {typeof endpoint.test_mode === 'boolean' && endpoint.test_mode && (
                          <div className="text-xs text-yellow-600 mt-1">Test Mode</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleToggleStatus(endpoint)}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                              endpoint.status === 'active' ? 'bg-green-600' : 'bg-gray-200'
                            }`}
                            title={`${endpoint.status === 'active' ? 'Deactivate' : 'Activate'} endpoint`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                endpoint.status === 'active' ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                          <span className={`text-xs font-medium ${
                            endpoint.status === 'active' ? 'text-green-800' : 'text-gray-600'
                          }`}>
                            {endpoint.status === 'active' ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(endpoint)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => router.push(`/admin/partners/test/${endpoint.id}`)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Test
                          </button>
                          <button
                            onClick={() => handleDelete(endpoint)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Create Modal */}
        {showCreateModal && (
          <CreateEndpointModal
            onClose={() => setShowCreateModal(false)}
            onSuccess={() => {
              setShowCreateModal(false);
              fetchEndpoints();
            }}
          />
        )}

        {/* Edit Modal */}
        {showEditModal && editingEndpoint && (
          <EditEndpointModal
            endpoint={editingEndpoint}
            onClose={() => {
              setShowEditModal(false);
              setEditingEndpoint(null);
            }}
            onSuccess={() => {
              setShowEditModal(false);
              setEditingEndpoint(null);
              fetchEndpoints();
            }}
          />
        )}

        {/* Test Result Modal */}
        {testingEndpoint && testResult && (
          <TestResultModal result={testResult} onClose={() => setTestResult(null)} />
        )}
      </div>
    </div>
  );
}

function CreateEndpointModal({ onClose, onSuccess }: { onClose: () => void; onSuccess: () => void }) {
  const [formData, setFormData] = useState({
    name: '',
    type: 'dsp',
    user_id: '',
    endpoint_url: '',
    protocol: 'openrtb',
    openrtb_version: '2.5',
    api_key: '',
    timeout_ms: 300,
    qps_limit: 100,
    revenue_share: 0,
    seat_id: '',
    test_mode: false,
    auth_type: 'none',
    auth_credentials: {},

    // Auction settings
    auction_type: 'first_price', // 'first_price' or 'second_price'
    bid_price_format: 'cpm', // 'cpm', 'cpv', 'cpc', or 'ecpm'

    // Ad format targeting
    ad_formats: [] as string[],

    // Geo targeting
    targeting_countries: [] as string[],
    targeting_states: [] as string[],

    // Device/OS/Browser targeting
    targeting_devices: [] as string[],
    targeting_os: [] as string[],
    targeting_browsers: [] as string[],
    targeting_connection_types: [] as string[],

    // Scheduling
    targeting_hourly_schedule: Array(24).fill(true) as boolean[], // Initialize all hours as true (targeted)
    targeting_daily_schedule: Array(7).fill(true) as boolean[],   // Initialize all days as true (targeted)

    // Whitelist/Blacklist
    whitelist_publishers: '',
    whitelist_websites: '',
    whitelist_zones: '',
    blacklist_publishers: '',
    blacklist_websites: '',
    blacklist_zones: '',

    // Partner exclusion (DSP excludes SSPs, SSP excludes DSPs)
    excluded_partners: [] as string[],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableUsers, setAvailableUsers] = useState<PartnerUser[]>([]);
  const [availableExclusionPartners, setAvailableExclusionPartners] = useState<PartnerUser[]>([]);

  // Targeting constants
  const adFormats = ['banner', 'native', 'in_page_push', 'popup'];
  const devices = ['Desktop', 'Mobile', 'Tablet'];
  const operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Other'];
  const connectionTypes = ['WiFi', 'Cellular', 'Broadband', 'Dial-up'];
  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // State for geo targeting
  const [countries, setCountries] = useState<any[]>([]);
  const [availableStates, setAvailableStates] = useState<any[]>([]);
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showStateDropdown, setShowStateDropdown] = useState(false);

  // Load countries on mount
  useEffect(() => {
    const allCountries = Country.getAllCountries();
    setCountries(allCountries);
  }, []);

  // Load states when countries change
  useEffect(() => {
    if (formData.targeting_countries.length > 0) {
      const states: any[] = [];
      formData.targeting_countries.forEach(countryCode => {
        const countryStates = State.getStatesOfCountry(countryCode);
        states.push(...countryStates);
      });
      setAvailableStates(states);
    } else {
      setAvailableStates([]);
    }
  }, [formData.targeting_countries]);

  // Fetch users when type changes
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch(`/api/admin/partners/users?type=${formData.type}`);
        if (response.ok) {
          const users = await response.json();
          setAvailableUsers(users);
        }
      } catch (error) {
        console.error('Failed to fetch users:', error);
      }
    };

    fetchUsers();
  }, [formData.type]);

  // Fetch exclusion partners (DSP excludes SSPs, SSP excludes DSPs)
  useEffect(() => {
    const exclusionType = formData.type === 'dsp' ? 'ssp' : 'dsp';

    const fetchExclusionPartners = async () => {
      try {
        const response = await fetch(`/api/admin/partners/users?type=${exclusionType}`);
        if (response.ok) {
          const partners = await response.json();
          setAvailableExclusionPartners(partners);
        }
      } catch (error) {
        console.error('Failed to fetch exclusion partners:', error);
      }
    };

    fetchExclusionPartners();
  }, [formData.type]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Check if click is outside country dropdown
      if (!target.closest('.country-dropdown-container')) {
        setShowCountryDropdown(false);
      }

      // Check if click is outside state dropdown
      if (!target.closest('.state-dropdown-container')) {
        setShowStateDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Helper function to close dropdowns after selection
  const handleCountrySelect = (countryCode: string) => {
    handleMultiSelect(countryCode, 'targeting_countries');
    // Don't close dropdown immediately to allow multiple selections
  };

  const handleStateSelect = (stateCode: string) => {
    handleMultiSelect(stateCode, 'targeting_states');
    // Don't close dropdown immediately to allow multiple selections
  };

  // Helper functions for multi-select handling
  const handleMultiSelect = (value: string, field: string) => {
    setFormData(prev => {
      const currentValues = (prev as any)[field] as string[];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return { ...prev, [field]: newValues };
    });
  };

  const handleSelectAll = (field: string, allValues: string[]) => {
    setFormData(prev => {
      const currentValues = (prev as any)[field] as string[];
      const newValues = currentValues.length === allValues.length ? [] : allValues;
      return { ...prev, [field]: newValues };
    });
  };

  const handleHourSelect = (hour: number) => {
    // hourly_schedule is not part of formData, removing this handler
    // setFormData(prev => ({
    //   ...prev,
    //   hourly_schedule: prev.hourly_schedule.includes(hour)
    //     ? prev.hourly_schedule.filter(h => h !== hour)
    //     : [...prev.hourly_schedule, hour].sort((a, b) => a - b)
    // }));
  };

  // Filtered countries and states for search
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  const filteredStates = availableStates.filter(state =>
    state.name.toLowerCase().includes(stateSearch.toLowerCase())
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Prepare targeting data
      const targetingData = {
        ad_formats: formData.ad_formats,
        geo: {
          countries: formData.targeting_countries,
          states: formData.targeting_states,
        },
        device: {
          devices: formData.targeting_devices,
          os: formData.targeting_os,
          browsers: formData.targeting_browsers,
          connection_types: formData.targeting_connection_types,
        },
        hourly_schedule: formData.targeting_hourly_schedule,
        daily_schedule: formData.targeting_daily_schedule,
        whitelist: {
          publishers: formData.whitelist_publishers.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          websites: formData.whitelist_websites.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          zones: formData.whitelist_zones.split(',').map((s: string) => s.trim()).filter((s: string) => s),
        },
        blacklist: {
          publishers: formData.blacklist_publishers.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          websites: formData.blacklist_websites.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          zones: formData.blacklist_zones.split(',').map((s: string) => s.trim()).filter((s: string) => s),
        },
        excluded_partners: formData.excluded_partners,
      };

      const response = await fetch('/api/admin/partners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          targeting: targetingData,
        }),
      });

      if (response.ok) {
        onSuccess();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to create endpoint');
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Create Partner Endpoint</h3>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Type *</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    type: e.target.value as 'dsp' | 'ssp',
                    user_id: '', // Reset user selection when type changes
                    endpoint_url: '', // Reset endpoint URL
                    revenue_share: e.target.value === 'ssp' ? 0 : 0 // Reset revenue share
                  }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="dsp">DSP (Demand Side Platform)</option>
                  <option value="ssp">SSP (Supply Side Platform)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Assign to User *</label>
                <select
                  value={formData.user_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, user_id: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select {formData.type.toUpperCase()} User</option>
                  {availableUsers.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.full_name} ({user.email})
                    </option>
                  ))}
                </select>
                {availableUsers.length === 0 && (
                  <p className="mt-1 text-xs text-red-600">
                    No {formData.type.toUpperCase()} users found. Create a {formData.type.toUpperCase()} user first.
                  </p>
                )}
              </div>
            </div>

            {/* Protocol Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Protocol *</label>
                <select
                  value={formData.protocol}
                  onChange={(e) => setFormData(prev => ({ ...prev, protocol: e.target.value as 'openrtb' | 'xml' }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="openrtb">OpenRTB (JSON)</option>
                  <option value="xml">XML Feed</option>
                </select>
              </div>

              {formData.protocol === 'openrtb' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">OpenRTB Version</label>
                  <select
                    value={formData.openrtb_version}
                    onChange={(e) => setFormData(prev => ({ ...prev, openrtb_version: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="2.5">2.5</option>
                    <option value="3.0">3.0</option>
                  </select>
                </div>
              )}
            </div>

            {/* Auction Type & Bid Price Format */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Auction Type *</label>
                <select
                  value={formData.auction_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, auction_type: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="first_price">First Price Auction</option>
                  <option value="second_price">Second Price Auction</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  {formData.auction_type === 'first_price'
                    ? 'Winner pays their bid price (most common in modern RTB)'
                    : 'Winner pays second highest bid price + $0.01 (traditional auction model)'
                  }
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Bid Price Format *</label>
                <select
                  value={formData.bid_price_format}
                  onChange={(e) => setFormData(prev => ({ ...prev, bid_price_format: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="cpm">CPM (Cost Per Mille)</option>
                  <option value="cpv">CPV (Cost Per View)</option>
                  <option value="cpc">CPC (Cost Per Click)</option>
                  <option value="ecpm">eCPM (Effective CPM)</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  {formData.type === 'dsp'
                    ? (formData.bid_price_format === 'cpm'
                        ? 'DSP sends bid prices in CPM format ($X per 1000 impressions)'
                        : formData.bid_price_format === 'cpv'
                        ? 'DSP sends bid prices in CPV format ($X per single impression)'
                        : formData.bid_price_format === 'cpc'
                        ? 'DSP sends bid prices in CPC format ($X per single click)'
                        : 'DSP sends bid prices in eCPM format ($X per 1000 impressions)')
                    : (formData.bid_price_format === 'cpm'
                        ? 'SSP receives bid prices in CPM format ($X per 1000 impressions)'
                        : formData.bid_price_format === 'cpv'
                        ? 'SSP receives bid prices in CPV format ($X per single impression)'
                        : formData.bid_price_format === 'cpc'
                        ? 'SSP receives bid prices in CPC format ($X per single click)'
                        : 'SSP receives bid prices in eCPM format ($X per 1000 impressions)')
                  }
                </p>
              </div>
            </div>

            {/* Endpoint URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {formData.type === 'dsp' ? 'DSP Endpoint URL *' : 'Platform Endpoint URL (Generated)'}
              </label>
              {formData.type === 'dsp' ? (
                <>
                  <input
                    type="url"
                    value={formData.endpoint_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, endpoint_url: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://dsp.example.com/api/bid"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Enter the DSP's endpoint URL where we will send bid requests
                  </p>
                </>
              ) : (
                <>
                  <div className="mt-1 p-3 bg-gray-50 border border-gray-300 rounded-md">
                    <p className="text-sm text-gray-700">
                      <strong>Your platform endpoint will be generated automatically:</strong>
                    </p>
                    <p className="text-xs text-gray-600 mt-1 font-mono break-all">
                      {formData.protocol === 'openrtb'
                        ? `${window.location.origin}/api/ssp/inventory?partner_id=[ID]`
                        : `${window.location.origin}/api/ssp/inventory-xml?partner_id=[ID]&ua=[USER_AGENT]&ip=[USER_IP]&subid=[ZONE_ID]&lang=[USER_LANG]&format=[AD_FORMATS]&ref=[PAGE_URL]&country=[COUNTRY]&device=[DEVICE_TYPE]&os_ver=[OS_VERSION]&browser=[BROWSER]&region=[REGION]&city=[CITY]`
                      }
                    </p>
                    {formData.protocol === 'xml' && (
                      <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                        <p className="text-blue-800 font-medium mb-1">Sample Macros for SSP Integration:</p>
                        <div className="text-blue-700 space-y-1">
                          <div><code>[USER_AGENT]</code> → Replace with user's browser agent</div>
                          <div><code>[USER_IP]</code> → Replace with user's IP address</div>
                          <div><code>[ZONE_ID]</code> → Replace with your zone/placement ID</div>
                          <div><code>[USER_LANG]</code> → Replace with user's language (e.g., en, es)</div>
                          <div><code>[AD_FORMATS]</code> → Replace with ad format (banner, popup, etc.)</div>
                          <div><code>[PAGE_URL]</code> → Replace with referring page URL</div>
                          <div><code>[COUNTRY]</code> → Replace with user's country code</div>
                          <div><code>[DEVICE_TYPE]</code> → Replace with device type (Desktop, Mobile)</div>
                        </div>
                      </div>
                    )}
                  </div>
                  <p className="mt-1 text-xs text-blue-600">
                    Provide this URL to the SSP partner for integration
                  </p>
                </>
              )}
            </div>

            {/* Ad Format Targeting */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Supported Ad Formats
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {adFormats.map((format) => (
                  <label key={format} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="checkbox"
                      checked={formData.ad_formats.includes(format)}
                      onChange={() => handleMultiSelect(format, 'ad_formats')}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm font-medium capitalize">
                      {format.replace('_', ' ')}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Geo Targeting */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Geographic Targeting</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Countries */}
                <div className="relative country-dropdown-container">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Countries ({formData.targeting_countries.length} selected)
                  </label>
                  <input
                    type="text"
                    placeholder="Search countries..."
                    value={countrySearch}
                    onChange={(e) => setCountrySearch(e.target.value)}
                    onFocus={() => setShowCountryDropdown(true)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  {showCountryDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      <div className="p-2 border-b">
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targeting_countries', countries.map(c => c.isoCode))}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targeting_countries.length === countries.length ? 'Deselect All' : 'Select All'}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowCountryDropdown(false)}
                          className="text-sm text-gray-600 hover:text-gray-800 ml-4"
                        >
                          Close
                        </button>
                      </div>
                      {filteredCountries.map((country) => (
                        <label key={country.isoCode} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.targeting_countries.includes(country.isoCode)}
                            onChange={() => handleCountrySelect(country.isoCode)}
                            className="mr-2"
                          />
                          <span className="text-sm">{country.name}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>

                {/* States */}
                <div className="relative state-dropdown-container">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    States/Provinces ({formData.targeting_states.length} selected)
                  </label>
                  <input
                    type="text"
                    placeholder={availableStates.length > 0 ? "Search states..." : "Select countries first"}
                    value={stateSearch}
                    onChange={(e) => setStateSearch(e.target.value)}
                    onFocus={() => setShowStateDropdown(true)}
                    disabled={availableStates.length === 0}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  />
                  {showStateDropdown && availableStates.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      <div className="p-2 border-b">
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targeting_states', availableStates.map(s => s.isoCode))}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targeting_states.length === availableStates.length ? 'Deselect All' : 'Select All'}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowStateDropdown(false)}
                          className="text-sm text-gray-600 hover:text-gray-800 ml-4"
                        >
                          Close
                        </button>
                      </div>
                      {filteredStates.map((state) => (
                        <label key={state.isoCode} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.targeting_states.includes(state.isoCode)}
                            onChange={() => handleStateSelect(state.isoCode)}
                            className="mr-2"
                          />
                          <span className="text-sm">{state.name}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Authentication */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Authentication Type</label>
                <select
                  value={formData.auth_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, auth_type: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="none">None</option>
                  <option value="api_key">API Key (Header)</option>
                  <option value="bearer">Bearer Token</option>
                </select>
              </div>

              {formData.auth_type !== 'none' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {formData.auth_type === 'api_key' ? 'API Key' : 'Bearer Token'}
                  </label>
                  <input
                    type="password"
                    value={formData.api_key}
                    onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter authentication key/token"
                  />
                </div>
              )}
            </div>

            {/* Device/OS/Browser Targeting */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Device & Platform Targeting</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Device Types */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Device Types
                  </label>
                  <div className="space-y-2">
                    {devices.map((device) => (
                      <label key={device} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_devices.includes(device)}
                          onChange={() => handleMultiSelect(device, 'targeting_devices')}
                          className="mr-2"
                        />
                        <span className="text-sm">{device}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Operating Systems */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Operating Systems
                  </label>
                  <div className="space-y-2">
                    {operatingSystems.map((os) => (
                      <label key={os} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_os.includes(os)}
                          onChange={() => handleMultiSelect(os, 'targeting_os')}
                          className="mr-2"
                        />
                        <span className="text-sm">{os}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Browsers */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Browsers
                  </label>
                  <div className="space-y-2">
                    {browsers.map((browser) => (
                      <label key={browser} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_browsers.includes(browser)}
                          onChange={() => handleMultiSelect(browser, 'targeting_browsers')}
                          className="mr-2"
                        />
                        <span className="text-sm">{browser}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Connection Types */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Connection Types
                  </label>
                  <div className="space-y-2">
                    {connectionTypes.map((connection) => (
                      <label key={connection} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_connection_types.includes(connection)}
                          onChange={() => handleMultiSelect(connection, 'targeting_connection_types')}
                          className="mr-2"
                        />
                        <span className="text-sm">{connection}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Partner Exclusion List */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                {formData.type === 'dsp' ? 'SSP Exclusion List' : 'DSP Exclusion List'}
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                {formData.type === 'dsp'
                  ? 'Select SSP partners to exclude from buying traffic for this DSP endpoint.'
                  : 'Select DSP partners to exclude from selling traffic to for this SSP endpoint.'
                }
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableExclusionPartners.map((partner) => (
                  <label key={partner.id} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="checkbox"
                      checked={formData.excluded_partners.includes(partner.id.toString())}
                      onChange={() => handleMultiSelect(partner.id.toString(), 'excluded_partners')}
                      className="mr-3 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                    />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{partner.full_name}</div>
                      <div className="text-xs text-gray-500">{partner.email}</div>
                      <div className="text-xs text-blue-600 uppercase">{partner.role}</div>
                    </div>
                  </label>
                ))}
              </div>
              {availableExclusionPartners.length === 0 && (
                <p className="text-sm text-gray-500 italic">
                  No {formData.type === 'dsp' ? 'SSP' : 'DSP'} partners available for exclusion.
                </p>
              )}
            </div>

            {/* Technical Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Timeout (ms)</label>
                <input
                  type="number"
                  value={formData.timeout_ms}
                  onChange={(e) => setFormData(prev => ({ ...prev, timeout_ms: parseInt(e.target.value) || 5000 }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  min="100"
                  max="5000"
                />
                <p className="mt-1 text-xs text-gray-500">
                  RTB timeout (100-5000ms). Lower values recommended for real-time bidding.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">QPS Limit</label>
                <input
                  type="number"
                  value={formData.qps_limit}
                  onChange={(e) => setFormData(prev => ({ ...prev, qps_limit: parseInt(e.target.value) || 100 }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  min="1"
                  max="10000"
                />
              </div>

              {formData.type === 'ssp' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Revenue Share (%)</label>
                  <input
                    type="number"
                    value={formData.revenue_share}
                    onChange={(e) => setFormData(prev => ({ ...prev, revenue_share: parseFloat(e.target.value) || 0 }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    min="0"
                    max="100"
                    step="0.01"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Percentage of revenue to share with SSP partner
                  </p>
                </div>
              )}
            </div>

            {/* Whitelist/Blacklist Targeting */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Whitelist */}
              <div className="space-y-4">
                <h5 className="text-md font-medium text-green-700">Whitelist (Allow Only)</h5>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publisher IDs
                  </label>
                  <input
                    type="text"
                    value={formData.whitelist_publishers}
                    onChange={(e) => setFormData(prev => ({ ...prev, whitelist_publishers: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated publisher IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website IDs
                  </label>
                  <input
                    type="text"
                    value={formData.whitelist_websites}
                    onChange={(e) => setFormData(prev => ({ ...prev, whitelist_websites: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated website IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Zone IDs
                  </label>
                  <input
                    type="text"
                    value={formData.whitelist_zones}
                    onChange={(e) => setFormData(prev => ({ ...prev, whitelist_zones: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated zone IDs</p>
                </div>
              </div>

              {/* Blacklist */}
              <div className="space-y-4">
                <h5 className="text-md font-medium text-red-700">Blacklist (Block)</h5>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publisher IDs
                  </label>
                  <input
                    type="text"
                    value={formData.blacklist_publishers}
                    onChange={(e) => setFormData(prev => ({ ...prev, blacklist_publishers: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated publisher IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website IDs
                  </label>
                  <input
                    type="text"
                    value={formData.blacklist_websites}
                    onChange={(e) => setFormData(prev => ({ ...prev, blacklist_websites: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated website IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Zone IDs
                  </label>
                  <input
                    type="text"
                    value={formData.blacklist_zones}
                    onChange={(e) => setFormData(prev => ({ ...prev, blacklist_zones: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated zone IDs</p>
                </div>
              </div>
            </div>

            {/* Additional Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Seat ID (Optional)</label>
                <input
                  type="text"
                  value={formData.seat_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, seat_id: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Partner seat identifier"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="test_mode"
                  checked={formData.test_mode}
                  onChange={(e) => setFormData(prev => ({ ...prev, test_mode: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="test_mode" className="ml-2 block text-sm text-gray-900">
                  Test Mode (for development/testing)
                </label>
              </div>
            </div>

            {/* Scheduling */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Hourly Schedule */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Hourly Schedule (UTC)</h4>
                <div className="grid grid-cols-6 sm:grid-cols-8 gap-2">
                  {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                    <label key={hour} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.targeting_hourly_schedule[hour]}
                        onChange={() => {
                          const newSchedule = [...formData.targeting_hourly_schedule];
                          newSchedule[hour] = !newSchedule[hour];
                          setFormData(prev => ({ ...prev, targeting_hourly_schedule: newSchedule }));
                        }}
                        className="form-checkbox h-4 w-4 text-blue-600 rounded"
                      />
                      <span className="text-sm text-gray-700">{hour.toString().padStart(2, '0')}:00</span>
                    </label>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Select hours when this endpoint is active (UTC timezone).
                </p>
              </div>

              {/* Daily Schedule */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Daily Schedule</h4>
                <div className="grid grid-cols-2 gap-2">
                  {daysOfWeek.map((day: string, index: number) => (
                    <label key={day} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.targeting_daily_schedule[index]}
                        onChange={() => {
                          const newSchedule = [...formData.targeting_daily_schedule];
                          newSchedule[index] = !newSchedule[index];
                          setFormData(prev => ({ ...prev, targeting_daily_schedule: newSchedule }));
                        }}
                        className="form-checkbox h-4 w-4 text-blue-600 rounded"
                      />
                      <span className="text-sm text-gray-700">{day}</span>
                    </label>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Select days of the week when this endpoint is active.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Creating...' : 'Create'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function EditEndpointModal({
  endpoint,
  onClose,
  onSuccess
}: {
  endpoint: PartnerEndpoint;
  onClose: () => void;
  onSuccess: () => void;
}) {
  // Constants needed for the edit modal (same as main component)
  const countries = Country.getAllCountries();
  const states = State.getAllStates();

  const adFormats = ['banner', 'native', 'in_page_push', 'popup'];
  const devices = ['Desktop', 'Mobile', 'Tablet'];
  const operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Other'];
  const connectionTypes = ['WiFi', 'Cellular', 'Ethernet'];
  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']; // Define daysOfWeek here


  const [formData, setFormData] = useState({
    name: endpoint.name,
    type: endpoint.type,
    user_id: endpoint.user_id?.toString() || '',
    endpoint_url: endpoint.endpoint_url,
    protocol: endpoint.protocol,
    openrtb_version: endpoint.openrtb_version,
    api_key: endpoint.api_key,
    timeout_ms: endpoint.timeout_ms,
    qps_limit: endpoint.qps_limit,
    revenue_share: endpoint.revenue_share,
    seat_id: endpoint.seat_id,
    test_mode: endpoint.test_mode,
    auth_type: endpoint.auth_type,
    auth_credentials: endpoint.auth_credentials,
    status: endpoint.status,
    auction_type: endpoint.auction_type || 'first_price',
    bid_price_format: endpoint.bid_price_format || 'cpm',

    // Targeting data (parse from JSON)
    ad_formats: endpoint.targeting?.ad_formats || [],
    targeting_countries: endpoint.targeting?.geo?.countries || [],
    targeting_states: endpoint.targeting?.geo?.states || [],
    targeting_devices: endpoint.targeting?.device?.devices || [],
    targeting_os: endpoint.targeting?.device?.os || [],
    targeting_browsers: endpoint.targeting?.device?.browsers || [],
    targeting_connection_types: endpoint.targeting?.device?.connection_types || [],

    // Scheduling
    targeting_hourly_schedule: endpoint.targeting?.hourly_schedule || Array(24).fill(true) as boolean[],
    targeting_daily_schedule: endpoint.targeting?.daily_schedule || Array(7).fill(true) as boolean[],

    // Whitelist/Blacklist
    whitelist_publishers: endpoint.targeting?.whitelist?.publishers?.join(', ') || '',
    whitelist_websites: endpoint.targeting?.whitelist?.websites?.join(', ') || '',
    whitelist_zones: endpoint.targeting?.whitelist?.zones?.join(', ') || '',
    blacklist_publishers: endpoint.targeting?.blacklist?.publishers?.join(', ') || '',
    blacklist_websites: endpoint.targeting?.blacklist?.websites?.join(', ') || '',
    blacklist_zones: endpoint.targeting?.blacklist?.zones?.join(', ') || '',

    // Partner exclusions - ensure they're strings for consistency
    excluded_partners: (endpoint.targeting?.excluded_partners || []).map((id: string) => id.toString()),
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableUsers, setAvailableUsers] = useState<PartnerUser[]>([]);
  const [availableExclusionPartners, setAvailableExclusionPartners] = useState<PartnerUser[]>([]);

  // Country/State dropdowns
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showStateDropdown, setShowStateDropdown] = useState(false);
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');

  // Get available states based on selected countries
  const availableStates = useMemo(() => {
    if (formData.targeting_countries.length === 0) return [];
    return states.filter(state =>
      formData.targeting_countries.includes(state.countryCode)
    );
  }, [formData.targeting_countries]);

  // Filter countries and states based on search
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  const filteredStates = availableStates.filter(state =>
    state.name.toLowerCase().includes(stateSearch.toLowerCase())
  );

  // Handler functions (same as create modal)
  const handleMultiSelect = (value: string, field: keyof typeof formData) => {
    setFormData(prev => {
      const currentValues = prev[field] as string[];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return { ...prev, [field]: newValues as any }; // Cast to any for now to resolve type error
    });
  };

  const handleCountrySelect = (countryCode: string) => {
    handleMultiSelect(countryCode, 'targeting_countries');
    // Clear states if country is deselected
    if (formData.targeting_countries.includes(countryCode)) {
      const statesToRemove = states
        .filter(state => state.countryCode === countryCode)
        .map(state => state.isoCode);
      setFormData(prev => ({
        ...prev,
        targeting_states: prev.targeting_states.filter((state: string) => !statesToRemove.includes(state))
      }));
    }
  };

  const handleStateSelect = (stateCode: string) => {
    handleMultiSelect(stateCode, 'targeting_states');
  };

  const handleSelectAll = (field: keyof typeof formData, allValues: string[]) => {
    setFormData(prev => {
      const currentValues = prev[field] as string[];
      const newValues = currentValues.length === allValues.length ? [] : allValues;
      return { ...prev, [field]: newValues as any }; // Cast to any for now to resolve type error
    });
  };

  // Fetch users when component mounts or type changes
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch(`/api/admin/partners/users?type=${formData.type}`);
        if (response.ok) {
          const users = await response.json();
          setAvailableUsers(users);
        }
      } catch (error) {
        console.error('Failed to fetch users:', error);
      }
    };

    fetchUsers();
  }, [formData.type]);

  // Fetch exclusion partners
  useEffect(() => {
    const fetchExclusionPartners = async () => {
      try {
        const oppositeType = formData.type === 'dsp' ? 'ssp' : 'dsp';
        const response = await fetch(`/api/admin/partners/users?type=${oppositeType}`);
        if (response.ok) {
          const users = await response.json();
          setAvailableExclusionPartners(users);
        }
      } catch (error) {
        console.error('Failed to fetch exclusion partners:', error);
      }
    };

    fetchExclusionPartners();
  }, [formData.type]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Build targeting data structure (same as create modal)
      const targetingData = {
        ad_formats: formData.ad_formats,
        geo: {
          countries: formData.targeting_countries,
          states: formData.targeting_states,
        },
        device: {
          devices: formData.targeting_devices,
          os: formData.targeting_os,
          browsers: formData.targeting_browsers,
          connection_types: formData.targeting_connection_types,
        },
        hourly_schedule: formData.targeting_hourly_schedule,
        daily_schedule: formData.targeting_daily_schedule,
        whitelist: {
          publishers: formData.whitelist_publishers.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          websites: formData.whitelist_websites.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          zones: formData.whitelist_zones.split(',').map((s: string) => s.trim()).filter((s: string) => s),
        },
        blacklist: {
          publishers: formData.blacklist_publishers.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          websites: formData.blacklist_websites.split(',').map((s: string) => s.trim()).filter((s: string) => s),
          zones: formData.blacklist_zones.split(',').map((s: string) => s.trim()).filter((s: string) => s),
        },
        excluded_partners: formData.excluded_partners,
      };



      const response = await fetch('/api/admin/partners', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: endpoint.id,
          ...formData,
          targeting: targetingData,
        }),
      });

      if (response.ok) {
        onSuccess();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to update endpoint');
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Partner Endpoint</h3>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Type *</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    type: e.target.value as 'dsp' | 'ssp',
                    user_id: '' // Reset user selection when type changes
                  }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="dsp">DSP (Demand Side Platform)</option>
                  <option value="ssp">SSP (Supply Side Platform)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Assign to User *</label>
                <select
                  value={formData.user_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, user_id: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select {formData.type.toUpperCase()} User</option>
                  {availableUsers.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.full_name} ({user.email})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="testing">Testing</option>
                </select>
              </div>
            </div>

            {/* Protocol Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Protocol *</label>
                <select
                  value={formData.protocol}
                  onChange={(e) => setFormData(prev => ({ ...prev, protocol: e.target.value as 'openrtb' | 'xml' }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="openrtb">OpenRTB (JSON)</option>
                  <option value="xml">XML Feed</option>
                </select>
              </div>

              {formData.protocol === 'openrtb' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">OpenRTB Version</label>
                  <select
                    value={formData.openrtb_version}
                    onChange={(e) => setFormData(prev => ({ ...prev, openrtb_version: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="2.5">2.5</option>
                    <option value="3.0">3.0</option>
                  </select>
                </div>
              )}
            </div>

            {/* Auction Type & Bid Price Format */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Auction Type *</label>
                <select
                  value={formData.auction_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, auction_type: e.target.value as 'first_price' | 'second_price' }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="first_price">First Price Auction</option>
                  <option value="second_price">Second Price Auction</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  {formData.auction_type === 'first_price'
                    ? 'Winner pays their bid price (most common in modern RTB)'
                    : 'Winner pays second highest bid price + $0.01 (traditional auction model)'
                  }
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Bid Price Format *</label>
                <select
                  value={formData.bid_price_format}
                  onChange={(e) => setFormData(prev => ({ ...prev, bid_price_format: e.target.value as 'cpm' | 'cpv' | 'cpc' | 'ecpm' }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="cpm">CPM (Cost Per Mille)</option>
                  <option value="cpv">CPV (Cost Per View)</option>
                  <option value="cpc">CPC (Cost Per Click)</option>
                  <option value="ecpm">eCPM (Effective CPM)</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  {formData.type === 'dsp'
                    ? (formData.bid_price_format === 'cpm'
                        ? 'DSP sends bid prices in CPM format ($X per 1000 impressions)'
                        : formData.bid_price_format === 'cpv'
                        ? 'DSP sends bid prices in CPV format ($X per single impression)'
                        : formData.bid_price_format === 'cpc'
                        ? 'DSP sends bid prices in CPC format ($X per single click)'
                        : 'DSP sends bid prices in eCPM format ($X per 1000 impressions)')
                    : (formData.bid_price_format === 'cpm'
                        ? 'SSP receives bid prices in CPM format ($X per 1000 impressions)'
                        : formData.bid_price_format === 'cpv'
                        ? 'SSP receives bid prices in CPV format ($X per single impression)'
                        : formData.bid_price_format === 'cpc'
                        ? 'SSP receives bid prices in CPC format ($X per single click)'
                        : 'SSP receives bid prices in eCPM format ($X per 1000 impressions)')
                  }
                </p>
              </div>
            </div>

            {/* Endpoint URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700">Endpoint URL *</label>
              <input
                type="url"
                value={formData.endpoint_url}
                onChange={(e) => setFormData(prev => ({ ...prev, endpoint_url: e.target.value }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Authentication */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Authentication Type</label>
                <select
                  value={formData.auth_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, auth_type: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="none">None</option>
                  <option value="api_key">API Key (Header)</option>
                  <option value="bearer">Bearer Token</option>
                </select>
              </div>

              {formData.auth_type !== 'none' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {formData.auth_type === 'api_key' ? 'API Key' : 'Bearer Token'}
                  </label>
                  <input
                    type="password"
                    value={formData.api_key}
                    onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter authentication key/token"
                  />
                </div>
              )}
            </div>

            {/* Ad Format Targeting */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Supported Ad Formats
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {adFormats.map((format) => (
                  <label key={format} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="checkbox"
                      checked={formData.ad_formats.includes(format)}
                      onChange={() => handleMultiSelect(format, 'ad_formats')}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm font-medium capitalize">
                      {format.replace('_', ' ')}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Geo Targeting */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Geographic Targeting</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Countries */}
                <div className="relative country-dropdown-container">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Countries ({formData.targeting_countries.length} selected)
                  </label>
                  <input
                    type="text"
                    placeholder="Search countries..."
                    value={countrySearch}
                    onChange={(e) => setCountrySearch(e.target.value)}
                    onFocus={() => setShowCountryDropdown(true)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  {showCountryDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      <div className="p-2 border-b">
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targeting_countries', countries.map(c => c.isoCode))}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targeting_countries.length === countries.length ? 'Deselect All' : 'Select All'}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowCountryDropdown(false)}
                          className="text-sm text-gray-600 hover:text-gray-800 ml-4"
                        >
                          Close
                        </button>
                      </div>
                      {filteredCountries.map((country) => (
                        <label key={country.isoCode} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.targeting_countries.includes(country.isoCode)}
                            onChange={() => handleCountrySelect(country.isoCode)}
                            className="mr-2"
                          />
                          <span className="text-sm">{country.name}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>

                {/* States */}
                <div className="relative state-dropdown-container">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    States/Provinces ({formData.targeting_states.length} selected)
                  </label>
                  <input
                    type="text"
                    placeholder={availableStates.length > 0 ? "Search states..." : "Select countries first"}
                    value={stateSearch}
                    onChange={(e) => setStateSearch(e.target.value)}
                    onFocus={() => setShowStateDropdown(true)}
                    disabled={availableStates.length === 0}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  />
                  {showStateDropdown && availableStates.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      <div className="p-2 border-b">
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targeting_states', availableStates.map(s => s.isoCode))}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targeting_states.length === availableStates.length ? 'Deselect All' : 'Select All'}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowStateDropdown(false)}
                          className="text-sm text-gray-600 hover:text-gray-800 ml-4"
                        >
                          Close
                        </button>
                      </div>
                      {filteredStates.map((state) => (
                        <label key={state.isoCode} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.targeting_states.includes(state.isoCode)}
                            onChange={() => handleStateSelect(state.isoCode)}
                            className="mr-2"
                          />
                          <span className="text-sm">{state.name}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Technical Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Timeout (ms)</label>
                <input
                  type="number"
                  value={formData.timeout_ms}
                  onChange={(e) => setFormData(prev => ({ ...prev, timeout_ms: parseInt(e.target.value) || 5000 }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  min="100"
                  max="5000"
                />
                <p className="mt-1 text-xs text-gray-500">
                  RTB timeout (100-5000ms). Lower values recommended for real-time bidding.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">QPS Limit</label>
                <input
                  type="number"
                  value={formData.qps_limit}
                  onChange={(e) => setFormData(prev => ({ ...prev, qps_limit: parseInt(e.target.value) || 100 }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  min="1"
                  max="10000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Revenue Share (%)</label>
                <input
                  type="number"
                  value={formData.revenue_share}
                  onChange={(e) => setFormData(prev => ({ ...prev, revenue_share: parseFloat(e.target.value) || 0 }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  min="0"
                  max="100"
                  step="0.01"
                />
              </div>
            </div>

            {/* Device/OS/Browser Targeting */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Device & Platform Targeting</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Device Types */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Device Types
                  </label>
                  <div className="space-y-2">
                    {devices.map((device) => (
                      <label key={device} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_devices.includes(device)}
                          onChange={() => handleMultiSelect(device, 'targeting_devices')}
                          className="mr-2"
                        />
                        <span className="text-sm">{device}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Operating Systems */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Operating Systems
                  </label>
                  <div className="space-y-2">
                    {operatingSystems.map((os) => (
                      <label key={os} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_os.includes(os)}
                          onChange={() => handleMultiSelect(os, 'targeting_os')}
                          className="mr-2"
                        />
                        <span className="text-sm">{os}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Browsers */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Browsers
                  </label>
                  <div className="space-y-2">
                    {browsers.map((browser) => (
                      <label key={browser} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_browsers.includes(browser)}
                          onChange={() => handleMultiSelect(browser, 'targeting_browsers')}
                          className="mr-2"
                        />
                        <span className="text-sm">{browser}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Connection Types */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Connection Types
                  </label>
                  <div className="space-y-2">
                    {connectionTypes.map((connection) => (
                      <label key={connection} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.targeting_connection_types.includes(connection)}
                          onChange={() => handleMultiSelect(connection, 'targeting_connection_types')}
                          className="mr-2"
                        />
                        <span className="text-sm">{connection}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Partner Exclusion List */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                {formData.type === 'dsp' ? 'SSP Exclusion List' : 'DSP Exclusion List'}
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                {formData.type === 'dsp'
                  ? 'Select SSP partners to exclude from buying traffic for this DSP endpoint.'
                  : 'Select DSP partners to exclude from selling traffic to for this SSP endpoint.'
                }
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableExclusionPartners.map((partner) => (
                  <label key={partner.id} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="checkbox"
                      checked={formData.excluded_partners.includes(partner.id.toString())}
                      onChange={() => handleMultiSelect(partner.id.toString(), 'excluded_partners')}
                      className="mr-3 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                    />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{partner.full_name}</div>
                      <div className="text-xs text-gray-500">{partner.email}</div>
                      <div className="text-xs text-blue-600 uppercase">{partner.role}</div>
                    </div>
                  </label>
                ))}
              </div>
              {availableExclusionPartners.length === 0 && (
                <p className="text-sm text-gray-500 italic">
                  No {formData.type === 'dsp' ? 'SSP' : 'DSP'} partners available for exclusion.
                </p>
              )}
            </div>

            {/* Whitelist/Blacklist Targeting */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Whitelist */}
              <div className="space-y-4">
                <h5 className="text-md font-medium text-green-700">Whitelist (Allow Only)</h5>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publisher IDs
                  </label>
                  <input
                    type="text"
                    value={formData.whitelist_publishers}
                    onChange={(e) => setFormData(prev => ({ ...prev, whitelist_publishers: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated publisher IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website IDs
                  </label>
                  <input
                    type="text"
                    value={formData.whitelist_websites}
                    onChange={(e) => setFormData(prev => ({ ...prev, whitelist_websites: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated website IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Zone IDs
                  </label>
                  <input
                    type="text"
                    value={formData.whitelist_zones}
                    onChange={(e) => setFormData(prev => ({ ...prev, whitelist_zones: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated zone IDs</p>
                </div>
              </div>

              {/* Blacklist */}
              <div className="space-y-4">
                <h5 className="text-md font-medium text-red-700">Blacklist (Block)</h5>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publisher IDs
                  </label>
                  <input
                    type="text"
                    value={formData.blacklist_publishers}
                    onChange={(e) => setFormData(prev => ({ ...prev, blacklist_publishers: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated publisher IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Website IDs
                  </label>
                  <input
                    type="text"
                    value={formData.blacklist_websites}
                    onChange={(e) => setFormData(prev => ({ ...prev, blacklist_websites: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated website IDs</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Zone IDs
                  </label>
                  <input
                    type="text"
                    value={formData.blacklist_zones}
                    onChange={(e) => setFormData(prev => ({ ...prev, blacklist_zones: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123, 456, 789"
                  />
                  <p className="text-xs text-gray-500 mt-1">Comma-separated zone IDs</p>
                </div>
              </div>
            </div>

            {/* Additional Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Seat ID (Optional)</label>
                <input
                  type="text"
                  value={formData.seat_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, seat_id: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Partner seat identifier"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="edit_test_mode"
                  checked={formData.test_mode}
                  onChange={(e) => setFormData(prev => ({ ...prev, test_mode: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="edit_test_mode" className="ml-2 block text-sm text-gray-900">
                  Test Mode (for development/testing)
                </label>
              </div>
            </div>

            {/* Scheduling */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Hourly Schedule */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Hourly Schedule (UTC)</h4>
                <div className="grid grid-cols-6 sm:grid-cols-8 gap-2">
                  {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                    <label key={hour} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.targeting_hourly_schedule[hour]}
                        onChange={() => {
                          const newSchedule = [...formData.targeting_hourly_schedule];
                          newSchedule[hour] = !newSchedule[hour];
                          setFormData(prev => ({ ...prev, targeting_hourly_schedule: newSchedule }));
                        }}
                        className="form-checkbox h-4 w-4 text-blue-600 rounded"
                      />
                      <span className="text-sm text-gray-700">{hour.toString().padStart(2, '0')}:00</span>
                    </label>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Select hours when this endpoint is active (UTC timezone).
                </p>
              </div>

              {/* Daily Schedule */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Daily Schedule</h4>
                <div className="grid grid-cols-2 gap-2">
                  {daysOfWeek.map((day: string, index: number) => (
                    <label key={day} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.targeting_daily_schedule[index]}
                        onChange={() => {
                          const newSchedule = [...formData.targeting_daily_schedule];
                          newSchedule[index] = !newSchedule[index];
                          setFormData(prev => ({ ...prev, targeting_daily_schedule: newSchedule }));
                        }}
                        className="form-checkbox h-4 w-4 text-blue-600 rounded"
                      />
                      <span className="text-sm text-gray-700">{day}</span>
                    </label>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Select days of the week when this endpoint is active.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Updating...' : 'Update'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function TestResultModal({ result, onClose }: { result: any; onClose: () => void }) {
  const testResult = result.test_result;
  const endpoint = result.endpoint;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Endpoint Test Results</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {endpoint && (
            <div className="mb-4 p-3 bg-gray-50 rounded-md">
              <h4 className="font-medium text-gray-900">{endpoint.name}</h4>
              <p className="text-sm text-gray-600">
                {endpoint.type.toUpperCase()} • {endpoint.protocol.toUpperCase()}
              </p>
            </div>
          )}

          <div className="space-y-4">
            {/* Test Status */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              <span className={`px-2 py-1 text-xs rounded-full ${
                testResult.success
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {testResult.success ? 'Success' : 'Failed'}
              </span>
            </div>

            {/* Response Time */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Response Time:</span>
              <span className="text-sm text-gray-900">{testResult.response_time}ms</span>
            </div>

            {/* Status Code */}
            {testResult.status_code > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">HTTP Status:</span>
                <span className={`text-sm ${
                  testResult.status_code >= 200 && testResult.status_code < 300
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {testResult.status_code}
                </span>
              </div>
            )}

            {/* Error */}
            {testResult.error && (
              <div>
                <span className="text-sm font-medium text-gray-700">Error:</span>
                <div className="mt-1 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{testResult.error}</p>
                </div>
              </div>
            )}

            {/* Response Body */}
            {testResult.response_body && (
              <div>
                <span className="text-sm font-medium text-gray-700">Response:</span>
                <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md">
                  <pre className="text-xs text-gray-800 whitespace-pre-wrap overflow-x-auto max-h-64">
                    {testResult.response_body}
                  </pre>
                </div>
              </div>
            )}

            {/* Test Recommendations */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h5 className="text-sm font-medium text-blue-900 mb-2">Test Analysis:</h5>
              <ul className="text-sm text-blue-800 space-y-1">
                {testResult.success ? (
                  <>
                    <li>✓ Endpoint is responding correctly</li>
                    <li>✓ Response time: {testResult.response_time}ms</li>
                    {testResult.response_time > 5000 && (
                      <li>⚠ Response time is high ({'>'}5s), consider optimizing</li>
                    )}
                  </>
                ) : (
                  <>
                    <li>✗ Endpoint test failed</li>
                    {testResult.error?.includes('timeout') && (
                      <li>• Consider increasing timeout value</li>
                    )}
                    {testResult.error?.includes('Network') && (
                      <li>• Check endpoint URL and network connectivity</li>
                    )}
                    {testResult.status_code >= 400 && (
                      <li>• Check authentication credentials and endpoint configuration</li>
                    )}
                  </>
                )}
              </ul>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}