'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

interface PartnerEndpoint {
  id: number;
  name: string;
  type: string;
  endpoint_url: string;
  protocol: string;
  openrtb_version: string;
  api_key: string;
  timeout_ms: number;
  auth_type: string;
}

export default function TestEndpointPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [endpoint, setEndpoint] = useState<PartnerEndpoint | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTesting, setIsTesting] = useState(false);
  const [requestMethod, setRequestMethod] = useState('POST');
  const [contentType, setContentType] = useState('application/json');
  const [requestBody, setRequestBody] = useState('');
  const [testResults, setTestResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Realistic OpenRTB bid request (simulates real impression)
  const defaultOpenRTBRequest = {
    id: `bid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    imp: [{
      id: '1',
      banner: {
        w: 300,
        h: 250,
        pos: 1,
        btype: [1, 3],
        battr: [1, 2, 3, 5, 6, 7, 8, 9, 10, 14]
      },
      displaymanager: 'Global-Ads-Media',
      displaymanagerver: '1.0',
      instl: 0,
      tagid: 'zone_123',
      bidfloor: 0.0001,
      bidfloorcur: 'USD',
      secure: 1
    }],
    site: {
      id: 'site_456',
      name: 'Example News Site',
      domain: 'news.example.com',
      cat: ['IAB12', 'IAB1'],
      sectioncat: ['IAB12-1'],
      pagecat: ['IAB12-1'],
      page: 'https://news.example.com/article/breaking-news',
      ref: 'https://google.com/search?q=news',
      search: 'breaking news',
      publisher: {
        id: 'pub_789',
        name: 'Example Publisher Network',
        cat: ['IAB12'],
        domain: 'publisher.example.com'
      },
      content: {
        id: 'content_123',
        title: 'Breaking News Article',
        url: 'https://news.example.com/article/breaking-news',
        cat: ['IAB12-1'],
        keywords: 'breaking,news,update',
        language: 'en'
      }
    },
    device: {
      ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      geo: {
        lat: 37.7749,
        lon: -122.4194,
        type: 1,
        country: 'USA',
        region: 'CA',
        city: 'San Francisco',
        zip: '94102'
      },
      ip: '*************',
      devicetype: 2,
      make: 'Apple',
      model: 'iPhone',
      os: 'iOS',
      osv: '17.1',
      h: 844,
      w: 390,
      js: 1,
      language: 'en',
      connectiontype: 2
    },
    user: {
      id: `user_${Math.random().toString(36).substr(2, 16)}`,
      yob: 1990,
      gender: 'M',
      keywords: 'tech,news,sports',
      geo: {
        country: 'USA',
        region: 'CA',
        city: 'San Francisco',
        zip: '94102'
      }
    },
    test: 1,
    at: 1,
    tmax: 120,
    cur: ['USD'],
    source: {
      fd: 1,
      tid: `transaction_${Date.now()}`
    },
    regs: {
      coppa: 0
    }
  };

  useEffect(() => {
    if (!session || session.user?.role !== 'admin') {
      router.push('/login');
      return;
    }

    fetchEndpoint();
  }, [session, params.id]);

  const fetchEndpoint = async () => {
    try {
      console.log('Fetching endpoint with ID:', params.id);
      const response = await fetch(`/api/admin/partners/${params.id}`);
      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Endpoint data:', data);
        setEndpoint(data);
        setError(null);

        // Set default request body based on protocol
        if (data.protocol === 'openrtb') {
          setRequestBody(JSON.stringify(defaultOpenRTBRequest, null, 2));
          setContentType('application/json');
          setRequestMethod('POST');
        } else if (data.protocol === 'xml') {
          // For XML DSP endpoints, show the actual URL that will be requested
          const sampleUrl = data.endpoint_url
            .replace('{user_agent}', encodeURIComponent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'))
            .replace('{ip_address}', '*************')
            .replace('{zone_id}', '123')
            .replace('{user_lang}', 'en-US')
            .replace('{ad_format}', 'popup')
            .replace('{page_url}', encodeURIComponent('https://news.example.com/article/breaking-news'))
            .replace('{os_version}', '10.0')
            .replace('{country}', 'USA')
            .replace('{region}', 'CA')
            .replace('{city}', 'San Francisco')
            .replace('{device_type}', 'desktop')
            .replace('{os}', 'Windows')
            .replace('{browser}', 'Chrome')
            .replace('{timestamp}', Date.now().toString())
            .replace('{click_id}', `click_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`)
            .replace('{campaign_id}', '456')
            .replace('{website_id}', '789');

          setRequestBody(sampleUrl);
          setContentType('application/xml');
          setRequestMethod('GET');
        }
      } else {
        const errorData = await response.json();
        console.error('API error:', errorData);
        setError(errorData.message || 'Failed to fetch endpoint');
      }
    } catch (error) {
      console.error('Failed to fetch endpoint:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendTestRequest = async () => {
    if (!endpoint) return;

    setIsTesting(true);
    setTestResults(null);

    try {
      const response = await fetch('/api/admin/partners/test-custom', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          endpoint_id: endpoint.id,
          request_method: requestMethod,
          content_type: contentType,
          request_body: requestBody,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults(data);
      } else {
        const errorData = await response.json();
        setTestResults({
          success: false,
          error: errorData.message || 'Test failed',
        });
      }
    } catch (error) {
      setTestResults({
        success: false,
        error: 'Network error occurred',
      });
    } finally {
      setIsTesting(false);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!endpoint && !isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">
            {error || 'Endpoint not found'}
          </h2>
          <p className="mt-2 text-gray-600">
            Endpoint ID: {params.id}
          </p>
          {error && (
            <p className="mt-2 text-red-600 text-sm">
              Error: {error}
            </p>
          )}
          <button
            onClick={() => router.push('/admin/partners')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Partners
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Test Endpoint: {endpoint.name}</h1>
          </div>
          <button
            onClick={() => router.push('/admin/partners')}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← Back to List
          </button>
        </div>

        {/* Endpoint Details */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Endpoint Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <span className="ml-2 text-blue-600">{endpoint.type.toUpperCase()} endpoint using {endpoint.protocol.toUpperCase()} protocol</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Effective Endpoint URL for Test:</span>
              <div className="mt-1 p-2 bg-gray-50 rounded text-xs font-mono break-all">
                {endpoint.endpoint_url}
              </div>
            </div>
            <div>
              <span className="font-medium text-gray-700">Supported Ad Formats:</span>
              <span className="ml-2 text-gray-600">Banner</span>
            </div>
          </div>
        </div>

        {/* XML DSP Info */}
        {endpoint.protocol === 'xml' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="text-md font-medium text-blue-900 mb-2">XML DSP Test Information</h3>
            <p className="text-sm text-blue-800 mb-3">
              This XML DSP endpoint will be tested with a GET request. Platform macros in the URL will be replaced with realistic test values.
            </p>
            <div className="text-xs text-blue-700 space-y-1">
              <div><strong>Test Scenario:</strong> User in San Francisco visits news website, triggers popup ad</div>
              <div><strong>Zone ID:</strong> 123 | <strong>IP:</strong> ************* | <strong>Language:</strong> en-US</div>
              <div><strong>User Agent:</strong> Chrome 120 on Windows 10 | <strong>Format:</strong> popup</div>
              <div><strong>Page:</strong> https://news.example.com/article/breaking-news</div>
            </div>
          </div>
        )}

        {/* Test Request Configuration */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Test Request Configuration</h2>
          <p className="text-sm text-gray-600 mb-4">
            Configure and send a test request. The body will auto-populate based on endpoint details and content type.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Request Method</label>
              <select
                value={requestMethod}
                onChange={(e) => setRequestMethod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Content Type</label>
              <select
                value={contentType}
                onChange={(e) => setContentType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="application/json">OpenRTB (JSON)</option>
                <option value="application/xml">XML</option>
              </select>
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Request Body</label>
            <textarea
              value={requestBody}
              onChange={(e) => setRequestBody(e.target.value)}
              className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
              placeholder="Enter request body..."
            />
          </div>

          <button
            onClick={handleSendTestRequest}
            disabled={isTesting}
            className="bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTesting ? 'Sending Test Request...' : 'Send Test Request'}
          </button>
        </div>

        {/* Test Results */}
        {testResults && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Test Results</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Response */}
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-2">Response</h3>

                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Status Code from Partner (or Test API):</span>
                    <div className="mt-1 text-sm text-gray-900">
                      {testResults.test_result?.status_code || 'N/A'}
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-700">Response Body from Partner:</span>
                    <textarea
                      value={testResults.test_result?.response_body || 'No response body'}
                      readOnly
                      className="mt-1 w-full h-64 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-xs"
                    />
                  </div>
                </div>
              </div>

              {/* Request Sent */}
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-2">Request Sent</h3>

                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Target URL (intended for actual request):</span>
                    <div className="mt-1 p-2 bg-gray-50 rounded text-xs font-mono break-all">
                      {endpoint.endpoint_url}
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-700">Request Method Sent:</span>
                    <div className="mt-1 text-sm text-gray-900">{requestMethod}</div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-700">Request Content-Type Sent:</span>
                    <div className="mt-1 text-sm text-gray-900">{contentType}</div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-700">Request Body Sent to Test API:</span>
                    <textarea
                      value={requestBody}
                      readOnly
                      className="mt-1 w-full h-64 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-xs"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Test Error */}
            {testResults.test_result?.error && (
              <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <h4 className="text-sm font-medium text-red-800 mb-2">Test Error</h4>
                <p className="text-sm text-red-600">{testResults.test_result.error}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
