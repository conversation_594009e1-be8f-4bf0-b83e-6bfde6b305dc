'use client';

import React, { useState, useEffect } from 'react';
import {
  HeartIcon,
  CpuChipIcon,
  CircleStackIcon,
  ServerIcon,
  SignalIcon,
  QueueListIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowPathIcon,
  ChartBarIcon,
  BoltIcon,
} from '@heroicons/react/24/outline';

// Card components matching the admin theme
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>{children}</div>
);

const CardHeader = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`px-6 py-4 border-b border-gray-200 ${className}`}>{children}</div>
);

const CardTitle = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <h3 className={`text-lg font-semibold text-gray-900 ${className}`}>{children}</h3>
);

const CardContent = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`px-6 py-4 ${className}`}>{children}</div>
);

interface HealthMetrics {
  system: {
    cpu: { usage: number; load: [number, number, number]; cores: number; temperature: number };
    memory: { total: number; used: number; free: number; usage: number; available: number };
    disk: { total: number; used: number; free: number; usage: number };
    network: { rx: number; tx: number; connections: { active: number; total: number } };
    processes: { pm2: { instances: number; memory: number; cpu: number; uptime: number; restarts: number } };
  };
  app: {
    performance: { qps: { current: number; peak: number; average: number }; responseTime: { avg: number; p95: number; p99: number } };
    queues: { impressions: any; cost: any; stats: any };
    database: { clickhouse: any; redis: any };
    business: { impressions: number; revenue: number; cost: number; profit: number; activeCampaigns: number; activeDsps: number; activeSsps: number };
    errors: { errorRate: number; timeoutRate: number; fallbackMode: boolean; fallbackJobs: number };
  };
  alerts: any[];
  overall: 'healthy' | 'warning' | 'critical' | 'down';
}

interface ServiceStatus {
  name: string;
  status: 'up' | 'down' | 'degraded' | 'maintenance';
  responseTime: number;
  uptime: number;
  version: string;
  port: number;
  endpoint: string;
}

export default function HealthMonitorPage() {
  const [healthData, setHealthData] = useState<HealthMetrics | null>(null);
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);


  useEffect(() => {
    fetchHealthData();
    fetchServices();
    fetchAlerts();

    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchHealthData();
        fetchServices();
        fetchAlerts();
      }, 10000); // Refresh every 10 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchHealthData = async () => {
    try {
      const response = await fetch('/api/admin/health-monitor?type=current');
      const result = await response.json();
      if (result.status === 'success') {
        setHealthData(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/admin/health-monitor?type=services');
      const result = await response.json();
      if (result.status === 'success') {
        setServices(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
    }
  };

  const fetchAlerts = async () => {
    try {
      const response = await fetch('/api/admin/health-monitor?type=alerts');
      const result = await response.json();
      if (result.status === 'success') {
        setAlerts(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch alerts:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
        return 'text-green-600 bg-green-100';
      case 'warning':
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
      case 'down':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'info':
        return 'text-blue-600 bg-blue-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-orange-600 bg-orange-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatBytes = (bytes: number) => {
    return `${bytes.toFixed(2)} GB`;
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(4)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">Loading health monitor...</div>
      </div>
    );
  }

  if (!healthData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl text-red-600">Failed to load health data</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Health Monitor</h1>
            <p className="mt-2 text-gray-600">Real-time system and application monitoring</p>
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Auto Refresh</span>
            </label>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(healthData.overall)}`}>
              <HeartIcon className="w-4 h-4 inline mr-1" />
              {healthData.overall.toUpperCase()}
            </div>
            <button
              onClick={() => {
                fetchHealthData();
                fetchServices();
                fetchAlerts();
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <ArrowPathIcon className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {/* Overall Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CpuChipIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600">CPU Usage</p>
                    <span className="text-2xl font-bold text-gray-900">{healthData.system.cpu.usage.toFixed(1)}%</span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Load: {healthData.system.cpu.load.map(l => l.toFixed(2)).join(', ')}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${healthData.system.cpu.usage > 80 ? 'bg-red-500' : healthData.system.cpu.usage > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                      style={{ width: `${Math.min(healthData.system.cpu.usage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CircleStackIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600">Memory Usage</p>
                    <span className="text-2xl font-bold text-gray-900">{healthData.system.memory.usage.toFixed(1)}%</span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {formatBytes(healthData.system.memory.used)} / {formatBytes(healthData.system.memory.total)}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${healthData.system.memory.usage > 85 ? 'bg-red-500' : healthData.system.memory.usage > 70 ? 'bg-yellow-500' : 'bg-green-500'}`}
                      style={{ width: `${Math.min(healthData.system.memory.usage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BoltIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600">Current QPS</p>
                    <span className="text-2xl font-bold text-gray-900">{formatNumber(healthData.app.performance.qps.current)}</span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Peak: {formatNumber(healthData.app.performance.qps.peak)}
                  </div>
                  <div className="text-xs text-green-600 mt-1">
                    Avg Response: {healthData.app.performance.responseTime.avg.toFixed(1)}ms
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-600">Revenue/Min</p>
                    <span className="text-2xl font-bold text-green-600">
                      {formatCurrency(healthData.app.business.revenue)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Profit: {formatCurrency(healthData.app.business.profit)}
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    {formatNumber(healthData.app.business.impressions)} impressions/min
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ServerIcon className="h-5 w-5 mr-2 text-gray-600" />
                System Resources
              </CardTitle>
            </CardHeader>
            <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Disk Usage</span>
                <span className="font-medium">{healthData.system.disk.usage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${healthData.system.disk.usage > 85 ? 'bg-red-500' : healthData.system.disk.usage > 70 ? 'bg-yellow-500' : 'bg-green-500'}`}
                  style={{ width: `${Math.min(healthData.system.disk.usage, 100)}%` }}
                ></div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Network RX/TX</span>
                <span className="font-medium">{formatBytes(healthData.system.network.rx)} / {formatBytes(healthData.system.network.tx)}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Active Connections</span>
                <span className="font-medium">{formatNumber(healthData.system.network.connections.active)}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">PM2 Instances</span>
                <span className="font-medium">{healthData.system.processes.pm2.instances}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">PM2 Memory</span>
                <span className="font-medium">{formatBytes(healthData.system.processes.pm2.memory / 1024)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <QueueListIcon className="h-5 w-5 mr-2 text-gray-600" />
                Queue Status
              </CardTitle>
            </CardHeader>
            <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Impressions Queue</span>
                  <span className="text-xs text-gray-500">
                    {healthData.app.queues.impressions.waiting}W / {healthData.app.queues.impressions.active}A
                  </span>
                </div>
                <div className="text-xs text-green-600">
                  ✓ {formatNumber(healthData.app.queues.impressions.completed)} completed
                </div>
                {healthData.app.queues.impressions.failed > 0 && (
                  <div className="text-xs text-red-600">
                    ✗ {formatNumber(healthData.app.queues.impressions.failed)} failed
                  </div>
                )}
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Cost Processing Queue</span>
                  <span className="text-xs text-gray-500">
                    {healthData.app.queues.cost.waiting}W / {healthData.app.queues.cost.active}A
                  </span>
                </div>
                <div className="text-xs text-green-600">
                  ✓ {formatNumber(healthData.app.queues.cost.completed)} completed
                </div>
                {healthData.app.queues.cost.failed > 0 && (
                  <div className="text-xs text-red-600">
                    ✗ {formatNumber(healthData.app.queues.cost.failed)} failed
                  </div>
                )}
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600">Stats Queue</span>
                  <span className="text-xs text-gray-500">
                    {healthData.app.queues.stats.waiting}W / {healthData.app.queues.stats.active}A
                  </span>
                </div>
                <div className="text-xs text-green-600">
                  ✓ {formatNumber(healthData.app.queues.stats.completed)} completed
                </div>
                {healthData.app.queues.stats.failed > 0 && (
                  <div className="text-xs text-red-600">
                    ✗ {formatNumber(healthData.app.queues.stats.failed)} failed
                  </div>
                )}
              </div>

              {healthData.app.errors.fallbackMode && (
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <div className="text-sm text-red-800 font-medium">⚠️ Fallback Mode Active</div>
                  <div className="text-xs text-red-600">
                    {healthData.app.errors.fallbackJobs} jobs pending
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

        {/* Business Metrics */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2 text-gray-600" />
              Business Metrics (Last Minute)
            </CardTitle>
          </CardHeader>
          <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{formatNumber(healthData.app.business.impressions)}</div>
              <div className="text-xs text-gray-500">Impressions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{formatCurrency(healthData.app.business.revenue)}</div>
              <div className="text-xs text-gray-500">Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{formatCurrency(healthData.app.business.cost)}</div>
              <div className="text-xs text-gray-500">Cost</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{formatCurrency(healthData.app.business.profit)}</div>
              <div className="text-xs text-gray-500">Profit</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">{formatNumber(healthData.app.business.activeCampaigns)}</div>
              <div className="text-xs text-gray-500">Campaigns</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{formatNumber(healthData.app.business.activeDsps)}</div>
              <div className="text-xs text-gray-500">DSPs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-teal-600">{formatNumber(healthData.app.business.activeSsps)}</div>
              <div className="text-xs text-gray-500">SSPs</div>
            </div>
          </div>
        </CardContent>
      </Card>

        {/* Services Status */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <SignalIcon className="h-5 w-5 mr-2 text-gray-600" />
              Service Status
            </CardTitle>
          </CardHeader>
          <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {services.map((service, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{service.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {service.status.toUpperCase()}
                  </span>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>Response: {service.responseTime}ms</div>
                  <div>Uptime: {service.uptime}%</div>
                  <div>Version: {service.version}</div>
                  {service.port > 0 && <div>Port: {service.port}</div>}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

        {/* Active Alerts */}
        {alerts.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 mr-2 text-red-600" />
                Active Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
            <div className="space-y-3">
              {alerts.slice(0, 10).map((alert, index) => (
                <div key={index} className="border-l-4 border-red-500 bg-red-50 p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAlertColor(alert.alert_type)}`}>
                          {alert.alert_type.toUpperCase()}
                        </span>
                        <span className="text-sm text-gray-500">{alert.alert_category}</span>
                      </div>
                      <div className="font-medium mt-1">{alert.alert_title}</div>
                      <div className="text-sm text-gray-600">{alert.alert_message}</div>
                      {alert.metric_name && (
                        <div className="text-xs text-gray-500 mt-1">
                          {alert.metric_name}: {alert.metric_value} (threshold: {alert.threshold_value})
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

        {/* Footer */}
        <div className="text-center text-sm text-gray-500 py-4">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center">
              <ClockIcon className="h-4 w-4 mr-1" />
              Last updated: {new Date().toLocaleString()}
            </div>
            <div className="flex items-center">
              <span className={`w-2 h-2 rounded-full mr-2 ${autoRefresh ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}