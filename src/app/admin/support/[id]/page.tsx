'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';

interface Ticket {
  id: number;
  user_id: number;
  user_name: string;
  user_email: string;
  user_role: string;
  subject: string;
  message: string;
  priority: string;
  status: string;
  category: string;
  created_at: string;
  updated_at: string;
}

interface Reply {
  id: number;
  message: string;
  is_admin: number;
  created_at: string;
  author_name: string;
  author_role: string;
}

export default function AdminSupportTicketView() {
  const { data: session } = useSession();
  const params = useParams();
  const router = useRouter();
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [replies, setReplies] = useState<Reply[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [replyMessage, setReplyMessage] = useState('');
  const [newStatus, setNewStatus] = useState('');

  useEffect(() => {
    if (session?.user?.role === 'admin' && params.id) {
      fetchTicket();
    }
  }, [session, params.id]);

  const fetchTicket = async () => {
    try {
      const response = await fetch(`/api/admin/support/tickets/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setTicket(data.ticket);
        setReplies(data.replies);
        setNewStatus(data.ticket.status);
      } else if (response.status === 404) {
        router.push('/admin/support');
      }
    } catch (error) {
      console.error('Failed to fetch ticket:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async () => {
    if (!ticket || newStatus === ticket.status) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticket.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        await fetchTicket();
      }
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!replyMessage.trim() || !ticket) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticket.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reply: replyMessage }),
      });

      if (response.ok) {
        setReplyMessage('');
        await fetchTicket();
      }
    } catch (error) {
      console.error('Failed to send reply:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Ticket not found</h2>
          <Link href="/admin/support" className="mt-4 text-blue-600 hover:text-blue-800">
            Back to Support
          </Link>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      open: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-blue-100 text-blue-800',
      closed: 'bg-gray-100 text-gray-800',
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityBadge = (priority: string) => {
    const priorityClasses = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800',
    };
    return priorityClasses[priority as keyof typeof priorityClasses] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/admin/support" className="text-blue-600 hover:text-blue-800 text-sm">
                ← Back to Support
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mt-2">Support Ticket #{ticket.id}</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 text-sm rounded-full capitalize ${getPriorityBadge(ticket.priority)}`}>
                {ticket.priority} Priority
              </span>
              <span className={`px-3 py-1 text-sm rounded-full capitalize ${getStatusBadge(ticket.status)}`}>
                {ticket.status}
              </span>
            </div>
          </div>
        </div>

        {/* Ticket Details */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{ticket.subject}</h2>
                <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                  <span>From: {ticket.user_name} ({ticket.user_email})</span>
                  <span>Role: {ticket.user_role}</span>
                  <span>Category: {ticket.category}</span>
                  <span>Created: {new Date(ticket.created_at).toLocaleString()}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <select
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm"
                >
                  <option value="open">Open</option>
                  <option value="pending">Pending</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
                {newStatus !== ticket.status && (
                  <button
                    onClick={handleStatusUpdate}
                    disabled={isUpdating}
                    className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isUpdating ? 'Updating...' : 'Update'}
                  </button>
                )}
              </div>
            </div>
          </div>
          <div className="px-6 py-4">
            <div className="prose max-w-none">
              <p className="whitespace-pre-wrap">{ticket.message}</p>
            </div>
          </div>
        </div>

        {/* Replies */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Conversation ({replies.length} {replies.length === 1 ? 'reply' : 'replies'})
            </h3>
          </div>
          <div className="divide-y divide-gray-200">
            {replies.map((reply) => (
              <div key={reply.id} className="px-6 py-4">
                <div className="flex items-start space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                    reply.is_admin ? 'bg-blue-600' : 'bg-gray-600'
                  }`}>
                    {reply.author_name.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{reply.author_name}</span>
                      {reply.is_admin && (
                        <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Admin</span>
                      )}
                      <span className="text-sm text-gray-500">
                        {new Date(reply.created_at).toLocaleString()}
                      </span>
                    </div>
                    <div className="mt-2 prose max-w-none">
                      <p className="whitespace-pre-wrap">{reply.message}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Reply Form */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Add Reply</h3>
          </div>
          <form onSubmit={handleReplySubmit} className="p-6">
            <div className="mb-4">
              <textarea
                value={replyMessage}
                onChange={(e) => setReplyMessage(e.target.value)}
                placeholder="Type your reply here..."
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isUpdating || !replyMessage.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {isUpdating ? 'Sending...' : 'Send Reply'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
