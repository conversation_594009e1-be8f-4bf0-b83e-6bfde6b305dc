'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface PayoutRequest {
  id: number;
  user_id: number;
  user_name: string;
  amount: number;
  method_type: string;
  method_details: any;
  status: string;
  requested_at: string;
  processed_at?: string;
}

export default function AdminPayouts() {
  const { data: session } = useSession();
  const [payoutRequests, setPayoutRequests] = useState<PayoutRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('pending');
  const [selectedRequest, setSelectedRequest] = useState<PayoutRequest | null>(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);

  useEffect(() => {
    fetchPayoutRequests();
  }, [filter]);

  const fetchPayoutRequests = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        ...(filter !== 'all' && { status: filter }),
      });

      const response = await fetch(`/api/admin/payouts?${params}`);
      if (response.ok) {
        const data = await response.json();
        setPayoutRequests(data);
      }
    } catch (error) {
      console.error('Failed to fetch payout requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproval = async (requestId: number, action: 'approve' | 'reject', reason?: string) => {
    try {
      const response = await fetch(`/api/admin/payouts/${requestId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, reason }),
      });

      if (response.ok) {
        fetchPayoutRequests();
        setShowApprovalModal(false);
        setSelectedRequest(null);
      }
    } catch (error) {
      console.error('Failed to process payout request:', error);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  };

  const filteredRequests = payoutRequests.filter(request => {
    if (filter === 'all') return true;
    return request.status === filter;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Payout Requests</h1>
          <p className="mt-2 text-gray-600">Review and approve publisher payout requests</p>
        </div>

        {/* Filter Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {['pending', 'approved', 'completed', 'rejected', 'all'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setFilter(tab)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                    filter === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab} ({payoutRequests.filter(r => tab === 'all' || r.status === tab).length})
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Payout Requests Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              {filter === 'all' ? 'All Requests' : `${filter.charAt(0).toUpperCase() + filter.slice(1)} Requests`} ({filteredRequests.length})
            </h2>
          </div>
          <div className="overflow-x-auto">
            {filteredRequests.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No payout requests found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filter === 'all' ? 'Payout requests will appear here.' : `No ${filter} payout requests found.`}
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Publisher</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRequests.map((request) => (
                    <tr key={request.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{request.user_name}</div>
                        <div className="text-sm text-gray-500">ID: {request.user_id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${request.amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 capitalize">{request.method_type}</div>
                        <div className="text-sm text-gray-500">
                          {request.method_type === 'paypal' && request.method_details?.email}
                          {request.method_type === 'crypto' && `${request.method_details?.currency}: ${request.method_details?.address?.slice(0, 10)}...`}
                          {request.method_type === 'bank' && `${request.method_details?.bank_name}: ***${request.method_details?.account_number?.slice(-4)}`}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full capitalize ${getStatusBadge(request.status)}`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(request.requested_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {request.status === 'pending' && (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => {
                                setSelectedRequest(request);
                                setShowApprovalModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Review
                            </button>
                          </div>
                        )}
                        {request.status === 'approved' && (
                          <button
                            onClick={() => handleApproval(request.id, 'approve')}
                            className="text-green-600 hover:text-green-900"
                          >
                            Mark Paid
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Approval Modal */}
        {showApprovalModal && selectedRequest && (
          <ApprovalModal
            request={selectedRequest}
            onClose={() => {
              setShowApprovalModal(false);
              setSelectedRequest(null);
            }}
            onApprove={(reason) => handleApproval(selectedRequest.id, 'approve', reason)}
            onReject={(reason) => handleApproval(selectedRequest.id, 'reject', reason)}
          />
        )}
      </div>
    </div>
  );
}

function ApprovalModal({ 
  request, 
  onClose, 
  onApprove, 
  onReject 
}: { 
  request: PayoutRequest; 
  onClose: () => void; 
  onApprove: (reason?: string) => void; 
  onReject: (reason: string) => void; 
}) {
  const [reason, setReason] = useState('');
  const [action, setAction] = useState<'approve' | 'reject' | null>(null);

  const handleSubmit = () => {
    if (action === 'approve') {
      onApprove(reason);
    } else if (action === 'reject') {
      onReject(reason);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Review Payout Request</h3>
          
          <div className="space-y-3 mb-6">
            <div>
              <span className="text-sm font-medium text-gray-700">Publisher:</span>
              <span className="ml-2 text-sm text-gray-900">{request.user_name}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-700">Amount:</span>
              <span className="ml-2 text-sm text-gray-900">${request.amount.toFixed(2)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-700">Method:</span>
              <span className="ml-2 text-sm text-gray-900 capitalize">{request.method_type}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-700">Details:</span>
              <div className="ml-2 text-sm text-gray-900">
                {request.method_type === 'paypal' && request.method_details?.email}
                {request.method_type === 'crypto' && (
                  <div>
                    <div>{request.method_details?.currency}</div>
                    <div className="font-mono text-xs">{request.method_details?.address}</div>
                  </div>
                )}
                {request.method_type === 'bank' && (
                  <div>
                    <div>{request.method_details?.bank_name}</div>
                    <div>Account: {request.method_details?.account_number}</div>
                    <div>Routing: {request.method_details?.routing_number}</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {action && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {action === 'approve' ? 'Approval Notes (Optional)' : 'Rejection Reason (Required)'}
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder={action === 'approve' ? 'Optional notes...' : 'Please provide a reason for rejection...'}
                required={action === 'reject'}
              />
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            {!action && (
              <>
                <button
                  onClick={() => setAction('reject')}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  Reject
                </button>
                <button
                  onClick={() => setAction('approve')}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Approve
                </button>
              </>
            )}
            {action && (
              <button
                onClick={handleSubmit}
                disabled={action === 'reject' && !reason.trim()}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                Confirm {action === 'approve' ? 'Approval' : 'Rejection'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
