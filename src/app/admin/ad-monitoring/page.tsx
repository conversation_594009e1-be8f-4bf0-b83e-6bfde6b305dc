'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface MonitoringResult {
  id: number;
  campaign_id: number;
  campaign_name: string;
  advertiser_name: string;
  region: string;
  status: string;
  issues_found: string[];
  screenshot_url?: string;
  landing_url: string;
  checked_at: string;
  created_at: string;
}

export default function AdMonitoring() {
  const { data: session } = useSession();
  const [results, setResults] = useState<MonitoringResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [stateFilter, setStateFilter] = useState('all');
  const [isScanning, setIsScanning] = useState(false);
  const [excludedAdvertisers, setExcludedAdvertisers] = useState<any[]>([]);
  const [showExclusionModal, setShowExclusionModal] = useState(false);
  const [allAdvertisers, setAllAdvertisers] = useState<any[]>([]);
  const [selectedAdvertisers, setSelectedAdvertisers] = useState<number[]>([]);
  const [advertiserSearch, setAdvertiserSearch] = useState('');

  const fetchResults = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        ...(filter !== 'all' && { status: filter }),
        ...(stateFilter !== 'all' && { state: stateFilter }),
      });

      const response = await fetch(`/api/admin/ad-monitoring?${params}`);
      if (response.ok) {
        const data = await response.json();
        setResults(data);
      }
    } catch (error) {
      console.error('Failed to fetch monitoring results:', error);
    } finally {
      setIsLoading(false);
    }
  }, [filter, stateFilter]);

  const fetchExcludedAdvertisers = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/monitoring-exclusions');
      if (response.ok) {
        const data = await response.json();
        setExcludedAdvertisers(data);
      }
    } catch (error) {
      console.error('Failed to fetch excluded advertisers:', error);
    }
  }, []);

  const fetchAllAdvertisers = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (advertiserSearch.trim()) {
        params.append('search', advertiserSearch);
      }

      const response = await fetch(`/api/admin/advertisers?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAllAdvertisers(data);
      }
    } catch (error) {
      console.error('Failed to fetch advertisers:', error);
    }
  }, [advertiserSearch]);

  const addExclusions = async () => {
    if (selectedAdvertisers.length === 0) return;

    try {
      // Add exclusions one by one
      const promises = selectedAdvertisers.map(advertiserId =>
        fetch('/api/admin/monitoring-exclusions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ advertiser_id: advertiserId }),
        })
      );

      const responses = await Promise.all(promises);
      const successCount = responses.filter(r => r.ok).length;
      const failCount = responses.length - successCount;

      if (successCount > 0) {
        setSelectedAdvertisers([]);
        setShowExclusionModal(false);
        fetchExcludedAdvertisers();

        if (failCount === 0) {
          alert(`${successCount} advertiser${successCount !== 1 ? 's' : ''} excluded from monitoring successfully`);
        } else {
          alert(`${successCount} advertiser${successCount !== 1 ? 's' : ''} excluded successfully, ${failCount} failed`);
        }
      } else {
        alert('Failed to exclude advertisers');
      }
    } catch (error) {
      console.error('Failed to add exclusions:', error);
      alert('Failed to exclude advertisers');
    }
  };

  const removeExclusion = async (advertiserId: number) => {
    if (!confirm('Are you sure you want to remove this exclusion? The advertiser will be included in future monitoring scans.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/monitoring-exclusions/${advertiserId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchExcludedAdvertisers();
        alert('Exclusion removed successfully');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to remove exclusion');
      }
    } catch (error) {
      console.error('Failed to remove exclusion:', error);
      alert('Failed to remove exclusion');
    }
  };

  useEffect(() => {
    if (session) {
      fetchResults();
      fetchExcludedAdvertisers();
      fetchAllAdvertisers();
    }
  }, [session, fetchResults, fetchExcludedAdvertisers, fetchAllAdvertisers]);

  // Fetch advertisers when search changes
  useEffect(() => {
    if (showExclusionModal) {
      fetchAllAdvertisers();
    }
  }, [advertiserSearch, showExclusionModal, fetchAllAdvertisers]);

  const startMonitoring = async () => {
    setIsScanning(true);
    try {
      const response = await fetch('/api/admin/ad-monitoring/scan', {
        method: 'POST',
      });

      if (response.ok) {
        // Refresh results after scan
        setTimeout(() => {
          fetchResults();
          setIsScanning(false);
        }, 5000);
      }
    } catch (error) {
      console.error('Failed to start monitoring:', error);
      setIsScanning(false);
    }
  };

  const handleAction = async (resultId: number, action: 'approve' | 'reject') => {
    try {
      const response = await fetch(`/api/admin/ad-monitoring/${resultId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });

      if (response.ok) {
        fetchResults();
      }
    } catch (error) {
      console.error('Failed to update monitoring result:', error);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      clean: 'bg-green-100 text-green-800',
      flagged: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-blue-100 text-blue-800',
      rejected: 'bg-gray-100 text-gray-800',
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  };

  const regions = [
    'US', 'UK', 'CA', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'NO', 'DK'
  ];

  const filteredResults = results.filter(result => {
    if (filter !== 'all' && result.status !== filter) return false;
    if (regionFilter !== 'all' && result.region !== regionFilter) return false;
    return true;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ad Monitoring</h1>
            <p className="mt-2 text-gray-600">Monitor ads across 12 regions for prohibited content</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowExclusionModal(true)}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Manage Exclusions
            </button>
            <button
              onClick={startMonitoring}
              disabled={isScanning}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {isScanning ? 'Scanning...' : 'Start Scan'}
            </button>
          </div>
        </div>

        {/* Exclusion Management Modal */}
        {showExclusionModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-900">Manage Monitoring Exclusions</h2>
                <button
                  onClick={() => setShowExclusionModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Add New Exclusions */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Add New Exclusions</h3>

                {/* Search Input */}
                <div className="mb-3">
                  <input
                    type="text"
                    placeholder="Search advertisers by name or email..."
                    value={advertiserSearch}
                    onChange={(e) => setAdvertiserSearch(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Advertiser Selection */}
                <div className="mb-3">
                  {/* Select All / Clear All */}
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Available Advertisers ({allAdvertisers.filter(advertiser => !excludedAdvertisers.some(excluded => excluded.id === advertiser.id)).length})
                    </span>
                    <div className="space-x-2">
                      <button
                        type="button"
                        onClick={() => {
                          const availableIds = allAdvertisers
                            .filter(advertiser => !excludedAdvertisers.some(excluded => excluded.id === advertiser.id))
                            .map(advertiser => advertiser.id);
                          setSelectedAdvertisers(availableIds);
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                        disabled={allAdvertisers.filter(advertiser => !excludedAdvertisers.some(excluded => excluded.id === advertiser.id)).length === 0}
                      >
                        Select All
                      </button>
                      <button
                        type="button"
                        onClick={() => setSelectedAdvertisers([])}
                        className="text-xs text-gray-600 hover:text-gray-800 transition-colors"
                        disabled={selectedAdvertisers.length === 0}
                      >
                        Clear All
                      </button>
                    </div>
                  </div>
                  <div className="h-64 overflow-y-auto border border-gray-300 rounded-md bg-white scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                    {allAdvertisers
                      .filter(advertiser => !excludedAdvertisers.some(excluded => excluded.id === advertiser.id))
                      .map((advertiser) => (
                        <label key={advertiser.id} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0">
                          <input
                            type="checkbox"
                            checked={selectedAdvertisers.includes(advertiser.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedAdvertisers([...selectedAdvertisers, advertiser.id]);
                              } else {
                                setSelectedAdvertisers(selectedAdvertisers.filter(id => id !== advertiser.id));
                              }
                            }}
                            className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-900 truncate">{advertiser.full_name}</div>
                            <div className="text-xs text-gray-500 truncate">ID: {advertiser.id} • {advertiser.email}</div>
                          </div>
                        </label>
                      ))}
                    {allAdvertisers.filter(advertiser => !excludedAdvertisers.some(excluded => excluded.id === advertiser.id)).length === 0 && (
                      <div className="p-4 text-center text-gray-500">
                        <div className="text-sm">
                          {advertiserSearch ? 'No advertisers found matching your search.' : 'All advertisers are already excluded.'}
                        </div>
                        {advertiserSearch && (
                          <div className="text-xs mt-1">Try a different search term or clear the search.</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Selected Count and Action */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    {selectedAdvertisers.length} advertiser{selectedAdvertisers.length !== 1 ? 's' : ''} selected
                  </div>
                  <button
                    onClick={addExclusions}
                    disabled={selectedAdvertisers.length === 0}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Exclude Selected ({selectedAdvertisers.length})
                  </button>
                </div>

                <p className="mt-2 text-sm text-gray-600">
                  Select advertisers to exclude from all monitoring scans (both automatic and manual).
                </p>
              </div>

              {/* Current Exclusions */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Current Exclusions ({excludedAdvertisers.length})
                </h3>
                {excludedAdvertisers.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <svg className="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <p>No advertisers are currently excluded from monitoring.</p>
                    <p className="text-sm">Add an advertiser ID above to exclude them from scans.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {excludedAdvertisers.map((advertiser) => (
                      <div key={advertiser.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">{advertiser.full_name}</div>
                          <div className="text-sm text-gray-500">ID: {advertiser.id} • {advertiser.email}</div>
                        </div>
                        <button
                          onClick={() => removeExclusion(advertiser.id)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowExclusionModal(false)}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Info Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Ad Monitoring System</h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>Automated Detection:</strong> Scans ads for prohibited content including adult content, illegal products, and misleading claims.</p>
            <p><strong>Multi-Region:</strong> Monitors ads across 12 different regions to ensure compliance with local regulations.</p>
            <p><strong>Auto-Rejection:</strong> Campaigns with prohibited content are automatically rejected with detailed reasons.</p>
            <p><strong>Advertiser Exclusions:</strong> Trusted advertisers can be permanently excluded from monitoring scans. Currently {excludedAdvertisers.length} advertiser{excludedAdvertisers.length !== 1 ? 's' : ''} excluded.</p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Filters</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="clean">Clean</option>
                  <option value="flagged">Flagged</option>
                  <option value="pending">Pending Review</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">State/Region</label>
                <select
                  value={stateFilter}
                  onChange={(e) => setStateFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All States/Regions</option>
                  {regions.map((region) => (
                    <option key={region} value={region}>{region}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Results List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Monitoring Results ({filteredResults.length})
            </h2>
          </div>
          <div className="overflow-x-auto">
            {filteredResults.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No monitoring results</h3>
                <p className="mt-1 text-sm text-gray-500">Start a scan to monitor ads for prohibited content.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Advertiser</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issues</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Screenshot</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredResults.map((result) => (
                    <tr key={result.id}>
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900">{result.campaign_name}</div>
                        <div className="text-sm text-gray-500">ID: {result.campaign_id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {result.advertiser_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">
                          {result.region}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full capitalize ${getStatusBadge(result.status)}`}>
                          {result.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        {result.issues_found.length > 0 ? (
                          <div className="text-sm text-red-600">
                            {result.issues_found.slice(0, 2).map((issue, index) => (
                              <div key={`issue-${result.id}-${index}-${issue.substring(0, 10)}`}>{issue}</div>
                            ))}
                            {result.issues_found.length > 2 && (
                              <div key={`more-issues-${result.id}`} className="text-gray-500">+{result.issues_found.length - 2} more</div>
                            )}
                          </div>
                        ) : (
                          <span className="text-sm text-green-600">No issues</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {result.screenshot_url ? (
                          <div className="flex items-center">
                            <img
                              src={result.screenshot_url}
                              alt="Screenshot"
                              className="w-16 h-12 object-cover rounded border cursor-pointer hover:opacity-75 transition-opacity"
                              onClick={() => window.open(result.screenshot_url, '_blank')}
                              title="Click to view full size"
                            />
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">No screenshot</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(result.checked_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {result.status === 'flagged' && [
                            <button
                              key={`approve-${result.id}`}
                              onClick={() => handleAction(result.id, 'approve')}
                              className="text-green-600 hover:text-green-900"
                            >
                              Approve
                            </button>,
                            <button
                              key={`reject-${result.id}`}
                              onClick={() => handleAction(result.id, 'reject')}
                              className="text-red-600 hover:text-red-900"
                            >
                              Reject
                            </button>
                          ]}
                          <button
                            key={`view-${result.id}`}
                            onClick={() => {
                              if (result.landing_url) {
                                window.open(result.landing_url, '_blank');
                              } else {
                                alert('This is a JS tag campaign with no landing URL to view.');
                              }
                            }}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            {result.landing_url ? 'View URL' : 'JS Tag'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Scanned</div>
            <div className="text-2xl font-bold text-gray-900">{results.length}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Clean Ads</div>
            <div className="text-2xl font-bold text-green-600">
              {results.filter(r => r.status === 'clean').length}
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Flagged</div>
            <div className="text-2xl font-bold text-red-600">
              {results.filter(r => r.status === 'flagged').length}
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Pending Review</div>
            <div className="text-2xl font-bold text-yellow-600">
              {results.filter(r => r.status === 'pending').length}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
