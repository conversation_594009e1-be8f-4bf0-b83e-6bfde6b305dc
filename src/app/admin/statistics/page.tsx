'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { formatNumber, formatCurrency } from '@/lib/format-utils';

interface User {
  id: number;
  full_name: string;
  role: string;
}

interface Website {
  id: number;
  name: string;
}

interface Campaign {
  id: number;
  name: string;
}

interface StatData {
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  revenue: number;
  ctr: number;
  advertiser_name?: string;
  publisher_name?: string;
  campaign_name?: string;
  website_name?: string;
  country?: string;
  os?: string;
  browser?: string;
  device?: string;
  name?: string;
  id?: string;
}

export default function AdminStatistics() {
  const { data: session } = useSession();
  const [advertisers, setAdvertisers] = useState<User[]>([]);
  const [publishers, setPublishers] = useState<User[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [websites, setWebsites] = useState<Website[]>([]);
  const [stats, setStats] = useState<StatData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [filters, setFilters] = useState({
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    dateTo: new Date().toISOString().split('T')[0],
    advertiserId: '',
    publisherId: '',
    campaignId: '',
    websiteId: '',
    groupBy: 'day',
  });

  useEffect(() => {
    fetchFilterData();
  }, []);

  useEffect(() => {
    fetchStatistics();
  }, [filters]);

  const fetchFilterData = async () => {
    try {
      // Fetch advertisers
      const advertisersResponse = await fetch('/api/admin/users?role=advertiser');
      if (advertisersResponse.ok) {
        const advertisersData = await advertisersResponse.json();
        setAdvertisers(advertisersData);
      }

      // Fetch publishers
      const publishersResponse = await fetch('/api/admin/users?role=publisher');
      if (publishersResponse.ok) {
        const publishersData = await publishersResponse.json();
        setPublishers(publishersData);
      }

      // Fetch campaigns
      const campaignsResponse = await fetch('/api/admin/campaigns');
      if (campaignsResponse.ok) {
        const campaignsData = await campaignsResponse.json();
        setCampaigns(campaignsData);
      }

      // Fetch websites
      const websitesResponse = await fetch('/api/admin/websites');
      if (websitesResponse.ok) {
        const websitesData = await websitesResponse.json();
        setWebsites(websitesData);
      }
    } catch (error) {
      console.error('Failed to fetch filter data:', error);
    }
  };

  const fetchStatistics = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        date_from: filters.dateFrom,
        date_to: filters.dateTo,
        group_by: filters.groupBy,
        ...(filters.advertiserId && { advertiser_id: filters.advertiserId }),
        ...(filters.publisherId && { publisher_id: filters.publisherId }),
        ...(filters.campaignId && { campaign_id: filters.campaignId }),
        ...(filters.websiteId && { website_id: filters.websiteId }),
      });

      const response = await fetch(`/api/admin/statistics/detailed?${params}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const refreshData = () => {
    fetchStatistics();
  };

  const exportCSV = async () => {
    try {
      const params = new URLSearchParams({
        date_from: filters.dateFrom,
        date_to: filters.dateTo,
        group_by: filters.groupBy,
        ...(filters.advertiserId && { advertiser_id: filters.advertiserId }),
        ...(filters.publisherId && { publisher_id: filters.publisherId }),
        ...(filters.campaignId && { campaign_id: filters.campaignId }),
        ...(filters.websiteId && { website_id: filters.websiteId }),
        format: 'csv',
      });

      const response = await fetch(`/api/admin/statistics/export?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `admin-stats-${filters.dateFrom}-${filters.dateTo}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export CSV:', error);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  const totals = stats.reduce((acc, stat) => ({
    impressions: acc.impressions + stat.impressions,
    clicks: acc.clicks + stat.clicks,
    conversions: acc.conversions + stat.conversions,
    revenue: acc.revenue + stat.revenue,
  }), { impressions: 0, clicks: 0, conversions: 0, revenue: 0 });

  const avgCTR = totals.impressions > 0 ? ((totals.clicks / totals.impressions) * 100).toFixed(2) : '0.00';

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Platform Statistics</h1>
            <p className="mt-2 text-gray-600">Monitor platform-wide performance and analytics</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={refreshData}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
            <button
              onClick={exportCSV}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Export CSV
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Filters</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Group By</label>
                <select
                  value={filters.groupBy}
                  onChange={(e) => handleFilterChange('groupBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="day">Day</option>
                  <option value="advertiser">Advertiser</option>
                  <option value="publisher">Publisher</option>
                  <option value="campaign">Campaign</option>
                  <option value="website">Website</option>
                  <option value="country">Country</option>
                  <option value="os">Operating System</option>
                  <option value="browser">Browser</option>
                  <option value="device">Device Type</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Advertiser</label>
                <select
                  value={filters.advertiserId}
                  onChange={(e) => handleFilterChange('advertiserId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Advertisers</option>
                  {advertisers.map((advertiser) => (
                    <option key={advertiser.id} value={advertiser.id}>
                      {advertiser.full_name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Publisher</label>
                <select
                  value={filters.publisherId}
                  onChange={(e) => handleFilterChange('publisherId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Publishers</option>
                  {publishers.map((publisher) => (
                    <option key={publisher.id} value={publisher.id}>
                      {publisher.full_name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Campaign</label>
                <select
                  value={filters.campaignId}
                  onChange={(e) => handleFilterChange('campaignId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Campaigns</option>
                  {campaigns.map((campaign) => (
                    <option key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                <select
                  value={filters.websiteId}
                  onChange={(e) => handleFilterChange('websiteId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Websites</option>
                  {websites.map((website) => (
                    <option key={website.id} value={website.id}>
                      {website.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Impressions</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.impressions)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Clicks</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.clicks)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Conversions</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.conversions)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Revenue</div>
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(totals.revenue)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Avg CTR</div>
            <div className="text-2xl font-bold text-gray-900">{avgCTR}%</div>
          </div>
        </div>

        {/* Charts */}
        {filters.groupBy === 'day' && stats.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="impressions" stroke="#3B82F6" name="Impressions" />
                  <Line type="monotone" dataKey="clicks" stroke="#10B981" name="Clicks" />
                  <Line type="monotone" dataKey="conversions" stroke="#8B5CF6" name="Conversions" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue & CTR Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="revenue" fill="#F59E0B" name="Revenue ($)" />
                  <Line yAxisId="right" type="monotone" dataKey="ctr" stroke="#EF4444" name="CTR (%)" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {(filters.groupBy !== 'day') && stats.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance by {filters.groupBy}</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey={
                    filters.groupBy === 'advertiser' ? 'advertiser_name' :
                    filters.groupBy === 'publisher' ? 'publisher_name' :
                    filters.groupBy === 'campaign' ? 'campaign_name' :
                    filters.groupBy === 'website' ? 'website_name' :
                    filters.groupBy === 'country' ? 'country' :
                    filters.groupBy === 'os' ? 'os' :
                    filters.groupBy === 'browser' ? 'browser' :
                    filters.groupBy === 'device' ? 'device' : 'name'
                  }
                  angle={-45}
                  textAnchor="end"
                  height={100}
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="impressions" fill="#3B82F6" name="Impressions" />
                <Bar dataKey="clicks" fill="#10B981" name="Clicks" />
                <Bar dataKey="conversions" fill="#8B5CF6" name="Conversions" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Statistics Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Detailed Statistics</h2>
          </div>
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Loading statistics...</p>
              </div>
            ) : stats.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                <p className="mt-1 text-sm text-gray-500">Statistics will appear here once there is platform activity.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {filters.groupBy === 'day' ? 'Date' :
                       filters.groupBy === 'advertiser' ? 'Advertiser' :
                       filters.groupBy === 'publisher' ? 'Publisher' :
                       filters.groupBy === 'campaign' ? 'Campaign' :
                       filters.groupBy === 'website' ? 'Website' :
                       filters.groupBy === 'country' ? 'Country' :
                       filters.groupBy === 'os' ? 'Operating System' :
                       filters.groupBy === 'browser' ? 'Browser' :
                       filters.groupBy === 'device' ? 'Device Type' : 'Name'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impressions</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversions</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {stats.map((stat, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {filters.groupBy === 'day' ? stat.date :
                         filters.groupBy === 'advertiser' ? stat.advertiser_name :
                         filters.groupBy === 'publisher' ? stat.publisher_name :
                         filters.groupBy === 'campaign' ? stat.campaign_name :
                         filters.groupBy === 'website' ? stat.website_name :
                         filters.groupBy === 'country' ? (stat.country || 'Unknown') :
                         filters.groupBy === 'os' ? (stat.os || 'Unknown') :
                         filters.groupBy === 'browser' ? (stat.browser || 'Unknown') :
                         filters.groupBy === 'device' ? (stat.device || 'Unknown') : stat.name || stat.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.impressions)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.clicks)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.conversions)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.ctr.toFixed(2)}%</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.revenue)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
