'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { Country, State } from 'country-state-city';

// Macro Info Component
const MacroInfo = ({ onMacroClick }: { onMacroClick: (macro: string) => void }) => {
  const macros = [
    { name: 'Click ID', value: '{click_id}', description: 'Unique click identifier' },
    { name: 'Campaign ID', value: '{campaign_id}', description: 'Campaign identifier' },
    { name: 'Zone ID', value: '{zone_id}', description: 'Ad zone identifier' },
    { name: 'Publisher ID', value: '{publisher_id}', description: 'Publisher identifier' },
    { name: 'Country', value: '{country}', description: 'User country code' },
    { name: 'State', value: '{state}', description: 'User state/region' },
    { name: 'City', value: '{city}', description: 'User city' },
    { name: 'Device Type', value: '{device_type}', description: 'Device type (Desktop/Mobile/Tablet)' },
    { name: 'OS', value: '{os}', description: 'Operating system' },
    { name: 'Browser', value: '{browser}', description: 'Browser name' },
    { name: 'IP Address', value: '{ip_address}', description: 'User IP address' },
    { name: 'User Agent', value: '{user_agent}', description: 'Browser user agent string' },
    { name: 'Timestamp', value: '{timestamp}', description: 'Click timestamp' },
    { name: 'Cost', value: '{cost}', description: 'Cost per click/impression' },
  ];

  return (
    <div className="mt-2 p-3 bg-gray-50 rounded-md">
      <h4 className="text-sm font-medium text-gray-700 mb-2">Available Macros (click to copy):</h4>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {macros.map((macro) => (
          <button
            key={macro.value}
            type="button"
            onClick={() => onMacroClick(macro.value)}
            className="text-left p-2 text-xs bg-white border border-gray-200 rounded hover:bg-blue-50 hover:border-blue-300"
            title={macro.description}
          >
            <div className="font-mono text-blue-600">{macro.value}</div>
            <div className="text-gray-500">{macro.name}</div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default function AdminCampaignEdit() {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const [campaign, setCampaign] = useState(null);
  const [loading, setLoading] = useState(true);

  const [formData, setFormData] = useState({
    name: '',
    cpmBid: '',
    dailyBudget: '',
    totalBudget: '',

    // Creative settings (editable)
    landingUrl: '',
    jsTag: '',
    nativeTitle: '',
    nativeDescription: '',
    pushTitle: '',
    pushDescription: '',

    // Geo targeting
    targetingCountries: [] as string[],
    targetingStates: [] as string[],

    // Device/OS/Browser targeting
    targetingDevices: [] as string[],
    targetingOs: [] as string[],
    targetingBrowsers: [] as string[],
    targetingConnectionTypes: [] as string[],

    // Schedule
    startDate: '',
    endDate: '',
    dailySchedule: [] as string[], // Days of week
    hourlySchedule: [] as number[], // Hours 0-23

    // Frequency cap
    frequencyCapValue: '',
    frequencyCapPeriod: 'day', // 'hour', 'day', 'week'

    // Whitelist/Blacklist
    whitelistPublishers: '',
    whitelistWebsites: '',
    whitelistZones: '',
    blacklistPublishers: '',
    blacklistWebsites: '',
    blacklistZones: '',
  });

  // Search states for dropdowns
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showStateDropdown, setShowStateDropdown] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Image upload states
  const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);
  const [nativeIconFile, setNativeIconFile] = useState<File | null>(null);
  const [nativeImageFile, setNativeImageFile] = useState<File | null>(null);
  const [pushImageFile, setPushImageFile] = useState<File | null>(null);

  // Image preview URLs
  const [bannerImagePreview, setBannerImagePreview] = useState<string>('');
  const [nativeIconPreview, setNativeIconPreview] = useState<string>('');
  const [nativeImagePreview, setNativeImagePreview] = useState<string>('');
  const [pushImagePreview, setPushImagePreview] = useState<string>('');

  // File input refs
  const bannerImageRef = useRef<HTMLInputElement>(null);
  const nativeIconRef = useRef<HTMLInputElement>(null);
  const nativeImageRef = useRef<HTMLInputElement>(null);
  const pushImageRef = useRef<HTMLInputElement>(null);

  // Refs for dropdown containers (moved here to avoid conditional hook calls)
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  const stateDropdownRef = useRef<HTMLDivElement>(null);

  // Country/State data
  const [countries, setCountries] = useState<any[]>([]);
  const [availableStates, setAvailableStates] = useState<any[]>([]);

  // Constants for form options
  const devices = ['Desktop', 'Mobile', 'Tablet'];
  const operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Other'];
  const connectionTypes = ['WiFi', 'Cellular', 'Broadband', 'Dial-up'];
  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Load countries on mount
  useEffect(() => {
    const loadCountries = async () => {
      try {
        console.log('Country import:', Country);
        console.log('Country.getAllCountries:', typeof Country.getAllCountries);

        // Try to get countries
        const allCountries = Country.getAllCountries();
        console.log('Raw countries result:', allCountries);

        if (allCountries && Array.isArray(allCountries) && allCountries.length > 0) {
          console.log('Loaded countries successfully:', allCountries.length, allCountries.slice(0, 3));
          setCountries(allCountries);
        } else {
          throw new Error('No countries returned from library');
        }
      } catch (error) {
        console.error('Error loading countries:', error);
        // Comprehensive fallback list
        const fallbackCountries = [
          { name: 'United States', isoCode: 'US' },
          { name: 'Canada', isoCode: 'CA' },
          { name: 'United Kingdom', isoCode: 'GB' },
          { name: 'Germany', isoCode: 'DE' },
          { name: 'France', isoCode: 'FR' },
          { name: 'Australia', isoCode: 'AU' },
          { name: 'Japan', isoCode: 'JP' },
          { name: 'Brazil', isoCode: 'BR' },
          { name: 'India', isoCode: 'IN' },
          { name: 'China', isoCode: 'CN' },
          { name: 'Russia', isoCode: 'RU' },
          { name: 'Mexico', isoCode: 'MX' },
          { name: 'Italy', isoCode: 'IT' },
          { name: 'Spain', isoCode: 'ES' },
          { name: 'Netherlands', isoCode: 'NL' },
        ];
        console.log('Using fallback countries:', fallbackCountries.length);
        setCountries(fallbackCountries);
      }
    };

    loadCountries();
  }, []);

  // Update available states when countries change
  useEffect(() => {
    if (formData.targetingCountries.length > 0) {
      try {
        const states: any[] = [];
        formData.targetingCountries.forEach(countryCode => {
          try {
            const countryStates = State.getStatesOfCountry(countryCode);
            if (countryStates && Array.isArray(countryStates)) {
              states.push(...countryStates);
            }
          } catch (error) {
            console.error(`Error loading states for country ${countryCode}:`, error);
            // Add fallback states for common countries
            if (countryCode === 'US') {
              states.push(
                { name: 'California', isoCode: 'CA' },
                { name: 'New York', isoCode: 'NY' },
                { name: 'Texas', isoCode: 'TX' },
                { name: 'Florida', isoCode: 'FL' }
              );
            } else if (countryCode === 'CA') {
              states.push(
                { name: 'Ontario', isoCode: 'ON' },
                { name: 'Quebec', isoCode: 'QC' },
                { name: 'British Columbia', isoCode: 'BC' }
              );
            }
          }
        });
        console.log('Loaded states for countries:', states.length);
        setAvailableStates(states);
      } catch (error) {
        console.error('Error in state loading effect:', error);
        setAvailableStates([]);
      }
    } else {
      setAvailableStates([]);
    }
  }, [formData.targetingCountries]);

  // Close dropdowns when clicking outside (moved here to avoid conditional hook calls)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click is outside country dropdown
      if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target as Node)) {
        setShowCountryDropdown(false);
      }
      // Check if click is outside state dropdown
      if (stateDropdownRef.current && !stateDropdownRef.current.contains(event.target as Node)) {
        setShowStateDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Function to populate form data from campaign
  const populateFormData = (campaignData: any) => {
    setFormData({
      name: campaignData.name || '',
      cpmBid: campaignData.cpm_bid?.toString() || '',
      dailyBudget: campaignData.daily_budget?.toString() || '',
      totalBudget: campaignData.total_budget?.toString() || '',

      // Creative settings
      landingUrl: campaignData.landing_url || '',
      jsTag: campaignData.js_tag || '',
      nativeTitle: campaignData.native_title || '',
      nativeDescription: campaignData.native_description || '',
      pushTitle: campaignData.push_title || '',
      pushDescription: campaignData.push_description || '',

      // Geo targeting
      targetingCountries: campaignData.targeting_countries || [],
      targetingStates: campaignData.targeting_states || [],

      // Device/OS/Browser targeting
      targetingDevices: campaignData.targeting_devices || [],
      targetingOs: campaignData.targeting_os || [],
      targetingBrowsers: campaignData.targeting_browsers || [],
      targetingConnectionTypes: campaignData.targeting_connection_types || [],

      // Schedule
      startDate: campaignData.start_date ? new Date(campaignData.start_date).toISOString().split('T')[0] : '',
      endDate: campaignData.end_date ? new Date(campaignData.end_date).toISOString().split('T')[0] : '',
      dailySchedule: campaignData.daily_schedule || [],
      hourlySchedule: campaignData.hourly_schedule || [],

      // Frequency cap
      frequencyCapValue: campaignData.frequency_cap_value?.toString() || '',
      frequencyCapPeriod: campaignData.frequency_cap_period === 1 ? 'hour' :
                          campaignData.frequency_cap_period === 3 ? 'week' : 'day',

      // Whitelist/Blacklist
      whitelistPublishers: campaignData.whitelist_publishers || '',
      whitelistWebsites: campaignData.whitelist_websites || '',
      whitelistZones: campaignData.whitelist_zones || '',
      blacklistPublishers: campaignData.blacklist_publishers || '',
      blacklistWebsites: campaignData.blacklist_websites || '',
      blacklistZones: campaignData.blacklist_zones || '',
    });

    // Set image previews from existing campaign data
    setBannerImagePreview(campaignData.banner_image_url || '');
    setNativeIconPreview(campaignData.native_icon_url || '');
    setNativeImagePreview(campaignData.native_image_url || '');
    setPushImagePreview(campaignData.push_image_url || '');
  };

  useEffect(() => {
    if (!session?.user?.id || !params.id) return;

    const fetchCampaign = async () => {
      try {
        const response = await fetch(`/api/admin/campaigns/${params.id}`);
        const data = await response.json();
        if (data.success) {
          setCampaign(data.campaign);
          // Populate form data with campaign data
          populateFormData(data.campaign);
        } else {
          router.push('/admin/campaigns');
        }
      } catch (error) {
        console.error('Error fetching campaign:', error);
        router.push('/admin/campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [session, params.id, router]);

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading campaign...</p>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Campaign not found</h1>
          <p className="mt-2 text-gray-500">The campaign you&apos;re looking for doesn&apos;t exist.</p>
          <button
            onClick={() => router.push('/admin/campaigns')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  // Refs for dropdown containers and click outside handler moved to top

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes for arrays
  const handleArrayChange = (name: string, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
        ? [...(prev[name as keyof typeof prev] as string[]), value]
        : (prev[name as keyof typeof prev] as string[]).filter(item => item !== value)
    }));
  };

  // Handle image file selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>, type: string) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        switch (type) {
          case 'banner':
            setBannerImageFile(file);
            setBannerImagePreview(result);
            break;
          case 'nativeIcon':
            setNativeIconFile(file);
            setNativeIconPreview(result);
            break;
          case 'nativeImage':
            setNativeImageFile(file);
            setNativeImagePreview(result);
            break;
          case 'push':
            setPushImageFile(file);
            setPushImagePreview(result);
            break;
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle macro click for landing URL
  const handleMacroClick = (macro: string) => {
    setFormData(prev => ({
      ...prev,
      landingUrl: prev.landingUrl + macro
    }));
  };

  // Handle multi-select for arrays (countries, devices, etc.)
  const handleMultiSelect = (value: string | number, fieldName: string) => {
    setFormData(prev => {
      const currentArray = prev[fieldName as keyof typeof prev] as (string | number)[];
      const isSelected = currentArray.includes(value);

      return {
        ...prev,
        [fieldName]: isSelected
          ? currentArray.filter(item => item !== value)
          : [...currentArray, value]
      };
    });
  };

  // Handle select all for arrays
  const handleSelectAll = (fieldName: string, allValues: (string | number)[]) => {
    setFormData(prev => {
      const currentArray = prev[fieldName as keyof typeof prev] as (string | number)[];
      const isAllSelected = allValues.every(value => currentArray.includes(value));

      return {
        ...prev,
        [fieldName]: isAllSelected ? [] : allValues
      };
    });
  };

  // Handle country search and selection
  const handleCountrySearch = (searchTerm: string) => {
    setCountrySearch(searchTerm);
    setShowCountryDropdown(true);
  };

  const handleCountrySelect = (countryCode: string) => {
    if (!formData.targetingCountries.includes(countryCode)) {
      setFormData(prev => ({
        ...prev,
        targetingCountries: [...prev.targetingCountries, countryCode]
      }));
    }
    setCountrySearch('');
    setShowCountryDropdown(false);
  };

  const removeCountry = (countryCode: string) => {
    setFormData(prev => ({
      ...prev,
      targetingCountries: prev.targetingCountries.filter(c => c !== countryCode),
      targetingStates: prev.targetingStates.filter(s => {
        const country = countries.find(c => c.isoCode === countryCode);
        if (!country) return true;
        const countryStates = State.getStatesOfCountry(countryCode);
        return !countryStates.some(state => state.isoCode === s);
      })
    }));
  };

  // Handle state search and selection
  const handleStateSearch = (searchTerm: string) => {
    setStateSearch(searchTerm);
    setShowStateDropdown(true);
  };

  const handleStateSelect = (stateCode: string) => {
    if (!formData.targetingStates.includes(stateCode)) {
      setFormData(prev => ({
        ...prev,
        targetingStates: [...prev.targetingStates, stateCode]
      }));
    }
    setStateSearch('');
    setShowStateDropdown(false);
  };

  const removeState = (stateCode: string) => {
    setFormData(prev => ({
      ...prev,
      targetingStates: prev.targetingStates.filter(s => s !== stateCode)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Basic validation
      const newErrors: Record<string, string> = {};

      if (!formData.name.trim()) {
        newErrors.name = 'Campaign name is required';
      }

      if (!formData.cpmBid || parseFloat(formData.cpmBid) < 0) {
        newErrors.cpmBid = 'CPM bid must be 0 or greater';
      }

      if (!formData.startDate) {
        newErrors.startDate = 'Start date is required';
      }

      // JS Tag validation for JS banner campaigns
      if (campaign?.type === 'banner' && (campaign.creative_type === 2 || campaign.creative_type === 'js')) {
        if (!formData.jsTag.trim()) {
          newErrors.jsTag = 'JavaScript tag is required for JS banner campaigns';
        }
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setIsLoading(false);
        return;
      }

      // Create FormData for submission
      const submitFormData = new FormData();

      // Basic campaign data
      submitFormData.append('name', formData.name);
      submitFormData.append('cpmBid', formData.cpmBid);
      submitFormData.append('dailyBudget', formData.dailyBudget);
      submitFormData.append('totalBudget', formData.totalBudget);

      // Creative fields
      submitFormData.append('landingUrl', formData.landingUrl);
      submitFormData.append('jsTag', formData.jsTag);
      submitFormData.append('nativeTitle', formData.nativeTitle);
      submitFormData.append('nativeDescription', formData.nativeDescription);
      submitFormData.append('pushTitle', formData.pushTitle);
      submitFormData.append('pushDescription', formData.pushDescription);

      // Targeting data
      submitFormData.append('targetingCountries', JSON.stringify(formData.targetingCountries));
      submitFormData.append('targetingStates', JSON.stringify(formData.targetingStates));
      submitFormData.append('targetingDevices', JSON.stringify(formData.targetingDevices));
      submitFormData.append('targetingOs', JSON.stringify(formData.targetingOs));
      submitFormData.append('targetingBrowsers', JSON.stringify(formData.targetingBrowsers));
      submitFormData.append('targetingConnectionTypes', JSON.stringify(formData.targetingConnectionTypes));

      // Schedule data
      submitFormData.append('startDate', formData.startDate);
      submitFormData.append('endDate', formData.endDate);
      submitFormData.append('dailySchedule', JSON.stringify(formData.dailySchedule));
      submitFormData.append('hourlySchedule', JSON.stringify(formData.hourlySchedule));

      // Frequency cap
      submitFormData.append('frequencyCapValue', formData.frequencyCapValue);
      submitFormData.append('frequencyCapPeriod', formData.frequencyCapPeriod);

      // Whitelist/Blacklist
      submitFormData.append('whitelistPublishers', formData.whitelistPublishers);
      submitFormData.append('whitelistWebsites', formData.whitelistWebsites);
      submitFormData.append('whitelistZones', formData.whitelistZones);
      submitFormData.append('blacklistPublishers', formData.blacklistPublishers);
      submitFormData.append('blacklistWebsites', formData.blacklistWebsites);
      submitFormData.append('blacklistZones', formData.blacklistZones);

      // Image files
      if (bannerImageFile) {
        submitFormData.append('bannerImage', bannerImageFile);
      }
      if (nativeIconFile) {
        submitFormData.append('nativeIcon', nativeIconFile);
      }
      if (nativeImageFile) {
        submitFormData.append('nativeImage', nativeImageFile);
      }
      if (pushImageFile) {
        submitFormData.append('pushImage', pushImageFile);
      }

      const response = await fetch(`/api/admin/campaigns/${params.id}`, {
        method: 'PUT',
        body: submitFormData,
      });

      const result = await response.json();

      if (result.success) {
        router.push('/admin/campaigns');
      } else {
        setErrors({ submit: result.message || 'Failed to update campaign' });
      }
    } catch (error) {
      console.error('Error updating campaign:', error);
      setErrors({ submit: 'An error occurred while updating the campaign' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Campaign (Admin)</h1>
              <p className="mt-2 text-gray-600">
                Campaign ID: #{campaign.id} | Type: {campaign.type} | Advertiser: {campaign.user_name}
              </p>
            </div>
            <button
              onClick={() => router.push('/admin/campaigns')}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              Back to Campaigns
            </button>
          </div>
        </div>

        {/* Error Display */}
        {errors.submit && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-red-800">{errors.submit}</div>
          </div>
        )}

        {/* Main Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-8">
            {/* Campaign Type Section - READ ONLY */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Type (Cannot be changed)</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'banner' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'banner'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      Banner Ads
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Display banner advertisements</p>
                </div>
                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'native' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'native'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      Native Ads
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Content-style advertisements</p>
                </div>
                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'in_page_push' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'in_page_push'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      In-Page Push
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Push-style notifications</p>
                </div>
                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'popup' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'popup'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      Popup Ads
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Popup advertisements</p>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">Campaign type cannot be changed after creation</p>
            </div>

            {/* Campaign Details Section */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Campaign Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                </div>

                <div>
                  <label htmlFor="cpmBid" className="block text-sm font-medium text-gray-700">
                    CPM Bid ($) *
                  </label>
                  <input
                    type="number"
                    id="cpmBid"
                    name="cpmBid"
                    step="0.0001"
                    min="0"
                    value={formData.cpmBid}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">Admin can set any bid amount (no minimum restriction)</p>
                  {errors.cpmBid && <p className="mt-1 text-sm text-red-600">{errors.cpmBid}</p>}
                </div>

                <div>
                  <label htmlFor="dailyBudget" className="block text-sm font-medium text-gray-700">
                    Daily Budget ($)
                  </label>
                  <input
                    type="number"
                    id="dailyBudget"
                    name="dailyBudget"
                    step="0.01"
                    min="0"
                    value={formData.dailyBudget}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Empty or 0 for unlimited"
                  />
                </div>

                <div>
                  <label htmlFor="totalBudget" className="block text-sm font-medium text-gray-700">
                    Total Budget ($)
                  </label>
                  <input
                    type="number"
                    id="totalBudget"
                    name="totalBudget"
                    step="0.01"
                    min="0"
                    value={formData.totalBudget}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Empty or 0 for unlimited"
                  />
                </div>
              </div>
            </div>

            {/* Creative Setup Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Creative Setup</h3>

              {/* Landing URL - Only show for non-JS campaigns */}
              {!(campaign?.type === 'banner' && (campaign.creative_type === 2 || campaign.creative_type === 'js')) && (
                <div className="mb-6">
                  <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                    Landing URL *
                  </label>
                  <input
                    type="url"
                    id="landingUrl"
                    name="landingUrl"
                    value={formData.landingUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/landing-page"
                    required
                  />
                  {errors.landingUrl && <p className="mt-1 text-sm text-red-600">{errors.landingUrl}</p>}

                  {/* Macro Info */}
                  <MacroInfo onMacroClick={handleMacroClick} />
                </div>
              )}

              {/* JS Tag for JS Banner Campaigns */}
              {campaign?.type === 'banner' && (campaign.creative_type === 2 || campaign.creative_type === 'js') && (
                <div className="mb-6">
                  <label htmlFor="jsTag" className="block text-sm font-medium text-gray-700">
                    JavaScript Tag *
                  </label>
                  <textarea
                    id="jsTag"
                    name="jsTag"
                    value={formData.jsTag}
                    onChange={handleChange}
                    rows={6}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="<script>...</script>"
                    required
                  />
                  {errors.jsTag && <p className="mt-1 text-sm text-red-600">{errors.jsTag}</p>}
                  <p className="mt-1 text-xs text-gray-500">
                    Enter the complete JavaScript tag for your banner ad
                  </p>
                </div>
              )}

              {/* Native Ad Fields */}
              {campaign?.type === 'native' && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="nativeTitle" className="block text-sm font-medium text-gray-700">
                        Native Ad Title *
                      </label>
                      <input
                        type="text"
                        id="nativeTitle"
                        name="nativeTitle"
                        value={formData.nativeTitle}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Compelling ad title"
                        maxLength={50}
                        required
                      />
                      {errors.nativeTitle && <p className="mt-1 text-sm text-red-600">{errors.nativeTitle}</p>}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="nativeDescription" className="block text-sm font-medium text-gray-700">
                      Native Ad Description *
                    </label>
                    <textarea
                      id="nativeDescription"
                      name="nativeDescription"
                      value={formData.nativeDescription}
                      onChange={handleChange}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Engaging description that attracts clicks"
                      maxLength={200}
                      required
                    />
                    {errors.nativeDescription && <p className="mt-1 text-sm text-red-600">{errors.nativeDescription}</p>}
                  </div>
                </>
              )}

              {/* Push Ad Fields */}
              {campaign?.type === 'in_page_push' && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="pushTitle" className="block text-sm font-medium text-gray-700">
                        Push Notification Title *
                      </label>
                      <input
                        type="text"
                        id="pushTitle"
                        name="pushTitle"
                        value={formData.pushTitle}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Attention-grabbing title"
                        maxLength={50}
                        required
                      />
                      {errors.pushTitle && <p className="mt-1 text-sm text-red-600">{errors.pushTitle}</p>}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="pushDescription" className="block text-sm font-medium text-gray-700">
                      Push Notification Description *
                    </label>
                    <textarea
                      id="pushDescription"
                      name="pushDescription"
                      value={formData.pushDescription}
                      onChange={handleChange}
                      rows={3}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Compelling message that drives action"
                      maxLength={150}
                      required
                    />
                    {errors.pushDescription && <p className="mt-1 text-sm text-red-600">{errors.pushDescription}</p>}
                  </div>
                </>
              )}
            </div>

            {/* Targeting Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Targeting Options</h3>

              {/* Geographic Targeting */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-800 mb-4">Geographic Targeting</h4>

                {/* Countries */}
                <div className="mb-6" ref={countryDropdownRef}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Countries
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700">Search and select countries:</span>
                    {countries.length > 0 && (
                      <button
                        type="button"
                        onClick={() => handleSelectAll('targetingCountries', countries.map(c => c.isoCode))}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {formData.targetingCountries.length === countries.length ? 'Deselect All' : 'Select All'}
                      </button>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={countrySearch}
                      onChange={(e) => handleCountrySearch(e.target.value)}
                      onFocus={() => setShowCountryDropdown(true)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Search and select countries..."
                    />

                    {showCountryDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {countries && countries.length > 0 ? (
                          countries
                            .filter(country =>
                              country.name.toLowerCase().includes(countrySearch.toLowerCase()) &&
                              !formData.targetingCountries.includes(country.isoCode)
                            )
                            .slice(0, 10)
                            .map(country => (
                              <button
                                key={country.isoCode}
                                type="button"
                                onClick={() => handleCountrySelect(country.isoCode)}
                                className="w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                              >
                                {country.name}
                              </button>
                            ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 text-sm">
                            Loading countries...
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Selected Countries */}
                  {formData.targetingCountries.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.targetingCountries.map(countryCode => {
                        const country = countries.find(c => c.isoCode === countryCode);
                        return (
                          <span
                            key={countryCode}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {country?.name || countryCode}
                            <button
                              type="button"
                              onClick={() => removeCountry(countryCode)}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                            >
                              ×
                            </button>
                          </span>
                        );
                      })}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Empty to target all countries
                  </p>
                </div>

                {/* States */}
                {formData.targetingCountries.length > 0 && (
                  <div className="mb-6" ref={stateDropdownRef}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target States/Regions
                    </label>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-700">Search and select states:</span>
                      {availableStates.length > 0 && (
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targetingStates', availableStates.map(s => s.isoCode))}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targetingStates.length === availableStates.length ? 'Deselect All' : 'Select All'}
                        </button>
                      )}
                    </div>
                    <div className="relative">
                      <input
                        type="text"
                        value={stateSearch}
                        onChange={(e) => handleStateSearch(e.target.value)}
                        onFocus={() => setShowStateDropdown(true)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Search and select states/regions..."
                      />

                      {showStateDropdown && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                          {availableStates && availableStates.length > 0 ? (
                            availableStates
                              .filter(state =>
                                state.name.toLowerCase().includes(stateSearch.toLowerCase()) &&
                                !formData.targetingStates.includes(state.isoCode)
                              )
                              .slice(0, 10)
                              .map(state => (
                                <button
                                  key={state.isoCode}
                                  type="button"
                                  onClick={() => handleStateSelect(state.isoCode)}
                                  className="w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                                >
                                  {state.name}
                                </button>
                              ))
                          ) : (
                            <div className="px-3 py-2 text-gray-500 text-sm">
                              {formData.targetingCountries.length > 0 ? 'Loading states...' : 'Select countries first'}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Selected States */}
                    {formData.targetingStates.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-2">
                        {formData.targetingStates.map(stateCode => {
                          const state = availableStates.find(s => s.isoCode === stateCode);
                          return (
                            <span
                              key={stateCode}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                            >
                              {state?.name || stateCode}
                              <button
                                type="button"
                                onClick={() => removeState(stateCode)}
                                className="ml-1 text-green-600 hover:text-green-800"
                              >
                                ×
                              </button>
                            </span>
                          );
                        })}
                      </div>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Empty to target all states/regions in selected countries
                    </p>
                  </div>
                )}
              </div>

              {/* Device & Platform Targeting */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-800 mb-4">Device & Platform Targeting</h4>

                {/* Device Types */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Device Types
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700">Select devices:</span>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingDevices', devices)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingDevices.length === devices.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    {devices.map(device => (
                      <button
                        key={device}
                        type="button"
                        onClick={() => handleMultiSelect(device, 'targetingDevices')}
                        className={`p-3 text-sm border rounded-lg transition-colors ${
                          formData.targetingDevices.includes(device)
                            ? 'bg-blue-50 border-blue-300 text-blue-700'
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {device}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty to target all device types
                  </p>
                </div>

                {/* Operating Systems */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Operating Systems
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700">Select operating systems:</span>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingOs', operatingSystems)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingOs.length === operatingSystems.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    {operatingSystems.map(os => (
                      <button
                        key={os}
                        type="button"
                        onClick={() => handleMultiSelect(os, 'targetingOs')}
                        className={`p-3 text-sm border rounded-lg transition-colors ${
                          formData.targetingOs.includes(os)
                            ? 'bg-blue-50 border-blue-300 text-blue-700'
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {os}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty to target all operating systems
                  </p>
                </div>

                {/* Browsers */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Browsers
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700">Select browsers:</span>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingBrowsers', browsers)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingBrowsers.length === browsers.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    {browsers.map(browser => (
                      <button
                        key={browser}
                        type="button"
                        onClick={() => handleMultiSelect(browser, 'targetingBrowsers')}
                        className={`p-3 text-sm border rounded-lg transition-colors ${
                          formData.targetingBrowsers.includes(browser)
                            ? 'bg-blue-50 border-blue-300 text-blue-700'
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {browser}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty to target all browsers
                  </p>
                </div>

                {/* Connection Types */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Connection Types
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700">Select connection types:</span>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingConnectionTypes', connectionTypes)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingConnectionTypes.length === connectionTypes.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    {connectionTypes.map(connectionType => (
                      <button
                        key={connectionType}
                        type="button"
                        onClick={() => handleMultiSelect(connectionType, 'targetingConnectionTypes')}
                        className={`p-3 text-sm border rounded-lg transition-colors ${
                          formData.targetingConnectionTypes.includes(connectionType)
                            ? 'bg-blue-50 border-blue-300 text-blue-700'
                            : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {connectionType}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Leave empty to target all connection types
                  </p>
                </div>
              </div>
            </div>

            {/* Schedule Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Schedule</h3>

              {/* Date Range */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                    Start Date *
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.startDate && <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>}
                </div>

                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty to run indefinitely
                  </p>
                  {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>}
                </div>
              </div>

              {/* Daily Schedule */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Days of Week
                </label>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-700">Select days:</span>
                  <button
                    type="button"
                    onClick={() => handleSelectAll('dailySchedule', daysOfWeek)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {formData.dailySchedule.length === daysOfWeek.length ? 'Deselect All' : 'Select All'}
                  </button>
                </div>
                <div className="grid grid-cols-7 gap-1">
                  {daysOfWeek.map((day) => (
                    <button
                      key={day}
                      type="button"
                      onClick={() => handleMultiSelect(day, 'dailySchedule')}
                      className={`p-2 text-xs border rounded transition-colors ${
                        formData.dailySchedule.includes(day)
                          ? 'bg-blue-50 border-blue-300 text-blue-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {day.slice(0, 3)}
                    </button>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Leave empty to run all days of the week
                </p>
              </div>

              {/* Hourly Schedule */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Hours of Day (24-hour format)
                </label>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-700">Select hours:</span>
                  <button
                    type="button"
                    onClick={() => handleSelectAll('hourlySchedule', Array.from({length: 24}, (_, i) => i))}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {formData.hourlySchedule.length === 24 ? 'Deselect All' : 'Select All'}
                  </button>
                </div>
                <div className="grid grid-cols-8 gap-1">
                  {Array.from({length: 24}, (_, hour) => (
                    <button
                      key={hour}
                      type="button"
                      onClick={() => handleMultiSelect(hour, 'hourlySchedule')}
                      className={`p-2 text-xs border rounded transition-colors ${
                        formData.hourlySchedule.includes(hour)
                          ? 'bg-blue-50 border-blue-300 text-blue-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {hour.toString().padStart(2, '0')}:00
                    </button>
                  ))}
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  Leave empty to run 24/7
                </p>
              </div>
            </div>

            {/* Frequency Cap Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Frequency Cap</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="frequencyCapValue" className="block text-sm font-medium text-gray-700">
                    Maximum Impressions
                  </label>
                  <input
                    type="number"
                    id="frequencyCapValue"
                    name="frequencyCapValue"
                    value={formData.frequencyCapValue}
                    onChange={handleChange}
                    min="1"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., 3"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty for no frequency cap
                  </p>
                </div>

                <div>
                  <label htmlFor="frequencyCapPeriod" className="block text-sm font-medium text-gray-700">
                    Time Period
                  </label>
                  <select
                    id="frequencyCapPeriod"
                    name="frequencyCapPeriod"
                    value={formData.frequencyCapPeriod}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="hour">Per Hour</option>
                    <option value="day">Per Day</option>
                    <option value="week">Per Week</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    How often the cap resets
                  </p>
                </div>
              </div>
              <p className="mt-4 text-sm text-gray-600">
                Frequency capping limits how many times the same user sees your ad within the specified time period.
              </p>
            </div>

            {/* Publisher & Website Controls Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Publisher & Website Controls</h3>
              <p className="text-sm text-gray-600 mb-6">
                Control where your ads appear by specifying publisher IDs, website IDs, or zone IDs.
                Whitelist allows only specified sources, while blacklist excludes them.
              </p>

              {/* Whitelist Section */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                  <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                  Whitelist (Allow Only)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Publisher IDs
                    </label>
                    <textarea
                      name="whitelistPublishers"
                      value={formData.whitelistPublishers}
                      onChange={handleChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                      placeholder="Enter publisher IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                      style={{ maxHeight: '150px' }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Website IDs
                    </label>
                    <textarea
                      name="whitelistWebsites"
                      value={formData.whitelistWebsites}
                      onChange={handleChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                      placeholder="Enter website IDs, one per line&#10;Example:&#10;101&#10;102&#10;103"
                      style={{ maxHeight: '150px' }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Zone IDs
                    </label>
                    <textarea
                      name="whitelistZones"
                      value={formData.whitelistZones}
                      onChange={handleChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                      placeholder="Enter zone IDs, one per line&#10;Example:&#10;501&#10;502&#10;503"
                      style={{ maxHeight: '150px' }}
                    />
                  </div>
                </div>
              </div>

              {/* Blacklist Section */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                  <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                  Blacklist (Exclude)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Publisher IDs
                    </label>
                    <textarea
                      name="blacklistPublishers"
                      value={formData.blacklistPublishers}
                      onChange={handleChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                      placeholder="Enter publisher IDs to exclude, one per line&#10;Example:&#10;999&#10;888&#10;777"
                      style={{ maxHeight: '150px' }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Website IDs
                    </label>
                    <textarea
                      name="blacklistWebsites"
                      value={formData.blacklistWebsites}
                      onChange={handleChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                      placeholder="Enter website IDs to exclude, one per line&#10;Example:&#10;201&#10;202&#10;203"
                      style={{ maxHeight: '150px' }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Zone IDs
                    </label>
                    <textarea
                      name="blacklistZones"
                      value={formData.blacklistZones}
                      onChange={handleChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                      placeholder="Enter zone IDs to exclude, one per line&#10;Example:&#10;601&#10;602&#10;603"
                      style={{ maxHeight: '150px' }}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Important Notes
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>Whitelist takes priority over blacklist</li>
                        <li>If whitelist is specified, only those sources will be used</li>
                        <li>Blacklist only applies when whitelist is empty</li>
                        <li>Leave all fields empty to allow all publishers, websites, and zones</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Form submission buttons */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/admin/campaigns')}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Saving...' : 'Save Campaign'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
