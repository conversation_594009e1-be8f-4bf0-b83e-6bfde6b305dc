'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface RealtimeStats {
  impressions_last_hour: number;
  clicks_last_hour: number;
  conversions_last_hour: number;
  revenue_last_hour: number;
  active_campaigns: number;
  active_websites: number;
  blocked_impressions: number;
  blocked_clicks: number;
  top_campaigns: Array<{
    id: number;
    name: string;
    impressions: number;
    clicks: number;
    ctr: number;
  }>;
  recent_activity: Array<{
    type: string;
    message: string;
    timestamp: string;
  }>;
}

export default function RealtimeDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<RealtimeStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchRealtimeStats();

    // Set up polling for real-time updates
    const interval = setInterval(fetchRealtimeStats, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchRealtimeStats = async () => {
    try {
      const response = await fetch('/api/admin/realtime-stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch realtime stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Real-time Dashboard</h1>
            <p className="mt-2 text-gray-600">Live platform activity and performance metrics</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600">Live</span>
          </div>
        </div>

        {/* Real-time Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[
            {
              key: 'impressions',
              title: 'Impressions (Last Hour)',
              value: stats?.impressions_last_hour?.toLocaleString() || 0,
              color: 'bg-blue-500',
              icon: (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )
            },
            {
              key: 'clicks',
              title: 'Clicks (Last Hour)',
              value: stats?.clicks_last_hour?.toLocaleString() || 0,
              color: 'bg-green-500',
              icon: (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                </svg>
              )
            },
            {
              key: 'conversions',
              title: 'Conversions (Last Hour)',
              value: stats?.conversions_last_hour?.toLocaleString() || 0,
              color: 'bg-purple-500',
              icon: (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              )
            },
            {
              key: 'revenue',
              title: 'Revenue (Last Hour)',
              value: `$${stats?.revenue_last_hour?.toFixed(2) || '0.00'}`,
              color: 'bg-yellow-500',
              icon: (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              )
            }
          ].map((metric) => (
            <div key={metric.key} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 ${metric.color} rounded-md flex items-center justify-center`}>
                    {metric.icon}
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{metric.title}</dt>
                    <dd className="text-lg font-medium text-gray-900">{metric.value}</dd>
                  </dl>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Platform Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Status</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Campaigns</span>
                <span className="text-sm font-medium text-gray-900">{stats?.active_campaigns || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Websites</span>
                <span className="text-sm font-medium text-gray-900">{stats?.active_websites || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Blocked Impressions</span>
                <span className="text-sm font-medium text-red-600">{stats?.blocked_impressions || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Blocked Clicks</span>
                <span className="text-sm font-medium text-red-600">{stats?.blocked_clicks || 0}</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Campaigns</h3>
            <div className="space-y-3">
              {stats?.top_campaigns && stats.top_campaigns.length > 0 ? (
                stats.top_campaigns.slice(0, 5).map((campaign, index) => (
                  <div key={`campaign-${campaign.id}-${index}`} className="flex justify-between items-center">
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900 truncate">{campaign.name}</div>
                      <div className="text-xs text-gray-500">{campaign.impressions} impressions • {campaign.ctr.toFixed(2)}% CTR</div>
                    </div>
                    <div className="text-sm font-medium text-gray-900">{campaign.clicks} clicks</div>
                  </div>
                ))
              ) : (
                <div className="text-sm text-gray-500">No active campaigns</div>
              )}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {stats?.recent_activity && stats.recent_activity.length > 0 ? (
                stats.recent_activity.map((activity, index) => (
                  <div key={`activity-${index}-${activity.timestamp}`} className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'click' ? 'bg-green-500' :
                      activity.type === 'impression' ? 'bg-blue-500' :
                      activity.type === 'conversion' ? 'bg-purple-500' :
                      activity.type === 'fraud' ? 'bg-red-500' :
                      'bg-gray-500'
                    }`}></div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-900">{activity.message}</div>
                      <div className="text-xs text-gray-500">{new Date(activity.timestamp).toLocaleTimeString()}</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-sm text-gray-500">No recent activity</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
