'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface EmailTemplate {
  id: number;
  name: string;
  subject: string;
  body: string;
  type: string;
  variables: string[];
  event: string;
  created_at: string;
  updated_at: string;
}

export default function AdminEmailTemplates() {
  const { data: session } = useSession();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/admin/email-templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data);
      }
    } catch (error) {
      console.error('Failed to fetch email templates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setShowEditModal(true);
  };

  const handleCreate = () => {
    setSelectedTemplate({
      id: 0,
      name: '',
      subject: '',
      body: '',
      type: 'email_verification',
      variables: [],
      event: '',
      created_at: '',
      updated_at: '',
    });
    setShowEditModal(true);
  };

  const handleDelete = async (templateId: number) => {
    if (!confirm('Are you sure you want to delete this email template?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/email-templates/${templateId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchTemplates(); // Refresh the list
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      alert('An error occurred while deleting the template');
    }
  };

  if (!session || session.user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const templateTypes = [
    { value: 'email_verification', label: 'Email Verification' },
    { value: 'welcome_advertiser', label: 'Welcome - Advertiser' },
    { value: 'welcome_publisher', label: 'Welcome - Publisher' },
    { value: 'account_created', label: 'DSP/SSP Account Created' },
    { value: 'campaign_approved', label: 'Campaign Approved' },
    { value: 'campaign_rejected', label: 'Campaign Rejected' },
    { value: 'website_approved', label: 'Website Approved' },
    { value: 'website_rejected', label: 'Website Rejected' },
    { value: 'funds_deposited', label: 'Funds Deposited' },
    { value: 'payout_processed', label: 'Payout Processed' },
    { value: 'funds_withdrawal', label: 'Funds Withdrawal' },
    { value: 'password_reset', label: 'Password Reset' },
    { value: 'payment_reminder', label: 'Payment Reminder' },
    { value: 'support_ticket_created', label: 'Support Ticket Created' },
    { value: 'support_ticket_replied', label: 'Support Ticket Replied' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Email Templates</h1>
            <p className="mt-2 text-gray-600">Manage email templates for platform notifications</p>
          </div>
          <button
            onClick={handleCreate}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Template
          </button>
        </div>

        {/* Templates List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Email Templates</h2>
          </div>
          <div className="overflow-x-auto">
            {templates.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No email templates</h3>
                <p className="mt-1 text-sm text-gray-500">Create your first email template to get started.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {templates.map((template) => (
                    <tr key={template.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{template.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full capitalize">
                          {template.type.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          {template.event || 'No event'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(template.updated_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleEdit(template)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(template.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Templates Statistics */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Template Statistics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 border">
              <h4 className="font-medium text-gray-900">Total Templates</h4>
              <p className="text-2xl font-bold text-blue-600 mt-1">{templates.length}</p>
            </div>
            <div className="bg-white rounded-lg p-4 border">
              <h4 className="font-medium text-gray-900">Template Types</h4>
              <p className="text-2xl font-bold text-green-600 mt-1">{templateTypes.length}</p>
            </div>
            <div className="bg-white rounded-lg p-4 border">
              <h4 className="font-medium text-gray-900">Last Updated</h4>
              <p className="text-sm text-gray-600 mt-1">
                {templates.length > 0
                  ? new Date(Math.max(...templates.map(t => new Date(t.updated_at).getTime()))).toLocaleDateString()
                  : 'No templates'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Edit/Create Modal */}
        {showEditModal && selectedTemplate && (
          <EmailTemplateModal
            template={selectedTemplate}
            templateTypes={templateTypes}
            onClose={() => {
              setShowEditModal(false);
              setSelectedTemplate(null);
            }}
            onSuccess={() => {
              setShowEditModal(false);
              setSelectedTemplate(null);
              fetchTemplates();
            }}
          />
        )}
      </div>
    </div>
  );
}

function EmailTemplateModal({
  template,
  templateTypes,
  onClose,
  onSuccess
}: {
  template: EmailTemplate;
  templateTypes: { value: string; label: string }[];
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [formData, setFormData] = useState({
    name: template.name,
    type: template.type,
    subject: template.subject,
    body: template.body,
    event: template.event,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const url = template.id ? `/api/admin/email-templates/${template.id}` : '/api/admin/email-templates';
      const method = template.id ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        onSuccess();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to save template');
      }
    } catch (error) {
      console.error('Failed to save template:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const platformEvents = [
    { value: 'user_email_verification', label: 'User Email Verification' },
    { value: 'user_welcome_advertiser', label: 'User Welcome (Advertiser)' },
    { value: 'user_welcome_publisher', label: 'User Welcome (Publisher)' },
    { value: 'admin_user_created', label: 'Admin Creates DSP/SSP User' },
    { value: 'campaign_approved', label: 'Campaign Approved by Admin' },
    { value: 'campaign_rejected', label: 'Campaign Rejected by Admin' },
    { value: 'website_approved', label: 'Website Approved by Admin' },
    { value: 'website_rejected', label: 'Website Rejected by Admin' },
    { value: 'funds_deposited', label: 'Funds Deposited to Account' },
    { value: 'payout_processed', label: 'Payout Processed' },
    { value: 'funds_withdrawal', label: 'Funds Withdrawal Processed' },
    { value: 'password_reset_requested', label: 'Password Reset Requested' },
    { value: 'payment_reminder', label: 'Payment Reminder Sent' },
    { value: 'support_ticket_created', label: 'Support Ticket Created' },
    { value: 'support_ticket_replied', label: 'Support Ticket Replied' },
    { value: 'account_suspended', label: 'Account Suspended' },
    { value: 'account_reactivated', label: 'Account Reactivated' },
    { value: 'campaign_budget_depleted', label: 'Campaign Budget Depleted' },
    { value: 'payout_threshold_reached', label: 'Payout Threshold Reached' },
    { value: 'monthly_report', label: 'Monthly Performance Report' },
  ];

  const availableVariables = {
    email_verification: ['{{user_name}}', '{{user_email}}', '{{platform_name}}', '{{verification_url}}'],
    welcome_advertiser: ['{{user_name}}', '{{user_email}}', '{{platform_name}}', '{{current_date}}', '{{dashboard_url}}'],
    welcome_publisher: ['{{user_name}}', '{{user_email}}', '{{platform_name}}', '{{current_date}}', '{{dashboard_url}}'],
    account_created: ['{{user_name}}', '{{user_email}}', '{{user_role}}', '{{password}}', '{{login_url}}', '{{platform_name}}'],
    campaign_approved: ['{{user_name}}', '{{campaign_name}}', '{{platform_name}}'],
    campaign_rejected: ['{{user_name}}', '{{campaign_name}}', '{{reason}}', '{{platform_name}}'],
    website_approved: ['{{user_name}}', '{{website_name}}', '{{platform_name}}'],
    website_rejected: ['{{user_name}}', '{{website_name}}', '{{reason}}', '{{platform_name}}'],
    funds_deposited: ['{{user_name}}', '{{amount}}', '{{account_balance}}', '{{platform_name}}'],
    payout_processed: ['{{user_name}}', '{{amount}}', '{{platform_name}}'],
    funds_withdrawal: ['{{user_name}}', '{{amount}}', '{{platform_name}}'],
    password_reset: ['{{user_name}}', '{{reset_link}}', '{{platform_name}}'],
    payment_reminder: ['{{user_name}}', '{{user_email}}', '{{account_balance}}', '{{platform_name}}'],
    support_ticket_created: ['{{user_name}}', '{{ticket_id}}', '{{subject}}', '{{platform_name}}'],
    support_ticket_replied: ['{{user_name}}', '{{ticket_id}}', '{{reply_message}}', '{{platform_name}}'],
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {template.id ? 'Edit Email Template' : 'Create Email Template'}
          </h3>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Template Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Template Type</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  disabled={template.id !== 0}
                  required
                >
                  {templateTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {template.id !== 0 && (
                  <p className="text-xs text-gray-500 mt-1">
                    Template type cannot be changed after creation
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Platform Event</label>
                <select
                  value={formData.event}
                  onChange={(e) => setFormData(prev => ({ ...prev, event: e.target.value }))}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select an event (optional)</option>
                  {platformEvents.map((event) => (
                    <option key={event.value} value={event.value}>
                      {event.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Subject</label>
              <input
                type="text"
                value={formData.subject}
                onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Email subject line"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Email Body (HTML)</label>
              <textarea
                value={formData.body}
                onChange={(e) => setFormData(prev => ({ ...prev, body: e.target.value }))}
                rows={12}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                placeholder="HTML email template content..."
                required
              />
            </div>

            {/* Available Variables */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Available Variables for {formData.type}:</h4>
              <div className="flex flex-wrap gap-2">
                {(availableVariables[formData.type as keyof typeof availableVariables] || []).map((variable) => (
                  <span
                    key={variable}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded cursor-pointer hover:bg-blue-200"
                    onClick={() => {
                      const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
                      if (textarea) {
                        const start = textarea.selectionStart;
                        const end = textarea.selectionEnd;
                        const newValue = formData.body.substring(0, start) + variable + formData.body.substring(end);
                        setFormData(prev => ({ ...prev, body: newValue }));
                      }
                    }}
                  >
                    {variable}
                  </span>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Saving...' : (template.id ? 'Update Template' : 'Create Template')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
