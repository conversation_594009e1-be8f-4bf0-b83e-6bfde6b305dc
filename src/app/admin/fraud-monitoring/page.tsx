'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';

interface FraudLog {
  id: number;
  timestamp: string;
  ip_address: string;
  user_agent: string;
  source_type: string;
  publisher_id: number;
  website_id: number;
  zone_id: number;
  partner_id: number;
  fraud_reason: string;
  risk_score: number;
  country: string;
  device_type: string;
  is_excluded: boolean;
  excluded_by: number;
  excluded_at: string;
}

interface FraudStats {
  source_type: string;
  fraud_reason: string;
  total_blocked: number;
  avg_risk_score: number;
  unique_ips: number;
  affected_publishers: number;
}

export default function FraudMonitoringPage() {
  const { data: session, status } = useSession();
  const [fraudLogs, setFraudLogs] = useState<FraudLog[]>([]);
  const [fraudStats, setFraudStats] = useState<FraudStats[]>([]);
  const [topReasons, setTopReasons] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedSourceType, setSelectedSourceType] = useState('');
  const [selectedPublisherId, setSelectedPublisherId] = useState('');

  // Exclusion management state
  const [showExclusionModal, setShowExclusionModal] = useState(false);
  const [allPublishers, setAllPublishers] = useState<any[]>([]);
  const [excludedPublishers, setExcludedPublishers] = useState<any[]>([]);
  const [selectedPublishers, setSelectedPublishers] = useState<number[]>([]);
  const [publisherSearch, setPublisherSearch] = useState('');

  const fetchFraudData = async () => {
    try {
      setLoading(true);

      // Fetch fraud logs
      const logsParams = new URLSearchParams({
        action: 'logs',
        limit: '50',
        offset: '0',
      });

      if (selectedSourceType) logsParams.append('source_type', selectedSourceType);
      if (selectedPublisherId) logsParams.append('publisher_id', selectedPublisherId);

      const logsResponse = await fetch(`/api/admin/fraud-monitoring?${logsParams}`);
      const logsData = await logsResponse.json();
      setFraudLogs(logsData.logs || []);

      // Fetch fraud statistics
      const statsResponse = await fetch('/api/admin/fraud-monitoring?action=statistics&days=7');
      const statsData = await statsResponse.json();
      setFraudStats(statsData.statistics || []);

      // Fetch top fraud reasons
      const reasonsResponse = await fetch('/api/admin/fraud-monitoring?action=top-reasons&days=7');
      const reasonsData = await reasonsResponse.json();
      setTopReasons(reasonsData.topReasons || []);

    } catch (error) {
      console.error('Error fetching fraud data:', error);
    } finally {
      setLoading(false);
    }
  };

  const excludeFraudDetection = async (fraudId: number, reason: string = 'Manual exclusion') => {
    try {
      const response = await fetch('/api/admin/fraud-monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'exclude',
          fraudId,
          reason,
        }),
      });

      if (response.ok) {
        alert('Fraud detection excluded successfully');
        fetchFraudData(); // Refresh data
      } else {
        alert('Failed to exclude fraud detection');
      }
    } catch (error) {
      console.error('Error excluding fraud detection:', error);
      alert('Error excluding fraud detection');
    }
  };

  const fetchExclusionData = async () => {
    try {
      const response = await fetch('/api/admin/fraud-exclusions');
      if (response.ok) {
        const data = await response.json();
        setAllPublishers(data.allPublishers || []);
        setExcludedPublishers(data.excludedPublishers || []);
      }
    } catch (error) {
      console.error('Error fetching exclusion data:', error);
    }
  };

  const addExclusions = async () => {
    if (selectedPublishers.length === 0) return;

    try {
      const response = await fetch('/api/admin/fraud-exclusions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'add',
          publisherIds: selectedPublishers,
        }),
      });

      if (response.ok) {
        alert(`Successfully excluded ${selectedPublishers.length} publisher(s) from fraud detection`);
        setSelectedPublishers([]);
        fetchExclusionData(); // Refresh data
      } else {
        alert('Failed to add exclusions');
      }
    } catch (error) {
      console.error('Error adding exclusions:', error);
      alert('Error adding exclusions');
    }
  };

  const removeExclusion = async (publisherId: number) => {
    try {
      const response = await fetch('/api/admin/fraud-exclusions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'remove',
          publisherId,
        }),
      });

      if (response.ok) {
        alert('Publisher removed from fraud detection exclusions');
        fetchExclusionData(); // Refresh data
      } else {
        alert('Failed to remove exclusion');
      }
    } catch (error) {
      console.error('Error removing exclusion:', error);
      alert('Error removing exclusion');
    }
  };

  // All useEffect hooks must be declared before any conditional returns
  useEffect(() => {
    fetchFraudData();
  }, [selectedSourceType, selectedPublisherId]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchFraudData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedSourceType, selectedPublisherId]);

  // Conditional logic after all hooks
  if (status === 'loading') return <div>Loading...</div>;
  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin');
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 70) return 'text-red-600 bg-red-100';
    if (score >= 50) return 'text-orange-600 bg-orange-100';
    return 'text-yellow-600 bg-yellow-100';
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Fraud Monitoring</h1>
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            Auto Refresh (30s)
          </label>
          <button
            onClick={() => {
              setShowExclusionModal(true);
              fetchExclusionData();
            }}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Manage Exclusions
          </button>
          <button
            onClick={fetchFraudData}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Refresh Now
          </button>
        </div>
      </div>

      {/* Exclusion Management Modal */}
      {showExclusionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Manage Fraud Detection Exclusions</h2>
              <button
                onClick={() => setShowExclusionModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Add New Exclusions */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Add New Exclusions</h3>

              {/* Search Input */}
              <div className="mb-3">
                <input
                  type="text"
                  placeholder="Search publishers by name or email..."
                  value={publisherSearch}
                  onChange={(e) => setPublisherSearch(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Publisher Selection */}
              <div className="mb-3">
                {/* Select All / Clear All */}
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    Available Publishers ({allPublishers.filter(publisher => !excludedPublishers.some(excluded => excluded.id === publisher.id)).length})
                  </span>
                  <div className="space-x-2">
                    <button
                      type="button"
                      onClick={() => {
                        const availableIds = allPublishers
                          .filter(publisher => !excludedPublishers.some(excluded => excluded.id === publisher.id))
                          .filter(publisher =>
                            publisherSearch === '' ||
                            publisher.full_name.toLowerCase().includes(publisherSearch.toLowerCase()) ||
                            publisher.email.toLowerCase().includes(publisherSearch.toLowerCase())
                          )
                          .map(publisher => publisher.id);
                        setSelectedPublishers(availableIds);
                      }}
                      className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      Select All
                    </button>
                    <button
                      type="button"
                      onClick={() => setSelectedPublishers([])}
                      className="text-xs text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
                <div className="h-64 overflow-y-auto border border-gray-300 rounded-md bg-white">
                  {allPublishers
                    .filter(publisher => !excludedPublishers.some(excluded => excluded.id === publisher.id))
                    .filter(publisher =>
                      publisherSearch === '' ||
                      publisher.full_name.toLowerCase().includes(publisherSearch.toLowerCase()) ||
                      publisher.email.toLowerCase().includes(publisherSearch.toLowerCase())
                    )
                    .map((publisher) => (
                      <label key={publisher.id} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0">
                        <input
                          type="checkbox"
                          checked={selectedPublishers.includes(publisher.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedPublishers([...selectedPublishers, publisher.id]);
                            } else {
                              setSelectedPublishers(selectedPublishers.filter(id => id !== publisher.id));
                            }
                          }}
                          className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 truncate">{publisher.full_name}</div>
                          <div className="text-xs text-gray-500 truncate">ID: {publisher.id} • {publisher.email}</div>
                        </div>
                      </label>
                    ))}
                  {allPublishers.filter(publisher =>
                    !excludedPublishers.some(excluded => excluded.id === publisher.id) &&
                    (publisherSearch === '' ||
                     publisher.full_name.toLowerCase().includes(publisherSearch.toLowerCase()) ||
                     publisher.email.toLowerCase().includes(publisherSearch.toLowerCase()))
                  ).length === 0 && (
                    <div className="p-4 text-center text-gray-500">
                      <div className="text-sm">
                        {publisherSearch ? 'No publishers found matching your search.' : 'All publishers are already excluded.'}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Selected Count and Action */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  {selectedPublishers.length} publisher{selectedPublishers.length !== 1 ? 's' : ''} selected
                </div>
                <button
                  onClick={addExclusions}
                  disabled={selectedPublishers.length === 0}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Exclude Selected ({selectedPublishers.length})
                </button>
              </div>

              <p className="mt-2 text-sm text-gray-600">
                Select publishers to exclude from all fraud detection (their traffic will not be monitored for fraud).
              </p>
            </div>

            {/* Current Exclusions */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Current Exclusions ({excludedPublishers.length})
              </h3>
              {excludedPublishers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <p>No publishers are currently excluded from fraud detection.</p>
                  <p className="text-sm">Add a publisher above to exclude them from fraud monitoring.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {excludedPublishers.map((publisher) => (
                    <div key={publisher.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900">{publisher.full_name}</div>
                        <div className="text-sm text-gray-500">ID: {publisher.id} • {publisher.email}</div>
                      </div>
                      <button
                        onClick={() => removeExclusion(publisher.id)}
                        className="text-red-600 hover:text-red-800 transition-colors"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowExclusionModal(false)}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Fraud Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2">Total Blocked (7 days)</h3>
          <p className="text-3xl font-bold text-red-600">
            {fraudStats.reduce((sum, stat) => sum + stat.total_blocked, 0)}
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2">Unique IPs Blocked</h3>
          <p className="text-3xl font-bold text-orange-600">
            {fraudStats.reduce((sum, stat) => sum + stat.unique_ips, 0)}
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-2">Affected Publishers</h3>
          <p className="text-3xl font-bold text-yellow-600">
            {fraudStats.reduce((sum, stat) => sum + stat.affected_publishers, 0)}
          </p>
        </div>
      </div>

      {/* Top Fraud Reasons */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h3 className="text-lg font-semibold mb-4">Top Fraud Reasons (7 days)</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {topReasons.map((reason, index) => (
            <div key={index} className="border rounded p-4">
              <h4 className="font-medium text-gray-900">{reason.fraud_reason}</h4>
              <p className="text-sm text-gray-600">Blocked: {reason.total_blocked}</p>
              <p className="text-sm text-gray-600">Avg Risk: {Math.round(reason.avg_risk_score)}</p>
              <p className="text-sm text-gray-600">Unique IPs: {reason.unique_ips}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <h3 className="text-lg font-semibold mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Source Type</label>
            <select
              value={selectedSourceType}
              onChange={(e) => setSelectedSourceType(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">All Sources</option>
              <option value="publisher_direct">Publisher Direct</option>
              <option value="dsp_inbound">DSP Inbound</option>
              <option value="ssp_inbound">SSP Inbound</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Publisher ID</label>
            <input
              type="number"
              value={selectedPublisherId}
              onChange={(e) => setSelectedPublisherId(e.target.value)}
              placeholder="Enter Publisher ID"
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
        </div>
      </div>

      {/* Live Fraud Logs */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Live Fraud Detection Logs</h3>
          <p className="text-sm text-gray-600">Real-time fraud detection events</p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Publisher/Zone
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fraud Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Risk Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    Loading fraud logs...
                  </td>
                </tr>
              ) : fraudLogs.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    No fraud logs found
                  </td>
                </tr>
              ) : (
                fraudLogs.map((log) => (
                  <tr key={log.id} className={log.is_excluded ? 'bg-green-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTimestamp(log.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                      {log.ip_address}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {log.source_type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.publisher_id > 0 && (
                        <div>
                          <div>Publisher: {log.publisher_id}</div>
                          {log.website_id > 0 && <div>Website: {log.website_id}</div>}
                          {log.zone_id > 0 && <div>Zone: {log.zone_id}</div>}
                        </div>
                      )}
                      {log.partner_id > 0 && <div>Partner: {log.partner_id}</div>}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {log.fraud_reason}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskScoreColor(log.risk_score)}`}>
                        {log.risk_score}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.is_excluded ? (
                        <span className="text-green-600 font-medium">Excluded</span>
                      ) : (
                        <button
                          onClick={() => excludeFraudDetection(log.id)}
                          className="bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600"
                        >
                          Exclude
                        </button>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
