'use client';

import React, { useState, useEffect } from 'react';
import {
  RocketLaunchIcon,
  ChartBarIcon,
  CloudIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

interface OptimizationStatus {
  health: {
    cache: boolean;
    connections: boolean;
    overall: boolean;
  };
  metrics: {
    cache: {
      memoryEntries: number;
      memoryUsagePercent: number;
      memoryMaxEntries: number;
    };
    connections: {
      https: { sockets: number; freeSockets: number; requests: number };
      http: { sockets: number; freeSockets: number; requests: number };
    };
    uptime: number;
  };
  performanceScore: number;
  recommendations: string[];
  timestamp: string;
}

export default function OptimizationPage() {
  const [status, setStatus] = useState<OptimizationStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/admin/optimization-status');
      const result = await response.json();
      if (result.success) {
        setStatus(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch optimization status:', error);
    } finally {
      setLoading(false);
    }
  };

  const executeAction = async (action: string) => {
    setActionLoading(true);
    try {
      const response = await fetch('/api/admin/optimization-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });
      
      const result = await response.json();
      if (result.success) {
        alert(result.message);
        await fetchStatus();
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to execute action:', error);
      alert('Failed to execute action');
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl">Loading optimization status...</div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl text-red-600">Failed to load optimization status</div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <RocketLaunchIcon className="w-8 h-8 mr-3 text-blue-600" />
            RTB Platform Optimization
          </h1>
          <p className="mt-2 text-gray-600">Monitor and manage high-performance optimizations</p>
        </div>

        {/* Performance Score */}
        <div className={`bg-white rounded-lg shadow p-6 mb-8 ${getScoreBg(status.performanceScore)}`}>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Performance Score</h2>
              <p className="text-gray-600">Overall optimization effectiveness</p>
            </div>
            <div className={`text-6xl font-bold ${getScoreColor(status.performanceScore)}`}>
              {status.performanceScore}%
            </div>
          </div>
        </div>

        {/* Health Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              {status.health.overall ? (
                <CheckCircleIcon className="w-8 h-8 text-green-500 mr-3" />
              ) : (
                <ExclamationTriangleIcon className="w-8 h-8 text-red-500 mr-3" />
              )}
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Overall Health</h3>
                <p className={`text-sm ${status.health.overall ? 'text-green-600' : 'text-red-600'}`}>
                  {status.health.overall ? 'Healthy' : 'Issues Detected'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <ChartBarIcon className={`w-8 h-8 mr-3 ${status.health.cache ? 'text-green-500' : 'text-red-500'}`} />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Cache System</h3>
                <p className={`text-sm ${status.health.cache ? 'text-green-600' : 'text-red-600'}`}>
                  {status.health.cache ? 'Operational' : 'Issues'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <CloudIcon className={`w-8 h-8 mr-3 ${status.health.connections ? 'text-green-500' : 'text-red-500'}`} />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Connections</h3>
                <p className={`text-sm ${status.health.connections ? 'text-green-600' : 'text-red-600'}`}>
                  {status.health.connections ? 'Active' : 'Issues'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Cache Metrics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Cache Metrics</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Memory Entries:</span>
                <span className="font-semibold">{status.metrics.cache.memoryEntries.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Memory Usage:</span>
                <span className="font-semibold">{status.metrics.cache.memoryUsagePercent.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Max Entries:</span>
                <span className="font-semibold">{status.metrics.cache.memoryMaxEntries.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Connection Metrics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Connection Pool</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">HTTPS Active:</span>
                <span className="font-semibold">{status.metrics.connections.https.sockets}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">HTTPS Free:</span>
                <span className="font-semibold">{status.metrics.connections.https.freeSockets}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">HTTP Active:</span>
                <span className="font-semibold">{status.metrics.connections.http.sockets}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">HTTP Free:</span>
                <span className="font-semibold">{status.metrics.connections.http.freeSockets}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommendations</h3>
          <ul className="space-y-2">
            {status.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <span className="text-gray-700">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
          <div className="flex space-x-4">
            <button
              onClick={() => executeAction('initialize')}
              disabled={actionLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <RocketLaunchIcon className="w-4 h-4 mr-2" />
              {actionLoading ? 'Processing...' : 'Initialize Optimization'}
            </button>
            
            <button
              onClick={fetchStatus}
              disabled={loading}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 flex items-center"
            >
              <ArrowPathIcon className="w-4 h-4 mr-2" />
              Refresh Status
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-gray-500 text-sm">
          Last updated: {new Date(status.timestamp).toLocaleString()}
        </div>
      </div>
    </div>
  );
}
