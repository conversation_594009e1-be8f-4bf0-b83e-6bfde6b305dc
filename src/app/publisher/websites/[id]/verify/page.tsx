'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useParams } from 'next/navigation';

interface Website {
  id: number;
  url: string;
  name: string;
  verification_status: string;
  verification_code: string;
  verification_method?: string;
  verified_at?: string;
}

export default function WebsiteVerification() {
  const { data: session } = useSession();
  const params = useParams();
  const websiteId = params.id as string;

  const [website, setWebsite] = useState<Website | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<'dns' | 'html' | 'meta'>('html');
  const [verificationResult, setVerificationResult] = useState<any>(null);

  useEffect(() => {
    if (session && websiteId) {
      fetchWebsite();
    }
  }, [session, websiteId]);

  const fetchWebsite = async () => {
    try {
      const response = await fetch(`/api/publisher/websites/${websiteId}`);
      if (response.ok) {
        const data = await response.json();
        setWebsite(data);
      }
    } catch (error) {
      console.error('Failed to fetch website:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async () => {
    if (!website) return;

    setIsVerifying(true);
    setVerificationResult(null);

    try {
      const response = await fetch(`/api/publisher/websites/${websiteId}/verify`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ method: selectedMethod }),
      });

      const result = await response.json();
      setVerificationResult(result);

      if (result.success) {
        // Refresh website data
        await fetchWebsite();
      }
    } catch (error) {
      setVerificationResult({
        success: false,
        details: 'Verification request failed',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  if (!session || session.user?.role !== 'publisher') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!website) {
    return <div>Website not found</div>;
  }

  const domain = new URL(website.url).hostname;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Website Verification</h1>
          <p className="mt-2 text-gray-600">Verify ownership of {website.name}</p>
        </div>

        {/* Website Info */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Website Information</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Website Name</label>
                <p className="mt-1 text-sm text-gray-900">{website.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Website URL</label>
                <p className="mt-1 text-sm text-gray-900">{website.url}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Domain</label>
                <p className="mt-1 text-sm text-gray-900">{domain}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                <span className={`mt-1 inline-flex px-2 py-1 text-xs rounded-full ${
                  website.verification_status === 'verified'
                    ? 'bg-green-100 text-green-800'
                    : website.verification_status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {website.verification_status}
                </span>
              </div>
            </div>
          </div>
        </div>

        {website.verification_status !== 'verified' && (
          <>
            {/* Verification Methods */}
            <div className="bg-white rounded-lg shadow mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Choose Verification Method</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      id="html"
                      name="verification-method"
                      type="radio"
                      value="html"
                      checked={selectedMethod === 'html'}
                      onChange={(e) => setSelectedMethod(e.target.value as 'html')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="html" className="ml-3 block text-sm font-medium text-gray-700">
                      HTML File Upload (Recommended)
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="meta"
                      name="verification-method"
                      type="radio"
                      value="meta"
                      checked={selectedMethod === 'meta'}
                      onChange={(e) => setSelectedMethod(e.target.value as 'meta')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="meta" className="ml-3 block text-sm font-medium text-gray-700">
                      HTML Meta Tag
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="dns"
                      name="verification-method"
                      type="radio"
                      value="dns"
                      checked={selectedMethod === 'dns'}
                      onChange={(e) => setSelectedMethod(e.target.value as 'dns')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label htmlFor="dns" className="ml-3 block text-sm font-medium text-gray-700">
                      DNS TXT Record
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Verification Instructions */}
            <div className="bg-white rounded-lg shadow mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Verification Instructions</h2>
              </div>
              <div className="p-6">
                {selectedMethod === 'html' && (
                  <div>
                    <h3 className="text-md font-medium text-gray-900 mb-3">HTML File Upload Method</h3>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                      <li>Create a new HTML file named: <code className="bg-gray-100 px-2 py-1 rounded">globaladsmedia-{website.verification_code}.html</code></li>
                      <li>Add the following content to the file:</li>
                    </ol>
                    <div className="mt-3 bg-gray-100 p-4 rounded-lg">
                      <code className="text-sm">
                        {`<!DOCTYPE html>
<html>
<head>
    <title>GlobalAdsMedia Verification</title>
</head>
<body>
    <h1>GlobalAdsMedia Website Verification</h1>
    <p>Verification token: ${website.verification_code}</p>
    <p>This file verifies ownership of ${domain} for GlobalAdsMedia.</p>
</body>
</html>`}
                      </code>
                    </div>
                    <ol start={3} className="list-decimal list-inside space-y-2 text-sm text-gray-700 mt-3">
                      <li>Upload this file to the root directory of your website</li>
                      <li>Make sure the file is accessible at: <code className="bg-gray-100 px-2 py-1 rounded">{website.url}/globaladsmedia-{website.verification_code}.html</code></li>
                      <li>Click the "Verify Website" button below</li>
                    </ol>
                  </div>
                )}

                {selectedMethod === 'meta' && (
                  <div>
                    <h3 className="text-md font-medium text-gray-900 mb-3">HTML Meta Tag Method</h3>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                      <li>Add the following meta tag to the &lt;head&gt; section of your website's homepage:</li>
                    </ol>
                    <div className="mt-3 bg-gray-100 p-4 rounded-lg">
                      <code className="text-sm">
                        {`<meta name="globaladsmedia-verification" content="${website.verification_code}" />`}
                      </code>
                    </div>
                    <ol start={2} className="list-decimal list-inside space-y-2 text-sm text-gray-700 mt-3">
                      <li>Save and publish your changes</li>
                      <li>Make sure the meta tag is visible in the page source of {website.url}</li>
                      <li>Click the "Verify Website" button below</li>
                    </ol>
                  </div>
                )}

                {selectedMethod === 'dns' && (
                  <div>
                    <h3 className="text-md font-medium text-gray-900 mb-3">DNS TXT Record Method</h3>
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                      <li>Log in to your domain registrar or DNS provider</li>
                      <li>Add a new TXT record with the following details:</li>
                    </ol>
                    <div className="mt-3 bg-gray-100 p-4 rounded-lg">
                      <div className="text-sm">
                        <p><strong>Type:</strong> TXT</p>
                        <p><strong>Name/Host:</strong> @ (or leave blank for root domain)</p>
                        <p><strong>Value:</strong> <code>globaladsmedia-verification={website.verification_code}</code></p>
                        <p><strong>TTL:</strong> 300 (or default)</p>
                      </div>
                    </div>
                    <ol start={3} className="list-decimal list-inside space-y-2 text-sm text-gray-700 mt-3">
                      <li>Save the DNS record</li>
                      <li>Wait for DNS propagation (may take up to 24 hours)</li>
                      <li>Click the "Verify Website" button below</li>
                    </ol>
                  </div>
                )}
              </div>
            </div>

            {/* Verification Button */}
            <div className="bg-white rounded-lg shadow mb-6">
              <div className="p-6">
                <button
                  onClick={handleVerification}
                  disabled={isVerifying}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isVerifying ? 'Verifying...' : 'Verify Website'}
                </button>
              </div>
            </div>
          </>
        )}

        {/* Verification Result */}
        {verificationResult && (
          <div className={`rounded-lg p-6 mb-6 ${
            verificationResult.success
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center">
              {verificationResult.success ? (
                <svg className="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
              <div>
                <h3 className={`text-lg font-medium ${
                  verificationResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {verificationResult.success ? 'Verification Successful!' : 'Verification Failed'}
                </h3>
                <p className={`text-sm ${
                  verificationResult.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {verificationResult.details}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Already Verified */}
        {website.verification_status === 'verified' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <div>
                <h3 className="text-lg font-medium text-green-800">Website Verified</h3>
                <p className="text-sm text-green-700">
                  This website has been successfully verified using the {website.verification_method} method
                  {website.verified_at && ` on ${new Date(website.verified_at).toLocaleDateString()}`}.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
