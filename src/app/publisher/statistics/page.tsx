'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Bar } from 'recharts';
import { formatNumber, formatCurrency } from '@/lib/format-utils';

interface Website {
  id: number;
  name: string;
}

interface StatData {
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  earnings: number;
  ctr: number;
  rpm: number;
  website_id?: number;
  website_name?: string;
  zone_name?: string;
  country?: string;
  os?: string;
  browser?: string;
  device?: string;
  name?: string;
  id?: string;
}

interface FraudStat {
  reason: string;
  total_blocked: number;
  avg_risk_score: number;
  unique_ips: number;
  daily_data: Array<{
    date: string;
    blocked: number;
    risk_score: number;
    unique_ips: number;
  }>;
}

export default function PublisherStatistics() {
  const { data: session } = useSession();
  const [websites, setWebsites] = useState<Website[]>([]);
  const [stats, setStats] = useState<StatData[]>([]);
  const [fraudStats, setFraudStats] = useState<FraudStat[]>([]);
  const [totalBlocked, setTotalBlocked] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showFraudStats, setShowFraudStats] = useState(false);

  const [filters, setFilters] = useState({
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    dateTo: new Date().toISOString().split('T')[0],
    websiteId: '',
    groupBy: 'day',
  });

  useEffect(() => {
    fetchWebsites();
  }, []);

  useEffect(() => {
    fetchStatistics();
    if (showFraudStats) {
      fetchFraudStatistics();
    }
  }, [filters, showFraudStats]);

  const fetchWebsites = async () => {
    try {
      const response = await fetch('/api/websites');
      if (response.ok) {
        const data = await response.json();
        setWebsites(data);
      }
    } catch (error) {
      console.error('Failed to fetch websites:', error);
    }
  };

  const fetchStatistics = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        date_from: filters.dateFrom,
        date_to: filters.dateTo,
        group_by: filters.groupBy,
        ...(filters.websiteId && { website_id: filters.websiteId }),
      });

      const response = await fetch(`/api/statistics/detailed?${params}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFraudStatistics = async () => {
    try {
      const days = Math.ceil((new Date(filters.dateTo).getTime() - new Date(filters.dateFrom).getTime()) / (1000 * 60 * 60 * 24));
      const response = await fetch(`/api/publisher/fraud-stats?days=${days}`);
      if (response.ok) {
        const data = await response.json();
        setFraudStats(data.fraudStats || []);
        setTotalBlocked(data.totalBlocked || 0);
      }
    } catch (error) {
      console.error('Failed to fetch fraud statistics:', error);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const refreshData = () => {
    fetchStatistics();
    if (showFraudStats) {
      fetchFraudStatistics();
    }
  };

  const exportCSV = async () => {
    try {
      const params = new URLSearchParams({
        date_from: filters.dateFrom,
        date_to: filters.dateTo,
        group_by: filters.groupBy,
        ...(filters.websiteId && { website_id: filters.websiteId }),
        format: 'csv',
      });

      const response = await fetch(`/api/statistics/export?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `publisher-stats-${filters.dateFrom}-${filters.dateTo}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export CSV:', error);
    }
  };

  if (!session || session.user?.role !== 'publisher') {
    return <div>Access denied</div>;
  }

  const totals = stats.reduce((acc, stat) => ({
    impressions: acc.impressions + stat.impressions,
    clicks: acc.clicks + stat.clicks,
    conversions: acc.conversions + stat.conversions,
    earnings: acc.earnings + stat.earnings,
  }), { impressions: 0, clicks: 0, conversions: 0, earnings: 0 });

  const avgCTR = totals.impressions > 0 ? ((totals.clicks / totals.impressions) * 100).toFixed(2) : '0.00';
  const avgRPM = totals.impressions > 0 ? ((totals.earnings / totals.impressions) * 1000).toFixed(2) : '0.00';

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Statistics</h1>
            <p className="mt-2 text-gray-600">Track your website performance and earnings</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={refreshData}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
            <button
              onClick={exportCSV}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Export CSV
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Filters</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                <select
                  value={filters.websiteId}
                  onChange={(e) => handleFilterChange('websiteId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Websites</option>
                  {websites.map((website) => (
                    <option key={website.id} value={website.id}>
                      {website.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Group By</label>
                <select
                  value={filters.groupBy}
                  onChange={(e) => handleFilterChange('groupBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="day">Day</option>
                  <option value="website">Website</option>
                  <option value="adzones">Ad Zones</option>
                  <option value="country">Country</option>
                  <option value="os">Operating System</option>
                  <option value="device">Device Type</option>
                  <option value="browser">Browser</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Impressions</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.impressions)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Clicks</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.clicks)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Conversions</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.conversions)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Earnings</div>
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(totals.earnings)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Avg RPM</div>
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(parseFloat(avgRPM))}</div>
          </div>
        </div>

        {/* Fraud Statistics Section */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <div>
              <h2 className="text-lg font-medium text-gray-900">Fraud Detection Statistics</h2>
              <p className="text-sm text-gray-600">Monitor blocked fraudulent traffic on your websites</p>
            </div>
            <button
              onClick={() => {
                setShowFraudStats(!showFraudStats);
                if (!showFraudStats) {
                  fetchFraudStatistics();
                }
              }}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              {showFraudStats ? 'Hide' : 'Show'} Fraud Stats
            </button>
          </div>

          {showFraudStats && (
            <div className="p-6">
              {fraudStats.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="mx-auto h-12 w-12 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No Fraud Detected</h3>
                  <p className="mt-1 text-sm text-gray-500">Great! No fraudulent traffic has been detected on your websites during this period.</p>
                </div>
              ) : (
                <>
                  {/* Fraud Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-red-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-red-600">Total Blocked</div>
                      <div className="text-2xl font-bold text-red-900">{formatNumber(totalBlocked)}</div>
                    </div>
                    <div className="bg-orange-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-orange-600">Fraud Types</div>
                      <div className="text-2xl font-bold text-orange-900">{fraudStats.length}</div>
                    </div>
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-yellow-600">Unique IPs</div>
                      <div className="text-2xl font-bold text-yellow-900">
                        {fraudStats.reduce((sum, stat) => sum + stat.unique_ips, 0)}
                      </div>
                    </div>
                  </div>

                  {/* Fraud Reasons Table */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Fraud Reason
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Blocked
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Avg Risk Score
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Unique IPs
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {fraudStats.map((stat, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {stat.reason}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatNumber(stat.total_blocked)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                stat.avg_risk_score >= 70 ? 'bg-red-100 text-red-800' :
                                stat.avg_risk_score >= 50 ? 'bg-orange-100 text-orange-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {Math.round(stat.avg_risk_score)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatNumber(stat.unique_ips)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        {/* Charts */}
        {filters.groupBy === 'day' && stats.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="impressions" stroke="#3B82F6" name="Impressions" />
                  <Line type="monotone" dataKey="clicks" stroke="#10B981" name="Clicks" />
                  <Line type="monotone" dataKey="conversions" stroke="#8B5CF6" name="Conversions" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Earnings & RPM Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="earnings" fill="#10B981" name="Earnings ($)" />
                  <Line yAxisId="right" type="monotone" dataKey="rpm" stroke="#F59E0B" name="RPM ($)" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {filters.groupBy === 'website' && stats.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Website Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="website_name" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="impressions" fill="#3B82F6" name="Impressions" />
                <Bar dataKey="clicks" fill="#10B981" name="Clicks" />
                <Bar dataKey="earnings" fill="#F59E0B" name="Earnings ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Statistics Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Detailed Statistics</h2>
          </div>
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Loading statistics...</p>
              </div>
            ) : stats.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                <p className="mt-1 text-sm text-gray-500">Statistics will appear here once you have active websites.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {filters.groupBy === 'day' ? 'Date' :
                       filters.groupBy === 'website' ? 'Website' :
                       filters.groupBy === 'adzones' ? 'Ad Zone' :
                       filters.groupBy === 'country' ? 'Country' :
                       filters.groupBy === 'os' ? 'Operating System' :
                       filters.groupBy === 'device' ? 'Device Type' :
                       filters.groupBy === 'browser' ? 'Browser' : 'Item'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impressions</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversions</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">RPM</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Earnings</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {stats.map((stat, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {filters.groupBy === 'day' ? stat.date :
                         filters.groupBy === 'website' ? stat.website_name :
                         filters.groupBy === 'adzones' ? stat.zone_name :
                         filters.groupBy === 'country' ? (stat.country || 'Unknown') :
                         filters.groupBy === 'os' ? (stat.os || 'Unknown') :
                         filters.groupBy === 'device' ? (stat.device || 'Unknown') :
                         filters.groupBy === 'browser' ? (stat.browser || 'Unknown') :
                         stat.name || stat.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.impressions)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.clicks)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.conversions)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.ctr.toFixed(2)}%</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.rpm)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.earnings)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
