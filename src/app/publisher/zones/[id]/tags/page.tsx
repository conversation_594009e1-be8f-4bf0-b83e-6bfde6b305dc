'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';

interface AdZone {
  id: number;
  name: string;
  ad_format: string;
  size: string;
  website_name: string;
  website_url: string;
}

export default function AdTagsPage() {
  const params = useParams();
  const { data: session } = useSession();
  const [zone, setZone] = useState<AdZone | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [copied, setCopied] = useState<string | null>(null);

  useEffect(() => {
    fetchZone();
  }, [params.id]);

  const fetchZone = async () => {
    try {
      const response = await fetch(`/api/zones/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setZone(data);
      }
    } catch (error) {
      console.error('Failed to fetch zone:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(type);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  if (!session || session.user?.role !== 'publisher') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
    </div>;
  }

  if (!zone) {
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Zone not found</h2>
        <p className="text-gray-600">The requested ad zone could not be found.</p>
      </div>
    </div>;
  }

  const generateJavaScriptTag = () => {
    const baseUrl = process.env.NEXT_PUBLIC_PLATFORM_URL || 'http://localhost:3000';
    return `<script type="text/javascript">
  (function() {
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = '${baseUrl}/js/ad.js';
    script.setAttribute('data-zone-id', '${zone.id}');
    script.setAttribute('data-format', '${zone.ad_format}');
    ${zone.size ? `script.setAttribute('data-size', '${zone.size}');` : ''}
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(script, s);
  })();
</script>`;
  };

  const generateIframeTag = () => {
    const baseUrl = process.env.NEXT_PUBLIC_PLATFORM_URL || 'http://localhost:3000';
    const [width, height] = zone.size ? zone.size.split('x') : ['300', '250'];
    return `<iframe src="${baseUrl}/api/serve?zone_id=${zone.id}&format=${zone.ad_format}&size=${zone.size}" 
        width="${width}" 
        height="${height}" 
        frameborder="0" 
        scrolling="no">
</iframe>`;
  };

  const generateDirectLink = () => {
    const baseUrl = process.env.NEXT_PUBLIC_PLATFORM_URL || 'http://localhost:3000';
    return `${baseUrl}/api/serve?zone_id=${zone.id}&format=${zone.ad_format}&size=${zone.size}`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Ad Tags</h1>
          <p className="mt-2 text-gray-600">
            Implementation codes for: <strong>{zone.name}</strong> on {zone.website_name}
          </p>
        </div>

        {/* Zone Info */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Zone Information</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Zone ID</label>
                <p className="mt-1 text-sm text-gray-900">{zone.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Format</label>
                <p className="mt-1 text-sm text-gray-900 capitalize">{zone.ad_format}</p>
              </div>
              {zone.size && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Size</label>
                  <p className="mt-1 text-sm text-gray-900">{zone.size}</p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700">Website</label>
                <p className="mt-1 text-sm text-gray-900">{zone.website_url}</p>
              </div>
            </div>
          </div>
        </div>

        {/* JavaScript Tag */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">JavaScript Tag (Recommended)</h2>
            <button
              onClick={() => copyToClipboard(generateJavaScriptTag(), 'js')}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
            >
              {copied === 'js' ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <div className="p-6">
            <p className="text-sm text-gray-600 mb-4">
              Paste this code where you want the ad to appear on your website.
            </p>
            <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm">
              <code>{generateJavaScriptTag()}</code>
            </pre>
          </div>
        </div>

        {/* Iframe Tag */}
        {zone.ad_format === 'banner' && (
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Iframe Tag</h2>
              <button
                onClick={() => copyToClipboard(generateIframeTag(), 'iframe')}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
              >
                {copied === 'iframe' ? 'Copied!' : 'Copy'}
              </button>
            </div>
            <div className="p-6">
              <p className="text-sm text-gray-600 mb-4">
                Alternative implementation using iframe (for banner ads only).
              </p>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm">
                <code>{generateIframeTag()}</code>
              </pre>
            </div>
          </div>
        )}

        {/* Direct Link */}
        {zone.ad_format === 'popup' && (
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Direct Link</h2>
              <button
                onClick={() => copyToClipboard(generateDirectLink(), 'direct')}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
              >
                {copied === 'direct' ? 'Copied!' : 'Copy'}
              </button>
            </div>
            <div className="p-6">
              <p className="text-sm text-gray-600 mb-4">
                Direct link for popup ads. Use this in your popup scripts.
              </p>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm">
                <code>{generateDirectLink()}</code>
              </pre>
            </div>
          </div>
        )}

        {/* Implementation Notes */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">Implementation Notes</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• JavaScript tags provide better performance and tracking</li>
            <li>• Make sure your website allows JavaScript execution</li>
            <li>• Test the implementation on a staging environment first</li>
            <li>• Contact support if you need help with implementation</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
