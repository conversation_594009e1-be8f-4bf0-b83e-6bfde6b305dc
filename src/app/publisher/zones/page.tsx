'use client';

import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';

interface AdZone {
  id: number;
  name: string;
  ad_format: string;
  size: string;
  config: string;
  status: string;
  created_at: string;
  website_name: string;
  website_url: string;
}

export default function PublisherZones() {
  const { data: session } = useSession();
  const [zones, setZones] = useState<AdZone[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCodeModal, setShowCodeModal] = useState(false);
  const [selectedZone, setSelectedZone] = useState<AdZone | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState({ name: '' });
  const [isDeleting, setIsDeleting] = useState<number | null>(null);

  useEffect(() => {
    if (session?.user?.role === 'publisher') {
      fetchZones();
    }
  }, [session]);

  const fetchZones = async () => {
    try {
      const response = await fetch('/api/zones');
      if (response.ok) {
        const data = await response.json();
        console.log('Fetched ad zones:', data);
        setZones(data);
      } else {
        console.error('Failed to fetch zones, status:', response.status);
      }
    } catch (error) {
      console.error('Failed to fetch zones:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGetCode = (zone: AdZone) => {
    setSelectedZone(zone);
    setShowCodeModal(true);
  };

  const handleEdit = (zone: AdZone) => {
    setSelectedZone(zone);
    setEditFormData({ name: zone.name });
    setShowEditModal(true);
  };

  const handleUpdateZone = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedZone) return;

    try {
      const response = await fetch(`/api/zones/${selectedZone.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editFormData.name }),
      });

      if (response.ok) {
        setShowEditModal(false);
        setSelectedZone(null);
        setEditFormData({ name: '' });
        fetchZones(); // Refresh the list
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to update ad zone');
      }
    } catch (error) {
      alert('An error occurred. Please try again.');
    }
  };

  const handleDelete = async (zone: AdZone) => {
    if (!confirm(`Are you sure you want to delete the ad zone "${zone.name}"? This action cannot be undone.`)) {
      return;
    }

    setIsDeleting(zone.id);
    try {
      const response = await fetch(`/api/zones/${zone.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchZones(); // Refresh the list
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to delete ad zone');
      }
    } catch (error) {
      alert('An error occurred. Please try again.');
    } finally {
      setIsDeleting(null);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Code copied to clipboard!');
    }).catch(() => {
      alert('Failed to copy code. Please copy manually.');
    });
  };

  if (!session || session.user?.role !== 'publisher') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading ad zones...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ad Zones</h1>
            <p className="mt-2 text-gray-600">Manage ad placements on your websites</p>
          </div>
          <Link
            href="/publisher/zones/create"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Ad Zone
          </Link>
        </div>

        {/* Ad Zones List */}
        <div className="bg-white rounded-lg shadow">
          {zones.length === 0 ? (
            <div className="p-6">
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No ad zones yet</h3>
                <p className="mt-1 text-sm text-gray-500">Create ad zones to start monetizing your websites.</p>
                <div className="mt-6">
                  <Link
                    href="/publisher/zones/create"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Create Ad Zone
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            <div className="overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Your Ad Zones ({zones.length})</h2>
              </div>
              <div className="divide-y divide-gray-200">
                {zones.map((zone) => (
                  <div key={zone.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h3 className="text-lg font-medium text-gray-900">{zone.name}</h3>
                          <span className={`ml-3 px-2 py-1 text-xs rounded-full ${
                            zone.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {zone.status}
                          </span>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <span className="font-medium">Website:</span>
                          <span className="ml-1">{zone.website_name}</span>
                          <span className="mx-2">•</span>
                          <span className="font-medium">Format:</span>
                          <span className="ml-1 capitalize">{zone.ad_format.replace('_', ' ')}</span>
                          {zone.size && (
                            <>
                              <span className="mx-2">•</span>
                              <span className="font-medium">Size:</span>
                              <span className="ml-1">{zone.size}</span>
                            </>
                          )}
                        </div>
                        <div className="mt-1 text-sm text-gray-500">
                          <span className="font-medium">Zone ID:</span>
                          <span className="ml-1 font-mono">{zone.id}</span>
                          <span className="mx-2">•</span>
                          <span className="font-medium">Created:</span>
                          <span className="ml-1">{new Date(zone.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="ml-4 flex space-x-2">
                        <button
                          onClick={() => handleGetCode(zone)}
                          className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                        >
                          Get Code
                        </button>
                        <button
                          onClick={() => handleEdit(zone)}
                          className="text-gray-600 hover:text-gray-900 text-sm font-medium"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(zone)}
                          disabled={isDeleting === zone.id}
                          className="text-red-600 hover:text-red-900 text-sm font-medium disabled:opacity-50"
                        >
                          {isDeleting === zone.id ? 'Deleting...' : 'Delete'}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Get Code Modal */}
        {showCodeModal && selectedZone && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Ad Zone Integration Code</h3>
                  <button
                    onClick={() => setShowCodeModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Zone Information</h4>
                    <div className="bg-gray-50 p-3 rounded-md text-sm">
                      <p><strong>Zone Name:</strong> {selectedZone.name}</p>
                      <p><strong>Zone ID:</strong> {selectedZone.id}</p>
                      <p><strong>Format:</strong> {selectedZone.ad_format.replace('_', ' ')}</p>
                      {selectedZone.size && <p><strong>Size:</strong> {selectedZone.size}</p>}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Universal Ad Tag</h4>
                    <p className="text-sm text-gray-600 mb-2">Place this single script where you want the ad to appear:</p>
                    <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm overflow-x-auto">
                      <pre>{`<script src="${window.location.origin}/api/js/adtag.js?zone_id=${selectedZone.id}&format=${selectedZone.ad_format}${(selectedZone.size && selectedZone.ad_format !== 'popup') ? `&size=${selectedZone.size}` : ''}"></script>`}</pre>
                    </div>
                    <button
                      onClick={() => copyToClipboard(`<script src="${window.location.origin}/api/js/adtag.js?zone_id=${selectedZone.id}&format=${selectedZone.ad_format}${(selectedZone.size && selectedZone.ad_format !== 'popup') ? `&size=${selectedZone.size}` : ''}"></script>`)}
                      className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      Copy Ad Tag
                    </button>
                  </div>

                  {/* Configuration for special formats */}
                  {(selectedZone.ad_format === 'native' || selectedZone.ad_format === 'in_page_push') && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Optional Configuration</h4>
                      <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm overflow-x-auto">
                        <pre>{`<script>
// Configure before loading the ad tag
GlobalAdsMedia.configure('${selectedZone.id}', {
${selectedZone.ad_format === 'native' ? `  gridLayout: true,        // Enable grid layout
  textStyle: 'bold'        // 'default', 'bold', 'italic'` : ''}${selectedZone.ad_format === 'in_page_push' ? `  position: 'bottom-right', // 'top-left', 'top-right', 'bottom-left', 'bottom-right'
  autoClose: true,         // Auto close notification
  closeDelay: 5000         // Close after 5 seconds` : ''}
});
</script>`}</pre>
                      </div>
                      <button
                        onClick={() => copyToClipboard(`<script>
// Configure before loading the ad tag
GlobalAdsMedia.configure('${selectedZone.id}', {
${selectedZone.ad_format === 'native' ? `  gridLayout: true,        // Enable grid layout
  textStyle: 'bold'        // 'default', 'bold', 'italic'` : ''}${selectedZone.ad_format === 'in_page_push' ? `  position: 'bottom-right', // 'top-left', 'top-right', 'bottom-left', 'bottom-right'
  autoClose: true,         // Auto close notification
  closeDelay: 5000         // Close after 5 seconds` : ''}
});
</script>`)}
                        className="mt-2 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                      >
                        Copy Configuration
                      </button>
                    </div>
                  )}

                  {/* Popup direct link */}
                  {selectedZone.ad_format === 'popup' && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Direct Popup Link</h4>
                      <p className="text-sm text-gray-600 mb-2">Use this link to send popup traffic directly:</p>
                      <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm overflow-x-auto">
                        <pre>{`${window.location.origin}/api/serve?zone_id=${selectedZone.id}&format=popup&redirect=1`}</pre>
                      </div>
                      <button
                        onClick={() => copyToClipboard(`${window.location.origin}/api/serve?zone_id=${selectedZone.id}&format=popup&redirect=1`)}
                        className="mt-2 px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
                      >
                        Copy Popup Link
                      </button>
                    </div>
                  )}

                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <h4 className="font-medium text-yellow-800 mb-2">Implementation Notes</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• <strong>Universal Tag:</strong> Single script that handles all ad formats automatically</li>
                      <li>• <strong>Banner Ads:</strong> Supports both image and JS tag campaigns</li>
                      <li>• <strong>Native Ads:</strong> Configure grid layout and text styling as needed</li>
                      <li>• <strong>Push Notifications:</strong> Configure position and auto-close behavior</li>
                      <li>• <strong>Popup Ads:</strong> Only trigger after user interaction (browser compliance)</li>
                      <li>• Place configuration script before the ad tag for custom settings</li>
                      <li>• Test the implementation on a staging environment first</li>
                    </ul>
                  </div>
                </div>

                <div className="flex justify-end mt-6">
                  <button
                    onClick={() => setShowCodeModal(false)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Edit Zone Modal */}
        {showEditModal && selectedZone && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Edit Ad Zone</h3>
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleUpdateZone} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Zone Name</label>
                    <input
                      type="text"
                      value={editFormData.name}
                      onChange={(e) => setEditFormData({ name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div className="bg-gray-50 p-3 rounded-md text-sm">
                    <p><strong>Zone ID:</strong> {selectedZone.id}</p>
                    <p><strong>Format:</strong> {selectedZone.ad_format.replace('_', ' ')}</p>
                    {selectedZone.size && <p><strong>Size:</strong> {selectedZone.size}</p>}
                    <p className="text-gray-600 mt-2">Note: Format and size cannot be changed after creation.</p>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowEditModal(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                    >
                      Update Zone
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
