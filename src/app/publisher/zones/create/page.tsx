'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

interface Website {
  id: number;
  name: string;
  url: string;
  status: string;
}

export default function CreateAdZone() {
  const router = useRouter();
  const { data: session } = useSession();

  const [websites, setWebsites] = useState<Website[]>([]);
  const [formData, setFormData] = useState({
    websiteId: '',
    name: '',
    adFormat: 'banner',
    size: '300x250',
    // Native ad configuration
    nativeConfig: {
      layout: 'grid',
      columns: 2,
      showIcon: true,
      titleLength: 25,
      descriptionLength: 90,
      imageRatio: '4:3',
      backgroundColor: '#ffffff',
      textColor: '#333333',
      linkColor: '#0066cc',
      borderRadius: '8',
      spacing: '16',
    },
    // Push notification configuration
    pushConfig: {
      position: 'top-right',
      animation: 'slide',
      displayDuration: 5000,
      showCloseButton: true,
      backgroundColor: '#ffffff',
      textColor: '#333333',
      borderColor: '#dddddd',
      borderRadius: '8',
      shadow: true,
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchWebsites();
  }, []);

  const fetchWebsites = async () => {
    try {
      const response = await fetch('/api/websites');
      if (response.ok) {
        const data = await response.json();
        setWebsites(data.filter((w: Website) => w.status === 'approved'));
      }
    } catch (error) {
      console.error('Failed to fetch websites:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleNativeConfigChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      nativeConfig: {
        ...prev.nativeConfig,
        [field]: value
      }
    }));
  };

  const handlePushConfigChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      pushConfig: {
        ...prev.pushConfig,
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/zones', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push('/publisher/zones');
      } else {
        const error = await response.json();
        setErrors({ submit: error.message || 'Failed to create ad zone' });
      }
    } catch (error) {
      setErrors({ submit: 'An error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!session || session.user?.role !== 'publisher') {
    return <div>Access denied</div>;
  }

  // Match banner sizes from advertiser campaign creation
  const bannerSizes = [
    { value: '300x250', label: '300x250 (Medium Rectangle)' },
    { value: '300x600', label: '300x600 (Half Page)' },
    { value: '160x600', label: '160x600 (Wide Skyscraper)' },
    { value: '728x90', label: '728x90 (Leaderboard)' },
    { value: '320x50', label: '320x50 (Mobile Banner)' },
    { value: '900x300', label: '900x300 (Large Banner)' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create Ad Zone</h1>
          <p className="mt-2 text-gray-600">Set up a new ad placement on your website</p>
        </div>

        <div className="bg-white rounded-lg shadow">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Website Selection */}
            <div>
              <label htmlFor="websiteId" className="block text-sm font-medium text-gray-700">
                Website *
              </label>
              <select
                id="websiteId"
                name="websiteId"
                value={formData.websiteId}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">Select a website</option>
                {websites.map((website) => (
                  <option key={website.id} value={website.id}>
                    {website.name} ({website.url})
                  </option>
                ))}
              </select>
              {websites.length === 0 && (
                <p className="mt-1 text-sm text-red-600">
                  You need to add and verify a website first.
                </p>
              )}
            </div>

            {/* Zone Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Zone Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Header Banner, Sidebar Ad, etc."
                required
              />
            </div>

            {/* Ad Format */}
            <div>
              <label htmlFor="adFormat" className="block text-sm font-medium text-gray-700">
                Ad Format *
              </label>
              <select
                id="adFormat"
                name="adFormat"
                value={formData.adFormat}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="banner">Banner</option>
                <option value="native">Native</option>
                <option value="in_page_push">In-Page Push</option>
                <option value="popup">Popup</option>
              </select>
            </div>

            {/* Size Selection (for banner ads) */}
            {formData.adFormat === 'banner' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Banner Size *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {bannerSizes.map((size) => (
                    <label key={size.value} className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                      formData.size === size.value ? 'border-blue-600 bg-blue-50' : 'border-gray-300'
                    }`}>
                      <input
                        type="radio"
                        name="size"
                        value={size.value}
                        checked={formData.size === size.value}
                        onChange={handleChange}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded-full border-2 mr-3 flex-shrink-0 ${
                        formData.size === size.value ? 'border-blue-600 bg-blue-600' : 'border-gray-300'
                      }`}>
                        {formData.size === size.value && (
                          <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                        )}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{size.label}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            )}

            {/* Native Ad Setup */}
            {formData.adFormat === 'native' && (
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Native Ad Configuration</h4>
                  <p className="text-sm text-blue-700">
                    Customize how native ads appear on your website to match your design.
                  </p>
                </div>

                {/* Layout Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Layout Style</label>
                    <select
                      value={formData.nativeConfig.layout}
                      onChange={(e) => handleNativeConfigChange('layout', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="grid">Grid Layout</option>
                      <option value="list">List Layout</option>
                      <option value="card">Card Layout</option>
                      <option value="inline">Inline Layout</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Columns (Grid Layout)</label>
                    <select
                      value={formData.nativeConfig.columns}
                      onChange={(e) => handleNativeConfigChange('columns', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      disabled={formData.nativeConfig.layout !== 'grid'}
                    >
                      <option value={1}>1 Column</option>
                      <option value={2}>2 Columns</option>
                      <option value={3}>3 Columns</option>
                      <option value={4}>4 Columns</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Image Ratio</label>
                    <select
                      value={formData.nativeConfig.imageRatio}
                      onChange={(e) => handleNativeConfigChange('imageRatio', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="4:3">4:3 (Standard)</option>
                      <option value="16:9">16:9 (Widescreen)</option>
                      <option value="1:1">1:1 (Square)</option>
                      <option value="3:2">3:2 (Classic)</option>
                    </select>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.nativeConfig.showIcon}
                        onChange={(e) => handleNativeConfigChange('showIcon', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Show advertiser icon</span>
                    </label>
                  </div>
                </div>

                {/* Text Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Title Length (characters)</label>
                    <input
                      type="number"
                      min="15"
                      max="50"
                      value={formData.nativeConfig.titleLength}
                      onChange={(e) => handleNativeConfigChange('titleLength', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description Length (characters)</label>
                    <input
                      type="number"
                      min="30"
                      max="150"
                      value={formData.nativeConfig.descriptionLength}
                      onChange={(e) => handleNativeConfigChange('descriptionLength', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* Styling Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                    <input
                      type="color"
                      value={formData.nativeConfig.backgroundColor}
                      onChange={(e) => handleNativeConfigChange('backgroundColor', e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Text Color</label>
                    <input
                      type="color"
                      value={formData.nativeConfig.textColor}
                      onChange={(e) => handleNativeConfigChange('textColor', e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Link Color</label>
                    <input
                      type="color"
                      value={formData.nativeConfig.linkColor}
                      onChange={(e) => handleNativeConfigChange('linkColor', e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Border Radius (px)</label>
                    <input
                      type="number"
                      min="0"
                      max="20"
                      value={formData.nativeConfig.borderRadius}
                      onChange={(e) => handleNativeConfigChange('borderRadius', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Spacing (px)</label>
                    <input
                      type="number"
                      min="8"
                      max="32"
                      value={formData.nativeConfig.spacing}
                      onChange={(e) => handleNativeConfigChange('spacing', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* Preview */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-3">Preview</h5>
                  <div
                    className={`grid gap-${Math.floor(parseInt(formData.nativeConfig.spacing) / 4)} ${
                      formData.nativeConfig.layout === 'grid' ? `grid-cols-${formData.nativeConfig.columns}` : 'grid-cols-1'
                    }`}
                    style={{ backgroundColor: formData.nativeConfig.backgroundColor }}
                  >
                    <div
                      className="p-3 border rounded"
                      style={{
                        borderRadius: `${formData.nativeConfig.borderRadius}px`,
                        color: formData.nativeConfig.textColor
                      }}
                    >
                      <div className="flex items-start space-x-3">
                        {formData.nativeConfig.showIcon && (
                          <div className="w-8 h-8 bg-gray-300 rounded flex-shrink-0"></div>
                        )}
                        <div className="flex-1">
                          <h6 className="font-medium text-sm" style={{ color: formData.nativeConfig.linkColor }}>
                            Sample Ad Title
                          </h6>
                          <p className="text-xs mt-1" style={{ color: formData.nativeConfig.textColor }}>
                            Sample ad description text that shows how your native ads will appear.
                          </p>
                        </div>
                      </div>
                      <div
                        className="mt-2 bg-gray-200 rounded"
                        style={{
                          aspectRatio: formData.nativeConfig.imageRatio.replace(':', '/'),
                          borderRadius: `${formData.nativeConfig.borderRadius}px`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* In-Page Push Setup */}
            {formData.adFormat === 'in_page_push' && (
              <div className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900 mb-2">In-Page Push Configuration</h4>
                  <p className="text-sm text-green-700">
                    Configure how push notifications appear on your website.
                  </p>
                </div>

                {/* Position Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
                    <select
                      value={formData.pushConfig.position}
                      onChange={(e) => handlePushConfigChange('position', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="top-right">Top Right</option>
                      <option value="top-left">Top Left</option>
                      <option value="bottom-right">Bottom Right</option>
                      <option value="bottom-left">Bottom Left</option>
                      <option value="top-center">Top Center</option>
                      <option value="bottom-center">Bottom Center</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Animation</label>
                    <select
                      value={formData.pushConfig.animation}
                      onChange={(e) => handlePushConfigChange('animation', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="slide">Slide In</option>
                      <option value="fade">Fade In</option>
                      <option value="bounce">Bounce In</option>
                      <option value="zoom">Zoom In</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Display Duration (seconds)</label>
                    <input
                      type="number"
                      min="3"
                      max="15"
                      value={formData.pushConfig.displayDuration / 1000}
                      onChange={(e) => handlePushConfigChange('displayDuration', parseInt(e.target.value) * 1000)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.pushConfig.showCloseButton}
                        onChange={(e) => handlePushConfigChange('showCloseButton', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Show close button</span>
                    </label>
                  </div>
                </div>

                {/* Styling Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                    <input
                      type="color"
                      value={formData.pushConfig.backgroundColor}
                      onChange={(e) => handlePushConfigChange('backgroundColor', e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Text Color</label>
                    <input
                      type="color"
                      value={formData.pushConfig.textColor}
                      onChange={(e) => handlePushConfigChange('textColor', e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Border Color</label>
                    <input
                      type="color"
                      value={formData.pushConfig.borderColor}
                      onChange={(e) => handlePushConfigChange('borderColor', e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Border Radius (px)</label>
                    <input
                      type="number"
                      min="0"
                      max="20"
                      value={formData.pushConfig.borderRadius}
                      onChange={(e) => handlePushConfigChange('borderRadius', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.pushConfig.shadow}
                        onChange={(e) => handlePushConfigChange('shadow', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Drop shadow</span>
                    </label>
                  </div>
                </div>

                {/* Preview */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-3">Preview</h5>
                  <div className="relative h-48 bg-gray-100 rounded-lg overflow-hidden">
                    <div
                      className={`absolute max-w-sm p-4 border ${formData.pushConfig.shadow ? 'shadow-lg' : ''} ${
                        formData.pushConfig.position.includes('top') ? 'top-4' : 'bottom-4'
                      } ${
                        formData.pushConfig.position.includes('right') ? 'right-4' :
                        formData.pushConfig.position.includes('left') ? 'left-4' :
                        'left-1/2 transform -translate-x-1/2'
                      }`}
                      style={{
                        backgroundColor: formData.pushConfig.backgroundColor,
                        borderColor: formData.pushConfig.borderColor,
                        borderRadius: `${formData.pushConfig.borderRadius}px`,
                        color: formData.pushConfig.textColor
                      }}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="w-12 h-12 bg-gray-300 rounded flex-shrink-0"></div>
                        <div className="flex-1">
                          <h6 className="font-medium text-sm">Sample Push Notification</h6>
                          <p className="text-xs mt-1">This is how your push ads will appear to visitors.</p>
                        </div>
                        {formData.pushConfig.showCloseButton && (
                          <button className="text-gray-400 hover:text-gray-600">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Position: {formData.pushConfig.position} • Duration: {formData.pushConfig.displayDuration / 1000}s • Animation: {formData.pushConfig.animation}
                  </p>
                </div>
              </div>
            )}

            {/* Popup Setup */}
            {formData.adFormat === 'popup' && (
              <div className="space-y-4">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h4 className="font-medium text-purple-900 mb-2">Popup Ad Setup</h4>
                  <p className="text-sm text-purple-700 mb-3">
                    Popup ads open in new windows or tabs when triggered by user interactions.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>Features:</strong>
                      <ul className="mt-1 text-purple-600">
                        <li>• Direct landing page redirect</li>
                        <li>• High impact format</li>
                        <li>• User-triggered display</li>
                        <li>• Full-screen experience</li>
                      </ul>
                    </div>
                    <div>
                      <strong>Best Practices:</strong>
                      <ul className="mt-1 text-purple-600">
                        <li>• Limit frequency per user</li>
                        <li>• Ensure mobile compatibility</li>
                        <li>• Use quality landing pages</li>
                        <li>• Monitor user experience</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {errors.submit && (
              <div className="text-red-600 text-sm">{errors.submit}</div>
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || websites.length === 0}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Creating...' : 'Create Ad Zone'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
