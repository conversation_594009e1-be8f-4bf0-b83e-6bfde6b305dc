'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

interface PayoutMethod {
  id: number;
  type: string;
  details: any;
  is_default: boolean;
  status: string;
  created_at: string;
}

interface PayoutRequest {
  id: number;
  amount: number;
  method_type: string;
  status: string;
  requested_at: string;
  processed_at?: string;
}

interface Transaction {
  id: number;
  type: string;
  amount: number;
  status: string;
  description: string;
  payment_method?: string;
  payment_id?: string;
  created_at: string;
  updated_at: string;
}

export default function PublisherPayouts() {
  const { data: session } = useSession();
  const [payoutMethods, setPayoutMethods] = useState<PayoutMethod[]>([]);
  const [payoutRequests, setPayoutRequests] = useState<PayoutRequest[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddMethod, setShowAddMethod] = useState(false);
  const [showRequestPayout, setShowRequestPayout] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PayoutMethod | null>(null);
  const [balance, setBalance] = useState(0);
  const [minPayoutAmount, setMinPayoutAmount] = useState(50);
  const [newMethod, setNewMethod] = useState({
    type: 'paypal',
    email: '',
    currency: 'USDT-TRC20',
    address: '',
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch payout methods
      const methodsResponse = await fetch('/api/publisher/payout-methods');
      if (methodsResponse.ok) {
        const methodsData = await methodsResponse.json();
        setPayoutMethods(methodsData);
      }

      // Fetch payout requests
      const requestsResponse = await fetch('/api/publisher/payout-requests');
      if (requestsResponse.ok) {
        const requestsData = await requestsResponse.json();
        setPayoutRequests(requestsData);
      }

      // Fetch transaction history (withdrawals/payouts only)
      const transactionsResponse = await fetch('/api/publisher/transactions?limit=10');
      if (transactionsResponse.ok) {
        const transactionsData = await transactionsResponse.json();
        setTransactions(transactionsData.transactions || []);
      }

      // Fetch user balance
      const userResponse = await fetch('/api/user/profile');
      if (userResponse.ok) {
        const userData = await userResponse.json();
        setBalance(userData.balance || 0);
      }

      // Fetch minimum payout amount from public settings
      const settingsResponse = await fetch('/api/public/settings');
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setMinPayoutAmount(settingsData.minimum_payout || 50);
      }
    } catch (error) {
      console.error('Failed to fetch payout data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddMethod = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      let details: any = {};

      if (newMethod.type === 'paypal') {
        details = { email: newMethod.email };
      } else if (newMethod.type === 'crypto') {
        details = { currency: newMethod.currency, address: newMethod.address };
      }

      const response = await fetch('/api/publisher/payout-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: newMethod.type,
          details,
        }),
      });

      if (response.ok) {
        setShowAddMethod(false);
        setNewMethod({
          type: 'paypal',
          email: '',
          currency: 'USDT-TRC20',
          address: '',
        });
        fetchData(); // Refresh the data
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to add payment method');
      }
    } catch (error) {
      console.error('Failed to add payment method:', error);
      alert('Failed to add payment method');
    }
  };

  const handleDeleteMethod = async (methodId: number) => {
    if (!confirm('Are you sure you want to delete this payment method?')) {
      return;
    }

    try {
      const response = await fetch(`/api/publisher/payout-methods/${methodId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchData(); // Refresh the data
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to delete payment method');
      }
    } catch (error) {
      console.error('Failed to delete payment method:', error);
      alert('Failed to delete payment method');
    }
  };

  const handleEditMethod = (method: PayoutMethod) => {
    setEditingMethod(method);
    setNewMethod({
      type: method.type,
      email: method.details.email || '',
      currency: method.details.currency || 'USDT-TRC20',
      address: method.details.address || '',
    });
    setShowAddMethod(true);
  };

  const handleUpdateMethod = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingMethod) return;

    try {
      let details: any = {};

      if (newMethod.type === 'paypal') {
        details = { email: newMethod.email };
      } else if (newMethod.type === 'crypto') {
        details = { currency: newMethod.currency, address: newMethod.address };
      }

      const response = await fetch(`/api/publisher/payout-methods/${editingMethod.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: newMethod.type,
          details,
        }),
      });

      if (response.ok) {
        setShowAddMethod(false);
        setEditingMethod(null);
        setNewMethod({
          type: 'paypal',
          email: '',
          currency: 'USDT-TRC20',
          address: '',
        });
        fetchData(); // Refresh the data
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to update payment method');
      }
    } catch (error) {
      console.error('Failed to update payment method:', error);
      alert('Failed to update payment method');
    }
  };

  if (!session || session.user?.role !== 'publisher') {
    return <div>Access denied</div>;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'withdrawal':
      case 'payout':
        return 'text-red-600';
      case 'adjustment':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Payouts</h1>
          <p className="mt-2 text-gray-600">Manage your payout methods and request withdrawals</p>
        </div>

        {/* Balance Card */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Available Balance</h2>
            <p className="text-3xl font-bold text-green-600">${balance.toFixed(2)}</p>
            <p className="mt-2 text-sm text-gray-500">Minimum payout amount is ${minPayoutAmount.toFixed(2)}</p>
          </div>
        </div>

        {/* Payout Methods */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">Payout Methods</h2>
            <button
              onClick={() => setShowAddMethod(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Add Method
            </button>
          </div>
          <div className="p-6">
            {payoutMethods.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No payout methods</h3>
                <p className="mt-1 text-sm text-gray-500">Add a payout method to receive payments.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {payoutMethods.map((method) => (
                  <div key={method.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="text-sm font-medium text-gray-900 capitalize">{method.type}</h3>
                          {method.is_default && (
                            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Default</span>
                          )}
                          <span className={`px-2 py-1 text-xs rounded-full capitalize ${getStatusBadge(method.status)}`}>
                            {method.status}
                          </span>
                        </div>
                        <div className="mt-1 text-sm text-gray-600">
                          {method.type === 'paypal' && `PayPal: ${method.details.email}`}
                          {method.type === 'crypto' && `${method.details.currency}: ${method.details.address}`}
                          {method.type === 'bank' && `Bank: ${method.details.account_number}`}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditMethod(method)}
                          className="text-blue-600 hover:text-blue-900 text-sm"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteMethod(method.id)}
                          className="text-red-600 hover:text-red-900 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>





        {/* Payout History */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">Payout History</h2>
            {payoutMethods.length > 0 && balance >= minPayoutAmount && (
              <button
                onClick={() => setShowRequestPayout(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Request Payout
              </button>
            )}
          </div>
          <div className="overflow-x-auto">
            {payoutRequests.length === 0 ? (
              <div className="p-6 text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No payout requests yet</h3>
                <p className="mt-1 text-sm text-gray-500">Your payout history will appear here.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processed</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payoutRequests.map((request) => (
                    <tr key={request.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${request.amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                        {request.method_type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full capitalize ${getStatusBadge(request.status)}`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(request.requested_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.processed_at ? new Date(request.processed_at).toLocaleDateString() : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Transaction History */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Transaction History</h2>
          </div>
          <div className="p-6">
            {transactions.length === 0 ? (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions yet</h3>
                <p className="mt-1 text-sm text-gray-500">Your withdrawal history will appear here.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        transaction.type === 'withdrawal' || transaction.type === 'payout' ? 'bg-red-100' : 'bg-purple-100'
                      }`}>
                        {(transaction.type === 'withdrawal' || transaction.type === 'payout') && (
                          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                          </svg>
                        )}
                        {transaction.type === 'adjustment' && (
                          <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900">{transaction.description}</p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(transaction.status)}`}>
                            {transaction.status}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <p className="text-xs text-gray-500">{formatDate(transaction.created_at)}</p>
                          {transaction.payment_method && (
                            <p className="text-xs text-gray-500 capitalize">via {transaction.payment_method}</p>
                          )}
                          {transaction.payment_id && (
                            <p className="text-xs text-gray-400">ID: {transaction.payment_id.slice(0, 8)}...</p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-semibold ${getTypeColor(transaction.type)}`}>
                        -{formatCurrency(Math.abs(transaction.amount))}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">{transaction.type}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Add Payment Method Modal */}
        {showAddMethod && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {editingMethod ? 'Edit Payment Method' : 'Add Payment Method'}
                  </h3>
                  <button
                    onClick={() => {
                      setShowAddMethod(false);
                      setEditingMethod(null);
                      setNewMethod({
                        type: 'paypal',
                        email: '',
                        currency: 'USDT-TRC20',
                        address: '',
                      });
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={editingMethod ? handleUpdateMethod : handleAddMethod} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method Type</label>
                    <select
                      value={newMethod.type}
                      onChange={(e) => setNewMethod({ ...newMethod, type: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="paypal">PayPal</option>
                      <option value="crypto">Cryptocurrency (USDT)</option>
                      <option value="bank">Bank Transfer (Contact Support)</option>
                    </select>
                  </div>

                  {newMethod.type === 'paypal' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">PayPal Email</label>
                      <input
                        type="email"
                        value={newMethod.email}
                        onChange={(e) => setNewMethod({ ...newMethod, email: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                  )}

                  {newMethod.type === 'crypto' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">USDT Network</label>
                        <select
                          value={newMethod.currency}
                          onChange={(e) => setNewMethod({ ...newMethod, currency: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="USDT-TRC20">USDT (TRC20)</option>
                          <option value="USDT-BSC">USDT (BSC/BEP20)</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Wallet Address</label>
                        <input
                          type="text"
                          value={newMethod.address}
                          onChange={(e) => setNewMethod({ ...newMethod, address: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter your USDT wallet address"
                          required
                        />
                      </div>
                    </>
                  )}

                  {newMethod.type === 'bank' && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-blue-800">
                            Bank Transfer Setup Required
                          </h3>
                          <div className="mt-2 text-sm text-blue-700">
                            <p>For bank transfer payouts, please create a support ticket with your banking details.</p>
                            <p className="mt-1"><strong>Minimum amount:</strong> $1,000</p>
                            <p className="mt-1"><strong>Processing time:</strong> 3-5 business days</p>
                          </div>
                          <div className="mt-4">
                            <Link
                              href="/publisher/support/create"
                              className="text-sm bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700"
                            >
                              Create Support Ticket
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {newMethod.type !== 'bank' && (
                    <div className="flex justify-end space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddMethod(false);
                          setEditingMethod(null);
                          setNewMethod({
                            type: 'paypal',
                            email: '',
                            currency: 'USDT-TRC20',
                            address: '',
                          });
                        }}
                        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                      >
                        {editingMethod ? 'Update Method' : 'Add Method'}
                      </button>
                    </div>
                  )}

                  {newMethod.type === 'bank' && (
                    <div className="flex justify-end pt-4">
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddMethod(false);
                          setEditingMethod(null);
                          setNewMethod({
                            type: 'paypal',
                            email: '',
                            currency: 'USDT-TRC20',
                            address: '',
                          });
                        }}
                        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                      >
                        Close
                      </button>
                    </div>
                  )}
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Request Payout Modal */}
        {showRequestPayout && (
          <RequestPayoutModal
            balance={balance}
            minPayoutAmount={minPayoutAmount}
            payoutMethods={payoutMethods}
            onClose={() => setShowRequestPayout(false)}
            onSuccess={() => {
              setShowRequestPayout(false);
              fetchData(); // Refresh data
            }}
          />
        )}
      </div>
    </div>
  );
}

function RequestPayoutModal({
  balance,
  minPayoutAmount,
  payoutMethods,
  onClose,
  onSuccess
}: {
  balance: number;
  minPayoutAmount: number;
  payoutMethods: PayoutMethod[];
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [amount, setAmount] = useState('');
  const [selectedMethodId, setSelectedMethodId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/publisher/payout-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: parseFloat(amount),
          method_id: parseInt(selectedMethodId),
        }),
      });

      if (response.ok) {
        onSuccess();
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to request payout');
      }
    } catch (error) {
      console.error('Failed to request payout:', error);
      alert('Failed to request payout');
    } finally {
      setIsSubmitting(false);
    }
  };

  const activePayoutMethods = payoutMethods.filter(method => method.status === 'active');

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Request Payout</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="mb-4 p-4 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              <p><strong>Available Balance:</strong> ${balance.toFixed(2)}</p>
              <p><strong>Minimum Payout:</strong> ${minPayoutAmount.toFixed(2)}</p>
            </div>
          </div>

          {activePayoutMethods.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-sm text-gray-600">No active payout methods available.</p>
              <p className="text-sm text-gray-600">Please add and verify a payout method first.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Payout Amount</label>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  min={minPayoutAmount}
                  max={balance}
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder={`Min: $${minPayoutAmount.toFixed(2)}`}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Payout Method</label>
                <select
                  value={selectedMethodId}
                  onChange={(e) => setSelectedMethodId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select a payout method</option>
                  {activePayoutMethods.map((method) => (
                    <option key={method.id} value={method.id}>
                      {method.type.toUpperCase()}: {
                        method.type === 'paypal' ? method.details.email :
                        method.type === 'crypto' ? `${method.details.currency} - ${method.details.address.slice(0, 10)}...` :
                        method.type === 'bank' ? `${method.details.bank_name} - ***${method.details.account_number?.slice(-4)}` :
                        'Unknown'
                      }
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || !amount || !selectedMethodId || parseFloat(amount) < minPayoutAmount || parseFloat(amount) > balance}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
                >
                  {isSubmitting ? 'Requesting...' : 'Request Payout'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
