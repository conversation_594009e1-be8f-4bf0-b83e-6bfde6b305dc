'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';
import { formatNumber, formatCurrency, formatDecimal } from '@/lib/format-utils';

const SortIcon = ({ direction }: { direction: string | null }) => {
  if (!direction) {
    return (
      <svg className="w-3 h-3 ml-1 inline-block text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
      </svg>
    );
  }
  return direction === 'asc' ? (
    <svg className="w-3 h-3 ml-1 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
    </svg>
  ) : (
    <svg className="w-3 h-3 ml-1 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
    </svg>
  );
};

interface StatData {
  date?: string;
  campaign_id?: number;
  campaign_name?: string;
  supplier_id?: number;
  supplier_name?: string;
  total_requests?: number;
  total_wins?: number;
  total_revenue?: number;
  total_spend?: number;
  platform_revenue?: number;
  avg_win_price?: number;
  win_rate?: number;
  wins?: number;
  total_win_price?: number;
  endpoint_id?: number;
  endpoint_name?: string;
  publisher_id?: number;
  publisher_name?: string;
  country?: string;
  os?: string;
  browser?: string;
  device?: string;
}

export default function DSPStatistics() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);

  const [filters, setFilters] = useState({
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    dateTo: new Date().toISOString().split('T')[0],
    groupBy: 'day',
  });

  const [sortConfig, setSortConfig] = useState({
    key: '',
    direction: 'desc' as 'asc' | 'desc'
  });

  const handleSort = (key: string) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  const getSortedData = (data: any[]) => {
    if (!sortConfig.key) return data;

    return [...data].sort((a, b) => {
      const key = sortConfig.key;
      let aValue = a[key];
      let bValue = b[key];

      // Handle undefined or null values
      if (aValue === undefined || aValue === null) aValue = 0;
      if (bValue === undefined || bValue === null) bValue = 0;

      // Convert to number for numeric columns
      if (['total_wins', 'total_requests', 'win_rate', 'total_spend', 'avg_win_price'].includes(key)) {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else {
        // For string comparison (country, os, browser, device, etc)
        aValue = String(aValue).toLowerCase();
        bValue = String(bValue).toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  useEffect(() => {
    fetchStatistics();
  }, [filters]);

  const fetchStatistics = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        date_from: filters.dateFrom,
        date_to: filters.dateTo,
        group_by: filters.groupBy,
      });

      const response = await fetch(`/api/dsp/statistics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data.data || {});
      } else {
        console.error('Failed to fetch DSP statistics:', response.status, response.statusText);
        setStats({});
      }
    } catch (error) {
      console.error('Failed to fetch DSP statistics:', error);
      setStats({});
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const refreshData = () => {
    fetchStatistics();
  };

  if (!session || session.user?.role !== 'dsp') {
    return <div>Access denied</div>;
  }

  // Calculate totals from the currently displayed data based on groupBy
  const calculateTotals = () => {
    let data: any[] = [];
    switch (filters.groupBy) {
      case 'day':
        data = stats.daily_breakdown || [];
        break;
      case 'endpoint':
        data = stats.endpoint_breakdown || [];
        break;
      case 'publisher':
        data = stats.publisher_breakdown || [];
        break;
      case 'country':
        data = stats.country_breakdown || [];
        break;
      case 'os':
        data = stats.os_breakdown || [];
        break;
      case 'browser':
        data = stats.browser_breakdown || [];
        break;
      case 'device':
        data = stats.device_breakdown || [];
        break;
      default:
        data = [];
    }

    const totalRequests = data.reduce((sum: number, item: any) => sum + (parseInt(item.total_requests) || 0), 0);
    const totalWins = data.reduce((sum: number, item: any) => sum + (parseInt(item.total_wins) || 0), 0);
    const totalSpend = data.reduce((sum: number, item: any) => sum + (parseFloat(item.total_spend) || 0), 0);
    const avgWinRate = totalRequests > 0 ? (totalWins / totalRequests) * 100 : 0;
    const avgWinPrice = totalWins > 0 ? totalSpend / totalWins : 0;

    return {
      total_requests: totalRequests,
      total_wins: totalWins,
      total_spend: totalSpend,
      avg_win_price: avgWinPrice,
      avg_win_rate: avgWinRate
    };
  };

  const totals = calculateTotals();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">DSP Statistics</h1>
            <p className="mt-2 text-gray-600">Track your bidding performance and wins</p>
          </div>
          <button
            onClick={refreshData}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Filters</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Group By</label>
                <select
                  value={filters.groupBy}
                  onChange={(e) => handleFilterChange('groupBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="day">Day</option>
                  <option value="endpoint">Endpoint</option>
                  <option value="publisher">Publisher ID</option>
                  <option value="country">Country</option>
                  <option value="os">Operating System</option>
                  <option value="browser">Browser</option>
                  <option value="device">Device</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Requests</div>
            <div className="text-2xl font-bold text-gray-900">{formatNumber(totals.total_requests || 0)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Wins</div>
            <div className="text-2xl font-bold text-green-600">{formatNumber(totals.total_wins || 0)}</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Win Rate</div>
            <div className="text-2xl font-bold text-blue-600">{(totals.avg_win_rate || 0).toFixed(2)}%</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-sm font-medium text-gray-500">Total Spend</div>
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(totals.total_spend || 0)}</div>
          </div>
        </div>

        {/* Charts */}
        {filters.groupBy === 'day' && stats.daily_breakdown && stats.daily_breakdown.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Requests & Wins Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats.daily_breakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="total_requests" stroke="#3B82F6" name="Requests" />
                  <Line type="monotone" dataKey="total_wins" stroke="#10B981" name="Wins" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Win Rate & Spend Trend</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={stats.daily_breakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="total_spend" fill="#F59E0B" name="Spend ($)" />
                  <Line yAxisId="right" type="monotone" dataKey="win_rate" stroke="#EF4444" name="Win Rate (%)" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {filters.groupBy === 'endpoint' && stats.endpoint_breakdown && stats.endpoint_breakdown.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Endpoint Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.endpoint_breakdown.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="endpoint_name" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#3B82F6" name="Wins" />
                <Bar dataKey="total_spend" fill="#10B981" name="Spend ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {filters.groupBy === 'publisher' && stats.publisher_breakdown && stats.publisher_breakdown.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Publisher Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.publisher_breakdown.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="publisher_name" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#8B5CF6" name="Wins" />
                <Bar dataKey="total_spend" fill="#F59E0B" name="Spend ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {filters.groupBy === 'country' && stats.country_breakdown && stats.country_breakdown.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Country Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.country_breakdown.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="country" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#10B981" name="Wins" />
                <Bar dataKey="total_spend" fill="#F59E0B" name="Spend ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {filters.groupBy === 'os' && stats.os_breakdown && stats.os_breakdown.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Operating System Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.os_breakdown.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="os" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#3B82F6" name="Wins" />
                <Bar dataKey="total_spend" fill="#F59E0B" name="Spend ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {filters.groupBy === 'browser' && stats.browser_breakdown && stats.browser_breakdown.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Browser Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.browser_breakdown.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="browser" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#EF4444" name="Wins" />
                <Bar dataKey="total_spend" fill="#F59E0B" name="Spend ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {filters.groupBy === 'device' && stats.device_breakdown && stats.device_breakdown.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Device Performance</h3>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={stats.device_breakdown.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="device" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_wins" fill="#8B5CF6" name="Wins" />
                <Bar dataKey="total_spend" fill="#F59E0B" name="Spend ($)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Statistics Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Detailed Statistics</h2>
          </div>
          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Loading statistics...</p>
              </div>
            ) : (
              <div className="p-6">
                {filters.groupBy === 'day' && stats.daily_breakdown && stats.daily_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('date')}>
                          Date
                          <SortIcon direction={sortConfig.key === 'date' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_requests')}>
                          Requests
                          <SortIcon direction={sortConfig.key === 'total_requests' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('win_rate')}>
                          Win Rate
                          <SortIcon direction={sortConfig.key === 'win_rate' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.daily_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.date}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_requests || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{(stat.win_rate || 0).toFixed(2)}%</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy === 'endpoint' && stats.endpoint_breakdown && stats.endpoint_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('endpoint_name')}>
                          Endpoint
                          <SortIcon direction={sortConfig.key === 'endpoint_name' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_requests')}>
                          Requests
                          <SortIcon direction={sortConfig.key === 'total_requests' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('win_rate')}>
                          Win Rate
                          <SortIcon direction={sortConfig.key === 'win_rate' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.endpoint_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.endpoint_name || `Endpoint ${stat.endpoint_id}`}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_requests || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{(stat.win_rate || 0).toFixed(2)}%</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy === 'publisher' && stats.publisher_breakdown && stats.publisher_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('publisher_name')}>
                          Publisher
                          <SortIcon direction={sortConfig.key === 'publisher_name' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_requests')}>
                          Requests
                          <SortIcon direction={sortConfig.key === 'total_requests' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('win_rate')}>
                          Win Rate
                          <SortIcon direction={sortConfig.key === 'win_rate' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.publisher_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.publisher_name || `Publisher ${stat.publisher_id}`}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_requests || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{(stat.win_rate || 0).toFixed(2)}%</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy === 'country' && stats.country_breakdown && stats.country_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('country')}>
                          Country
                          <SortIcon direction={sortConfig.key === 'country' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.country_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.country || 'Unknown'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy === 'os' && stats.os_breakdown && stats.os_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('os')}>
                          Operating System
                          <SortIcon direction={sortConfig.key === 'os' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.os_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.os || 'Unknown'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy === 'browser' && stats.browser_breakdown && stats.browser_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('browser')}>
                          Browser
                          <SortIcon direction={sortConfig.key === 'browser' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.browser_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.browser || 'Unknown'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {filters.groupBy === 'device' && stats.device_breakdown && stats.device_breakdown.length > 0 && (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('device')}>
                          Device
                          <SortIcon direction={sortConfig.key === 'device' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_wins')}>
                          Wins
                          <SortIcon direction={sortConfig.key === 'total_wins' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('total_spend')}>
                          Total Spend
                          <SortIcon direction={sortConfig.key === 'total_spend' ? sortConfig.direction : null} />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('avg_win_price')}>
                          Avg Win Price
                          <SortIcon direction={sortConfig.key === 'avg_win_price' ? sortConfig.direction : null} />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getSortedData(stats.device_breakdown).map((stat: StatData, index: number) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{stat.device || 'Unknown'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(stat.total_wins || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(stat.total_spend || 0)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDecimal(stat.avg_win_price || 0, 6)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}

                {(!stats.daily_breakdown && !stats.endpoint_breakdown && !stats.publisher_breakdown && !stats.country_breakdown && !stats.os_breakdown && !stats.browser_breakdown && !stats.device_breakdown) && (
                  <div className="text-center py-12">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                    <p className="mt-1 text-sm text-gray-500">Statistics will appear here once you have RTB activity.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
