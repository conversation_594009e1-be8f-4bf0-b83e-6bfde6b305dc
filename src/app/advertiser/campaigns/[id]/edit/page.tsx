'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Country, State } from 'country-state-city';

// Enhanced MacroInfo component with direct insertion functionality
interface MacroInfoProps {
  onMacroClick: (macro: string) => void;
}

const MacroInfo = ({ onMacroClick }: MacroInfoProps) => {
  const macros = [
    { name: '{click_id}', description: 'Unique click identifier' },
    { name: '{user_id}', description: 'User identifier' },
    { name: '{campaign_id}', description: 'Campaign identifier' },
    { name: '{publisher_id}', description: 'Publisher identifier' },
    { name: '{zone_id}', description: 'Zone identifier' },
    { name: '{country}', description: 'User country code' },
    { name: '{device_type}', description: 'Device type (desktop/mobile/tablet)' },
    { name: '{os}', description: 'Operating system' },
    { name: '{browser}', description: 'Browser name' },
    { name: '{timestamp}', description: 'Unix timestamp' },
    { name: '{ip}', description: 'User IP address' },
    { name: '{referer}', description: 'Referrer URL' },
    { name: '{user_lang}', description: 'User language code' },
    { name: '{ad_format}', description: 'Ad format type' },
    { name: '{page_url}', description: 'Referring page URL' },
    { name: '{os_version}', description: 'Operating system version' },
  ];

  return (
    <div className="mt-2 p-3 bg-gray-50 rounded-md">
      <p className="text-sm font-medium text-gray-700 mb-2">Available Macros (Click to insert):</p>
      <div className="grid grid-cols-4 gap-2">
        {macros.map((macro) => (
          <button
            key={macro.name}
            type="button"
            onClick={() => onMacroClick(macro.name)}
            className="p-2 bg-white rounded border hover:bg-blue-50 hover:border-blue-300 transition-colors cursor-pointer text-left"
          >
            <code className="text-xs font-mono text-blue-600 block">{macro.name}</code>
            <p className="text-xs text-gray-500 mt-1">{macro.description}</p>
          </button>
        ))}
      </div>
    </div>
  );
};

export default function EditCampaign() {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const [campaign, setCampaign] = useState(null);
  const [loading, setLoading] = useState(true);

  // Minimum bid settings from admin
  const [minimumBids, setMinimumBids] = useState({
    banner: '0.50',
    native: '0.75',
    in_page_push: '0.25',
    popup: '0.10',
  });

  const [formData, setFormData] = useState({
    name: '',
    cpmBid: '',
    dailyBudget: '',
    totalBudget: '',

    // Creative settings (editable)
    landingUrl: '',
    jsTag: '',
    creativeType: '',
    nativeTitle: '',
    nativeDescription: '',
    pushTitle: '',
    pushDescription: '',

    // Geo targeting
    targetingCountries: [] as string[],
    targetingStates: [] as string[],

    // Device/OS/Browser targeting
    targetingDevices: [] as string[],
    targetingOs: [] as string[],
    targetingBrowsers: [] as string[],
    targetingConnectionTypes: [] as string[],

    // Schedule
    startDate: '',
    endDate: '',
    dailySchedule: [] as string[], // Days of week
    hourlySchedule: [] as number[], // Hours 0-23

    // Frequency cap
    frequencyCapValue: '',
    frequencyCapPeriod: 'day', // 'hour', 'day', 'week'

    // Whitelist/Blacklist
    whitelistPublishers: '',
    whitelistWebsites: '',
    whitelistZones: '',
    blacklistPublishers: '',
    blacklistWebsites: '',
    blacklistZones: '',
  });

  // Search states for dropdowns
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showStateDropdown, setShowStateDropdown] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Image upload states
  const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);
  const [nativeIconFile, setNativeIconFile] = useState<File | null>(null);
  const [nativeImageFile, setNativeImageFile] = useState<File | null>(null);
  const [pushImageFile, setPushImageFile] = useState<File | null>(null);

  // Image preview URLs
  const [bannerImagePreview, setBannerImagePreview] = useState<string>('');
  const [nativeIconPreview, setNativeIconPreview] = useState<string>('');
  const [nativeImagePreview, setNativeImagePreview] = useState<string>('');
  const [pushImagePreview, setPushImagePreview] = useState<string>('');

  // File input refs
  const bannerImageRef = useRef<HTMLInputElement>(null);
  const nativeIconRef = useRef<HTMLInputElement>(null);
  const nativeImageRef = useRef<HTMLInputElement>(null);
  const pushImageRef = useRef<HTMLInputElement>(null);

  // Country/State data
  const [countries, setCountries] = useState<any[]>([]);
  const [availableStates, setAvailableStates] = useState<any[]>([]);

  // Constants for form options
  const devices = ['Desktop', 'Mobile', 'Tablet'];
  const operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Other'];
  const connectionTypes = ['WiFi', 'Cellular', 'Broadband', 'Dial-up'];
  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Load countries on mount
  useEffect(() => {
    const allCountries = Country.getAllCountries();
    setCountries(allCountries);
  }, []);

  // Fetch minimum bid settings
  useEffect(() => {
    const fetchMinimumBids = async () => {
      try {
        const response = await fetch('/api/settings/minimum-bids');
        const result = await response.json();
        if (result.success) {
          setMinimumBids(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch minimum bids:', error);
        // Keep default values if fetch fails
      }
    };

    fetchMinimumBids();
  }, []);

  // Update available states when countries change
  useEffect(() => {
    if (formData.targetingCountries.length > 0) {
      const states: any[] = [];
      formData.targetingCountries.forEach(countryCode => {
        const countryStates = State.getStatesOfCountry(countryCode);
        states.push(...countryStates);
      });
      setAvailableStates(states);
    } else {
      setAvailableStates([]);
    }
  }, [formData.targetingCountries]);

  // Function to populate form data from campaign
  const populateFormData = (campaignData: any) => {
    setFormData({
      name: campaignData.name || '',
      cpmBid: campaignData.cpm_bid?.toString() || '',
      dailyBudget: campaignData.daily_budget?.toString() || '',
      totalBudget: campaignData.total_budget?.toString() || '',

      // Creative settings
      landingUrl: campaignData.landing_url || '',
      jsTag: campaignData.js_tag || '',
      creativeType: campaignData.creative_type,
      nativeTitle: campaignData.native_title || '',
      nativeDescription: campaignData.native_description || '',
      pushTitle: campaignData.push_title || '',
      pushDescription: campaignData.push_description || '',

      // Geo targeting
      targetingCountries: campaignData.targeting_countries || [],
      targetingStates: campaignData.targeting_states || [],

      // Device/OS/Browser targeting
      targetingDevices: campaignData.targeting_devices || [],
      targetingOs: campaignData.targeting_os || [],
      targetingBrowsers: campaignData.targeting_browsers || [],
      targetingConnectionTypes: campaignData.targeting_connection_types || [],

      // Schedule
      startDate: campaignData.start_date ? new Date(campaignData.start_date).toISOString().split('T')[0] : '',
      endDate: campaignData.end_date ? new Date(campaignData.end_date).toISOString().split('T')[0] : '',
      dailySchedule: campaignData.daily_schedule || [],
      hourlySchedule: campaignData.hourly_schedule || [],

      // Frequency cap
      frequencyCapValue: campaignData.frequency_cap_value?.toString() || '',
      frequencyCapPeriod: campaignData.frequency_cap_period === 1 ? 'hour' :
                          campaignData.frequency_cap_period === 3 ? 'week' : 'day',

      // Whitelist/Blacklist
      whitelistPublishers: campaignData.whitelist_publishers || '',
      whitelistWebsites: campaignData.whitelist_websites || '',
      whitelistZones: campaignData.whitelist_zones || '',
      blacklistPublishers: campaignData.blacklist_publishers || '',
      blacklistWebsites: campaignData.blacklist_websites || '',
      blacklistZones: campaignData.blacklist_zones || '',
    });

    // Set image previews from existing campaign data
    setBannerImagePreview(campaignData.banner_image_url || '');
    setNativeIconPreview(campaignData.native_icon_url || '');
    setNativeImagePreview(campaignData.native_image_url || '');
    setPushImagePreview(campaignData.push_image_url || '');
  };

  useEffect(() => {
    if (!session?.user?.id || !params.id) return;

    const fetchCampaign = async () => {
      try {
        const response = await fetch(`/api/campaigns/${params.id}`);
        const data = await response.json();
        if (data.success) {
          setCampaign(data.campaign);
          // Populate form data with campaign data
          populateFormData(data.campaign);
        } else {
          router.push('/advertiser/campaigns');
        }
      } catch (error) {
        console.error('Error fetching campaign:', error);
        router.push('/advertiser/campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [session, params.id, router]);

  // Form handlers
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Real-time CPM bid validation
    if (name === 'cpmBid' && campaign && value) {
      const bidValue = parseFloat(value);
      const minimumBid = parseFloat(minimumBids[campaign.type as keyof typeof minimumBids] || '0');

      if (!isNaN(bidValue) && bidValue > 0 && bidValue < minimumBid) {
        setErrors(prev => ({
          ...prev,
          cpmBid: `CPM bid must be at least $${minimumBid.toFixed(2)} for ${campaign.type} campaigns`
        }));
      }
    }

    // Real-time daily budget validation
    if (name === 'dailyBudget' && value) {
      const dailyBudgetValue = parseFloat(value);

      if (!isNaN(dailyBudgetValue) && dailyBudgetValue > 0 && dailyBudgetValue < 30) {
        setErrors(prev => ({
          ...prev,
          dailyBudget: 'Daily budget must be at least $30.00'
        }));
      }
    }
  };

  const handleMultiSelect = (value: string | number, field: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter((item: any) => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSelectAll = (field: string, allValues: (string | number)[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].length === allValues.length ? [] : allValues
    }));
  };

  // Macro insertion handler
  const handleMacroClick = (macro: string) => {
    // For now, we'll just copy to clipboard
    navigator.clipboard.writeText(macro);
  };

  // Image upload handlers
  const handleImageUpload = (file: File, type: 'banner' | 'nativeIcon' | 'nativeImage' | 'push') => {
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        switch (type) {
          case 'banner':
            setBannerImageFile(file);
            setBannerImagePreview(result);
            break;
          case 'nativeIcon':
            setNativeIconFile(file);
            setNativeIconPreview(result);
            break;
          case 'nativeImage':
            setNativeImageFile(file);
            setNativeImagePreview(result);
            break;
          case 'push':
            setPushImageFile(file);
            setPushImagePreview(result);
            break;
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageRemove = (type: 'banner' | 'nativeIcon' | 'nativeImage' | 'push') => {
    switch (type) {
      case 'banner':
        setBannerImageFile(null);
        setBannerImagePreview('');
        if (bannerImageRef.current) bannerImageRef.current.value = '';
        break;
      case 'nativeIcon':
        setNativeIconFile(null);
        setNativeIconPreview('');
        if (nativeIconRef.current) nativeIconRef.current.value = '';
        break;
      case 'nativeImage':
        setNativeImageFile(null);
        setNativeImagePreview('');
        if (nativeImageRef.current) nativeImageRef.current.value = '';
        break;
      case 'push':
        setPushImageFile(null);
        setPushImagePreview('');
        if (pushImageRef.current) pushImageRef.current.value = '';
        break;
    }
  };

  // Handle country search and selection
  const handleCountrySearch = (searchTerm: string) => {
    setCountrySearch(searchTerm);
    setShowCountryDropdown(true);
  };

  const handleCountrySelect = (countryCode: string) => {
    if (!formData.targetingCountries.includes(countryCode)) {
      setFormData(prev => ({
        ...prev,
        targetingCountries: [...prev.targetingCountries, countryCode]
      }));
    }
    setCountrySearch('');
    setShowCountryDropdown(false);
  };

  const removeCountry = (countryCode: string) => {
    setFormData(prev => ({
      ...prev,
      targetingCountries: prev.targetingCountries.filter(c => c !== countryCode),
      targetingStates: prev.targetingStates.filter(s => {
        const country = countries.find(c => c.isoCode === countryCode);
        if (!country) return true;
        const countryStates = State.getStatesOfCountry(countryCode);
        return !countryStates.some(state => state.isoCode === s);
      })
    }));
  };

  // Handle state search and selection
  const handleStateSearch = (searchTerm: string) => {
    setStateSearch(searchTerm);
    setShowStateDropdown(true);
  };

  const handleStateSelect = (stateCode: string) => {
    if (!formData.targetingStates.includes(stateCode)) {
      setFormData(prev => ({
        ...prev,
        targetingStates: [...prev.targetingStates, stateCode]
      }));
    }
    setStateSearch('');
    setShowStateDropdown(false);
  };

  const removeState = (stateCode: string) => {
    setFormData(prev => ({
      ...prev,
      targetingStates: prev.targetingStates.filter(s => s !== stateCode)
    }));
  };

  // Filter functions for search
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  const filteredStates = availableStates.filter(state =>
    state.name.toLowerCase().includes(stateSearch.toLowerCase())
  );

  // Refs for dropdown containers
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  const stateDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click is outside country dropdown
      if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target as Node)) {
        setShowCountryDropdown(false);
      }
      // Check if click is outside state dropdown
      if (stateDropdownRef.current && !stateDropdownRef.current.contains(event.target as Node)) {
        setShowStateDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate required fields
      const newErrors: Record<string, string> = {};

      if (!formData.name.trim()) {
        newErrors.name = 'Campaign name is required';
      }

      // CPM bid validation with minimum bid check
      if (!formData.cpmBid) {
        newErrors.cpmBid = 'CPM bid is required';
      } else {
        const bidValue = parseFloat(formData.cpmBid);
        const campaignType = campaign?.type;
        const minimumBid = parseFloat(minimumBids[campaignType as keyof typeof minimumBids] || '0');

        if (isNaN(bidValue) || bidValue <= 0) {
          newErrors.cpmBid = 'CPM bid must be a valid positive number';
        } else if (bidValue < minimumBid) {
          newErrors.cpmBid = `CPM bid must be at least $${minimumBid.toFixed(2)} for ${campaignType} campaigns`;
        }
      }

      if (!formData.startDate) {
        newErrors.startDate = 'Start date is required';
      }

      // Daily budget validation (optional but if provided, must be >= $30)
      if (formData.dailyBudget) {
        const dailyBudgetValue = parseFloat(formData.dailyBudget);
        if (!isNaN(dailyBudgetValue) && dailyBudgetValue > 0 && dailyBudgetValue < 30) {
          newErrors.dailyBudget = 'Daily budget must be at least $30.00';
        }
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setIsLoading(false);
        return;
      }

      // Create FormData for file uploads
      const submitFormData = new FormData();

      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          submitFormData.append(key, JSON.stringify(value));
        } else if (value !== null && value !== undefined) {
          submitFormData.append(key, value.toString());
        }
      });

      // Add creativeType to form data (needed for backend update)
      submitFormData.append('creativeType', formData.creativeType);

      // Add image files if they exist
      if (bannerImageFile) {
        submitFormData.append('bannerImageFile', bannerImageFile);
      }
      if (nativeIconFile) {
        submitFormData.append('nativeIconFile', nativeIconFile);
      }
      if (nativeImageFile) {
        submitFormData.append('nativeImageFile', nativeImageFile);
      }
      if (pushImageFile) {
        submitFormData.append('pushImageFile', pushImageFile);
      }

      const response = await fetch(`/api/campaigns/${params.id}`, {
        method: 'PUT',
        body: submitFormData,
      });

      const data = await response.json();

      if (data.success) {
        router.push('/advertiser/campaigns');
      } else {
        setErrors({ submit: data.message || 'Failed to update campaign' });
      }
    } catch (error) {
      console.error('Error updating campaign:', error);
      setErrors({ submit: 'An error occurred while updating the campaign' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!session || session.user?.role !== 'advertiser') {
    return <div>Access denied</div>;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading campaign...</p>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Campaign not found</h1>
          <p className="mt-2 text-gray-500">The campaign you&apos;re looking for doesn&apos;t exist.</p>
          <button
            onClick={() => router.push('/advertiser/campaigns')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Campaign</h1>
              <p className="mt-2 text-gray-600">
                Campaign ID: #{campaign.id} | Type: {campaign.type}
              </p>
            </div>
            <button
              onClick={() => router.push('/advertiser/campaigns')}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              Back to Campaigns
            </button>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-8">
            {/* Campaign Type Section - DISABLED */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Campaign Type *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {[
                  { value: 'banner', label: 'Banner', description: 'Display banner ads in various sizes' },
                  { value: 'native', label: 'Native', description: 'Native ads that blend with content' },
                  { value: 'in_page_push', label: 'In-Page Push', description: 'Push-style notifications' },
                  { value: 'popup', label: 'Popup', description: 'Popup ads with landing URL only' },
                ].map((type) => (
                  <div key={type.value} className="relative">
                    <label className={`flex flex-col p-4 border-2 rounded-lg cursor-not-allowed transition-colors h-24 ${
                      campaign.type === type.value
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-300 bg-gray-100'
                    }`}>
                      <input
                        type="radio"
                        checked={campaign.type === type.value}
                        disabled
                        className="sr-only"
                      />
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full border-2 mb-2 ${
                        campaign.type === type.value
                          ? 'border-blue-600 bg-blue-600'
                          : 'border-gray-300'
                      }`}>
                        {campaign.type === type.value && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span className={`font-medium text-sm ${
                        campaign.type === type.value ? 'text-gray-900' : 'text-gray-400'
                      }`}>{type.label}</span>
                      <span className={`text-xs mt-1 leading-tight ${
                        campaign.type === type.value ? 'text-gray-600' : 'text-gray-400'
                      }`}>{type.description}</span>
                    </label>
                  </div>
                ))}
              </div>
              <p className="mt-2 text-xs text-gray-500">
                Campaign type cannot be changed after creation
              </p>
            </div>

            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Campaign Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                </div>

                <div>
                  <label htmlFor="cpmBid" className="block text-sm font-medium text-gray-700">
                    CPM Bid (USD) *
                  </label>
                  <input
                    type="number"
                    id="cpmBid"
                    name="cpmBid"
                    value={formData.cpmBid}
                    onChange={handleChange}
                    step="0.01"
                    min="0.01"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {!errors.cpmBid && (
                    <p className="mt-1 text-xs text-gray-500">
                      Minimum CPM bid for {campaign.type} campaigns: ${minimumBids[campaign.type as keyof typeof minimumBids] || '0.01'}
                    </p>
                  )}
                  {errors.cpmBid && <p className="mt-1 text-sm text-red-600">{errors.cpmBid}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <label htmlFor="dailyBudget" className="block text-sm font-medium text-gray-700">
                    Daily Budget (USD)
                  </label>
                  <input
                    type="number"
                    id="dailyBudget"
                    name="dailyBudget"
                    value={formData.dailyBudget}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for unlimited daily budget. Minimum: $30 if set
                  </p>
                  {errors.dailyBudget && <p className="mt-1 text-sm text-red-600">{errors.dailyBudget}</p>}
                </div>

                <div>
                  <label htmlFor="totalBudget" className="block text-sm font-medium text-gray-700">
                    Total Budget (USD)
                  </label>
                  <input
                    type="number"
                    id="totalBudget"
                    name="totalBudget"
                    value={formData.totalBudget}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for unlimited total budget
                  </p>
                  {errors.totalBudget && <p className="mt-1 text-sm text-red-600">{errors.totalBudget}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                    Start Date *
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.startDate && <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>}
                </div>

                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>}
                </div>
              </div>
            </div>

            {/* Creative Setup Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Creative Setup</h3>

              {/* Banner Size Selection - DISABLED */}
              {campaign.type === 'banner' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Banner Size (Cannot be changed)
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {['728x90', '300x250', '320x50', '160x600'].map((size) => (
                      <div
                        key={size}
                        className={`p-3 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${
                          campaign.banner_size === size ? 'border-blue-500' : 'border-gray-300'
                        }`}
                      >
                        <div className="flex items-center">
                          <input
                            type="radio"
                            checked={campaign.banner_size === size}
                            disabled
                            className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                          />
                          <label className="ml-2 block text-sm font-medium text-gray-400">
                            {size}
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Landing URL - Show for image banners, native, in-page push, and popup */}
              {((campaign.type === 'banner' && campaign.creative_type !== 2 && campaign.creative_type !== 'js') ||
                campaign.type === 'native' ||
                campaign.type === 'in_page_push' ||
                campaign.type === 'popup') && (
                <div className="mb-6">
                  <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                    Landing URL *
                  </label>
                  <input
                    type="url"
                    id="landingUrl"
                    name="landingUrl"
                    value={formData.landingUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <MacroInfo onMacroClick={handleMacroClick} />
                  {errors.landingUrl && <p className="mt-1 text-sm text-red-600">{errors.landingUrl}</p>}
                </div>
              )}

              {/* Banner Image Upload for Image Banner campaigns */}
              {campaign.type === 'banner' && campaign.creative_type !== 2 && campaign.creative_type !== 'js' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banner Image *
                  </label>
                  <div className="mt-1">
                    {bannerImagePreview ? (
                      <div className="relative inline-block">
                        <img
                          src={bannerImagePreview}
                          alt="Banner Preview"
                          className="max-w-full h-auto border border-gray-300 rounded-md"
                          style={{ maxHeight: '200px' }}
                        />
                        <button
                          type="button"
                          onClick={() => handleImageRemove('banner')}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ) : (
                      <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                        <div className="space-y-1 text-center">
                          <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                          <div className="text-sm text-gray-600">
                            <label htmlFor="bannerImage" className="cursor-pointer">
                              <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                              <input
                                id="bannerImage"
                                ref={bannerImageRef}
                                type="file"
                                className="sr-only"
                                accept="image/*"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) handleImageUpload(file, 'banner');
                                }}
                              />
                            </label>
                            <span> or drag and drop</span>
                          </div>
                          <p className="text-xs text-gray-500">PNG, JPG up to 10MB (exact banner size recommended)</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* JS Tag for JS Banner campaigns */}
              {campaign.type === 'banner' && (campaign.creative_type === 2 || campaign.creative_type === 'js') && (
                <div className="mb-6">
                  <label htmlFor="jsTag" className="block text-sm font-medium text-gray-700">
                    JavaScript Tag *
                  </label>
                  <textarea
                    id="jsTag"
                    name="jsTag"
                    rows={6}
                    value={formData.jsTag}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="<script>...</script>"
                    required
                  />
                  {errors.jsTag && <p className="mt-1 text-sm text-red-600">{errors.jsTag}</p>}
                </div>
              )}

              {/* Native Ad Creative */}
              {campaign.type === 'native' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="nativeTitle" className="block text-sm font-medium text-gray-700">
                        Native Ad Title *
                      </label>
                      <textarea
                        id="nativeTitle"
                        name="nativeTitle"
                        rows={3}
                        value={formData.nativeTitle}
                        onChange={handleChange}
                        maxLength={50}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.nativeTitle.length}/50 characters
                      </p>
                      {errors.nativeTitle && <p className="mt-1 text-sm text-red-600">{errors.nativeTitle}</p>}
                    </div>

                    <div>
                      <label htmlFor="nativeDescription" className="block text-sm font-medium text-gray-700">
                        Native Ad Description *
                      </label>
                      <textarea
                        id="nativeDescription"
                        name="nativeDescription"
                        rows={3}
                        value={formData.nativeDescription}
                        onChange={handleChange}
                        maxLength={150}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.nativeDescription.length}/150 characters
                      </p>
                      {errors.nativeDescription && <p className="mt-1 text-sm text-red-600">{errors.nativeDescription}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Native Icon
                    </label>
                    <div className="mt-1">
                      {nativeIconPreview ? (
                        <div className="relative inline-block">
                          <img
                            src={nativeIconPreview}
                            alt="Native Icon Preview"
                            className="w-20 h-20 object-cover rounded-md border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageRemove('nativeIcon')}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                          <div className="space-y-1 text-center">
                            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <div className="text-sm text-gray-600">
                              <label htmlFor="nativeIcon" className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                                <input
                                  id="nativeIcon"
                                  ref={nativeIconRef}
                                  type="file"
                                  className="sr-only"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file, 'nativeIcon');
                                  }}
                                />
                              </label>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG up to 2MB (64x64px recommended)</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Native Image
                    </label>
                    <div className="mt-1">
                      {nativeImagePreview ? (
                        <div className="relative inline-block">
                          <img
                            src={nativeImagePreview}
                            alt="Native Image Preview"
                            className="w-64 h-32 object-cover rounded-md border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageRemove('nativeImage')}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                          <div className="space-y-1 text-center">
                            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <div className="text-sm text-gray-600">
                              <label htmlFor="nativeImage" className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                                <input
                                  id="nativeImage"
                                  ref={nativeImageRef}
                                  type="file"
                                  className="sr-only"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file, 'nativeImage');
                                  }}
                                />
                              </label>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG up to 10MB (1200x628px recommended)</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* In-Page Push Creative */}
              {campaign.type === 'in_page_push' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="pushTitle" className="block text-sm font-medium text-gray-700">
                        Push Notification Title *
                      </label>
                      <textarea
                        id="pushTitle"
                        name="pushTitle"
                        rows={3}
                        value={formData.pushTitle}
                        onChange={handleChange}
                        maxLength={50}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.pushTitle.length}/50 characters
                      </p>
                      {errors.pushTitle && <p className="mt-1 text-sm text-red-600">{errors.pushTitle}</p>}
                    </div>

                    <div>
                      <label htmlFor="pushDescription" className="block text-sm font-medium text-gray-700">
                        Push Notification Description *
                      </label>
                      <textarea
                        id="pushDescription"
                        name="pushDescription"
                        rows={3}
                        value={formData.pushDescription}
                        onChange={handleChange}
                        maxLength={150}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.pushDescription.length}/150 characters
                      </p>
                      {errors.pushDescription && <p className="mt-1 text-sm text-red-600">{errors.pushDescription}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Push Notification Image
                    </label>
                    <div className="mt-1">
                      {pushImagePreview ? (
                        <div className="relative inline-block">
                          <img
                            src={pushImagePreview}
                            alt="Push Image Preview"
                            className="w-48 h-32 object-cover rounded-md border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageRemove('push')}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                          <div className="space-y-1 text-center">
                            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <div className="text-sm text-gray-600">
                              <label htmlFor="pushImage" className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                                <input
                                  id="pushImage"
                                  ref={pushImageRef}
                                  type="file"
                                  className="sr-only"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file, 'push');
                                  }}
                                />
                              </label>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG up to 10MB (1200x628px recommended)</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Geo Targeting Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Geo Targeting</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Country Targeting */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Countries
                  </label>
                  <div className="relative" ref={countryDropdownRef}>
                    <input
                      type="text"
                      value={countrySearch}
                      onChange={(e) => handleCountrySearch(e.target.value)}
                      onFocus={() => setShowCountryDropdown(true)}
                      placeholder="Search countries..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />

                    {showCountryDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {filteredCountries.length > 0 ? (
                          filteredCountries.map((country) => (
                            <button
                              key={country.isoCode}
                              type="button"
                              onClick={() => handleCountrySelect(country.isoCode)}
                              className="w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                              disabled={formData.targetingCountries.includes(country.isoCode)}
                            >
                              <span className={formData.targetingCountries.includes(country.isoCode) ? 'text-gray-400' : 'text-gray-900'}>
                                {country.name} ({country.isoCode})
                              </span>
                            </button>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500">No countries found</div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Selected Countries */}
                  {formData.targetingCountries.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.targetingCountries.map((countryCode) => {
                        const country = countries.find(c => c.isoCode === countryCode);
                        return (
                          <span
                            key={countryCode}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {country?.name || countryCode}
                            <button
                              type="button"
                              onClick={() => removeCountry(countryCode)}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                            >
                              ×
                            </button>
                          </span>
                        );
                      })}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Empty for worldwide targeting
                  </p>
                </div>

                {/* State Targeting */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target States/Regions
                  </label>
                  <div className="relative" ref={stateDropdownRef}>
                    <input
                      type="text"
                      value={stateSearch}
                      onChange={(e) => handleStateSearch(e.target.value)}
                      onFocus={() => setShowStateDropdown(true)}
                      placeholder={formData.targetingCountries.length > 0 ? "Search states..." : "Select countries first"}
                      disabled={formData.targetingCountries.length === 0}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    />

                    {showStateDropdown && formData.targetingCountries.length > 0 && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {filteredStates.length > 0 ? (
                          filteredStates.map((state) => (
                            <button
                              key={state.isoCode}
                              type="button"
                              onClick={() => handleStateSelect(state.isoCode)}
                              className="w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                              disabled={formData.targetingStates.includes(state.isoCode)}
                            >
                              <span className={formData.targetingStates.includes(state.isoCode) ? 'text-gray-400' : 'text-gray-900'}>
                                {state.name} ({state.isoCode})
                              </span>
                            </button>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500">No states found</div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Selected States */}
                  {formData.targetingStates.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {formData.targetingStates.map((stateCode) => {
                        const state = availableStates.find(s => s.isoCode === stateCode);
                        return (
                          <span
                            key={stateCode}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {state?.name || stateCode}
                            <button
                              type="button"
                              onClick={() => removeState(stateCode)}
                              className="ml-1 text-green-600 hover:text-green-800"
                            >
                              ×
                            </button>
                          </span>
                        );
                      })}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Empty for all states in selected countries
                  </p>
                </div>
              </div>
            </div>

            {/* Device/OS/Browser Targeting Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Device & Platform Targeting</h3>

              <div className="space-y-6">
                {/* Device Targeting */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Device Types
                    </label>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingDevices', devices)}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingDevices.length === devices.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    {devices.map((device) => (
                      <button
                        key={device}
                        type="button"
                        onClick={() => handleMultiSelect(device, 'targetingDevices')}
                        className={`p-3 border-2 rounded-lg text-sm font-medium transition-colors ${
                          formData.targetingDevices.includes(device)
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {device}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty for all device types
                  </p>
                </div>

                {/* Operating System Targeting */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Operating Systems
                    </label>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingOs', operatingSystems)}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingOs.length === operatingSystems.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    {operatingSystems.map((os) => (
                      <button
                        key={os}
                        type="button"
                        onClick={() => handleMultiSelect(os, 'targetingOs')}
                        className={`p-3 border-2 rounded-lg text-sm font-medium transition-colors ${
                          formData.targetingOs.includes(os)
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {os}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty for all operating systems
                  </p>
                </div>

                {/* Browser Targeting */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Browsers
                    </label>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingBrowsers', browsers)}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingBrowsers.length === browsers.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    {browsers.map((browser) => (
                      <button
                        key={browser}
                        type="button"
                        onClick={() => handleMultiSelect(browser, 'targetingBrowsers')}
                        className={`p-3 border-2 rounded-lg text-sm font-medium transition-colors ${
                          formData.targetingBrowsers.includes(browser)
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {browser}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty for all browsers
                  </p>
                </div>

                {/* Connection Type Targeting */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Connection Types
                    </label>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('targetingConnectionTypes', connectionTypes)}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      {formData.targetingConnectionTypes.length === connectionTypes.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-4 gap-3">
                    {connectionTypes.map((connectionType) => (
                      <button
                        key={connectionType}
                        type="button"
                        onClick={() => handleMultiSelect(connectionType, 'targetingConnectionTypes')}
                        className={`p-3 border-2 rounded-lg text-sm font-medium transition-colors ${
                          formData.targetingConnectionTypes.includes(connectionType)
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {connectionType}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty for all connection types
                  </p>
                </div>
              </div>
            </div>

            {/* Schedule Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule & Frequency</h3>

              <div className="space-y-6">
                {/* Daily Schedule */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Days of Week
                    </label>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('dailySchedule', daysOfWeek)}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      {formData.dailySchedule.length === daysOfWeek.length ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-7 gap-2">
                    {daysOfWeek.map((day) => (
                      <button
                        key={day}
                        type="button"
                        onClick={() => handleMultiSelect(day, 'dailySchedule')}
                        className={`p-2 border-2 rounded-lg text-xs font-medium transition-colors ${
                          formData.dailySchedule.includes(day)
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {day.substring(0, 3)}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty for all days of the week
                  </p>
                </div>

                {/* Hourly Schedule */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Hours of Day (24-hour format)
                    </label>
                    <button
                      type="button"
                      onClick={() => handleSelectAll('hourlySchedule', Array.from({length: 24}, (_, i) => i))}
                      className="text-xs text-blue-600 hover:text-blue-800"
                    >
                      {formData.hourlySchedule.length === 24 ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                  <div className="grid grid-cols-12 gap-1">
                    {Array.from({length: 24}, (_, i) => i).map((hour) => (
                      <button
                        key={hour}
                        type="button"
                        onClick={() => handleMultiSelect(hour, 'hourlySchedule')}
                        className={`p-2 border-2 rounded text-xs font-medium transition-colors ${
                          formData.hourlySchedule.includes(hour)
                            ? 'border-blue-600 bg-blue-50 text-blue-700'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {hour.toString().padStart(2, '0')}
                      </button>
                    ))}
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty for all hours of the day
                  </p>
                </div>

                {/* Frequency Cap */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Frequency Cap
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="frequencyCapValue" className="block text-sm font-medium text-gray-700">
                        Max Impressions
                      </label>
                      <input
                        type="number"
                        id="frequencyCapValue"
                        name="frequencyCapValue"
                        value={formData.frequencyCapValue}
                        onChange={handleChange}
                        min="1"
                        placeholder="0"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="frequencyCapPeriod" className="block text-sm font-medium text-gray-700">
                        Per Period
                      </label>
                      <select
                        id="frequencyCapPeriod"
                        name="frequencyCapPeriod"
                        value={formData.frequencyCapPeriod}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="hour">Per Hour</option>
                        <option value="day">Per Day</option>
                        <option value="week">Per Week</option>
                      </select>
                    </div>
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Empty or 0 for no frequency cap
                  </p>
                </div>
              </div>
            </div>

            {/* Whitelist/Blacklist Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Publisher & Zone Filtering</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Whitelist Section */}
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-gray-800">Whitelist (Allow Only)</h4>

                  <div>
                    <label htmlFor="whitelistPublishers" className="block text-sm font-medium text-gray-700">
                      Publisher IDs
                    </label>
                    <textarea
                      id="whitelistPublishers"
                      name="whitelistPublishers"
                      rows={3}
                      value={formData.whitelistPublishers}
                      onChange={handleChange}
                      placeholder="Enter publisher IDs separated by commas (e.g., 123, 456, 789)"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Only show ads on these publisher accounts
                    </p>
                  </div>

                  <div>
                    <label htmlFor="whitelistWebsites" className="block text-sm font-medium text-gray-700">
                      Website IDs
                    </label>
                    <textarea
                      id="whitelistWebsites"
                      name="whitelistWebsites"
                      rows={3}
                      value={formData.whitelistWebsites}
                      onChange={handleChange}
                      placeholder="Enter website IDs separated by commas (e.g., 123, 456, 789)"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Only show ads on these specific websites
                    </p>
                  </div>

                  <div>
                    <label htmlFor="whitelistZones" className="block text-sm font-medium text-gray-700">
                      Zone IDs
                    </label>
                    <textarea
                      id="whitelistZones"
                      name="whitelistZones"
                      rows={3}
                      value={formData.whitelistZones}
                      onChange={handleChange}
                      placeholder="Enter zone IDs separated by commas (e.g., 123, 456, 789)"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Only show ads in these specific ad zones
                    </p>
                  </div>
                </div>

                {/* Blacklist Section */}
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-gray-800">Blacklist (Block)</h4>

                  <div>
                    <label htmlFor="blacklistPublishers" className="block text-sm font-medium text-gray-700">
                      Publisher IDs
                    </label>
                    <textarea
                      id="blacklistPublishers"
                      name="blacklistPublishers"
                      rows={3}
                      value={formData.blacklistPublishers}
                      onChange={handleChange}
                      placeholder="Enter publisher IDs separated by commas (e.g., 123, 456, 789)"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Never show ads on these publisher accounts
                    </p>
                  </div>

                  <div>
                    <label htmlFor="blacklistWebsites" className="block text-sm font-medium text-gray-700">
                      Website IDs
                    </label>
                    <textarea
                      id="blacklistWebsites"
                      name="blacklistWebsites"
                      rows={3}
                      value={formData.blacklistWebsites}
                      onChange={handleChange}
                      placeholder="Enter website IDs separated by commas (e.g., 123, 456, 789)"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Never show ads on these specific websites
                    </p>
                  </div>

                  <div>
                    <label htmlFor="blacklistZones" className="block text-sm font-medium text-gray-700">
                      Zone IDs
                    </label>
                    <textarea
                      id="blacklistZones"
                      name="blacklistZones"
                      rows={3}
                      value={formData.blacklistZones}
                      onChange={handleChange}
                      placeholder="Enter zone IDs separated by commas (e.g., 123, 456, 789)"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Never show ads in these specific ad zones
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Important Note
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        Whitelist takes priority over blacklist. If you specify both whitelist and blacklist for the same type,
                        only the whitelist will be applied. Leave all fields empty to target all available inventory.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/advertiser/campaigns')}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Updating...' : 'Update Campaign'}
              </button>
            </div>

            {errors.submit && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
