'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Country, State } from 'country-state-city';

// Enhanced MacroInfo component with direct insertion functionality
interface MacroInfoProps {
  onMacroClick: (macro: string) => void;
}

const MacroInfo = ({ onMacroClick }: MacroInfoProps) => {
  const macros = [
    { name: '{click_id}', description: 'Unique click identifier' },
    { name: '{user_id}', description: 'User identifier' },
    { name: '{campaign_id}', description: 'Campaign identifier' },
    { name: '{publisher_id}', description: 'Publisher identifier' },
    { name: '{zone_id}', description: 'Zone identifier' },
    { name: '{country}', description: 'User country code' },
    { name: '{device_type}', description: 'Device type (desktop/mobile/tablet)' },
    { name: '{os}', description: 'Operating system' },
    { name: '{browser}', description: 'Browser name' },
    { name: '{timestamp}', description: 'Unix timestamp' },
    { name: '{ip}', description: 'User IP address' },
    { name: '{referer}', description: 'Referrer URL' },
    { name: '{user_lang}', description: 'User language code' },
    { name: '{ad_format}', description: 'Ad format type' },
    { name: '{page_url}', description: 'Referring page URL' },
    { name: '{os_version}', description: 'Operating system version' },
  ];

  return (
    <div className="mt-2 p-3 bg-gray-50 rounded-md">
      <p className="text-sm font-medium text-gray-700 mb-2">Available Macros (Click to insert):</p>
      <div className="grid grid-cols-4 gap-2">
        {macros.map((macro) => (
          <button
            key={macro.name}
            type="button"
            onClick={() => onMacroClick(macro.name)}
            className="p-2 bg-white rounded border hover:bg-blue-50 hover:border-blue-300 transition-colors cursor-pointer text-left"
          >
            <code className="text-xs font-mono text-blue-600 block">{macro.name}</code>
            <p className="text-xs text-gray-500 mt-1">{macro.description}</p>
          </button>
        ))}
      </div>
    </div>
  );
};

export default function EditCampaign() {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const [campaign, setCampaign] = useState(null);
  const [loading, setLoading] = useState(true);

  // Minimum bid settings from admin
  const [minimumBids, setMinimumBids] = useState({
    banner: '0.50',
    native: '0.75',
    in_page_push: '0.25',
    popup: '0.10',
  });

  const [formData, setFormData] = useState({
    name: '',
    cpmBid: '',
    dailyBudget: '',
    totalBudget: '',

    // Creative settings (editable)
    landingUrl: '',
    jsTag: '',
    nativeTitle: '',
    nativeDescription: '',
    pushTitle: '',
    pushDescription: '',

    // Geo targeting
    targetingCountries: [] as string[],
    targetingStates: [] as string[],

    // Device/OS/Browser targeting
    targetingDevices: [] as string[],
    targetingOs: [] as string[],
    targetingBrowsers: [] as string[],
    targetingConnectionTypes: [] as string[],

    // Schedule
    startDate: '',
    endDate: '',
    dailySchedule: [] as string[], // Days of week
    hourlySchedule: [] as number[], // Hours 0-23

    // Frequency cap
    frequencyCapValue: '',
    frequencyCapPeriod: 'day', // 'hour', 'day', 'week'

    // Whitelist/Blacklist
    whitelistPublishers: '',
    whitelistWebsites: '',
    whitelistZones: '',
    blacklistPublishers: '',
    blacklistWebsites: '',
    blacklistZones: '',
  });

  // Search states for dropdowns
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showStateDropdown, setShowStateDropdown] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Image upload states
  const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);
  const [nativeIconFile, setNativeIconFile] = useState<File | null>(null);
  const [nativeImageFile, setNativeImageFile] = useState<File | null>(null);
  const [pushImageFile, setPushImageFile] = useState<File | null>(null);

  // Image preview URLs
  const [bannerImagePreview, setBannerImagePreview] = useState<string>('');
  const [nativeIconPreview, setNativeIconPreview] = useState<string>('');
  const [nativeImagePreview, setNativeImagePreview] = useState<string>('');
  const [pushImagePreview, setPushImagePreview] = useState<string>('');

  // File input refs
  const bannerImageRef = useRef<HTMLInputElement>(null);
  const nativeIconRef = useRef<HTMLInputElement>(null);
  const nativeImageRef = useRef<HTMLInputElement>(null);
  const pushImageRef = useRef<HTMLInputElement>(null);

  // Country/State data
  const [countries, setCountries] = useState<any[]>([]);
  const [availableStates, setAvailableStates] = useState<any[]>([]);

  // Constants for form options
  const devices = ['Desktop', 'Mobile', 'Tablet'];
  const operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Other'];
  const connectionTypes = ['WiFi', 'Cellular', 'Broadband', 'Dial-up'];
  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Load countries on mount
  useEffect(() => {
    const allCountries = Country.getAllCountries();
    setCountries(allCountries);
  }, []);

  // Fetch minimum bid settings
  useEffect(() => {
    const fetchMinimumBids = async () => {
      try {
        const response = await fetch('/api/settings/minimum-bids');
        const result = await response.json();
        if (result.success) {
          setMinimumBids(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch minimum bids:', error);
        // Keep default values if fetch fails
      }
    };

    fetchMinimumBids();
  }, []);

  // Update available states when countries change
  useEffect(() => {
    if (formData.targetingCountries.length > 0) {
      const states: any[] = [];
      formData.targetingCountries.forEach(countryCode => {
        const countryStates = State.getStatesOfCountry(countryCode);
        states.push(...countryStates);
      });
      setAvailableStates(states);
    } else {
      setAvailableStates([]);
    }
  }, [formData.targetingCountries]);

  // Function to populate form data from campaign
  const populateFormData = (campaignData: any) => {
    setFormData({
      name: campaignData.name || '',
      cpmBid: campaignData.cpm_bid?.toString() || '',
      dailyBudget: campaignData.daily_budget?.toString() || '',
      totalBudget: campaignData.total_budget?.toString() || '',

      // Creative settings
      landingUrl: campaignData.landing_url || '',
      jsTag: campaignData.js_tag || '',
      nativeTitle: campaignData.native_title || '',
      nativeDescription: campaignData.native_description || '',
      pushTitle: campaignData.push_title || '',
      pushDescription: campaignData.push_description || '',

      // Geo targeting
      targetingCountries: campaignData.targeting_countries || [],
      targetingStates: campaignData.targeting_states || [],

      // Device/OS/Browser targeting
      targetingDevices: campaignData.targeting_devices || [],
      targetingOs: campaignData.targeting_os || [],
      targetingBrowsers: campaignData.targeting_browsers || [],
      targetingConnectionTypes: campaignData.targeting_connection_types || [],

      // Schedule
      startDate: campaignData.start_date ? new Date(campaignData.start_date).toISOString().split('T')[0] : '',
      endDate: campaignData.end_date ? new Date(campaignData.end_date).toISOString().split('T')[0] : '',
      dailySchedule: campaignData.daily_schedule || [],
      hourlySchedule: campaignData.hourly_schedule || [],

      // Frequency cap
      frequencyCapValue: campaignData.frequency_cap_value?.toString() || '',
      frequencyCapPeriod: campaignData.frequency_cap_period === 1 ? 'hour' :
                          campaignData.frequency_cap_period === 3 ? 'week' : 'day',

      // Whitelist/Blacklist
      whitelistPublishers: campaignData.whitelist_publishers || '',
      whitelistWebsites: campaignData.whitelist_websites || '',
      whitelistZones: campaignData.whitelist_zones || '',
      blacklistPublishers: campaignData.blacklist_publishers || '',
      blacklistWebsites: campaignData.blacklist_websites || '',
      blacklistZones: campaignData.blacklist_zones || '',
    });

    // Set image previews from existing campaign data
    setBannerImagePreview(campaignData.banner_image_url || '');
    setNativeIconPreview(campaignData.native_icon_url || '');
    setNativeImagePreview(campaignData.native_image_url || '');
    setPushImagePreview(campaignData.push_image_url || '');
  };

  // Form handlers
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Real-time CPM bid validation
    if (name === 'cpmBid' && campaign && value) {
      const bidValue = parseFloat(value);
      const minimumBid = parseFloat(minimumBids[campaign.type as keyof typeof minimumBids] || '0');

      if (!isNaN(bidValue) && bidValue > 0 && bidValue < minimumBid) {
        setErrors(prev => ({
          ...prev,
          cpmBid: `CPM bid must be at least $${minimumBid.toFixed(2)} for ${campaign.type} campaigns`
        }));
      }
    }

    // Real-time daily budget validation
    if (name === 'dailyBudget' && value) {
      const dailyBudgetValue = parseFloat(value);

      if (!isNaN(dailyBudgetValue) && dailyBudgetValue > 0 && dailyBudgetValue < 30) {
        setErrors(prev => ({
          ...prev,
          dailyBudget: 'Daily budget must be at least $30.00'
        }));
      }
    }
  };

  const handleMultiSelect = (value: string | number, field: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter((item: any) => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleSelectAll = (field: string, allValues: (string | number)[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].length === allValues.length ? [] : allValues
    }));
  };

  // Macro insertion handler
  const handleMacroClick = (macro: string) => {
    // For now, we'll just copy to clipboard
    navigator.clipboard.writeText(macro);
  };

  // Image upload handlers
  const handleImageUpload = (file: File, type: 'banner' | 'nativeIcon' | 'nativeImage' | 'push') => {
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        switch (type) {
          case 'banner':
            setBannerImageFile(file);
            setBannerImagePreview(result);
            break;
          case 'nativeIcon':
            setNativeIconFile(file);
            setNativeIconPreview(result);
            break;
          case 'nativeImage':
            setNativeImageFile(file);
            setNativeImagePreview(result);
            break;
          case 'push':
            setPushImageFile(file);
            setPushImagePreview(result);
            break;
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageRemove = (type: 'banner' | 'nativeIcon' | 'nativeImage' | 'push') => {
    switch (type) {
      case 'banner':
        setBannerImageFile(null);
        setBannerImagePreview('');
        if (bannerImageRef.current) bannerImageRef.current.value = '';
        break;
      case 'nativeIcon':
        setNativeIconFile(null);
        setNativeIconPreview('');
        if (nativeIconRef.current) nativeIconRef.current.value = '';
        break;
      case 'nativeImage':
        setNativeImageFile(null);
        setNativeImagePreview('');
        if (nativeImageRef.current) nativeImageRef.current.value = '';
        break;
      case 'push':
        setPushImageFile(null);
        setPushImagePreview('');
        if (pushImageRef.current) pushImageRef.current.value = '';
        break;
    }
  };

  // Handle country search and selection
  const handleCountrySearch = (searchTerm: string) => {
    setCountrySearch(searchTerm);
    setShowCountryDropdown(true);
  };

  const handleCountrySelect = (countryCode: string) => {
    if (!formData.targetingCountries.includes(countryCode)) {
      setFormData(prev => ({
        ...prev,
        targetingCountries: [...prev.targetingCountries, countryCode]
      }));
    }
    setCountrySearch('');
    setShowCountryDropdown(false);
  };

  const removeCountry = (countryCode: string) => {
    setFormData(prev => ({
      ...prev,
      targetingCountries: prev.targetingCountries.filter(c => c !== countryCode),
      targetingStates: prev.targetingStates.filter(s => {
        const country = countries.find(c => c.isoCode === countryCode);
        if (!country) return true;
        const countryStates = State.getStatesOfCountry(countryCode);
        return !countryStates.some(state => state.isoCode === s);
      })
    }));
  };

  // Handle state search and selection
  const handleStateSearch = (searchTerm: string) => {
    setStateSearch(searchTerm);
    setShowStateDropdown(true);
  };

  const handleStateSelect = (stateCode: string) => {
    if (!formData.targetingStates.includes(stateCode)) {
      setFormData(prev => ({
        ...prev,
        targetingStates: [...prev.targetingStates, stateCode]
      }));
    }
    setStateSearch('');
    setShowStateDropdown(false);
  };

  const removeState = (stateCode: string) => {
    setFormData(prev => ({
      ...prev,
      targetingStates: prev.targetingStates.filter(s => s !== stateCode)
    }));
  };

  // Filter functions for search
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  const filteredStates = availableStates.filter(state =>
    state.name.toLowerCase().includes(stateSearch.toLowerCase())
  );

  // Refs for dropdown containers
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  const stateDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click is outside country dropdown
      if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target as Node)) {
        setShowCountryDropdown(false);
      }
      // Check if click is outside state dropdown
      if (stateDropdownRef.current && !stateDropdownRef.current.contains(event.target as Node)) {
        setShowStateDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (!session?.user?.id || !params.id) return;

    const fetchCampaign = async () => {
      try {
        const response = await fetch(`/api/campaigns/${params.id}`);
        const data = await response.json();
        if (data.success) {
          setCampaign(data.campaign);
          // Populate form data with campaign data
          populateFormData(data.campaign);
        } else {
          router.push('/advertiser/campaigns');
        }
      } catch (error) {
        console.error('Error fetching campaign:', error);
        router.push('/advertiser/campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [session, params.id, router]);

  // Form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate required fields
      const newErrors: Record<string, string> = {};

      if (!formData.name.trim()) {
        newErrors.name = 'Campaign name is required';
      }

      // CPM bid validation with minimum bid check
      if (!formData.cpmBid) {
        newErrors.cpmBid = 'CPM bid is required';
      } else {
        const bidValue = parseFloat(formData.cpmBid);
        const campaignType = campaign?.type;
        const minimumBid = parseFloat(minimumBids[campaignType as keyof typeof minimumBids] || '0');

        if (isNaN(bidValue) || bidValue <= 0) {
          newErrors.cpmBid = 'CPM bid must be a valid positive number';
        } else if (bidValue < minimumBid) {
          newErrors.cpmBid = `CPM bid must be at least $${minimumBid.toFixed(2)} for ${campaignType} campaigns`;
        }
      }

      if (!formData.startDate) {
        newErrors.startDate = 'Start date is required';
      }

      // JS Tag validation for JS banner campaigns
      if (campaign?.type === 'banner' && (campaign.creative_type === 2 || campaign.creative_type === 'js')) {
        if (!formData.jsTag.trim()) {
          newErrors.jsTag = 'JavaScript tag is required for JS banner campaigns';
        }
      }

      // Daily budget validation (optional but if provided, must be >= $30)
      if (formData.dailyBudget) {
        const dailyBudgetValue = parseFloat(formData.dailyBudget);
        if (isNaN(dailyBudgetValue) || dailyBudgetValue <= 0) {
          newErrors.dailyBudget = 'Daily budget must be a valid positive number';
        } else if (dailyBudgetValue < 30) {
          newErrors.dailyBudget = 'Daily budget must be at least $30.00';
        }
      }

      // Total budget validation (optional but if provided, must be positive)
      if (formData.totalBudget) {
        const totalBudgetValue = parseFloat(formData.totalBudget);
        if (isNaN(totalBudgetValue) || totalBudgetValue <= 0) {
          newErrors.totalBudget = 'Total budget must be a valid positive number';
        }
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors);
        setIsLoading(false);
        return;
      }

      // Prepare form data for submission
      const submitFormData = new FormData();

      // Basic fields
      submitFormData.append('name', formData.name);
      submitFormData.append('cpmBid', formData.cpmBid);
      submitFormData.append('dailyBudget', formData.dailyBudget);
      submitFormData.append('totalBudget', formData.totalBudget);
      submitFormData.append('startDate', formData.startDate);
      submitFormData.append('endDate', formData.endDate);

      // Creative fields
      submitFormData.append('landingUrl', formData.landingUrl);
      submitFormData.append('jsTag', formData.jsTag);
      submitFormData.append('nativeTitle', formData.nativeTitle);
      submitFormData.append('nativeDescription', formData.nativeDescription);
      submitFormData.append('pushTitle', formData.pushTitle);
      submitFormData.append('pushDescription', formData.pushDescription);

      // Targeting fields
      submitFormData.append('targetingCountries', JSON.stringify(formData.targetingCountries));
      submitFormData.append('targetingStates', JSON.stringify(formData.targetingStates));
      submitFormData.append('targetingDevices', JSON.stringify(formData.targetingDevices));
      submitFormData.append('targetingOs', JSON.stringify(formData.targetingOs));
      submitFormData.append('targetingBrowsers', JSON.stringify(formData.targetingBrowsers));
      submitFormData.append('targetingConnectionTypes', JSON.stringify(formData.targetingConnectionTypes));

      // Schedule fields
      submitFormData.append('dailySchedule', JSON.stringify(formData.dailySchedule));
      submitFormData.append('hourlySchedule', JSON.stringify(formData.hourlySchedule));

      // Frequency cap fields
      submitFormData.append('frequencyCapValue', formData.frequencyCapValue);
      submitFormData.append('frequencyCapPeriod', formData.frequencyCapPeriod);

      // Whitelist/Blacklist fields
      submitFormData.append('whitelistPublishers', formData.whitelistPublishers);
      submitFormData.append('whitelistWebsites', formData.whitelistWebsites);
      submitFormData.append('whitelistZones', formData.whitelistZones);
      submitFormData.append('blacklistPublishers', formData.blacklistPublishers);
      submitFormData.append('blacklistWebsites', formData.blacklistWebsites);
      submitFormData.append('blacklistZones', formData.blacklistZones);

      // Image files
      if (bannerImageFile) {
        submitFormData.append('bannerImage', bannerImageFile);
      }
      if (nativeIconFile) {
        submitFormData.append('nativeIcon', nativeIconFile);
      }
      if (nativeImageFile) {
        submitFormData.append('nativeImage', nativeImageFile);
      }
      if (pushImageFile) {
        submitFormData.append('pushImage', pushImageFile);
      }

      const response = await fetch(`/api/campaigns/${params.id}`, {
        method: 'PUT',
        body: submitFormData,
      });

      const data = await response.json();

      if (data.success) {
        router.push('/advertiser/campaigns');
      } else {
        setErrors({ submit: data.message || 'Failed to update campaign' });
      }
    } catch (error) {
      console.error('Error updating campaign:', error);
      setErrors({ submit: 'An error occurred while updating the campaign' });
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading campaign...</p>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Campaign not found</h1>
          <p className="mt-2 text-gray-500">The campaign you&apos;re looking for doesn&apos;t exist.</p>
          <button
            onClick={() => router.push('/advertiser/campaigns')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Campaigns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Campaign</h1>
              <p className="mt-2 text-gray-600">
                Campaign ID: #{campaign.id} | Type: {campaign.type}
              </p>
            </div>
            <button
              onClick={() => router.push('/advertiser/campaigns')}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              Back to Campaigns
            </button>
          </div>
        </div>

        {/* Main Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-8">
            {/* Campaign Type Section - DISABLED */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Type (Cannot be changed)</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 border-blue-500`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'banner'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      Banner Ads
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Display banner advertisements</p>
                </div>

                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'native' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'native'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      Native Ads
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Content-style advertisements</p>
                </div>

                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'in_page_push' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'in_page_push'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      In-Page Push
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Push-style notifications</p>
                </div>

                <div className={`relative p-4 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${campaign.type === 'popup' ? 'border-blue-500' : 'border-gray-300'}`}>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      checked={campaign.type === 'popup'}
                      disabled
                      className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                    />
                    <label className="ml-3 block text-sm font-medium text-gray-400">
                      Popup Ads
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">Popup advertisements</p>
                </div>
              </div>
            </div>

            {/* Campaign Details Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Campaign Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                </div>

                <div>
                  <label htmlFor="cpmBid" className="block text-sm font-medium text-gray-700">
                    CPM Bid (USD) *
                  </label>
                  <input
                    type="number"
                    id="cpmBid"
                    name="cpmBid"
                    value={formData.cpmBid}
                    onChange={handleChange}
                    step="0.01"
                    min="0.01"
                    placeholder={`Min: ${minimumBids[campaign.type as keyof typeof minimumBids] || '0.01'}`}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {!errors.cpmBid && (
                    <p className="mt-1 text-xs text-gray-500">
                      Minimum CPM bid for {campaign.type} campaigns: ${minimumBids[campaign.type as keyof typeof minimumBids] || '0.01'}
                    </p>
                  )}
                  {errors.cpmBid && <p className="mt-1 text-sm text-red-600">{errors.cpmBid}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <label htmlFor="dailyBudget" className="block text-sm font-medium text-gray-700">
                    Daily Budget (USD)
                  </label>
                  <input
                    type="number"
                    id="dailyBudget"
                    name="dailyBudget"
                    value={formData.dailyBudget}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for unlimited daily budget. Minimum: $30 if set
                  </p>
                  {errors.dailyBudget && <p className="mt-1 text-sm text-red-600">{errors.dailyBudget}</p>}
                </div>

                <div>
                  <label htmlFor="totalBudget" className="block text-sm font-medium text-gray-700">
                    Total Budget (USD)
                  </label>
                  <input
                    type="number"
                    id="totalBudget"
                    name="totalBudget"
                    value={formData.totalBudget}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for unlimited total budget
                  </p>
                  {errors.totalBudget && <p className="mt-1 text-sm text-red-600">{errors.totalBudget}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                    Start Date *
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.startDate && <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>}
                </div>

                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Empty to run indefinitely
                  </p>
                  {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>}
                </div>
              </div>
            </div>

            {/* Creative Setup Section - DISABLED for ad format selection */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Creative Setup</h3>

              {/* Banner Size Selection - DISABLED */}
              {campaign.type === 'banner' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Banner Size (Cannot be changed)
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {['728x90', '300x250', '320x50', '160x600'].map((size) => (
                      <div
                        key={size}
                        className={`p-3 border-2 rounded-lg cursor-not-allowed bg-gray-100 ${
                          campaign.banner_size === size ? 'border-blue-500' : 'border-gray-300'
                        }`}
                      >
                        <div className="flex items-center">
                          <input
                            type="radio"
                            checked={campaign.banner_size === size}
                            disabled
                            className="h-4 w-4 text-blue-600 border-gray-300 cursor-not-allowed"
                          />
                          <label className="ml-2 block text-sm font-medium text-gray-400">
                            {size}
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Landing URL - Show for image banners, native, and in-page push */}
              {((campaign.type === 'banner' && campaign.creative_type !== 2 && campaign.creative_type !== 'js') || campaign.type === 'native' || campaign.type === 'in_page_push') && (
                <div className="mb-6">
                  <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                    Landing URL *
                  </label>
                  <input
                    type="url"
                    id="landingUrl"
                    name="landingUrl"
                    value={formData.landingUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <MacroInfo onMacroClick={handleMacroClick} />
                  {errors.landingUrl && <p className="mt-1 text-sm text-red-600">{errors.landingUrl}</p>}
                </div>
              )}

              {/* Native Ad Creative */}
              {campaign.type === 'native' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="nativeTitle" className="block text-sm font-medium text-gray-700">
                        Native Ad Title *
                      </label>
                      <textarea
                        id="nativeTitle"
                        name="nativeTitle"
                        rows={3}
                        value={formData.nativeTitle}
                        onChange={handleChange}
                        maxLength={50}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.nativeTitle.length}/50 characters
                      </p>
                      {errors.nativeTitle && <p className="mt-1 text-sm text-red-600">{errors.nativeTitle}</p>}
                    </div>

                    <div>
                      <label htmlFor="nativeDescription" className="block text-sm font-medium text-gray-700">
                        Native Ad Description *
                      </label>
                      <textarea
                        id="nativeDescription"
                        name="nativeDescription"
                        rows={3}
                        value={formData.nativeDescription}
                        onChange={handleChange}
                        maxLength={150}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.nativeDescription.length}/150 characters
                      </p>
                      {errors.nativeDescription && <p className="mt-1 text-sm text-red-600">{errors.nativeDescription}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Native Icon
                    </label>
                    <div className="mt-1">
                      {nativeIconPreview ? (
                        <div className="relative inline-block">
                          <img
                            src={nativeIconPreview}
                            alt="Native Icon Preview"
                            className="w-20 h-20 object-cover rounded-md border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageRemove('nativeIcon')}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                          <div className="space-y-1 text-center">
                            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <div className="text-sm text-gray-600">
                              <label htmlFor="nativeIcon" className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                                <input
                                  id="nativeIcon"
                                  ref={nativeIconRef}
                                  type="file"
                                  className="sr-only"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file, 'nativeIcon');
                                  }}
                                />
                              </label>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG up to 2MB (64x64px recommended)</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Native Image
                    </label>
                    <div className="mt-1">
                      {nativeImagePreview ? (
                        <div className="relative inline-block">
                          <img
                            src={nativeImagePreview}
                            alt="Native Image Preview"
                            className="w-64 h-32 object-cover rounded-md border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageRemove('nativeImage')}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                          <div className="space-y-1 text-center">
                            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <div className="text-sm text-gray-600">
                              <label htmlFor="nativeImage" className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                                <input
                                  id="nativeImage"
                                  ref={nativeImageRef}
                                  type="file"
                                  className="sr-only"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file, 'nativeImage');
                                  }}
                                />
                              </label>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG up to 10MB (1200x628px recommended)</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* In-Page Push Creative */}
              {campaign.type === 'in_page_push' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="pushTitle" className="block text-sm font-medium text-gray-700">
                        Push Notification Title *
                      </label>
                      <textarea
                        id="pushTitle"
                        name="pushTitle"
                        rows={3}
                        value={formData.pushTitle}
                        onChange={handleChange}
                        maxLength={50}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.pushTitle.length}/50 characters
                      </p>
                      {errors.pushTitle && <p className="mt-1 text-sm text-red-600">{errors.pushTitle}</p>}
                    </div>

                    <div>
                      <label htmlFor="pushDescription" className="block text-sm font-medium text-gray-700">
                        Push Notification Description *
                      </label>
                      <textarea
                        id="pushDescription"
                        name="pushDescription"
                        rows={3}
                        value={formData.pushDescription}
                        onChange={handleChange}
                        maxLength={150}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.pushDescription.length}/150 characters
                      </p>
                      {errors.pushDescription && <p className="mt-1 text-sm text-red-600">{errors.pushDescription}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Push Notification Image
                    </label>
                    <div className="mt-1">
                      {pushImagePreview ? (
                        <div className="relative inline-block">
                          <img
                            src={pushImagePreview}
                            alt="Push Image Preview"
                            className="w-48 h-32 object-cover rounded-md border border-gray-300"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageRemove('push')}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                          <div className="space-y-1 text-center">
                            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <div className="text-sm text-gray-600">
                              <label htmlFor="pushImage" className="cursor-pointer">
                                <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                                <input
                                  id="pushImage"
                                  ref={pushImageRef}
                                  type="file"
                                  className="sr-only"
                                  accept="image/*"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file, 'push');
                                  }}
                                />
                              </label>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG up to 10MB (360x240px recommended)</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Banner Creative - Only for image banners */}
              {campaign.type === 'banner' && campaign.creative_type !== 2 && campaign.creative_type !== 'js' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banner Image
                  </label>
                  <div className="mt-1">
                    {bannerImagePreview ? (
                      <div className="relative inline-block">
                        <img
                          src={bannerImagePreview}
                          alt="Banner Image Preview"
                          className="w-80 h-40 object-cover rounded-md border border-gray-300"
                        />
                        <button
                          type="button"
                          onClick={() => handleImageRemove('banner')}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ) : (
                      <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400">
                        <div className="space-y-1 text-center">
                          <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                          <div className="text-sm text-gray-600">
                            <label htmlFor="bannerImage" className="cursor-pointer">
                              <span className="text-blue-600 hover:text-blue-500">Upload a file</span>
                              <input
                                id="bannerImage"
                                ref={bannerImageRef}
                                type="file"
                                className="sr-only"
                                accept="image/*"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) handleImageUpload(file, 'banner');
                                }}
                              />
                            </label>
                            <span> or drag and drop</span>
                          </div>
                          <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Landing URL for Popup campaigns */}
              {campaign.type === 'popup' && (
                <div className="mb-6">
                  <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                    Landing URL *
                  </label>
                  <input
                    type="url"
                    id="landingUrl"
                    name="landingUrl"
                    value={formData.landingUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <MacroInfo onMacroClick={handleMacroClick} />
                  {errors.landingUrl && <p className="mt-1 text-sm text-red-600">{errors.landingUrl}</p>}
                </div>
              )}

              {/* JS Tag for JS Banner campaigns */}
              {(campaign.type === 'banner' && (campaign.creative_type === 2 || campaign.creative_type === 'js')) && (
                <div>
                  <label htmlFor="jsTag" className="block text-sm font-medium text-gray-700 mb-2">
                    JavaScript Tag *
                  </label>
                  <textarea
                    id="jsTag"
                    name="jsTag"
                    rows={8}
                    value={formData.jsTag}
                    onChange={handleChange}
                    placeholder="Enter your JavaScript tag code here..."
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm resize-none"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Enter the complete JavaScript tag code for your banner campaign
                  </p>
                  {errors.jsTag && <p className="mt-1 text-sm text-red-600">{errors.jsTag}</p>}
                </div>
              )}
            </div>

            {/* Targeting Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Targeting Options</h3>

              {/* Geographic Targeting */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-800 mb-4">Geographic Targeting</h4>

                {/* Countries */}
                <div className="mb-6" ref={countryDropdownRef}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Countries
                  </label>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-700">Search and select countries:</span>
                    {countries.length > 0 && (
                      <button
                        type="button"
                        onClick={() => handleSelectAll('targetingCountries', countries.map(c => c.isoCode))}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {formData.targetingCountries.length === countries.length ? 'Deselect All' : 'Select All'}
                      </button>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search countries..."
                      value={countrySearch}
                      onChange={(e) => setCountrySearch(e.target.value)}
                      onFocus={() => setShowCountryDropdown(true)}
                      onClick={() => setShowCountryDropdown(true)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                      {showCountryDropdown && (
                        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                          <div className="px-3 py-2 border-b border-gray-200">
                            <button
                              type="button"
                              onClick={() => handleSelectAll('targetingCountries', countries.map(c => c.isoCode))}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              {formData.targetingCountries.length === countries.length ? 'Deselect All' : 'Select All'}
                            </button>
                          </div>
                          {filteredCountries.map((country) => (
                            <div
                              key={country.isoCode}
                              onClick={() => handleMultiSelect(country.isoCode, 'targetingCountries')}
                              className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50 ${
                                formData.targetingCountries.includes(country.isoCode) ? 'bg-blue-50 text-blue-900' : 'text-gray-900'
                              }`}
                            >
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  checked={formData.targetingCountries.includes(country.isoCode)}
                                  onChange={() => {}}
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                />
                                <span className="ml-3 block truncate">{country.name}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {formData.targetingCountries.map((countryCode) => {
                        const country = countries.find(c => c.isoCode === countryCode);
                        return country ? (
                          <span
                            key={countryCode}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {country.name}
                            <button
                              type="button"
                              onClick={() => handleMultiSelect(countryCode, 'targetingCountries')}
                              className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600"
                            >
                              ×
                            </button>
                          </span>
                        ) : null;
                      })}
                    </div>
                  </div>

                  {/* States */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      States/Provinces
                    </label>
                    <div className="relative" ref={stateDropdownRef}>
                      <input
                        type="text"
                        placeholder="Search states..."
                        value={stateSearch}
                        onChange={(e) => setStateSearch(e.target.value)}
                        onFocus={() => setShowStateDropdown(true)}
                        onClick={() => setShowStateDropdown(true)}
                        disabled={availableStates.length === 0}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                      />
                      {showStateDropdown && availableStates.length > 0 && (
                        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                          <div className="px-3 py-2 border-b border-gray-200">
                            <button
                              type="button"
                              onClick={() => handleSelectAll('targetingStates', availableStates.map(s => s.isoCode))}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              {formData.targetingStates.length === availableStates.length ? 'Deselect All' : 'Select All'}
                            </button>
                          </div>
                          {filteredStates.map((state) => (
                            <div
                              key={state.isoCode}
                              onClick={() => handleMultiSelect(state.isoCode, 'targetingStates')}
                              className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50 ${
                                formData.targetingStates.includes(state.isoCode) ? 'bg-blue-50 text-blue-900' : 'text-gray-900'
                              }`}
                            >
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  checked={formData.targetingStates.includes(state.isoCode)}
                                  onChange={() => {}}
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                                />
                                <span className="ml-3 block truncate">{state.name}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {formData.targetingStates.map((stateCode) => {
                        const state = availableStates.find(s => s.isoCode === stateCode);
                        return state ? (
                          <span
                            key={stateCode}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {state.name}
                            <button
                              type="button"
                              onClick={() => handleMultiSelect(stateCode, 'targetingStates')}
                              className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-400 hover:bg-green-200 hover:text-green-600"
                            >
                              ×
                            </button>
                          </span>
                        ) : null;
                      })}
                    </div>
                    {availableStates.length === 0 && (
                      <p className="mt-1 text-xs text-gray-500">Select countries first to enable state targeting</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Device & Platform Targeting */}
              <div className="mb-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Device & Platform Targeting</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Device Types */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Device Types
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Select devices:</span>
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targetingDevices', devices)}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targetingDevices.length === devices.length ? 'Deselect All' : 'Select All'}
                        </button>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        {devices.map((device) => (
                          <label key={device} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={formData.targetingDevices.includes(device)}
                              onChange={() => handleMultiSelect(device, 'targetingDevices')}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{device}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Operating Systems */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Operating Systems
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Select OS:</span>
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targetingOs', operatingSystems)}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targetingOs.length === operatingSystems.length ? 'Deselect All' : 'Select All'}
                        </button>
                      </div>
                      <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                        {operatingSystems.map((os) => (
                          <label key={os} className="flex items-center py-1">
                            <input
                              type="checkbox"
                              checked={formData.targetingOs.includes(os)}
                              onChange={() => handleMultiSelect(os, 'targetingOs')}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{os}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  {/* Browsers */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Browsers
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Select browsers:</span>
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targetingBrowsers', browsers)}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targetingBrowsers.length === browsers.length ? 'Deselect All' : 'Select All'}
                        </button>
                      </div>
                      <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                        {browsers.map((browser) => (
                          <label key={browser} className="flex items-center py-1">
                            <input
                              type="checkbox"
                              checked={formData.targetingBrowsers.includes(browser)}
                              onChange={() => handleMultiSelect(browser, 'targetingBrowsers')}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{browser}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Connection Types */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Connection Types
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">Select connections:</span>
                        <button
                          type="button"
                          onClick={() => handleSelectAll('targetingConnectionTypes', connectionTypes)}
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {formData.targetingConnectionTypes.length === connectionTypes.length ? 'Deselect All' : 'Select All'}
                        </button>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        {connectionTypes.map((connection) => (
                          <label key={connection} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={formData.targetingConnectionTypes.includes(connection)}
                              onChange={() => handleMultiSelect(connection, 'targetingConnectionTypes')}
                              className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{connection}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Scheduling Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Scheduling</h3>

              {/* Daily Schedule */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Days of Week
                </label>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-700">Select days:</span>
                  <button
                    type="button"
                    onClick={() => handleSelectAll('dailySchedule', daysOfWeek)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {formData.dailySchedule.length === daysOfWeek.length ? 'Deselect All' : 'Select All'}
                  </button>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {daysOfWeek.map((day) => (
                    <label key={day} className="flex items-center p-2 border rounded-md hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={formData.dailySchedule.includes(day)}
                        onChange={() => handleMultiSelect(day, 'dailySchedule')}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">{day}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Hourly Schedule */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Hours of Day (24-hour format)
                </label>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-700">Select hours:</span>
                  <button
                    type="button"
                    onClick={() => handleSelectAll('hourlySchedule', Array.from({length: 24}, (_, i) => i))}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {formData.hourlySchedule.length === 24 ? 'Deselect All' : 'Select All'}
                  </button>
                </div>
                <div className="grid grid-cols-8 gap-1">
                  {Array.from({length: 24}, (_, hour) => (
                    <button
                      key={hour}
                      type="button"
                      onClick={() => handleMultiSelect(hour, 'hourlySchedule')}
                      className={`p-2 text-xs border rounded text-center hover:bg-gray-50 ${
                        formData.hourlySchedule.includes(hour)
                          ? 'bg-blue-100 border-blue-300 text-blue-800'
                          : 'bg-white border-gray-300 text-gray-700'
                      }`}
                    >
                      {hour.toString().padStart(2, '0')}:00
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Frequency Cap Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Frequency Cap</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="frequencyCapValue" className="block text-sm font-medium text-gray-700">
                    Maximum Impressions
                  </label>
                  <input
                    type="number"
                    id="frequencyCapValue"
                    name="frequencyCapValue"
                    value={formData.frequencyCapValue}
                    onChange={handleChange}
                    min="1"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for no frequency cap
                  </p>
                </div>

                <div>
                  <label htmlFor="frequencyCapPeriod" className="block text-sm font-medium text-gray-700">
                    Time Period
                  </label>
                  <select
                    id="frequencyCapPeriod"
                    name="frequencyCapPeriod"
                    value={formData.frequencyCapPeriod}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="hour">Per Hour</option>
                    <option value="day">Per Day</option>
                    <option value="week">Per Week</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Whitelist/Blacklist Section */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Publisher Whitelist/Blacklist</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Whitelist */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">Whitelist (Allow Only)</h4>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="whitelistPublishers" className="block text-sm font-medium text-gray-700">
                        Publisher IDs
                      </label>
                      <div className="mt-1 relative">
                        <textarea
                          id="whitelistPublishers"
                          name="whitelistPublishers"
                          rows={3}
                          value={formData.whitelistPublishers}
                          onChange={handleChange}
                          placeholder="Enter Publisher IDs, one per line"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="whitelistWebsites" className="block text-sm font-medium text-gray-700">
                        Website IDs
                      </label>
                      <div className="mt-1 relative">
                        <textarea
                          id="whitelistWebsites"
                          name="whitelistWebsites"
                          rows={3}
                          value={formData.whitelistWebsites}
                          onChange={handleChange}
                          placeholder="Enter Website IDs, one per line"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="whitelistZones" className="block text-sm font-medium text-gray-700">
                        Zone IDs
                      </label>
                      <div className="mt-1 relative">
                        <textarea
                          id="whitelistZones"
                          name="whitelistZones"
                          rows={3}
                          value={formData.whitelistZones}
                          onChange={handleChange}
                          placeholder="Enter Zone IDs, one per line"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Blacklist */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">Blacklist (Block)</h4>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="blacklistPublishers" className="block text-sm font-medium text-gray-700">
                        Publisher IDs
                      </label>
                      <div className="mt-1 relative">
                        <textarea
                          id="blacklistPublishers"
                          name="blacklistPublishers"
                          rows={3}
                          value={formData.blacklistPublishers}
                          onChange={handleChange}
                          placeholder="Enter Publisher IDs, one per line"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="blacklistWebsites" className="block text-sm font-medium text-gray-700">
                        Website IDs
                      </label>
                      <div className="mt-1 relative">
                        <textarea
                          id="blacklistWebsites"
                          name="blacklistWebsites"
                          rows={3}
                          value={formData.blacklistWebsites}
                          onChange={handleChange}
                          placeholder="Enter Website IDs, one per line"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="blacklistZones" className="block text-sm font-medium text-gray-700">
                        Zone IDs
                      </label>
                      <div className="mt-1 relative">
                        <textarea
                          id="blacklistZones"
                          name="blacklistZones"
                          rows={3}
                          value={formData.blacklistZones}
                          onChange={handleChange}
                          placeholder="Enter Zone IDs, one per line"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Section */}
            <div className="border-t border-gray-200 pt-6">
              {errors.submit && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{errors.submit}</p>
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => router.push('/advertiser/campaigns')}
                  className="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}