'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Country, State } from 'country-state-city';

// Enhanced MacroInfo component with direct insertion functionality
interface MacroInfoProps {
  onMacroClick: (macro: string) => void;
}

const MacroInfo = ({ onMacroClick }: MacroInfoProps) => {
  const macros = [
    { name: '{click_id}', description: 'Unique click identifier' },
    { name: '{user_id}', description: 'User identifier' },
    { name: '{campaign_id}', description: 'Campaign identifier' },
    { name: '{publisher_id}', description: 'Publisher identifier' },
    { name: '{zone_id}', description: 'Zone identifier' },
    { name: '{country}', description: 'User country code' },
    { name: '{device_type}', description: 'Device type (desktop/mobile/tablet)' },
    { name: '{os}', description: 'Operating system' },
    { name: '{browser}', description: 'Browser name' },
    { name: '{timestamp}', description: 'Unix timestamp' },
    { name: '{ip}', description: 'User IP address' },
    { name: '{referer}', description: 'Referrer URL' },
    { name: '{user_lang}', description: 'User language code' },
    { name: '{ad_format}', description: 'Ad format type' },
    { name: '{page_url}', description: 'Referring page URL' },
    { name: '{os_version}', description: 'Operating system version' },
  ];

  return (
    <div className="mt-2 p-3 bg-gray-50 rounded-md">
      <p className="text-sm font-medium text-gray-700 mb-2">Available Macros (Click to insert):</p>
      <div className="grid grid-cols-4 gap-2">
        {macros.map((macro) => (
          <button
            key={macro.name}
            type="button"
            onClick={() => onMacroClick(macro.name)}
            className="p-2 bg-white rounded border hover:bg-blue-50 hover:border-blue-300 transition-colors cursor-pointer text-left"
          >
            <code className="text-xs font-mono text-blue-600 block">{macro.name}</code>
            <p className="text-xs text-gray-500 mt-1">{macro.description}</p>
          </button>
        ))}
      </div>
    </div>
  );
};

export default function CreateCampaign() {
  const router = useRouter();
  const { data: session } = useSession();

  // Minimum bid settings from admin
  const [minimumBids, setMinimumBids] = useState({
    banner: '0.50',
    native: '0.75',
    in_page_push: '0.25',
    popup: '0.10',
  });

  const [formData, setFormData] = useState({
    type: '', // Required, no default
    name: '',
    cpmBid: '',
    dailyBudget: '',
    totalBudget: '',

    // Creative settings
    bannerSize: '',
    creativeType: '', // 'image' or 'js'
    imageFile: null as File | null,
    jsTag: '',
    landingUrl: '',

    // Native ad specific
    nativeTitle: '',
    nativeDescription: '',
    nativeIconFile: null as File | null,
    nativeImageFile: null as File | null,

    // In-page push specific
    pushTitle: '',
    pushDescription: '',
    pushImageFile: null as File | null,

    // Geo targeting
    targetingCountries: [] as string[],
    targetingStates: [] as string[],

    // Device/OS/Browser targeting
    targetingDevices: [] as string[],
    targetingOs: [] as string[],
    targetingBrowsers: [] as string[],
    targetingConnectionTypes: [] as string[],

    // Schedule
    startDate: '',
    endDate: '',
    dailySchedule: [] as string[], // Days of week
    hourlySchedule: [] as number[], // Hours 0-23

    // Frequency cap
    frequencyCapValue: '',
    frequencyCapPeriod: 'day', // 'hour', 'day', 'week'

    // Whitelist/Blacklist
    whitelistPublishers: '',
    whitelistWebsites: '',
    whitelistZones: '',
    blacklistPublishers: '',
    blacklistWebsites: '',
    blacklistZones: '',
  });

  // Search states for dropdowns
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showStateDropdown, setShowStateDropdown] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Country/State data
  const [countries, setCountries] = useState<any[]>([]);
  const [availableStates, setAvailableStates] = useState<any[]>([]);

  // Constants for form options
  const bannerSizes = [
    { value: '300x250', label: '300x250 (Medium Rectangle)' },
    { value: '300x600', label: '300x600 (Half Page)' },
    { value: '160x600', label: '160x600 (Wide Skyscraper)' },
    { value: '728x90', label: '728x90 (Leaderboard)' },
    { value: '320x50', label: '320x50 (Mobile Banner)' },
    { value: '900x300', label: '900x300 (Large Banner)' },
  ];

  const devices = ['Desktop', 'Mobile', 'Tablet'];
  const operatingSystems = ['Windows', 'macOS', 'Linux', 'iOS', 'Android', 'Other'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera', 'Other'];
  const connectionTypes = ['WiFi', 'Cellular', 'Broadband', 'Dial-up'];
  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Load countries on mount
  useEffect(() => {
    const allCountries = Country.getAllCountries();
    setCountries(allCountries);
  }, []);

  // Update available states when countries change
  useEffect(() => {
    if (formData.targetingCountries.length > 0) {
      const states: any[] = [];
      formData.targetingCountries.forEach(countryCode => {
        const countryStates = State.getStatesOfCountry(countryCode);
        states.push(...countryStates);
      });
      setAvailableStates(states);
    } else {
      setAvailableStates([]);
    }
  }, [formData.targetingCountries]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Real-time CPM bid validation
    if (name === 'cpmBid' && formData.type && value) {
      const bidValue = parseFloat(value);
      const minimumBid = parseFloat(minimumBids[formData.type as keyof typeof minimumBids] || '0');

      if (!isNaN(bidValue) && bidValue > 0 && bidValue < minimumBid) {
        setErrors(prev => ({
          ...prev,
          cpmBid: `CPM bid must be at least $${minimumBid.toFixed(2)} for ${formData.type} campaigns`
        }));
      }
    }

    // Real-time daily budget validation
    if (name === 'dailyBudget' && value) {
      const dailyBudgetValue = parseFloat(value);

      if (!isNaN(dailyBudgetValue) && dailyBudgetValue > 0 && dailyBudgetValue < 30) {
        setErrors(prev => ({
          ...prev,
          dailyBudget: 'Daily budget must be at least $30.00'
        }));
      }
    }

    // Clear CPM bid error when campaign type changes
    if (name === 'type' && errors.cpmBid) {
      setErrors(prev => ({ ...prev, cpmBid: '' }));
    }
  };

  const validateImageRatio = (file: File, expectedRatio: number): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const actualRatio = img.width / img.height;
        const tolerance = 0.1; // 10% tolerance
        resolve(Math.abs(actualRatio - expectedRatio) <= tolerance);
      };
      img.onerror = () => resolve(false);
      img.src = URL.createObjectURL(file);
    });
  };

  const getBannerRatio = (size: string): number => {
    const [width, height] = size.split('x').map(Number);
    return width / height;
  };

  const getExpectedRatio = (type: string, isIcon = false): number => {
    if (type === 'native') {
      return isIcon ? 1 : 4/3; // 1:1 for icon, 4:3 for image
    }
    if (type === 'in_page_push') {
      return 4/3; // 4:3 for push images
    }
    return 1; // Default
  };

  // Helper functions for multi-select
  const handleMultiSelect = (value: string | number, field: string) => {
    setFormData(prev => {
      const currentArray = prev[field as keyof typeof prev] as (string | number)[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [field]: newArray };
    });
  };

  // Insert macro into landing URL field
  const insertMacro = (macro: string) => {
    setFormData(prev => ({
      ...prev,
      landingUrl: prev.landingUrl + macro
    }));
  };

  const handleSelectAll = (field: string, allValues: (string | number)[]) => {
    setFormData(prev => {
      const currentArray = prev[field as keyof typeof prev] as (string | number)[];
      const newArray = currentArray.length === allValues.length ? [] : allValues;
      return { ...prev, [field]: newArray };
    });
  };

  // File change handler
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate image ratio based on ad type and field
    let expectedRatio = 1;
    let isValid = true;

    if (formData.type === 'banner' && field === 'imageFile' && formData.bannerSize) {
      expectedRatio = getBannerRatio(formData.bannerSize);
      isValid = await validateImageRatio(file, expectedRatio);
    } else if (formData.type === 'native') {
      if (field === 'nativeIconFile') {
        expectedRatio = 1; // 1:1 for icon
        isValid = await validateImageRatio(file, expectedRatio);
      } else if (field === 'nativeImageFile') {
        expectedRatio = 4/3; // 4:3 for image
        isValid = await validateImageRatio(file, expectedRatio);
      }
    } else if (formData.type === 'in_page_push' && field === 'pushImageFile') {
      expectedRatio = 4/3; // 4:3 for push images
      isValid = await validateImageRatio(file, expectedRatio);
    }

    if (!isValid) {
      setErrors(prev => ({
        ...prev,
        [field]: `Image must have a ${expectedRatio.toFixed(2)}:1 ratio`
      }));
      return;
    }

    setFormData(prev => ({ ...prev, [field]: file }));
    setErrors(prev => ({ ...prev, [field]: '' }));
  };

  // Filter functions for search
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  const filteredStates = availableStates.filter(state =>
    state.name.toLowerCase().includes(stateSearch.toLowerCase())
  );

  // Fetch minimum bid settings
  useEffect(() => {
    const fetchMinimumBids = async () => {
      try {
        const response = await fetch('/api/settings/minimum-bids');
        const result = await response.json();
        if (result.success) {
          setMinimumBids(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch minimum bids:', error);
        // Keep default values if fetch fails
      }
    };

    fetchMinimumBids();
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-container')) {
        setShowCountryDropdown(false);
        setShowStateDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    // Validation
    const newErrors: Record<string, string> = {};

    if (!formData.type) newErrors.type = 'Campaign type is required';
    if (!formData.name.trim()) newErrors.name = 'Campaign name is required';

    // CPM bid validation with minimum bid check
    if (!formData.cpmBid) {
      newErrors.cpmBid = 'CPM bid is required';
    } else {
      const bidValue = parseFloat(formData.cpmBid);
      const minimumBid = parseFloat(minimumBids[formData.type as keyof typeof minimumBids] || '0');

      if (isNaN(bidValue) || bidValue <= 0) {
        newErrors.cpmBid = 'CPM bid must be a valid positive number';
      } else if (bidValue < minimumBid) {
        newErrors.cpmBid = `CPM bid must be at least $${minimumBid.toFixed(2)} for ${formData.type} campaigns`;
      }
    }

    // Daily budget validation (optional but if provided, must be >= $30)
    if (formData.dailyBudget) {
      const dailyBudgetValue = parseFloat(formData.dailyBudget);
      if (isNaN(dailyBudgetValue) || dailyBudgetValue <= 0) {
        newErrors.dailyBudget = 'Daily budget must be a valid positive number';
      } else if (dailyBudgetValue < 30) {
        newErrors.dailyBudget = 'Daily budget must be at least $30.00';
      }
    }

    // Total budget validation (optional but if provided, must be positive)
    if (formData.totalBudget) {
      const totalBudgetValue = parseFloat(formData.totalBudget);
      if (isNaN(totalBudgetValue) || totalBudgetValue <= 0) {
        newErrors.totalBudget = 'Total budget must be a valid positive number';
      }
    }
    if (!formData.startDate) newErrors.startDate = 'Start date is required';

    // Creative validation based on type
    if (formData.type === 'banner') {
      if (!formData.bannerSize) newErrors.bannerSize = 'Banner size is required';
      if (!formData.creativeType) newErrors.creativeType = 'Creative type is required';
      if (formData.creativeType === 'image' && !formData.imageFile) {
        newErrors.imageFile = 'Image file is required';
      }
      if (formData.creativeType === 'js' && !formData.jsTag.trim()) {
        newErrors.jsTag = 'JS tag is required';
      }
      if (formData.creativeType === 'image' && !formData.landingUrl.trim()) {
        newErrors.landingUrl = 'Landing URL is required';
      }
    }

    if (formData.type === 'native') {
      if (!formData.nativeTitle.trim()) newErrors.nativeTitle = 'Native title is required';
      if (!formData.nativeDescription.trim()) newErrors.nativeDescription = 'Native description is required';
      if (!formData.nativeImageFile) newErrors.nativeImageFile = 'Native image is required';
      if (!formData.landingUrl.trim()) newErrors.landingUrl = 'Landing URL is required';
    }

    if (formData.type === 'in_page_push') {
      if (!formData.pushTitle.trim()) newErrors.pushTitle = 'Push title is required';
      if (!formData.pushDescription.trim()) newErrors.pushDescription = 'Push description is required';
      if (!formData.pushImageFile) newErrors.pushImageFile = 'Push image is required';
      if (!formData.landingUrl.trim()) newErrors.landingUrl = 'Landing URL is required';
    }

    if (formData.type === 'popup') {
      if (!formData.landingUrl.trim()) newErrors.landingUrl = 'Landing URL is required';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsLoading(false);
      return;
    }

    try {
      // Create FormData for file uploads
      const submitData = new FormData();

      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (value instanceof File) {
          submitData.append(key, value);
        } else if (Array.isArray(value)) {
          submitData.append(key, JSON.stringify(value));
        } else if (value !== null && value !== undefined) {
          submitData.append(key, value.toString());
        }
      });

      const response = await fetch('/api/campaigns', {
        method: 'POST',
        body: submitData,
      });

      if (response.ok) {
        router.push('/advertiser/campaigns');
      } else {
        const error = await response.json();
        setErrors({ submit: error.message || 'Failed to create campaign' });
      }
    } catch (error) {
      setErrors({ submit: 'An error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!session || session.user?.role !== 'advertiser') {
    return <div>Access denied</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Create Campaign</h1>
              <p className="mt-2 text-gray-600">Set up a new advertising campaign</p>
            </div>
            <button
              type="button"
              onClick={() => router.push('/advertiser/campaigns')}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              Back to Campaigns
            </button>
          </div>
        </div>

        {/* Main Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-8">
            {/* Campaign Type - Radio Buttons (moved to top) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Campaign Type *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {[
                  { value: 'banner', label: 'Banner', description: 'Display banner ads in various sizes' },
                  { value: 'native', label: 'Native', description: 'Native ads that blend with content' },
                  { value: 'in_page_push', label: 'In-Page Push', description: 'Push-style notifications' },
                  { value: 'popup', label: 'Popup', description: 'Popup ads with landing URL only' },
                ].map((type) => (
                  <div key={type.value} className="relative">
                    <label className={`flex flex-col p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors h-24 ${
                      formData.type === type.value
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-300'
                    }`}>
                      <input
                        type="radio"
                        name="type"
                        value={type.value}
                        checked={formData.type === type.value}
                        onChange={handleChange}
                        className="sr-only"
                      />
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full border-2 mb-2 ${
                        formData.type === type.value
                          ? 'border-blue-600 bg-blue-600'
                          : 'border-gray-300'
                      }`}>
                        {formData.type === type.value && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span className="font-medium text-gray-900 text-sm">{type.label}</span>
                      <span className="text-xs text-gray-500 mt-1 leading-tight">{type.description}</span>
                    </label>
                  </div>
                ))}
              </div>
              {errors.type && <p className="mt-2 text-sm text-red-600">{errors.type}</p>}
            </div>

            {/* Campaign Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Campaign Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter campaign name"
                required
              />
              {errors.name && <p className="mt-2 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* Budget Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Budget & Bidding</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="cpmBid" className="block text-sm font-medium text-gray-700">
                  CPM Bid ($) *
                  {formData.type && (
                    <span className="text-sm font-normal text-gray-500 ml-2">
                      (Min: ${minimumBids[formData.type as keyof typeof minimumBids]})
                    </span>
                  )}
                </label>
                <input
                  type="number"
                  id="cpmBid"
                  name="cpmBid"
                  value={formData.cpmBid}
                  onChange={handleChange}
                  step="0.01"
                  min={formData.type ? minimumBids[formData.type as keyof typeof minimumBids] : "0.01"}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder={formData.type ? `Min: ${minimumBids[formData.type as keyof typeof minimumBids]}` : "0.00"}
                  required
                />
                {formData.type && !errors.cpmBid && (
                  <p className="mt-1 text-xs text-gray-500">
                    Minimum CPM bid for {formData.type} campaigns: ${minimumBids[formData.type as keyof typeof minimumBids]}
                  </p>
                )}
                {errors.cpmBid && <p className="mt-2 text-sm text-red-600">{errors.cpmBid}</p>}
              </div>

              <div>
                <label htmlFor="dailyBudget" className="block text-sm font-medium text-gray-700">
                  Daily Budget ($) (Optional)
                  <span className="text-sm font-normal text-gray-500 ml-2">
                  </span>
                </label>
                <input
                  type="number"
                  id="dailyBudget"
                  name="dailyBudget"
                  value={formData.dailyBudget}
                  onChange={handleChange}
                  step="0.01"
                  min="30"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Min: 30.00 (optional)"
                />
                {!errors.dailyBudget && (
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for unlimited daily budget (minimum $30.00 if set)
                  </p>
                )}
                {errors.dailyBudget && <p className="mt-2 text-sm text-red-600">{errors.dailyBudget}</p>}
              </div>

              <div>
                <label htmlFor="totalBudget" className="block text-sm font-medium text-gray-700">
                  Total Budget ($) (Optional)
                </label>
                <input
                  type="number"
                  id="totalBudget"
                  name="totalBudget"
                  value={formData.totalBudget}
                  onChange={handleChange}
                  step="0.01"
                  min="1"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="No limit (optional)"
                />
                {!errors.totalBudget && (
                  <p className="mt-1 text-xs text-gray-500">
                    Empty or 0 for unlimited total budget
                  </p>
                )}
                {errors.totalBudget && <p className="mt-2 text-sm text-red-600">{errors.totalBudget}</p>}
              </div>
              </div>
            </div>

            {/* Basic Schedule */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule</h3>

              {/* Start and End Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                    Start Date *
                  </label>
                  <input
                    type="datetime-local"
                    id="startDate"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.startDate && <p className="mt-2 text-sm text-red-600">{errors.startDate}</p>}
                </div>

                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                    End Date (Optional)
                  </label>
                  <input
                    type="datetime-local"
                    id="endDate"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  {errors.endDate && <p className="mt-2 text-sm text-red-600">{errors.endDate}</p>}
                </div>
              </div>
            </div>

            {/* Creative Setup - Based on Selected Ad Format */}
            <div className="border-t border-gray-200 pt-6">
              {formData.type ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Creative Setup</h3>

                {/* Banner Creative */}
                {formData.type === 'banner' && (
                  <div className="space-y-6">
                    {/* Banner Size Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Banner Size *
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {bannerSizes.map((size) => (
                          <label key={size.value} className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 min-h-[60px] ${
                            formData.bannerSize === size.value ? 'border-blue-600 bg-blue-50' : 'border-gray-300'
                          }`}>
                            <input
                              type="radio"
                              name="bannerSize"
                              value={size.value}
                              checked={formData.bannerSize === size.value}
                              onChange={handleChange}
                              className="sr-only"
                            />
                            <div className={`w-4 h-4 rounded-full border-2 mr-3 flex-shrink-0 ${
                              formData.bannerSize === size.value ? 'border-blue-600 bg-blue-600' : 'border-gray-300'
                            }`}>
                              {formData.bannerSize === size.value && (
                                <div className="w-1.5 h-1.5 bg-white rounded-full mx-auto mt-0.5"></div>
                              )}
                            </div>
                            <span className="text-sm font-medium leading-tight">{size.label}</span>
                          </label>
                        ))}
                      </div>
                      {errors.bannerSize && <p className="mt-2 text-sm text-red-600">{errors.bannerSize}</p>}
                    </div>

                    {/* Creative Type Selection */}
                    {formData.bannerSize && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          Creative Type *
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <label className={`flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                            formData.creativeType === 'image' ? 'border-blue-600 bg-blue-50' : 'border-gray-300'
                          }`}>
                            <input
                              type="radio"
                              name="creativeType"
                              value="image"
                              checked={formData.creativeType === 'image'}
                              onChange={handleChange}
                              className="sr-only"
                            />
                            <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                              formData.creativeType === 'image' ? 'border-blue-600 bg-blue-600' : 'border-gray-300'
                            }`}>
                              {formData.creativeType === 'image' && (
                                <div className="w-1.5 h-1.5 bg-white rounded-full mx-auto mt-0.5"></div>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Image Upload</span>
                              <p className="text-sm text-gray-500">Upload an image that matches the banner size ratio</p>
                            </div>
                          </label>
                          <label className={`flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                            formData.creativeType === 'js' ? 'border-blue-600 bg-blue-50' : 'border-gray-300'
                          }`}>
                            <input
                              type="radio"
                              name="creativeType"
                              value="js"
                              checked={formData.creativeType === 'js'}
                              onChange={handleChange}
                              className="sr-only"
                            />
                            <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                              formData.creativeType === 'js' ? 'border-blue-600 bg-blue-600' : 'border-gray-300'
                            }`}>
                              {formData.creativeType === 'js' && (
                                <div className="w-1.5 h-1.5 bg-white rounded-full mx-auto mt-0.5"></div>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">JS Tag</span>
                              <p className="text-sm text-gray-500">Provide JavaScript tag/script</p>
                            </div>
                          </label>
                        </div>
                        {errors.creativeType && <p className="mt-2 text-sm text-red-600">{errors.creativeType}</p>}
                      </div>
                    )}

                    {/* Image Upload for Banner */}
                    {formData.creativeType === 'image' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Banner Image * (Must match {formData.bannerSize} ratio)
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, 'imageFile')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        {errors.imageFile && <p className="mt-2 text-sm text-red-600">{errors.imageFile}</p>}
                      </div>
                    )}

                    {/* JS Tag for Banner */}
                    {formData.creativeType === 'js' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          JavaScript Tag *
                        </label>
                        <textarea
                          name="jsTag"
                          value={formData.jsTag}
                          onChange={handleChange}
                          rows={6}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="<script>...</script>"
                        />
                        {errors.jsTag && <p className="mt-2 text-sm text-red-600">{errors.jsTag}</p>}
                      </div>
                    )}

                    {/* Landing URL for Banner - Only for Image type */}
                    {formData.creativeType === 'image' && (
                      <div>
                        <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                          Landing URL *
                        </label>
                        <input
                          type="url"
                          id="landingUrl"
                          name="landingUrl"
                          value={formData.landingUrl}
                          onChange={handleChange}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://example.com/landing?click_id={click_id}"
                          required
                        />
                        <MacroInfo onMacroClick={insertMacro} />
                        {errors.landingUrl && <p className="mt-2 text-sm text-red-600">{errors.landingUrl}</p>}
                      </div>
                    )}
                  </div>
                )}

                {/* Native Creative */}
                {formData.type === 'native' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Native Title * (Max 50 characters)
                        </label>
                        <input
                          type="text"
                          name="nativeTitle"
                          value={formData.nativeTitle}
                          onChange={handleChange}
                          maxLength={50}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Compelling ad title"
                        />
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-gray-500">{formData.nativeTitle.length}/50 characters</span>
                        </div>
                        {errors.nativeTitle && <p className="mt-2 text-sm text-red-600">{errors.nativeTitle}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Native Description * (Max 150 characters)
                        </label>
                        <textarea
                          name="nativeDescription"
                          value={formData.nativeDescription}
                          onChange={handleChange}
                          maxLength={150}
                          rows={3}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                          style={{ height: '72px' }}
                          placeholder="Brief description of your offer"
                        />
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-gray-500">{formData.nativeDescription.length}/150 characters</span>
                        </div>
                        {errors.nativeDescription && <p className="mt-2 text-sm text-red-600">{errors.nativeDescription}</p>}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Native Icon (Optional - 1:1 ratio)
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, 'nativeIconFile')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        {errors.nativeIconFile && <p className="mt-2 text-sm text-red-600">{errors.nativeIconFile}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Native Image * (4:3 ratio)
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, 'nativeImageFile')}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        {errors.nativeImageFile && <p className="mt-2 text-sm text-red-600">{errors.nativeImageFile}</p>}
                      </div>
                    </div>

                    <div>
                      <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                        Landing URL *
                      </label>
                      <input
                        type="url"
                        id="landingUrl"
                        name="landingUrl"
                        value={formData.landingUrl}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://example.com/landing?click_id={click_id}"
                        required
                      />
                      <MacroInfo onMacroClick={insertMacro} />
                      {errors.landingUrl && <p className="mt-2 text-sm text-red-600">{errors.landingUrl}</p>}
                    </div>
                  </div>
                )}

                {/* In-Page Push Creative */}
                {formData.type === 'in_page_push' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Push Title * (Max 40 characters)
                        </label>
                        <input
                          type="text"
                          name="pushTitle"
                          value={formData.pushTitle}
                          onChange={handleChange}
                          maxLength={40}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Push notification title"
                        />
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-gray-500">{formData.pushTitle.length}/40 characters</span>
                        </div>
                        {errors.pushTitle && <p className="mt-2 text-sm text-red-600">{errors.pushTitle}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Push Description * (Max 120 characters)
                        </label>
                        <textarea
                          name="pushDescription"
                          value={formData.pushDescription}
                          onChange={handleChange}
                          maxLength={120}
                          rows={3}
                          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                          style={{ height: '72px' }}
                          placeholder="Push notification description"
                        />
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-gray-500">{formData.pushDescription.length}/120 characters</span>
                        </div>
                        {errors.pushDescription && <p className="mt-2 text-sm text-red-600">{errors.pushDescription}</p>}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Push Image * (4:3 ratio)
                      </label>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleFileChange(e, 'pushImageFile')}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                      {errors.pushImageFile && <p className="mt-2 text-sm text-red-600">{errors.pushImageFile}</p>}
                    </div>

                    <div>
                      <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                        Landing URL *
                      </label>
                      <input
                        type="url"
                        id="landingUrl"
                        name="landingUrl"
                        value={formData.landingUrl}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://example.com/landing?click_id={click_id}"
                        required
                      />
                      <MacroInfo onMacroClick={insertMacro} />
                      {errors.landingUrl && <p className="mt-2 text-sm text-red-600">{errors.landingUrl}</p>}
                    </div>
                  </div>
                )}

                {/* Popup Creative */}
                {formData.type === 'popup' && (
                  <div>
                    <label htmlFor="landingUrl" className="block text-sm font-medium text-gray-700">
                      Landing URL *
                    </label>
                    <input
                      type="url"
                      id="landingUrl"
                      name="landingUrl"
                      value={formData.landingUrl}
                      onChange={handleChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com/landing?click_id={click_id}"
                      required
                    />
                    <MacroInfo onMacroClick={insertMacro} />
                    {errors.landingUrl && <p className="mt-2 text-sm text-red-600">{errors.landingUrl}</p>}
                  </div>
                )}
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Creative Setup</h3>
                  <p className="text-gray-500">Select a campaign type above to configure creative settings</p>
                </div>
              )}
            </div>

            {/* Geo Targeting */}
            <div className="border-t border-gray-200 pt-6">
              {formData.type ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Geo Targeting</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Countries */}
                  <div className="relative dropdown-container">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Countries
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search countries..."
                        value={countrySearch}
                        onChange={(e) => setCountrySearch(e.target.value)}
                        onFocus={(e) => {
                          e.stopPropagation();
                          setShowCountryDropdown(true);
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowCountryDropdown(true);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                      {showCountryDropdown && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                          <div className="p-2 border-b">
                            <button
                              type="button"
                              onClick={() => handleSelectAll('targetingCountries', countries.map(c => c.isoCode))}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              {formData.targetingCountries.length === countries.length ? 'Deselect All' : 'Select All'}
                            </button>
                          </div>
                          {filteredCountries.map((country) => (
                            <label key={country.isoCode} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.targetingCountries.includes(country.isoCode)}
                                onChange={() => handleMultiSelect(country.isoCode, 'targetingCountries')}
                                className="mr-2"
                              />
                              <span className="text-sm">{country.flag} {country.name}</span>
                            </label>
                          ))}
                        </div>
                      )}
                    </div>
                    {formData.targetingCountries.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {formData.targetingCountries.slice(0, 3).map(countryCode => {
                          const country = countries.find(c => c.isoCode === countryCode);
                          return country ? (
                            <span key={countryCode} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              {country.flag} {country.name}
                              <button
                                type="button"
                                onClick={() => handleMultiSelect(countryCode, 'targetingCountries')}
                                className="ml-1 text-blue-600 hover:text-blue-800"
                              >
                                ×
                              </button>
                            </span>
                          ) : null;
                        })}
                        {formData.targetingCountries.length > 3 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                            +{formData.targetingCountries.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* States */}
                  <div className="relative dropdown-container">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target States
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search states..."
                        value={stateSearch}
                        onChange={(e) => setStateSearch(e.target.value)}
                        onFocus={(e) => {
                          e.stopPropagation();
                          setShowStateDropdown(true);
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowStateDropdown(true);
                        }}
                        disabled={availableStates.length === 0}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                      />
                      {showStateDropdown && availableStates.length > 0 && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                          <div className="p-2 border-b">
                            <button
                              type="button"
                              onClick={() => handleSelectAll('targetingStates', availableStates.map(s => s.isoCode))}
                              className="text-sm text-blue-600 hover:text-blue-800"
                            >
                              {formData.targetingStates.length === availableStates.length ? 'Deselect All' : 'Select All'}
                            </button>
                          </div>
                          {filteredStates.map((state) => (
                            <label key={state.isoCode} className="flex items-center p-2 hover:bg-gray-50 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.targetingStates.includes(state.isoCode)}
                                onChange={() => handleMultiSelect(state.isoCode, 'targetingStates')}
                                className="mr-2"
                              />
                              <span className="text-sm">{state.name}</span>
                            </label>
                          ))}
                        </div>
                      )}
                    </div>
                    {formData.targetingStates.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {formData.targetingStates.slice(0, 3).map(stateCode => {
                          const state = availableStates.find(s => s.isoCode === stateCode);
                          return state ? (
                            <span key={stateCode} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              {state.name}
                              <button
                                type="button"
                                onClick={() => handleMultiSelect(stateCode, 'targetingStates')}
                                className="ml-1 text-blue-600 hover:text-blue-800"
                              >
                                ×
                              </button>
                            </span>
                          ) : null;
                        })}
                        {formData.targetingStates.length > 3 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                            +{formData.targetingStates.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Geo Targeting</h3>
                  <p className="text-gray-500">Select a campaign type above to configure geo targeting</p>
                </div>
              )}
            </div>

            {/* Device, OS, Browser, ISP/Connection Type Targeting */}
            <div className="border-t border-gray-200 pt-6">
              {formData.type ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Device & Platform Targeting</h3>

                <div className="mb-8">
                  {/* Device Types */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Device Types
                    </label>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-700">Select devices:</span>
                      <button
                        type="button"
                        onClick={() => handleSelectAll('targetingDevices', devices)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {formData.targetingDevices.length === devices.length ? 'Deselect All' : 'Select All'}
                      </button>
                    </div>
                    <div className="grid grid-cols-3 gap-3">
                      {devices.map(device => (
                        <button
                          key={device}
                          type="button"
                          onClick={() => handleMultiSelect(device, 'targetingDevices')}
                          className={`p-3 text-sm border rounded-lg transition-colors ${
                            formData.targetingDevices.includes(device)
                              ? 'bg-blue-50 border-blue-300 text-blue-700'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {device}
                        </button>
                      ))}
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Empty to target all device types
                    </p>
                  </div>

                  {/* Operating Systems */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Operating Systems
                    </label>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-700">Select operating systems:</span>
                      <button
                        type="button"
                        onClick={() => handleSelectAll('targetingOs', operatingSystems)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {formData.targetingOs.length === operatingSystems.length ? 'Deselect All' : 'Select All'}
                      </button>
                    </div>
                    <div className="grid grid-cols-3 gap-3">
                      {operatingSystems.map(os => (
                        <button
                          key={os}
                          type="button"
                          onClick={() => handleMultiSelect(os, 'targetingOs')}
                          className={`p-3 text-sm border rounded-lg transition-colors ${
                            formData.targetingOs.includes(os)
                              ? 'bg-blue-50 border-blue-300 text-blue-700'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {os}
                        </button>
                      ))}
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Empty to target all operating systems
                    </p>
                  </div>

                  {/* Browsers */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Browsers
                    </label>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-700">Select browsers:</span>
                      <button
                        type="button"
                        onClick={() => handleSelectAll('targetingBrowsers', browsers)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {formData.targetingBrowsers.length === browsers.length ? 'Deselect All' : 'Select All'}
                      </button>
                    </div>
                    <div className="grid grid-cols-3 gap-3">
                      {browsers.map(browser => (
                        <button
                          key={browser}
                          type="button"
                          onClick={() => handleMultiSelect(browser, 'targetingBrowsers')}
                          className={`p-3 text-sm border rounded-lg transition-colors ${
                            formData.targetingBrowsers.includes(browser)
                              ? 'bg-blue-50 border-blue-300 text-blue-700'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {browser}
                        </button>
                      ))}
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Empty to target all browsers
                    </p>
                  </div>

                  {/* Connection Types */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Connection Types
                    </label>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-700">Select connection types:</span>
                      <button
                        type="button"
                        onClick={() => handleSelectAll('targetingConnectionTypes', connectionTypes)}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        {formData.targetingConnectionTypes.length === connectionTypes.length ? 'Deselect All' : 'Select All'}
                      </button>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {connectionTypes.map(connectionType => (
                        <button
                          key={connectionType}
                          type="button"
                          onClick={() => handleMultiSelect(connectionType, 'targetingConnectionTypes')}
                          className={`p-3 text-sm border rounded-lg transition-colors ${
                            formData.targetingConnectionTypes.includes(connectionType)
                              ? 'bg-blue-50 border-blue-300 text-blue-700'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {connectionType}
                        </button>
                      ))}
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      Empty to target all connection types
                    </p>
                  </div>
                </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Device & Platform Targeting</h3>
                  <p className="text-gray-500">Select a campaign type above to configure device targeting</p>
                </div>
              )}
            </div>



            {/* Enhanced Schedule */}
            <div className="border-t border-gray-200 pt-6">
              {formData.type ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Advanced Schedule</h3>

                  {/* Daily Schedule */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Days of Week (Empty for all days)
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                      {daysOfWeek.map((day) => (
                        <label key={day} className="flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.dailySchedule.includes(day)}
                            onChange={() => handleMultiSelect(day, 'dailySchedule')}
                            className="mr-2"
                          />
                          <span className="text-sm">{day.slice(0, 3)}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Hourly Schedule */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hours of Day (Empty for all hours)
                    </label>
                    <div className="grid grid-cols-8 gap-2">
                      {Array.from({ length: 24 }, (_, i) => (
                        <label key={i} className="flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.hourlySchedule.includes(i)}
                            onChange={() => handleMultiSelect(i, 'hourlySchedule')}
                            className="mr-2"
                          />
                          <span className="text-sm">{i.toString().padStart(2, '0')}:00</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Schedule</h3>
                  <p className="text-gray-500">Select a campaign type above to configure advanced scheduling</p>
                </div>
              )}
            </div>

            {/* Enhanced Frequency Cap */}
            <div className="border-t border-gray-200 pt-6">
              {formData.type ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Frequency Cap</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="frequencyCapValue" className="block text-sm font-medium text-gray-700">
                      Max Impressions per User
                    </label>
                    <input
                      type="number"
                      id="frequencyCapValue"
                      name="frequencyCapValue"
                      value={formData.frequencyCapValue}
                      onChange={handleChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0 = unlimited"
                      min="0"
                    />
                  </div>
                  <div>
                    <label htmlFor="frequencyCapPeriod" className="block text-sm font-medium text-gray-700">
                      Time Period
                    </label>
                    <select
                      id="frequencyCapPeriod"
                      name="frequencyCapPeriod"
                      value={formData.frequencyCapPeriod}
                      onChange={handleChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="hour">Per Hour</option>
                      <option value="day">Per Day</option>
                      <option value="week">Per Week</option>
                    </select>
                  </div>
                </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Frequency Cap</h3>
                  <p className="text-gray-500">Select a campaign type above to configure frequency capping</p>
                </div>
              )}
            </div>

            {/* Whitelist/Blacklist */}
            <div className="border-t border-gray-200 pt-6">
              {formData.type ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Whitelist/Blacklist (Optional)</h3>

                {/* Whitelist Section */}
                <div className="mb-8">
                  <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                    <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                    Whitelist (Allow Only)
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Publisher IDs
                      </label>
                      <textarea
                        name="whitelistPublishers"
                        value={formData.whitelistPublishers}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                        placeholder="Enter publisher IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                        style={{ maxHeight: '150px' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Website IDs
                      </label>
                      <textarea
                        name="whitelistWebsites"
                        value={formData.whitelistWebsites}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                        placeholder="Enter website IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                        style={{ maxHeight: '150px' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Zone IDs
                      </label>
                      <textarea
                        name="whitelistZones"
                        value={formData.whitelistZones}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                        placeholder="Enter zone IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                        style={{ maxHeight: '150px' }}
                      />
                    </div>
                  </div>
                </div>

                {/* Blacklist Section */}
                <div className="mb-4">
                  <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                    <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                    Blacklist (Block)
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Publisher IDs
                      </label>
                      <textarea
                        name="blacklistPublishers"
                        value={formData.blacklistPublishers}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                        placeholder="Enter publisher IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                        style={{ maxHeight: '150px' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Website IDs
                      </label>
                      <textarea
                        name="blacklistWebsites"
                        value={formData.blacklistWebsites}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                        placeholder="Enter website IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                        style={{ maxHeight: '150px' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Zone IDs
                      </label>
                      <textarea
                        name="blacklistZones"
                        value={formData.blacklistZones}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none overflow-y-auto"
                        placeholder="Enter zone IDs, one per line&#10;Example:&#10;123&#10;456&#10;789"
                        style={{ maxHeight: '150px' }}
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-blue-800">
                    <strong>Note:</strong> Blacklist rules take priority over whitelist rules.
                    Empty for all available inventory. Use commas to separate multiple IDs.
                  </p>
                </div>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Whitelist/Blacklist</h3>
                  <p className="text-gray-500">Select a campaign type above to configure whitelist/blacklist settings</p>
                </div>
              )}
            </div>

            {errors.submit && (
              <div className="text-red-600 text-sm">{errors.submit}</div>
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Creating...' : 'Create Campaign'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
