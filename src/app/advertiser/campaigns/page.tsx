'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function AdvertiserCampaigns() {
  const { data: session } = useSession();
  const router = useRouter();
  const [campaigns, setCampaigns] = useState([]);
  const [filteredCampaigns, setFilteredCampaigns] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showArchived, setShowArchived] = useState(false);
  const [rejectionReasons, setRejectionReasons] = useState({});
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [selectedRejection, setSelectedRejection] = useState(null);

  // Fetch campaigns
  useEffect(() => {
    if (!session?.user?.id) return;

    const fetchCampaigns = async () => {
      try {
        const response = await fetch('/api/campaigns');
        const data = await response.json();
        if (data.success) {
          setCampaigns(data.campaigns || []);
          // Fetch rejection reasons for rejected campaigns
          fetchRejectionReasons(data.campaigns || []);
        }
      } catch (error) {
        console.error('Error fetching campaigns:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, [session]);

  // Fetch rejection reasons for rejected campaigns
  const fetchRejectionReasons = async (campaignList) => {
    const rejectedCampaigns = campaignList.filter(c => c.status === 'rejected');
    const reasons = {};

    for (const campaign of rejectedCampaigns) {
      try {
        const response = await fetch(`/api/campaigns/${campaign.id}/rejection-reason`);
        if (response.ok) {
          const data = await response.json();
          reasons[campaign.id] = data;
        }
      } catch (error) {
        console.error(`Error fetching rejection reason for campaign ${campaign.id}:`, error);
      }
    }

    setRejectionReasons(reasons);
  };

  // Show rejection reason modal
  const showRejectionReason = (campaign) => {
    const reason = rejectionReasons[campaign.id];
    if (reason) {
      setSelectedRejection({
        campaign,
        ...reason
      });
      setShowRejectionModal(true);
    }
  };

  // Filter campaigns based on search and status
  useEffect(() => {
    const filtered = campaigns.filter(campaign => {
      // Filter by archived status
      const isArchived = campaign.status === 'archived';
      if (showArchived !== isArchived) return false;

      // Filter by search term
      if (searchTerm && !campaign.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Filter by status (excluding archived filter)
      if (statusFilter && statusFilter !== campaign.status) {
        return false;
      }

      return true;
    });

    setFilteredCampaigns(filtered);
  }, [campaigns, searchTerm, statusFilter, showArchived]);

  // Action handlers
  const handlePlayPause = async (campaignId: number, currentStatus: string) => {
    // Prevent action on pending or rejected campaigns
    if (currentStatus === 'pending' || currentStatus === 'rejected') {
      alert(`Cannot modify ${currentStatus} campaigns`);
      return;
    }

    const newStatus = currentStatus === 'active' ? 'paused' : 'active';
    try {
      console.log('Updating campaign status:', campaignId, 'from', currentStatus, 'to', newStatus);
      const response = await fetch(`/api/campaigns/${campaignId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();
      console.log('Status update response:', data);

      if (response.ok) {
        setCampaigns(prev => prev.map(campaign =>
          campaign.id === campaignId ? { ...campaign, status: newStatus } : campaign
        ));
        alert(`Campaign ${newStatus === 'active' ? 'started' : 'paused'} successfully!`);
      } else {
        alert(`Failed to update campaign: ${data.message}`);
      }
    } catch (error) {
      console.error('Error updating campaign status:', error);
      alert('Error updating campaign. Please try again.');
    }
  };

  const handleCopy = async (campaignId: number) => {
    try {
      const response = await fetch(`/api/campaigns/${campaignId}/copy`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        setCampaigns(prev => [data.campaign, ...prev]);
      }
    } catch (error) {
      console.error('Error copying campaign:', error);
    }
  };

  const handleArchive = async (campaignId: number) => {
    try {
      console.log('Archiving campaign:', campaignId);
      const response = await fetch(`/api/campaigns/${campaignId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'archived' }),
      });

      const data = await response.json();
      console.log('Archive response:', data);

      if (response.ok) {
        setCampaigns(prev => prev.map(campaign =>
          campaign.id === campaignId ? { ...campaign, status: 'archived' } : campaign
        ));
        alert('Campaign archived successfully!');
      } else {
        alert(`Failed to archive campaign: ${data.message}`);
      }
    } catch (error) {
      console.error('Error archiving campaign:', error);
      alert('Error archiving campaign. Please try again.');
    }
  };

  const handleEdit = (campaignId: number) => {
    router.push(`/advertiser/campaigns/${campaignId}/edit`);
  };

  const handleResubmit = async (campaignId: number) => {
    try {
      const response = await fetch(`/api/campaigns/${campaignId}/resubmit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      const data = await response.json();

      if (response.ok) {
        setCampaigns(prev => prev.map(campaign =>
          campaign.id === campaignId ? { ...campaign, status: 'pending' } : campaign
        ));
        alert('Campaign resubmitted for review successfully!');
      } else {
        alert(`Failed to resubmit campaign: ${data.message}`);
      }
    } catch (error) {
      console.error('Error resubmitting campaign:', error);
      alert('Error resubmitting campaign. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Campaigns</h1>
              <div className="mt-2 flex space-x-4">
                <button
                  onClick={() => setShowArchived(false)}
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    !showArchived
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Active Campaigns ({campaigns.filter(c => c.status !== 'archived').length})
                </button>
                <button
                  onClick={() => setShowArchived(true)}
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    showArchived
                      ? 'bg-gray-100 text-gray-700 border border-gray-300'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Archived ({campaigns.filter(c => c.status === 'archived').length})
                </button>
              </div>
            </div>
            <Link
              href="/advertiser/campaigns/create"
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Campaign
            </Link>
          </div>
        </div>

        {/* Campaigns List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">
                {showArchived ? 'Archived Campaigns' : 'Active Campaigns'} ({filteredCampaigns.length})
              </h2>
              {campaigns.length > 0 && (
                <div className="flex space-x-4">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                  >
                    <option value="">All Status</option>
                    {!showArchived && (
                      <>
                        <option value="pending">Pending</option>
                        <option value="active">Active</option>
                        <option value="paused">Paused</option>
                        <option value="rejected">Rejected</option>
                      </>
                    )}
                    {showArchived && (
                      <option value="archived">Archived</option>
                    )}
                  </select>
                  <input
                    type="text"
                    placeholder="Search campaigns..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm w-64"
                  />
                </div>
              )}
            </div>
          </div>

          {loading ? (
            <div className="p-6">
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-500">Loading campaigns...</p>
              </div>
            </div>
          ) : filteredCampaigns.length === 0 ? (
            <div className="p-6">
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {showArchived ? 'No archived campaigns' : 'No campaigns yet'}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {showArchived
                    ? 'You haven\'t archived any campaigns yet.'
                    : 'Get started by creating your first campaign.'
                  }
                </p>
                {!showArchived && (
                  <div className="mt-6">
                    <Link
                      href="/advertiser/campaigns/create"
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Create Campaign
                    </Link>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Campaign
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      CPM Bid
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCampaigns.map((campaign) => (
                    <tr key={campaign.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{campaign.name}</div>
                          <div className="text-sm text-gray-500">ID: {campaign.id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                          {campaign.type.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                            campaign.status === 'active' ? 'bg-green-100 text-green-800' :
                            campaign.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            campaign.status === 'paused' ? 'bg-gray-100 text-gray-800' :
                            campaign.status === 'archived' ? 'bg-purple-100 text-purple-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {campaign.status}
                          </span>
                          {campaign.status === 'rejected' && rejectionReasons[campaign.id] && (
                            <button
                              onClick={() => showRejectionReason(campaign)}
                              className="text-xs text-red-600 hover:text-red-800 underline"
                              title="View rejection reason"
                            >
                              View Reason
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${parseFloat(campaign.cpm_bid || 0).toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(campaign.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {/* Play/Pause Button */}
                          {!showArchived && campaign.status !== 'rejected' && (
                            <button
                              onClick={() => handlePlayPause(campaign.id, campaign.status)}
                              disabled={campaign.status === 'pending'}
                              className={`p-2 rounded-full ${
                                campaign.status === 'pending'
                                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50'
                                  : campaign.status === 'active'
                                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                                  : 'bg-green-100 text-green-600 hover:bg-green-200'
                              }`}
                              title={
                                campaign.status === 'pending' ? 'Campaign is pending approval' :
                                campaign.status === 'active' ? 'Pause Campaign' : 'Start Campaign'
                              }
                            >
                              {campaign.status === 'active' ? (
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                                </svg>
                              ) : (
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M8 5v14l11-7z"/>
                                </svg>
                              )}
                            </button>
                          )}

                          {/* Resubmit Button for Rejected Campaigns */}
                          {!showArchived && campaign.status === 'rejected' && (
                            <button
                              onClick={() => handleResubmit(campaign.id)}
                              className="p-2 rounded-full bg-yellow-100 text-yellow-600 hover:bg-yellow-200"
                              title="Resubmit for Review"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            </button>
                          )}

                          {/* Copy Button */}
                          <button
                            onClick={() => handleCopy(campaign.id)}
                            className="p-2 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200"
                            title="Copy Campaign"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>

                          {/* Edit Button */}
                          <button
                            onClick={() => handleEdit(campaign.id)}
                            className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200"
                            title="Edit Campaign"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>

                          {/* Archive Button */}
                          {!showArchived && (
                            <button
                              onClick={() => handleArchive(campaign.id)}
                              className="p-2 rounded-full bg-orange-100 text-orange-600 hover:bg-orange-200"
                              title="Archive Campaign"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m6 5l-3 3-3-3" />
                              </svg>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Rejection Reason Modal */}
        {showRejectionModal && selectedRejection && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Campaign Rejection Reason</h3>
                  <button
                    onClick={() => setShowRejectionModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="mb-4">
                  <div className="bg-red-50 p-3 rounded-md text-sm">
                    <p><strong>Campaign:</strong> {selectedRejection.campaign.name}</p>
                    <p><strong>Campaign ID:</strong> #{selectedRejection.campaign.id}</p>
                    <p><strong>Rejected on:</strong> {new Date(selectedRejection.timestamp).toLocaleString()}</p>
                    {selectedRejection.admin_name && (
                      <p><strong>Reviewed by:</strong> {selectedRejection.admin_name}</p>
                    )}
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason for Rejection:
                  </label>
                  <div className="bg-gray-50 p-3 rounded-md border">
                    <p className="text-sm text-gray-800 whitespace-pre-wrap">{selectedRejection.reason}</p>
                  </div>
                </div>

                {/* Screenshot Evidence */}
                {selectedRejection.screenshot && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Screenshot Evidence:
                    </label>
                    <div className="bg-gray-50 p-3 rounded-md border">
                      <div className="mb-2">
                        <p className="text-xs text-gray-600">
                          Captured from: {selectedRejection.screenshot.region} region on{' '}
                          {new Date(selectedRejection.screenshot.checked_at).toLocaleString()}
                        </p>
                        {selectedRejection.screenshot.issues_found.length > 0 && (
                          <div className="mt-1">
                            <p className="text-xs text-red-600 font-medium">Issues detected:</p>
                            <ul className="text-xs text-red-600 list-disc list-inside">
                              {selectedRejection.screenshot.issues_found.map((issue: string, index: number) => (
                                <li key={index}>{issue}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      <img
                        src={selectedRejection.screenshot.url}
                        alt="Ad monitoring screenshot"
                        className="max-w-full h-auto border rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                        onClick={() => window.open(selectedRejection.screenshot.url, '_blank')}
                        title="Click to view full size"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Click image to view full size
                      </p>
                    </div>
                  </div>
                )}

                <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-800 mb-4">
                  <p><strong>What to do next:</strong></p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Review the rejection reason carefully</li>
                    <li>Make the necessary changes to your campaign</li>
                    <li>Edit your campaign to address the issues</li>
                    <li>Your campaign will be automatically resubmitted for review</li>
                  </ul>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowRejectionModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => {
                      setShowRejectionModal(false);
                      handleResubmit(selectedRejection.campaign.id);
                    }}
                    className="px-4 py-2 bg-yellow-600 text-white rounded-md text-sm font-medium hover:bg-yellow-700"
                  >
                    Resubmit for Review
                  </button>
                  <button
                    onClick={() => {
                      setShowRejectionModal(false);
                      handleEdit(selectedRejection.campaign.id);
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                  >
                    Edit Campaign
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
