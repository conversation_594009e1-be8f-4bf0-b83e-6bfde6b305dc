'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

export default function DepositPage() {
  const router = useRouter();
  const { data: session } = useSession();

  const [formData, setFormData] = useState({
    amount: '',
    paymentMethod: 'stripe',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [minimumDeposits, setMinimumDeposits] = useState({
    stripe: 100,
    paypal: 100,
  });

  // Processing fee rate (3.5%)
  const PROCESSING_FEE_RATE = 0.035;

  // Calculate processing fee
  const calculateProcessingFee = (amount: number) => {
    return amount * PROCESSING_FEE_RATE;
  };

  // Calculate total amount (deposit + fee)
  const calculateTotalAmount = (amount: number) => {
    return amount + calculateProcessingFee(amount);
  };

  // Fetch minimum deposit settings
  useEffect(() => {
    const fetchMinimumDeposits = async () => {
      try {
        const response = await fetch('/api/settings/minimum-deposits');
        const result = await response.json();
        if (result.success) {
          setMinimumDeposits({
            stripe: parseFloat(result.data.stripe),
            paypal: parseFloat(result.data.paypal),
          });
        }
      } catch (error) {
        console.error('Failed to fetch minimum deposits:', error);
        // Keep default values if fetch fails
      }
    };

    fetchMinimumDeposits();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const depositAmount = parseFloat(formData.amount);
    const currentMinimum = minimumDeposits[formData.paymentMethod as keyof typeof minimumDeposits];
    if (depositAmount < currentMinimum) {
      setErrors({ amount: `Minimum deposit amount is $${currentMinimum}` });
      return;
    }

    setIsLoading(true);

    try {
      // Calculate total amount including processing fee
      const processingFee = calculateProcessingFee(depositAmount);
      const totalAmount = calculateTotalAmount(depositAmount);

      const response = await fetch('/api/payments/deposit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          amount: totalAmount.toFixed(2), // Send total amount to payment processor
          depositAmount: depositAmount.toFixed(2), // Keep original deposit amount for records
          processingFee: processingFee.toFixed(2), // Include processing fee for records
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.redirectUrl) {
          window.location.href = data.redirectUrl;
        } else {
          router.push('/advertiser/billing');
        }
      } else {
        const error = await response.json();
        setErrors({ submit: error.message || 'Failed to process deposit' });
      }
    } catch (error) {
      setErrors({ submit: 'An error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!session || session.user?.role !== 'advertiser') {
    return <div>Access denied</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Add Funds</h1>
          <p className="mt-2 text-gray-600">Deposit money to your advertising account</p>
        </div>

        <div className="bg-white rounded-lg shadow">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Amount */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                Deposit Amount (USD) *
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  step="0.01"
                  min={minimumDeposits[formData.paymentMethod as keyof typeof minimumDeposits]}
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleChange}
                  className="block w-full pl-7 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder={`${minimumDeposits[formData.paymentMethod as keyof typeof minimumDeposits]}.00`}
                  required
                />
              </div>
              {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
              <p className="mt-1 text-sm text-gray-500">
                Minimum deposit: ${minimumDeposits[formData.paymentMethod as keyof typeof minimumDeposits]}
              </p>
            </div>

            {/* Payment Method */}
            <div>
              <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700">
                Payment Method *
              </label>
              <select
                id="paymentMethod"
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="stripe">Credit/Debit Card (Stripe)</option>
                <option value="paypal">PayPal</option>
              </select>
            </div>

            {/* Payment Method Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Secure Payment Processing
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      {formData.paymentMethod === 'stripe'
                        ? 'Your payment will be processed securely through Stripe. We accept Visa, MasterCard, American Express, and Discover.'
                        : 'Your payment will be processed securely through PayPal. You can use your PayPal balance or linked payment methods.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Deposit Summary */}
            {formData.amount && parseFloat(formData.amount) >= minimumDeposits[formData.paymentMethod as keyof typeof minimumDeposits] && (
              <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Deposit Summary</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Deposit Amount:</span>
                    <span className="text-gray-900">${parseFloat(formData.amount).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Processing Fee (3.5%):</span>
                    <span className="text-gray-900">${calculateProcessingFee(parseFloat(formData.amount)).toFixed(2)}</span>
                  </div>
                  <div className="border-t border-gray-200 pt-1 flex justify-between font-medium">
                    <span className="text-gray-900">Total Charge:</span>
                    <span className="text-gray-900">${calculateTotalAmount(parseFloat(formData.amount)).toFixed(2)}</span>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  <p>The processing fee covers payment processing costs. Your account will be credited with the deposit amount.</p>
                </div>
              </div>
            )}

            {errors.submit && (
              <div className="text-red-600 text-sm">{errors.submit}</div>
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || !formData.amount || parseFloat(formData.amount) < minimumDeposits[formData.paymentMethod as keyof typeof minimumDeposits]}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Processing...' : 'Proceed to Payment'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
