'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';

interface Campaign {
  id: string;
  name: string;
  status: string;
}

function PixelTrackingContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<string>('');
  const [conversionType, setConversionType] = useState<string>('sale');
  const [conversionValue, setConversionValue] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copiedPixel, setCopiedPixel] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return;

    if (!session || session.user?.role !== 'advertiser') {
      router.push('/auth/signin');
      return;
    }

    fetchCampaigns();

    // Set campaign from URL parameter
    const campaignParam = searchParams.get('campaign');
    if (campaignParam) {
      setSelectedCampaign(campaignParam);
    }
  }, [session, status, router, searchParams]);

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/campaigns');
      if (response.ok) {
        const data = await response.json();
        setCampaigns(data.campaigns || []);
      } else {
        throw new Error('Failed to fetch campaigns');
      }
    } catch (error) {
      console.error('Failed to fetch campaigns:', error);
      setError('Failed to load campaigns. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generatePixelCode = (type: 'image' | 'javascript') => {
    if (!selectedCampaign) return '';

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://your-domain.com';
    const pixelUrl = `${baseUrl}/api/conversion?click_id={click_id}&type=${conversionType}${conversionValue ? `&value=${conversionValue}` : ''}`;

    if (type === 'image') {
      return `<img src="${pixelUrl}" width="1" height="1" style="display:none;" />`;
    } else {
      return `<script>
  // Conversion tracking pixel
  var img = new Image();
  img.src = "${pixelUrl}";
  img.style.display = "none";
  document.body.appendChild(img);
</script>`;
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPixel(type);
      setTimeout(() => setCopiedPixel(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading pixel tracking setup...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchCampaigns}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Link
              href="/advertiser/tracking"
              className="text-blue-600 hover:text-blue-700 flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Tracking
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Pixel Tracking Setup</h1>
          <p className="mt-2 text-gray-600">Generate and implement conversion tracking pixels for your campaigns</p>
        </div>

        {/* Configuration Form */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Pixel Configuration</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Campaign Selection */}
              <div>
                <label htmlFor="campaign" className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign
                </label>
                <select
                  id="campaign"
                  value={selectedCampaign}
                  onChange={(e) => setSelectedCampaign(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a campaign</option>
                  {campaigns.map((campaign) => (
                    <option key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Conversion Type */}
              <div>
                <label htmlFor="conversionType" className="block text-sm font-medium text-gray-700 mb-2">
                  Conversion Type
                </label>
                <select
                  id="conversionType"
                  value={conversionType}
                  onChange={(e) => setConversionType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="sale">Sale</option>
                  <option value="lead">Lead</option>
                  <option value="signup">Sign Up</option>
                  <option value="download">Download</option>
                  <option value="custom">Custom</option>
                </select>
              </div>

              {/* Conversion Value */}
              <div>
                <label htmlFor="conversionValue" className="block text-sm font-medium text-gray-700 mb-2">
                  Conversion Value (Optional)
                </label>
                <input
                  type="number"
                  id="conversionValue"
                  value={conversionValue}
                  onChange={(e) => setConversionValue(e.target.value)}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Generated Pixels */}
        {selectedCampaign && (
          <div className="space-y-6">
            {/* Image Pixel */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Image Pixel (Recommended)</h3>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Most Compatible
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">Simple image tag that works in all browsers and email clients</p>
              </div>
              <div className="p-6">
                <div className="bg-gray-50 rounded-md p-4 mb-4">
                  <code className="text-sm text-gray-800 break-all">
                    {generatePixelCode('image')}
                  </code>
                </div>
                <button
                  onClick={() => copyToClipboard(generatePixelCode('image'), 'image')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  {copiedPixel === 'image' ? (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Copied!
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      Copy Image Pixel
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* JavaScript Pixel */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">JavaScript Pixel</h3>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Advanced
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">JavaScript implementation for more control and error handling</p>
              </div>
              <div className="p-6">
                <div className="bg-gray-50 rounded-md p-4 mb-4">
                  <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                    {generatePixelCode('javascript')}
                  </pre>
                </div>
                <button
                  onClick={() => copyToClipboard(generatePixelCode('javascript'), 'javascript')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  {copiedPixel === 'javascript' ? (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Copied!
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      Copy JavaScript Pixel
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Implementation Guide */}
        <div className="bg-white rounded-lg shadow mt-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Implementation Guide</h3>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">1. Important Note about {'{click_id}'}</h4>
                <p className="text-sm text-gray-600 mb-2">
                  The <code className="bg-gray-100 px-1 rounded">{'{click_id}'}</code> parameter in the pixel code is a placeholder that needs to be replaced with the actual click ID from our ad serving system.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div className="flex">
                    <svg className="w-5 h-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <h5 className="text-sm font-medium text-yellow-800">Dynamic Click ID Required</h5>
                      <p className="text-sm text-yellow-700 mt-1">
                        When users click on your ads, they&apos;ll be redirected to your landing page with a <code>click_id</code> parameter.
                        You need to capture this parameter and replace <code>{'{click_id}'}</code> in the pixel code with the actual value.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">2. Where to Place the Pixel</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Thank you page after purchase/conversion</li>
                  <li>• Confirmation page after form submission</li>
                  <li>• Download completion page</li>
                  <li>• Any page where conversion occurs</li>
                </ul>
              </div>

              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">3. Testing Your Pixel</h4>
                <p className="text-sm text-gray-600 mb-2">
                  To test if your pixel is working correctly:
                </p>
                <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                  <li>Click on one of your ads to get a valid click_id</li>
                  <li>Replace {'{click_id}'} in the pixel with the actual click_id</li>
                  <li>Place the pixel on your test page</li>
                  <li>Visit the page and check your campaign statistics for conversions</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PixelTracking() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <PixelTrackingContent />
    </Suspense>
  );
}
