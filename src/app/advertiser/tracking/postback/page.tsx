'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';

interface Campaign {
  id: string;
  name: string;
  status: string;
}

interface PostbackConfig {
  id?: string;
  campaign_id: string;
  postback_url: string;
  method: 'GET' | 'POST';
  parameters: { [key: string]: string };
  is_active: boolean;
}

function PostbackTrackingContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<string>('');
  const [postbackUrl, setPostbackUrl] = useState<string>('');
  const [method, setMethod] = useState<'GET' | 'POST'>('GET');
  const [customParams, setCustomParams] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [existingConfig, setExistingConfig] = useState<PostbackConfig | null>(null);

  const availableMacros = [
    { key: 'click_id', description: 'Unique click identifier' },
    { key: 'campaign_id', description: 'Campaign ID' },
    { key: 'conversion_value', description: 'Conversion value' },
    { key: 'conversion_type', description: 'Type of conversion' },
    { key: 'timestamp', description: 'Conversion timestamp' },
    { key: 'ip_address', description: 'User IP address' },
    { key: 'user_agent', description: 'User agent string' },
    { key: 'country', description: 'User country code' },
    { key: 'region', description: 'User region/state' },
    { key: 'city', description: 'User city' },
    { key: 'device_type', description: 'Device type' },
    { key: 'os', description: 'Operating system' },
    { key: 'browser', description: 'Browser name' },
    { key: 'user_lang', description: 'User language code' },
    { key: 'ad_format', description: 'Ad format type' },
    { key: 'page_url', description: 'Referring page URL' },
    { key: 'os_version', description: 'Operating system version' },
  ];

  useEffect(() => {
    if (status === 'loading') return;

    if (!session || session.user?.role !== 'advertiser') {
      router.push('/auth/signin');
      return;
    }

    fetchCampaigns();

    // Set campaign from URL parameter
    const campaignParam = searchParams.get('campaign');
    if (campaignParam) {
      setSelectedCampaign(campaignParam);
    }
  }, [session, status, router, searchParams]);

  useEffect(() => {
    if (selectedCampaign) {
      fetchPostbackConfig(selectedCampaign);
    }
  }, [selectedCampaign]);

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/campaigns');
      if (response.ok) {
        const data = await response.json();
        setCampaigns(data.campaigns || []);
      } else {
        throw new Error('Failed to fetch campaigns');
      }
    } catch (error) {
      console.error('Failed to fetch campaigns:', error);
      setError('Failed to load campaigns. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPostbackConfig = async (campaignId: string) => {
    try {
      const response = await fetch(`/api/campaigns/${campaignId}/postback`);
      if (response.ok) {
        const config = await response.json();
        setExistingConfig(config);
        setPostbackUrl(config.postback_url || '');
        setMethod(config.method || 'GET');
        setCustomParams(config.parameters || {});
      } else if (response.status !== 404) {
        throw new Error('Failed to fetch postback configuration');
      }
    } catch (error) {
      console.error('Failed to fetch postback config:', error);
    }
  };

  const addCustomParam = () => {
    const key = `param_${Object.keys(customParams).length + 1}`;
    setCustomParams({ ...customParams, [key]: '' });
  };

  const updateCustomParam = (oldKey: string, newKey: string, value: string) => {
    const newParams = { ...customParams };
    delete newParams[oldKey];
    newParams[newKey] = value;
    setCustomParams(newParams);
  };

  const removeCustomParam = (key: string) => {
    const newParams = { ...customParams };
    delete newParams[key];
    setCustomParams(newParams);
  };

  const generatePostbackUrl = () => {
    if (!postbackUrl) return '';

    const url = new URL(postbackUrl);

    // Add standard parameters
    url.searchParams.set('click_id', '{click_id}');
    url.searchParams.set('conversion_value', '{conversion_value}');
    url.searchParams.set('conversion_type', '{conversion_type}');
    url.searchParams.set('timestamp', '{timestamp}');

    // Add custom parameters
    Object.entries(customParams).forEach(([key, value]) => {
      if (key && value) {
        url.searchParams.set(key, value);
      }
    });

    return url.toString();
  };

  const savePostbackConfig = async () => {
    if (!selectedCampaign || !postbackUrl) {
      setError('Please select a campaign and enter a postback URL');
      return;
    }

    try {
      setIsSaving(true);
      setError(null);

      const config: PostbackConfig = {
        campaign_id: selectedCampaign,
        postback_url: postbackUrl,
        method,
        parameters: customParams,
        is_active: true,
      };

      const response = await fetch(`/api/campaigns/${selectedCampaign}/postback`, {
        method: existingConfig ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        setSuccess('Postback configuration saved successfully!');
        fetchPostbackConfig(selectedCampaign);
        setTimeout(() => setSuccess(null), 3000);
      } else {
        throw new Error('Failed to save postback configuration');
      }
    } catch (error) {
      console.error('Failed to save postback config:', error);
      setError('Failed to save postback configuration. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const insertMacro = (macro: string) => {
    const key = `macro_${macro}`;
    setCustomParams({ ...customParams, [key]: `{${macro}}` });
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading postback setup...</p>
        </div>
      </div>
    );
  }

  if (error && !selectedCampaign) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchCampaigns}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Link
              href="/advertiser/tracking"
              className="text-blue-600 hover:text-blue-700 flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Tracking
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">S2S Postback Setup</h1>
          <p className="mt-2 text-gray-600">Configure server-to-server postbacks for reliable conversion tracking</p>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        )}

        {error && selectedCampaign && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Configuration Form */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Postback Configuration</h2>
            {existingConfig && (
              <p className="text-sm text-green-600 mt-1">✓ Existing configuration found</p>
            )}
          </div>
          <div className="p-6 space-y-6">
            {/* Campaign Selection */}
            <div>
              <label htmlFor="campaign" className="block text-sm font-medium text-gray-700 mb-2">
                Campaign *
              </label>
              <select
                id="campaign"
                value={selectedCampaign}
                onChange={(e) => setSelectedCampaign(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a campaign</option>
                {campaigns.map((campaign) => (
                  <option key={campaign.id} value={campaign.id}>
                    {campaign.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Postback URL */}
            <div>
              <label htmlFor="postbackUrl" className="block text-sm font-medium text-gray-700 mb-2">
                Postback URL *
              </label>
              <input
                type="url"
                id="postbackUrl"
                value={postbackUrl}
                onChange={(e) => setPostbackUrl(e.target.value)}
                placeholder="https://your-server.com/postback"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                The URL where we'll send conversion notifications
              </p>
            </div>

            {/* HTTP Method */}
            <div>
              <label htmlFor="method" className="block text-sm font-medium text-gray-700 mb-2">
                HTTP Method
              </label>
              <select
                id="method"
                value={method}
                onChange={(e) => setMethod(e.target.value as 'GET' | 'POST')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
              </select>
            </div>

            {/* Custom Parameters */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Custom Parameters
                </label>
                <button
                  type="button"
                  onClick={addCustomParam}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Parameter
                </button>
              </div>

              <div className="space-y-3">
                {Object.entries(customParams).map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={key}
                      onChange={(e) => updateCustomParam(key, e.target.value, value)}
                      placeholder="Parameter name"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <span className="text-gray-500">=</span>
                    <input
                      type="text"
                      value={value}
                      onChange={(e) => updateCustomParam(key, key, e.target.value)}
                      placeholder="Parameter value or {macro}"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => removeCustomParam(key)}
                      className="text-red-600 hover:text-red-700 p-1"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={savePostbackConfig}
                disabled={isSaving || !selectedCampaign || !postbackUrl}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {existingConfig ? 'Update Configuration' : 'Save Configuration'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Available Macros */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Available Macros</h3>
            <p className="text-sm text-gray-600 mt-1">Click to add macro to custom parameters</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableMacros.map((macro) => (
                <div
                  key={macro.key}
                  onClick={() => insertMacro(macro.key)}
                  className="p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <code className="text-sm font-mono text-blue-600">{`{${macro.key}}`}</code>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">{macro.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Generated URL Preview */}
        {selectedCampaign && postbackUrl && (
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Generated Postback URL</h3>
              <p className="text-sm text-gray-600 mt-1">Preview of the URL that will be called on conversions</p>
            </div>
            <div className="p-6">
              <div className="bg-gray-50 rounded-md p-4 mb-4">
                <code className="text-sm text-gray-800 break-all">
                  {generatePostbackUrl()}
                </code>
              </div>
              <div className="text-xs text-gray-600">
                <p className="mb-2"><strong>Method:</strong> {method}</p>
                <p><strong>Note:</strong> Macros like {'{click_id}'} will be replaced with actual values when the postback is triggered.</p>
              </div>
            </div>
          </div>
        )}

        {/* Implementation Guide */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Implementation Guide</h3>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">How S2S Postbacks Work</h4>
                <ol className="text-sm text-gray-600 space-y-2 list-decimal list-inside">
                  <li>User clicks on your ad and is redirected to your landing page</li>
                  <li>User completes a conversion action on your website</li>
                  <li>Your server sends a conversion notification to our postback URL</li>
                  <li>Our system records the conversion and attributes it to the original click</li>
                </ol>
              </div>

              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">Setting Up Your Endpoint</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Your postback URL should be an endpoint on your server that can receive HTTP requests. When a conversion occurs,
                  we'll send a request to this URL with the configured parameters.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h5 className="text-sm font-medium text-blue-800 mb-2">Example Response</h5>
                  <p className="text-sm text-blue-700">
                    Your endpoint should return a 200 HTTP status code to confirm receipt of the conversion data.
                  </p>
                </div>
              </div>

              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">Testing Your Postback</h4>
                <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                  <li>Set up your postback URL endpoint to log incoming requests</li>
                  <li>Click on one of your ads to generate a click_id</li>
                  <li>Trigger a test conversion using our conversion API or pixel</li>
                  <li>Check your server logs to verify the postback was received</li>
                </ol>
              </div>

              <div>
                <h4 className="text-md font-medium text-gray-900 mb-2">Security Considerations</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Use HTTPS for your postback URL to ensure data security</li>
                  <li>• Validate incoming requests to prevent spam or malicious data</li>
                  <li>• Consider implementing request signing for additional security</li>
                  <li>• Monitor your endpoint for unusual traffic patterns</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PostbackTracking() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <PostbackTrackingContent />
    </Suspense>
  );
}
