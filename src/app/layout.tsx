import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Sidebar from "@/components/layout/Sidebar";
import Footer from "@/components/layout/Footer";
import SessionProvider from "@/components/providers/SessionProvider";
import MatomoTracker from "@/components/analytics/MatomoTracker";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Global Ads Media - Ad Exchange Platform",
  description: "Professional ad exchange platform for advertisers and publishers",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased min-h-screen flex flex-col`}>
        <MatomoTracker />
        <SessionProvider>
          <Header />
          <div className="flex flex-1">
            <Sidebar />
            <main className="flex-1 overflow-auto">
              {children}
            </main>
          </div>
          <Footer />
        </SessionProvider>
      </body>
    </html>
  );
}
