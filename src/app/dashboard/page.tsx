import { redirect } from 'next/navigation';
import { auth } from '@/lib/auth';

export default async function DashboardPage() {
  const session = await auth();

  if (!session) {
    redirect('/auth/signin');
  }

  const role = session.user?.role;

  switch (role) {
    case 'advertiser':
      redirect('/advertiser/dashboard');
    case 'publisher':
      redirect('/publisher/dashboard');
    case 'admin':
      redirect('/admin/dashboard');
    case 'dsp':
      redirect('/dsp/dashboard');
    case 'ssp':
      redirect('/ssp/dashboard');
    default:
      redirect('/auth/signin');
  }
}
