'use client';

import Link from 'next/link';

export default function VerificationSuccessPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <img
            className="mx-auto h-12 w-auto"
            src="/logo-black.png"
            alt="Global Ads Media"
          />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Email Verified!
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Your account has been successfully activated
          </p>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Account Activated Successfully
            </h3>

            <p className="text-sm text-gray-600 mb-6">
              Your email has been verified and your account is now active. You can now sign in and start using the platform.
            </p>

            <div className="space-y-4">
              <Link
                href="/auth/signin"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Sign In to Your Account
              </Link>

              <div className="text-xs text-gray-500">
                <p>Welcome to Global Ads Media! We've also sent you a welcome email with getting started information.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
