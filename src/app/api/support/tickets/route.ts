import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');

    let whereClause = 'st.user_id = {userId:UInt32}';
    let queryParams: any = { userId: parseInt(session.user.id) };

    if (status) {
      whereClause += ' AND st.status = {status:String}';
      queryParams.status = status;
    }

    const result = await clickhouse.query({
      query: `
        SELECT 
          st.id,
          st.subject,
          st.message,
          st.priority,
          st.status,
          st.category,
          st.created_at,
          st.updated_at,
          COUNT(str.id) as reply_count
        FROM support_tickets st
        LEFT JOIN support_ticket_replies str ON st.id = str.ticket_id
        WHERE ${whereClause}
        GROUP BY st.id, st.subject, st.message, st.priority, st.status, st.category, st.created_at, st.updated_at
        ORDER BY st.created_at DESC
      `,
      query_params: queryParams,
    });

    const tickets = await result.json();
    return NextResponse.json(tickets.data);

  } catch (error) {
    console.error('Support tickets fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { subject, message, category, priority } = await request.json();

    if (!subject || !message) {
      return NextResponse.json(
        { message: 'Subject and message are required' },
        { status: 400 }
      );
    }

    if (!['low', 'medium', 'high', 'urgent'].includes(priority)) {
      return NextResponse.json(
        { message: 'Invalid priority level' },
        { status: 400 }
      );
    }

    const validCategories = [
      'general', 'technical', 'billing', 'account', 'campaign', 
      'website', 'adzone', 'earnings', 'payout', 'integration', 
      'reporting', 'targeting'
    ];

    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { message: 'Invalid category' },
        { status: 400 }
      );
    }

    // Generate ticket ID
    const ticketId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create support ticket
    await clickhouse.insert({
      table: 'support_tickets',
      values: [{
        id: ticketId,
        user_id: parseInt(session.user.id),
        subject,
        message,
        priority,
        status: 'open',
        category,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Support ticket created successfully',
      ticket_id: ticketId,
    });

  } catch (error) {
    console.error('Support ticket creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
