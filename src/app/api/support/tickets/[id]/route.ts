import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import client from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const ticketId = parseInt(id);

    if (isNaN(ticketId)) {
      return NextResponse.json(
        { message: 'Invalid ticket ID' },
        { status: 400 }
      );
    }

    // Get ticket details
    const ticketResult = await client.query({
      query: `
        SELECT
          st.id,
          st.user_id,
          st.subject,
          st.message,
          st.priority,
          st.status,
          st.category,
          st.created_at,
          st.updated_at,
          u.full_name as user_name,
          u.email as user_email
        FROM support_tickets st
        LEFT JOIN users u ON st.user_id = u.id
        WHERE st.id = {ticketId:UInt64} AND st.user_id = {userId:UInt32}
      `,
      query_params: {
        ticketId,
        userId: parseInt(session.user.id)
      },
    });

    const tickets = await ticketResult.json();

    if (tickets.data.length === 0) {
      return NextResponse.json(
        { message: 'Ticket not found' },
        { status: 404 }
      );
    }

    const ticket = tickets.data[0];

    // Get ticket replies
    const repliesResult = await client.query({
      query: `
        SELECT
          str.id,
          str.message,
          str.is_admin,
          str.created_at,
          u.full_name as user_name,
          u.email as user_email
        FROM support_ticket_replies str
        LEFT JOIN users u ON str.user_id = u.id
        WHERE str.ticket_id = {ticketId:UInt64}
        ORDER BY str.created_at ASC
      `,
      query_params: { ticketId },
    });

    const replies = await repliesResult.json();

    return NextResponse.json({
      ticket,
      replies: replies.data
    });

  } catch (error) {
    console.error('Support ticket fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const ticketId = parseInt(id);
    const { message } = await request.json();

    if (isNaN(ticketId)) {
      return NextResponse.json(
        { message: 'Invalid ticket ID' },
        { status: 400 }
      );
    }

    if (!message || message.trim().length === 0) {
      return NextResponse.json(
        { message: 'Message is required' },
        { status: 400 }
      );
    }

    // Verify ticket belongs to user
    const ticketResult = await client.query({
      query: 'SELECT id, user_id FROM support_tickets WHERE id = {ticketId:UInt64} AND user_id = {userId:UInt32}',
      query_params: {
        ticketId,
        userId: parseInt(session.user.id)
      },
    });

    const tickets = await ticketResult.json();

    if (tickets.data.length === 0) {
      return NextResponse.json(
        { message: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Generate reply ID
    const replyId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Add reply
    await client.insert({
      table: 'support_ticket_replies',
      values: [{
        id: replyId,
        ticket_id: ticketId,
        user_id: parseInt(session.user.id),
        message: message.trim(),
        is_admin: 0,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Update ticket updated_at timestamp
    await client.command({
      query: `
        ALTER TABLE support_tickets
        UPDATE updated_at = {updated_at:String}
        WHERE id = {ticketId:UInt64}
      `,
      query_params: {
        ticketId,
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      },
    });

    return NextResponse.json({
      message: 'Reply added successfully',
      reply_id: replyId,
    });

  } catch (error) {
    console.error('Support ticket reply error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
