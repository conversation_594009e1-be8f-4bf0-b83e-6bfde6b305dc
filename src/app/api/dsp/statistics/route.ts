  import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth'
import clickhouse from '@/lib/clickhouse';

interface PartnerEndpoint {
  id: number;
  name: string;
}

interface StatBreakdown {
  total_requests?: number;
  total_wins?: number;
  total_revenue?: number;
  total_spend?: number;
  avg_win_price?: number;
  win_rate?: number;
  endpoint_id?: number;
  endpoint_name?: string;
  publisher_id?: number;
  publisher_name?: string;
  country?: string;
  os?: string;
  browser?: string;
  device?: string;
}

interface EndpointStats {
  endpoint_id: number;
  endpoint_name: string;
  total_wins: number;
  total_spend: number;
  avg_win_price: number;
  data_source: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const apiKey = searchParams.get('api_key');

    let session = null;
    let userId = null;

    // Check for API key in query parameters
    if (!apiKey) {
      // Fallback to session auth for browser requests
      session = await auth();
      if (!session || session.user?.role !== 'dsp') {
        return NextResponse.json(
          { message: 'Unauthorized - Missing API key' },
          { status: 401 }
        );
      }
      userId = parseInt(session.user.id);
    } else {
      // Validate API key format
      if (!apiKey.startsWith('dsp_')) {
        return NextResponse.json(
          { message: 'Unauthorized - Invalid API key format' },
          { status: 401 }
        );
      }

      // TODO: Validate API key against database and get user ID
      // For now, we'll use a placeholder user ID
      userId = 1; // This should be retrieved from the API key validation
    }

    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const groupBy = searchParams.get('group_by') || 'day';
    console.log('🔍 DSP Statistics API - User ID:', userId, 'Auth Type:', apiKey ? 'API Key' : 'Session');

    // Get all DSP partner endpoint IDs for this user
    const partnerResult = await clickhouse.query({
      query: `
        SELECT id, name FROM partner_endpoints
        WHERE user_id = {userId:UInt32} AND type = 'dsp'
        ORDER BY id
      `,
      query_params: { userId },
    });
    const partnerData = await partnerResult.json();
    console.log('🔍 DSP Statistics API - Partner endpoints found:', partnerData);

    if (partnerData.data.length === 0) {
      console.log('❌ DSP Statistics API - No partner endpoints found for user:', userId);
      return NextResponse.json(
        { message: 'No DSP partner endpoints found for this user' },
        { status: 404 }
      );
    }

    // Use all partner endpoint IDs for statistics
    const partnerIds = partnerData.data.map((p: any) => p.id);
    console.log('🔍 DSP Statistics API - Using Partner IDs:', partnerIds, 'Date range:', dateFrom, 'to', dateTo);

    let stats: any = {};

    if (groupBy === 'day') {
      // Get request data from request_stats table
      const requestResult = await clickhouse.query({
        query: `
          SELECT
            date,
            sum(total_requests) as total_requests,
            sum(total_wins) as total_wins,
            sum(total_revenue) as total_revenue
          FROM request_stats
          WHERE source_type = 'dsp_inbound'
            AND partner_id IN (${partnerIds.join(',')})
            AND date >= {dateFrom:String}
            AND date <= {dateTo:String}
          GROUP BY date
          ORDER BY date
        `,
        query_params: { dateFrom, dateTo },
      });

      const requestData = await requestResult.json();

      // Get spend data from impressions table
      const spendResult = await clickhouse.query({
        query: `
          SELECT
            DATE(timestamp) as date,
            SUM(cost_deducted) as total_spend,
            AVG(cost_deducted) as avg_win_price
          FROM impressions
          WHERE source_type = 2
            AND dsp_partner_id IN (${partnerIds.join(',')})
            AND DATE(timestamp) >= {dateFrom:String}
            AND DATE(timestamp) <= {dateTo:String}
          GROUP BY date
          ORDER BY date
        `,
        query_params: { dateFrom, dateTo },
      });

      const spendData = await spendResult.json();

      // Merge request and spend data
      const processedData = (requestData.data || []).map((requestRow: any) => {
        const spendRow: any = spendData.data.find((s: any) => s.date === requestRow.date);
        return {
          ...requestRow,
          total_spend: spendRow ? parseFloat(spendRow.total_spend || 0) : 0,
          avg_win_price: spendRow ? parseFloat((spendRow.avg_win_price || 0).toFixed(6)) : 0,
          win_rate: requestRow.total_requests > 0 ? Math.round((requestRow.total_wins / requestRow.total_requests) * 100 * 100) / 100 : 0
        };
      });

      stats.daily_breakdown = processedData;

    } else if (groupBy === 'endpoint') {
      // Get request data from request_stats
      const requestResult = await clickhouse.query({
        query: `
          SELECT
            rs.partner_id as endpoint_id,
            pe.name as endpoint_name,
            SUM(rs.total_requests) as total_requests,
            SUM(rs.total_wins) as total_wins,
            SUM(rs.total_revenue) as total_revenue
          FROM request_stats rs
          LEFT JOIN partner_endpoints pe ON rs.partner_id = pe.id
          WHERE rs.source_type = 'dsp_inbound'
            AND rs.partner_id IN (${partnerIds.join(',')})
            AND rs.date >= {dateFrom:String}
            AND rs.date <= {dateTo:String}
          GROUP BY rs.partner_id, pe.name
          ORDER BY total_wins DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const requestData = await requestResult.json();

      // Get spend data from impressions
      const spendResult = await clickhouse.query({
        query: `
          SELECT
            i.dsp_partner_id as endpoint_id,
            SUM(i.cost_deducted) as total_spend,
            AVG(i.cost_deducted) as avg_win_price
          FROM impressions i
          WHERE i.source_type = 2
            AND i.dsp_partner_id IN (${partnerIds.join(',')})
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.dsp_partner_id
          ORDER BY total_spend DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const spendData = await spendResult.json();

      // Merge request and spend data
      const processedData = (requestData.data || []).map((requestRow: any) => {
        const spendRow: any = spendData.data.find((s: any) => s.endpoint_id === requestRow.endpoint_id);
        return {
          ...requestRow,
          total_spend: spendRow ? parseFloat(spendRow.total_spend || 0) : 0,
          avg_win_price: spendRow ? parseFloat(spendRow.avg_win_price || 0) : 0,
          win_rate: requestRow.total_requests > 0 ? Math.round((requestRow.total_wins / requestRow.total_requests) * 100 * 100) / 100 : 0
        };
      });

      stats.endpoint_breakdown = processedData;

    } else if (groupBy === 'publisher') {
      // Use impressions for publisher breakdown
      const publisherResult = await clickhouse.query({
        query: `
          SELECT
            i.website_id as publisher_id,
            CONCAT('Website ', toString(i.website_id)) as publisher_name,
            COUNT(*) as total_wins,
            SUM(i.cost_deducted) as total_spend,
            AVG(i.cost_deducted) as avg_win_price
          FROM impressions i
          WHERE i.source_type = 2
            AND i.partner_endpoint_id IN (${partnerIds.join(',')})
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.website_id
          ORDER BY total_wins DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const publisherData = await publisherResult.json();
      stats.publisher_breakdown = publisherData.data || [];
    } else if (groupBy === 'country') {
      // Use impressions for country breakdown
      const countryResult = await clickhouse.query({
        query: `
          SELECT
            i.country as country,
            COUNT(*) as total_wins,
            SUM(i.cost_deducted) as total_spend,
            AVG(i.cost_deducted) as avg_win_price
          FROM impressions i
          WHERE i.source_type = 2
            AND i.partner_endpoint_id IN (${partnerIds.join(',')})
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.country
          ORDER BY total_wins DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const countryData = await countryResult.json();
      stats.country_breakdown = countryData.data || [];
    } else if (groupBy === 'os') {
      // Use impressions for OS breakdown
      const osResult = await clickhouse.query({
        query: `
          SELECT
            i.os as os,
            COUNT(*) as total_wins,
            SUM(i.cost_deducted) as total_spend,
            AVG(i.cost_deducted) as avg_win_price
          FROM impressions i
          WHERE i.source_type = 2
            AND i.partner_endpoint_id IN (${partnerIds.join(',')})
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.os
          ORDER BY total_wins DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const osData = await osResult.json();
      stats.os_breakdown = osData.data || [];
    } else if (groupBy === 'browser') {
      // Use impressions for browser breakdown
      const browserResult = await clickhouse.query({
        query: `
          SELECT
            i.browser as browser,
            COUNT(*) as total_wins,
            SUM(i.cost_deducted) as total_spend,
            AVG(i.cost_deducted) as avg_win_price
          FROM impressions i
          WHERE i.source_type = 2
            AND i.partner_endpoint_id IN (${partnerIds.join(',')})
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.browser
          ORDER BY total_wins DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const browserData = await browserResult.json();
      stats.browser_breakdown = browserData.data || [];
    } else if (groupBy === 'device') {
      // For device breakdown, use impressions only since request_stats may not have device_type
      const deviceResult = await clickhouse.query({
        query: `
          SELECT
            i.device_type as device,
            COUNT(*) as total_requests,
            COUNT(*) as total_wins,
            SUM(i.cost_deducted) as total_revenue,
            SUM(i.cost_deducted) as total_spend,
            AVG(i.cost_deducted) as avg_win_price
          FROM impressions i
          WHERE i.source_type = 2
            AND i.partner_endpoint_id IN (${partnerIds.join(',')})
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.device_type
          ORDER BY total_wins DESC
        `,
        query_params: { dateFrom, dateTo },
      });

      const deviceData = await deviceResult.json();
      const processedData = (deviceData.data || []).map((row: any) => ({
        ...row,
        win_rate: 100 // Since we're counting impressions, win rate is 100%
      }));
      stats.device_breakdown = processedData;
    }

    // Get request totals from request_stats
    const requestTotalsResult = await clickhouse.query({
      query: `
        SELECT
          sum(total_requests) as total_requests,
          sum(total_wins) as total_wins,
          sum(total_revenue) as total_revenue
        FROM request_stats
        WHERE source_type = 'dsp_inbound'
          AND partner_id IN (${partnerIds.join(',')})
          AND date >= {dateFrom:String}
          AND date <= {dateTo:String}
      `,
      query_params: { dateFrom, dateTo },
    });

    const requestTotalsData = await requestTotalsResult.json();
    const requestTotals: any = requestTotalsData.data[0] || {
      total_requests: 0,
      total_wins: 0,
      total_revenue: 0
    };

    // Get spend totals from impressions
    const spendTotalsResult = await clickhouse.query({
      query: `
        SELECT
          SUM(cost_deducted) as total_spend,
          AVG(cost_deducted) as avg_win_price
        FROM impressions
        WHERE source_type = 2
          AND partner_endpoint_id IN (${partnerIds.join(',')})
          AND DATE(timestamp) >= {dateFrom:String}
          AND DATE(timestamp) <= {dateTo:String}
      `,
      query_params: { dateFrom, dateTo },
    });

    const spendTotalsData = await spendTotalsResult.json();
    const spendTotals: any = spendTotalsData.data[0] || {
      total_spend: 0,
      avg_win_price: 0
    };

    // Combine totals and calculate win rate
    const totals = {
      total_requests: parseInt(String(requestTotals.total_requests) || '0'),
      total_wins: parseInt(String(requestTotals.total_wins) || '0'),
      total_revenue: parseFloat(String(requestTotals.total_revenue) || '0'),
      total_spend: parseFloat(String(spendTotals.total_spend) || '0'),
      avg_win_price: parseFloat(String(spendTotals.avg_win_price) || '0'),
      avg_win_rate: requestTotals.total_requests > 0 ? Math.round((requestTotals.total_wins / requestTotals.total_requests) * 100 * 100) / 100 : 0
    };

    return NextResponse.json({
      success: true,
      data: {
        totals,
        ...stats
      }
    });

  } catch (error: any) {
    console.error('DSP statistics error:', error);
    return NextResponse.json(
      { message: 'Internal server error', error: error.message },
      { status: 500 }
    );
  }
}
