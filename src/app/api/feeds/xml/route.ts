import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'in_page_push';
    const country = searchParams.get('country') || '';
    const limit = parseInt(searchParams.get('limit') || '50');

    // Get active campaigns for the specified format
    let query = `
      SELECT c.*, cr.title, cr.description, cr.image_url
      FROM campaigns c
      LEFT JOIN creatives cr ON c.id = cr.campaign_id
      WHERE c.status = 'active'
      AND c.type = {format:String}
      AND c.start_date <= now()
      AND (c.end_date IS NULL OR c.end_date >= now())
      AND c.daily_spent < c.daily_budget
      AND c.total_spent < c.total_budget
    `;

    let queryParams: any = { format };

    if (country) {
      query += ` AND (length(c.targeting_geo) = 0 OR has(c.targeting_geo, {country:String}))`;
      queryParams.country = country;
    }

    query += ` ORDER BY c.cpm_bid DESC LIMIT {limit:UInt32}`;
    queryParams.limit = limit;

    const campaignsResult = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const campaigns = await campaignsResult.json();

    // Generate XML feed
    let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n';

    if (format === 'in_page_push') {
      xmlContent += '<push_ads>\n';

      campaigns.data.forEach((campaign: any) => {
        xmlContent += '  <ad>\n';
        xmlContent += `    <id>${campaign.id}</id>\n`;
        xmlContent += `    <title><![CDATA[${campaign.title || 'Advertisement'}]]></title>\n`;
        xmlContent += `    <description><![CDATA[${campaign.description || 'Click to learn more'}]]></description>\n`;
        xmlContent += `    <url><![CDATA[${campaign.landing_url}]]></url>\n`;
        xmlContent += `    <image><![CDATA[${campaign.image_url || ''}]]></image>\n`;
        xmlContent += `    <bid>${campaign.cpm_bid}</bid>\n`;
        xmlContent += `    <category>general</category>\n`;
        xmlContent += '  </ad>\n';
      });

      xmlContent += '</push_ads>';

    } else if (format === 'popup') {
      xmlContent += '<popup_ads>\n';

      campaigns.data.forEach((campaign: any) => {
        xmlContent += '  <ad>\n';
        xmlContent += `    <id>${campaign.id}</id>\n`;
        xmlContent += `    <url><![CDATA[${campaign.landing_url}]]></url>\n`;
        xmlContent += `    <bid>${campaign.cpm_bid}</bid>\n`;
        xmlContent += `    <frequency>1</frequency>\n`;
        xmlContent += `    <category>general</category>\n`;
        xmlContent += '  </ad>\n';
      });

      xmlContent += '</popup_ads>';
    }

    return new NextResponse(xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=300', // 5 minutes cache
      },
    });

  } catch (error) {
    console.error('XML feed error:', error);
    return new NextResponse(
      '<?xml version="1.0" encoding="UTF-8"?><error>Internal server error</error>',
      {
        status: 500,
        headers: { 'Content-Type': 'application/xml' },
      }
    );
  }
}
