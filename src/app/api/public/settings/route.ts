import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const result = await clickhouse.query({
      query: `
        SELECT setting_key, setting_value
        FROM platform_settings
        WHERE setting_key IN ('minimum_payout', 'platform_name', 'minimum_cpm_banner', 'minimum_cpm_native', 'minimum_cpm_in_page_push', 'minimum_cpm_popup')
        ORDER BY setting_key
      `,
    });

    const settingsData = await result.json();

    // Convert to object format
    const settings: Record<string, any> = {};
    settingsData.data.forEach((row: any) => {
      // Convert numeric values
      if (['minimum_payout', 'minimum_cpm_banner', 'minimum_cpm_native', 'minimum_cpm_in_page_push', 'minimum_cpm_popup'].includes(row.setting_key)) {
        settings[row.setting_key] = parseFloat(row.setting_value) || 0;
      } else {
        settings[row.setting_key] = row.setting_value;
      }
    });

    // Set defaults if not found
    if (!settings.minimum_payout) settings.minimum_payout = 50;
    if (!settings.platform_name) settings.platform_name = 'RTB Platform';
    if (!settings.minimum_cpm_banner) settings.minimum_cpm_banner = 0.5;
    if (!settings.minimum_cpm_native) settings.minimum_cpm_native = 0.5;
    if (!settings.minimum_cpm_in_page_push) settings.minimum_cpm_in_page_push = 0.5;
    if (!settings.minimum_cpm_popup) settings.minimum_cpm_popup = 0.5;

    return NextResponse.json(settings);

  } catch (error) {
    console.error('Public settings fetch error:', error);
    // Return defaults on error
    return NextResponse.json({
      minimum_payout: 50,
      platform_name: 'RTB Platform',
      minimum_cpm_banner: 0.5,
      minimum_cpm_native: 0.5,
      minimum_cpm_in_page_push: 0.5,
      minimum_cpm_popup: 0.5,
    });
  }
}
