import { NextRequest, NextResponse } from 'next/server';
import ApiAuthMiddleware, { AuthenticatedRequest } from '@/lib/api-auth-middleware';
import clickhouse from '@/lib/clickhouse';

/**
 * DSP/SSP Stats API Endpoint
 * Provides comprehensive statistics for DSPs and SSPs
 */

async function handleStatsRequest(request: AuthenticatedRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const apiKey = request.apiKey!;
    
    // Parse query parameters
    const startDate = searchParams.get('start_date') || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
    const endDate = searchParams.get('end_date') || new Date().toISOString().slice(0, 10);
    const granularity = searchParams.get('granularity') || 'day'; // hour, day, week, month
    const metrics = searchParams.get('metrics')?.split(',') || ['impressions', 'clicks', 'revenue', 'cost'];
    const campaignId = searchParams.get('campaign_id');
    const websiteId = searchParams.get('website_id');
    const format = searchParams.get('format') || 'json'; // json, csv

    // Validate date range
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return NextResponse.json({
        error: 'Invalid date format. Use YYYY-MM-DD format.',
        code: 'INVALID_DATE_FORMAT',
      }, { status: 400 });
    }

    if (end < start) {
      return NextResponse.json({
        error: 'End date must be after start date.',
        code: 'INVALID_DATE_RANGE',
      }, { status: 400 });
    }

    // Check date range limit (max 90 days)
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff > 90) {
      return NextResponse.json({
        error: 'Date range cannot exceed 90 days.',
        code: 'DATE_RANGE_TOO_LARGE',
      }, { status: 400 });
    }

    // Build base query conditions
    let whereConditions = [`timestamp >= '${startDate} 00:00:00'`, `timestamp <= '${endDate} 23:59:59'`];
    
    // Filter by partner type and ID
    if (apiKey.keyType === 'dsp') {
      whereConditions.push(`dsp_partner_id = ${apiKey.partnerId}`);
    } else if (apiKey.keyType === 'ssp') {
      whereConditions.push(`ssp_partner_id = ${apiKey.partnerId}`);
    }

    // Additional filters
    if (campaignId) {
      whereConditions.push(`campaign_id = ${parseInt(campaignId)}`);
    }
    if (websiteId) {
      whereConditions.push(`website_id = ${parseInt(websiteId)}`);
    }

    const whereClause = whereConditions.join(' AND ');

    // Build time grouping
    let timeGrouping = '';
    switch (granularity) {
      case 'hour':
        timeGrouping = "toStartOfHour(timestamp) as time_period";
        break;
      case 'day':
        timeGrouping = "toDate(timestamp) as time_period";
        break;
      case 'week':
        timeGrouping = "toMonday(timestamp) as time_period";
        break;
      case 'month':
        timeGrouping = "toStartOfMonth(timestamp) as time_period";
        break;
      default:
        timeGrouping = "toDate(timestamp) as time_period";
    }

    // Get main statistics
    const statsQuery = `
      SELECT 
        ${timeGrouping},
        count() as impressions,
        sum(CASE WHEN source_type = 'click' THEN 1 ELSE 0 END) as clicks,
        sum(cost_deducted) as cost,
        sum(publisher_revenue) as revenue,
        avg(cost_deducted) as avg_cpm,
        countDistinct(campaign_id) as unique_campaigns,
        countDistinct(website_id) as unique_websites,
        countDistinct(ip_address) as unique_users
      FROM impressions 
      WHERE ${whereClause}
      GROUP BY time_period
      ORDER BY time_period ASC
    `;

    const statsResult = await clickhouse.query({ query: statsQuery });
    const statsData = await statsResult.json();

    // Get top performing campaigns (if DSP)
    let topCampaigns = [];
    if (apiKey.keyType === 'dsp' || apiKey.keyType === 'admin') {
      const campaignsQuery = `
        SELECT 
          campaign_id,
          count() as impressions,
          sum(cost_deducted) as cost,
          avg(cost_deducted) as avg_cpm,
          countDistinct(website_id) as websites_reached
        FROM impressions 
        WHERE ${whereClause}
        GROUP BY campaign_id
        ORDER BY impressions DESC
        LIMIT 10
      `;

      const campaignsResult = await clickhouse.query({ query: campaignsQuery });
      const campaignsData = await campaignsResult.json();
      topCampaigns = campaignsData.data;
    }

    // Get top performing websites (if SSP)
    let topWebsites = [];
    if (apiKey.keyType === 'ssp' || apiKey.keyType === 'admin') {
      const websitesQuery = `
        SELECT 
          website_id,
          count() as impressions,
          sum(publisher_revenue) as revenue,
          avg(publisher_revenue) as avg_rpm,
          countDistinct(campaign_id) as campaigns_served
        FROM impressions 
        WHERE ${whereClause}
        GROUP BY website_id
        ORDER BY impressions DESC
        LIMIT 10
      `;

      const websitesResult = await clickhouse.query({ query: websitesQuery });
      const websitesData = await websitesResult.json();
      topWebsites = websitesData.data;
    }

    // Get geographic breakdown
    const geoQuery = `
      SELECT 
        country,
        count() as impressions,
        sum(cost_deducted) as cost,
        sum(publisher_revenue) as revenue
      FROM impressions 
      WHERE ${whereClause}
      GROUP BY country
      ORDER BY impressions DESC
      LIMIT 20
    `;

    const geoResult = await clickhouse.query({ query: geoQuery });
    const geoData = await geoResult.json();

    // Get device breakdown
    const deviceQuery = `
      SELECT 
        device_type,
        count() as impressions,
        sum(cost_deducted) as cost,
        sum(publisher_revenue) as revenue
      FROM impressions 
      WHERE ${whereClause}
      GROUP BY device_type
      ORDER BY impressions DESC
    `;

    const deviceResult = await clickhouse.query({ query: deviceQuery });
    const deviceData = await deviceResult.json();

    // Calculate totals
    const totals = statsData.data.reduce((acc: any, row: any) => ({
      impressions: acc.impressions + row.impressions,
      clicks: acc.clicks + row.clicks,
      cost: acc.cost + row.cost,
      revenue: acc.revenue + row.revenue,
    }), { impressions: 0, clicks: 0, cost: 0, revenue: 0 });

    // Build response
    const response = {
      success: true,
      data: {
        summary: {
          total_impressions: totals.impressions,
          total_clicks: totals.clicks,
          total_cost: totals.cost,
          total_revenue: totals.revenue,
          ctr: totals.impressions > 0 ? (totals.clicks / totals.impressions * 100).toFixed(4) : '0.0000',
          avg_cpm: totals.impressions > 0 ? (totals.cost / totals.impressions * 1000).toFixed(4) : '0.0000',
          avg_rpm: totals.impressions > 0 ? (totals.revenue / totals.impressions * 1000).toFixed(4) : '0.0000',
        },
        time_series: statsData.data,
        top_campaigns: topCampaigns,
        top_websites: topWebsites,
        geographic_breakdown: geoData.data,
        device_breakdown: deviceData.data,
      },
      meta: {
        start_date: startDate,
        end_date: endDate,
        granularity,
        metrics,
        total_records: statsData.data.length,
        api_key_type: apiKey.keyType,
        partner_id: apiKey.partnerId,
        generated_at: new Date().toISOString(),
      },
    };

    // Handle CSV format
    if (format === 'csv') {
      const csvData = this.convertToCSV(statsData.data);
      return new NextResponse(csvData, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="stats_${startDate}_${endDate}.csv"`,
        },
      });
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Stats API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

function convertToCSV(data: any[]): string {
  if (data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(','),
    ...data.map(row => headers.map(header => {
      const value = row[header];
      return typeof value === 'string' ? `"${value}"` : value;
    }).join(','))
  ];
  
  return csvRows.join('\n');
}

// Export the authenticated handler
export const GET = ApiAuthMiddleware.withAuth(handleStatsRequest, {
  requiredPermission: 'stats:read',
  allowedKeyTypes: ['dsp', 'ssp', 'admin'],
});

// Handle unsupported methods
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
