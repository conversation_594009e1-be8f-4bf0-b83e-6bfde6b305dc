import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { RequestTracker } from '@/lib/request-tracker';

interface PartnerEndpointData {
  id: number;
  user_id: number;
  type: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const campaignId = searchParams.get('campaign_id');
    const price = searchParams.get('price');
    const partnerId = searchParams.get('partner_id');

    if (!campaignId) {
      return new NextResponse('Missing campaign_id', { status: 400 });
    }

    const winPrice = parseFloat(price || '0');

    // Get campaign and partner details for proper win tracking
    const campaignResult = await clickhouse.query({
      query: `
        SELECT c.*, u.id as advertiser_id
        FROM campaigns c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.id = {campaignId:UInt32}
      `,
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return new NextResponse('Campaign not found', { status: 404 });
    }

    const campaign = campaigns.data[0];

    // Get partner details if partnerId is provided
    let partnerEndpointId = 0;
    let supplierId = 0;

    if (partnerId) {
      const partnerResult = await clickhouse.query({
        query: `
          SELECT id, user_id, type
          FROM partner_endpoints
          WHERE id = {partnerId:UInt32}
        `,
        query_params: { partnerId: parseInt(partnerId) },
      });

      const partners = await partnerResult.json();
      if (partners.data.length > 0) {
        const partnerData = partners.data[0] as PartnerEndpointData;
        partnerEndpointId = partnerData.id;
        supplierId = partnerData.user_id;
      }
    }

    // Get actual revenue share settings from platform_settings
    const revenueShareResult = await clickhouse.query({
      query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
    });

    const revenueShareData = await revenueShareResult.json();
    const publisherRevenueShare = revenueShareData.data.length > 0 ? parseFloat((revenueShareData.data[0] as any).setting_value) : 20;

    // Calculate revenue split using actual settings
    const supplierRevenue = winPrice * (publisherRevenueShare / 100);
    const platformRevenue = winPrice - supplierRevenue;

    // Track DSP RTB win
    await RequestTracker.trackWin({
      winType: 'dsp_rtb',
      campaignId: parseInt(campaignId),
      winnerId: (campaign as any).advertiser_id,
      supplierId: supplierId,
      partnerEndpointId: partnerEndpointId,
      winPrice: winPrice,
      platformRevenue: platformRevenue,
      supplierRevenue: supplierRevenue,
    });

    // Update campaign spend
    if (winPrice > 0) {
      await clickhouse.command({
        query: `
          ALTER TABLE campaigns
          UPDATE daily_spent = daily_spent + {winPrice:Decimal(18,8)},
                 total_spent = total_spent + {winPrice:Decimal(18,8)}
          WHERE id = {campaignId:UInt32}
        `,
        query_params: {
          winPrice,
          campaignId: parseInt(campaignId),
        },
      });
    }

    return new NextResponse('OK', { status: 200 });

  } catch (error) {
    console.error('RTB win notification error:', error);
    return new NextResponse('Error', { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  // Handle POST requests for win notifications
  return GET(request);
}
