import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import logger from '@/lib/logger';
import { Campaign } from '@/lib/auction-service';
import { clickHouseBatchWriter } from '@/lib/clickhouse-batch-writer';

export async function POST(request: NextRequest) {
  try {
    const xmlBody = await request.text();
    const { searchParams } = new URL(request.url);
    const partnerId = searchParams.get('partner_id');

    // Log incoming request
    logger.ssp.info(`SSP XML Bid Request from partner ${partnerId || 'unknown'}: Received POST request`);

    // Parse XML bid request (simplified parser for demo)
    const parseXMLBidRequest = (xml: string) => {
      // This is a simplified XML parser - in production, use a proper XML parser
      const idMatch = xml.match(/<id[^>]*>([^<]+)<\/id>/);
      const formatMatch = xml.match(/<format[^>]*>([^<]+)<\/format>/);
      const countryMatch = xml.match(/<country[^>]*>([^<]+)<\/country>/);
      const publisherMatch = xml.match(/<publisher[^>]*>([^<]+)<\/publisher>/);
      const siteMatch = xml.match(/<site[^>]*>([^<]+)<\/site>/);
      const bidfloorMatch = xml.match(/<bidfloor[^>]*>([^<]+)<\/bidfloor>/);

      return {
        id: idMatch ? idMatch[1] : `xml_${Date.now()}`,
        format: formatMatch ? formatMatch[1] : 'in_page_push',
        country: countryMatch ? countryMatch[1] : '',
        publisher: publisherMatch ? publisherMatch[1] : '',
        site: siteMatch ? siteMatch[1] : '',
        bidfloor: bidfloorMatch ? parseFloat(bidfloorMatch[1]) : 0.01,
      };
    };

    const bidRequest = parseXMLBidRequest(xmlBody);

    // Get eligible campaigns for XML format (In-Page Push, Popup)
    const campaignsResult = await clickhouse.query({
      query: `
        SELECT c.*, u.balance as advertiser_balance, u.role as user_role
        FROM campaigns c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.status = 'active'
        AND c.type = {format:String}
        AND c.start_date <= now()
        AND (c.end_date IS NULL OR c.end_date >= now())
        AND c.cpm_bid >= {bidfloor:Decimal(10,4)}
        AND (
          u.role = 'dsp' OR
          (
            u.role = 'advertiser'
            AND u.balance >= (c.cpm_bid / 1000)
            AND (c.daily_budget = 0 OR c.daily_spent < c.daily_budget)
            AND (c.total_budget = 0 OR c.total_spent < c.total_budget)
          )
        )
        AND (length(c.targeting_geo) = 0 OR has(c.targeting_geo, {country:String}))
        ORDER BY c.cpm_bid DESC
        LIMIT 1
      `,
      query_params: {
        format: bidRequest.format,
        bidfloor: bidRequest.bidfloor,
        country: bidRequest.country || 'US',
      },
    });

    const campaigns = await campaignsResult.json() as { data: Campaign[] };
    const winningCampaign: Campaign = campaigns.data[0];

    if (!winningCampaign) {
      logger.ssp.info(`SSP XML Bid Request from partner ${partnerId || 'unknown'}: No winning campaign found.`);
      // No bid response
      return new NextResponse(
        '<?xml version="1.0" encoding="UTF-8"?><response><status>no_bid</status></response>',
        {
          headers: { 'Content-Type': 'application/xml' },
        }
      );
    }

    // Ensure cpm_bid is a valid number before calculating bidPrice
    if (typeof winningCampaign.cpm_bid !== 'number' || isNaN(winningCampaign.cpm_bid)) {
      logger.ssp.warn(`SSP XML Bid Request from partner ${partnerId || 'unknown'}: Winning campaign cpm_bid is invalid:`, winningCampaign.cpm_bid);
      return new NextResponse(
        '<?xml version="1.0" encoding="UTF-8"?><response><status>no_bid</status></response>',
        {
          headers: { 'Content-Type': 'application/xml' },
        }
      );
    }

    // Calculate bid price based on partner type (same logic as OpenRTB)
    let bidPrice = winningCampaign.cpm_bid / 1000;

    if (partnerId) {
      const partnerResult = await clickhouse.query({
        query: `
          SELECT type, revenue_share, bid_price_format
          FROM partner_endpoints
          WHERE id = {partnerId:UInt32} AND status = 'active'
        `,
        query_params: { partnerId: parseInt(partnerId) },
      });

      const partners = await partnerResult.json();
      if (partners.data.length > 0) {
        const partner = partners.data[0] as any;
        if (partner.type === 'ssp') {
          let revenueShare = partner.revenue_share;

          // Fallback to global settings if partner revenue share is 0 or null
          if (!revenueShare || revenueShare <= 0) {
            const revenueShareResult = await clickhouse.query({
              query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
            });

            const revenueShareData = await revenueShareResult.json();
            revenueShare = revenueShareData.data.length > 0
              ? parseFloat((revenueShareData.data[0] as any).setting_value)
              : 20; // Default fallback - 20% as specified
          }

          // Calculate bid price with revenue share
          bidPrice = (winningCampaign.cpm_bid * (revenueShare / 100)) / 1000;

          // Convert to SSP's preferred format
          const sspBidPriceFormat = partner.bid_price_format || 'cpm';
          if (sspBidPriceFormat === 'cpv' || sspBidPriceFormat === 'cpc') {
            // Convert CPM to CPV/CPC (divide by 1000)
            bidPrice = bidPrice / 1000;
          }
          // If sspBidPriceFormat is 'cpm' or 'ecpm', keep as-is (already in CPM)
        }
      }
    }

    // Log the winner information
    logger.ssp.info(`SSP XML Bid Request from partner ${partnerId || 'unknown'}: Winner found: local with bid $${bidPrice}`);

    // Generate XML bid response
    let xmlResponse = '<?xml version="1.0" encoding="UTF-8"?>\n';

    if (bidRequest.format === 'in_page_push') {
      xmlResponse += '<push_response>\n';
      xmlResponse += `  <request_id>${bidRequest.id}</request_id>\n`;
      xmlResponse += `  <status>success</status>\n`;
      xmlResponse += '  <ad>\n';
      xmlResponse += `    <id>${winningCampaign.id}</id>\n`;
      xmlResponse += `    <title><![CDATA[${winningCampaign.title || 'Advertisement'}]]></title>\n`;
      xmlResponse += `    <description><![CDATA[${winningCampaign.description || 'Click to learn more'}]]></description>\n`;
      xmlResponse += `    <url><![CDATA[${winningCampaign.landing_url}]]></url>\n`;
      xmlResponse += `    <image><![CDATA[${winningCampaign.image_url || ''}]]></image>\n`;
      xmlResponse += `    <bid>${bidPrice}</bid>\n`;
      xmlResponse += `    <impression_url><![CDATA[${process.env.NEXTAUTH_URL}/api/rtb/impression?campaign_id=${winningCampaign.id}&format=xml&partner_id=${partnerId || ''}]]></impression_url>\n`;
      xmlResponse += `    <click_url><![CDATA[${process.env.NEXTAUTH_URL}/api/click?campaign_id=${winningCampaign.id}&redirect=${encodeURIComponent(winningCampaign.landing_url)}]]></click_url>\n`;
      xmlResponse += '  </ad>\n';
      xmlResponse += '</push_response>';

    } else if (bidRequest.format === 'popup') {
      xmlResponse += '<popup_response>\n';
      xmlResponse += `  <request_id>${bidRequest.id}</request_id>\n`;
      xmlResponse += `  <status>success</status>\n`;
      xmlResponse += '  <ad>\n';
      xmlResponse += `    <id>${winningCampaign.id}</id>\n`;
      xmlResponse += `    <url><![CDATA[${winningCampaign.landing_url}]]></url>\n`;
      xmlResponse += `    <bid>${bidPrice}</bid>\n`;
      xmlResponse += `    <impression_url><![CDATA[${process.env.NEXTAUTH_URL}/api/rtb/impression?campaign_id=${winningCampaign.id}&format=xml&partner_id=${partnerId || ''}]]></impression_url>\n`;
      xmlResponse += '  </ad>\n';
      xmlResponse += '</popup_response>';
    }

    // Log the bid for tracking
    await clickHouseBatchWriter.write('rtb_wins', {
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        campaign_id: winningCampaign.id,
        win_price: bidPrice,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
    });

    return new NextResponse(xmlResponse, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    logger.ssp.error('XML RTB error:', error);
    return new NextResponse(
      '<?xml version="1.0" encoding="UTF-8"?><response><status>error</status></response>',
      {
        status: 500,
        headers: { 'Content-Type': 'application/xml' },
      }
    );
  }
}

export async function GET(request: NextRequest) {
  // Handle GET requests for XML feeds (similar to existing /api/feeds/xml)
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'in_page_push';
    const country = searchParams.get('country') || '';
    const limit = parseInt(searchParams.get('limit') || '50');

    // Log incoming GET request
    logger.ssp.info(`SSP XML Bid Request: Received GET request for format ${format}`);

    // Get active campaigns for the specified format
    let query = `
      SELECT c.*, cr.title, cr.description, cr.image_url
      FROM campaigns c
      LEFT JOIN creatives cr ON c.id = cr.campaign_id
      WHERE c.status = 'active'
      AND c.type = {format:String}
      AND c.start_date <= now()
      AND (c.end_date IS NULL OR c.end_date >= now())
      AND c.daily_spent < c.daily_budget
      AND c.total_spent < c.total_budget
    `;

    let queryParams: any = { format };

    if (country) {
      query += ` AND (length(c.targeting_geo) = 0 OR has(c.targeting_geo, {country:String}))`;
      queryParams.country = country;
    }

    query += ` ORDER BY c.cpm_bid DESC LIMIT {limit:UInt32}`;
    queryParams.limit = limit;

    const campaignsResult = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const campaigns = await campaignsResult.json();

    // Generate XML feed (reuse existing logic from /api/feeds/xml)
    let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n';

    if (format === 'in_page_push') {
      xmlContent += '<push_ads>\n';

      campaigns.data.forEach((campaign) => {
        const typedCampaign = campaign as Campaign;
        xmlContent += '  <ad>\n';
        xmlContent += `    <id>${typedCampaign.id}</id>\n`;
        xmlContent += `    <title><![CDATA[${typedCampaign.title || 'Advertisement'}]]></title>\n`;
        xmlContent += `    <description><![CDATA[${typedCampaign.description || 'Click to learn more'}]]></description>\n`;
        xmlContent += `    <url><![CDATA[${typedCampaign.landing_url}]]></url>\n`;
        xmlContent += `    <image><![CDATA[${typedCampaign.image_url || ''}]]></image>\n`;
        xmlContent += `    <bid>${typedCampaign.cpm_bid}</bid>\n`;
        xmlContent += `    <category>general</category>\n`;
        xmlContent += '  </ad>\n';
      });

      xmlContent += '</push_ads>';

    } else if (format === 'popup') {
      xmlContent += '<popup_ads>\n';

      campaigns.data.forEach((campaign) => {
        const typedCampaign = campaign as Campaign;
        xmlContent += '  <ad>\n';
        xmlContent += `    <id>${typedCampaign.id}</id>\n`;
        xmlContent += `    <url><![CDATA[${typedCampaign.landing_url}]]></url>\n`;
        xmlContent += `    <bid>${typedCampaign.cpm_bid}</bid>\n`;
        xmlContent += `    <frequency>1</frequency>\n`;
        xmlContent += `    <category>general</category>\n`;
        xmlContent += '  </ad>\n';
      });

      xmlContent += '</popup_ads>';
    }

    return new NextResponse(xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'no-cache',
      },
    });
  } catch (error) {
    logger.ssp.error('Error generating SSP XML feed:', error);
    return new NextResponse(
      '<?xml version="1.0" encoding="UTF-8"?><response><status>error</status></response>',
      {
        status: 500,
        headers: { 'Content-Type': 'application/xml' },
      }
    );
  }
}
