import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { CostProcessor } from '@/lib/cost-processor';
import { RequestTracker } from '@/lib/request-tracker';
import { cache, CACHE_KEYS, CACHE_TTL, CacheHelper } from '@/lib/cache';
import { AsyncDSPService } from '@/lib/async-dsp-service';
import logger from '@/lib/logger';

interface Website {
  url: string;
}

// Type definitions for database objects
interface Campaign {
  id: number;
  cpm_bid: number; // mCPM (milli CPM), e.g., 1.2 for $1.20 CPM
  landing_url: string;
  creative_type: string | number;
  banner_image_url?: string; // Optional for banner campaigns
  js_tag?: string; // Optional for JS campaigns
  title?: string; // Optional for native campaigns
  description?: string; // Optional for native campaigns
  width?: number; // Optional, for banner size
  height?: number; // Optional, for banner size
  // Add other relevant campaign fields as needed
}

interface Partner {
  id: number;
  name: string;
  endpoint_url: string;
  protocol: string;
  openrtb_version?: string;
  api_key?: string;
  timeout_ms?: number;
  auth_type?: string;
  auth_credentials?: string;
  seat_id?: string;
  targeting?: string;
  bid_price_format?: string;
  qps_limit?: number;
  [key: string]: any;
}

export interface AuctionRequest {
  zoneId: number;
  websiteId: number;
  format: string;
  size: string;
  userAgent: string;
  ipAddress: string;
  country: string;
  state: string;
  city: string;
  deviceType: string;
  os: string;
  browser: string;
  referrerUrl?: string;
  connectionType?: string;
  publisherId?: number;
}

export interface AuctionBid {
  source: 'local' | 'dsp' | 'ssp'; // Added 'ssp' source
  campaignId?: number;
  partnerId?: number;
  partnerName?: string;
  bidPrice: number; // CPM
  adMarkup?: string;
  landingUrl?: string;
  creativeType?: string;
  winUrl?: string;
  data?: any; // Original data object (campaign, partner, etc.)
  sspRevenueShare?: number; // Added for SSP inventory
}

export interface AuctionResult {
  winner: AuctionBid | null;
  allBids: AuctionBid[];
  auctionId: string;
}

// Type definitions for incoming RTB Bid Request
interface BidRequest {
  id: string;
  imp: Array<{
    id: string;
    banner?: {
      w: number;
      h: number;
      format?: Array<{ w: number; h: number }>;
    };
    native?: {
      request: string;
      ver?: string;
    };
    bidfloor?: number;
    bidfloorcur?: string;
    tagid?: string; // Assuming zoneId might be passed as tagid in impression
  }>;
  site?: {
    id: string;
    name?: string;
    domain?: string;
    cat?: string[];
    page?: string;
    ref?: string;
    publisher?: {
      id: string;
      name?: string;
    };
  };
  app?: {
    id: string;
    name?: string;
    bundle?: string;
    cat?: string[];
    publisher?: {
      id: string;
      name?: string;
    };
  };
  device?: {
    ua?: string;
    geo?: {
      country?: string;
      region?: string;
      state?: string;
      city?: string;
      lat?: number;
      lon?: number;
    };
    ip?: string;
    devicetype?: number;
    os?: string;
    osv?: string;
    h?: number;
    w?: number;
  };
  user?: {
    id?: string;
    buyeruid?: string;
    geo?: {
      country?: string;
      region?: string;
      state?: string;
      city?: string;
    };
  };
  at?: number;
  tmax?: number;
  cur?: string[];
  bcat?: string[];
  badv?: string[];
}

interface BidResponse {
  id: string;
  seatbid?: Array<{
    bid: Array<{
      id: string;
      impid: string;
      price: number;
      adid?: string;
      nurl?: string;
      adm?: string;
      adomain?: string[];
      cid: string;
      crid: string;
      cat?: string[];
      w?: number;
      h?: number;
    }>;
    seat?: string;
  }>;
  bidid?: string;
  cur?: string;
  nbr?: number;
}

// Define a type for the fetched partner data from cache/DB
interface CachedPartnerData {
  type: string;
  name: string; // Added name property
  revenue_share?: number; // Optional field
  user_id: number;
  bid_price_format?: string; // Optional field
}

// Define a type for fetched SSP Inventory data from cache/DB
interface CachedSSPInventoryData {
  id: number;
  ssp_id: number;
  format: string;
  size: string;
  floor_price: number;
  auction_type: number;
  revenue_share: number; // Revenue share of the SSP
  ssp_name: string; // Name of the SSP partner
}

export async function POST(request: NextRequest) {
  // Start timing the request processing
  const startTime = process.hrtime.bigint();

  let bidRequest: BidRequest | undefined; // Declare bidRequest here to be available in the outer catch
  let zoneId: number | undefined; // Declare zoneId here

  try {
    // Parse request body with error handling
    try {
      const body = await request.text();
      if (!body || body.trim() === '') {
        logger.dsp.warn('RTB bid request: Empty body received');
        // Exit early with a no-bid response for empty body
        return NextResponse.json({
          id: 'test_request',
          nbr: 2, // Invalid request
        } as BidResponse);
      }
      bidRequest = JSON.parse(body);
    } catch (parseError) {
      logger.dsp.error('RTB bid request JSON parse error:', parseError);
       // Exit early with a no-bid response for parsing errors
      return NextResponse.json({
        id: 'parse_error',
        nbr: 2, // Invalid request
      } as BidResponse);
    }

    const { searchParams } = new URL(request.url);
    const partnerId = searchParams.get('partner_id');

    // Validate bid request structure after parsing
    if (!bidRequest || !bidRequest.id || !bidRequest.imp || bidRequest.imp.length === 0) {
      logger.dsp.warn('RTB bid request: Invalid request format after parsing', { bidRequestId: bidRequest?.id });
       // Exit early for invalid request structure
      return NextResponse.json({
        id: bidRequest?.id || 'unknown',
        nbr: 2, // Invalid request
      } as BidResponse);
    }

    // Determine zoneId - try tagid from the first impression object
    // OpenRTB 2.5 allows tagid to carry site-specific impression IDs, often used for zone IDs.
    zoneId = bidRequest.imp[0]?.tagid ? parseInt(bidRequest.imp[0].tagid) : undefined; // Assign to zoneId

    if (zoneId === undefined || isNaN(zoneId)) {
       logger.dsp.error('RTB bid request error: Missing or invalid zoneId in tagid', { bidRequestId: bidRequest.id, tagid: bidRequest.imp[0]?.tagid });
        // Exit early for missing or invalid zoneId
       return NextResponse.json({
         id: bidRequest.id,
         nbr: 2,
       } as BidResponse);
    }

    logger.dsp.info(`RTB bid request received for zone ${zoneId} from partner ${partnerId || 'unknown'}`, { bidRequestId: bidRequest.id });

    // Get partner information if this is from an SSP (identified by partnerId in query param)
    let partnerRevenueShare: number | null = null;
    let isSSPPartner = false;
    let sspBidPriceFormat = 'cpm'; // Default format
    let partnerName = 'unknown';

    if (partnerId) {
      const parsedPartnerId = parseInt(partnerId);
      if (!isNaN(parsedPartnerId)) {
      // Use cache for partner lookup
        const partner = await CacheHelper.getOrSet<
          CachedPartnerData | null
        >(
          CACHE_KEYS.PARTNER_ENDPOINT(parsedPartnerId),
        async () => {
            logger.dsp.info(`Partner ${partnerId}: Cache miss, querying DB...`);
          const partnerResult = await clickhouse.query({
            query: `
                SELECT type, revenue_share, user_id, name, bid_price_format
              FROM partner_endpoints
              WHERE id = {partnerId:UInt32} AND status = 'active'
            `,
              query_params: { partnerId: parsedPartnerId },
          });
          const partners = await partnerResult.json();
            // Ensure returned data matches CachedPartnerData type and has 'name'
            const partnerData = partners.data.length > 0 ? (partners.data[0] as CachedPartnerData) : null; // Assert type here
            if (partnerData) {
                 logger.dsp.info(`Partner ${partnerId}: DB query returned partner data.`);
                 // Assign name from partnerData if available
                 partnerName = partnerData.name || partnerName;
            } else {
                 logger.dsp.warn(`Partner ${partnerId}: DB query did not find active partner.`);
            }
            return partnerData;
        },
        CACHE_TTL.PARTNERS
      );

        // Check if partner is an SSP and has required properties
        if (partner) {
           if (partner.type === 'ssp') {
        isSSPPartner = true;
             // Safely access revenue_share, defaulting to null if undefined
             partnerRevenueShare = partner.revenue_share !== undefined ? partner.revenue_share : null;
             // Safely access bid_price_format, defaulting to 'cpm' if undefined
        sspBidPriceFormat = partner.bid_price_format || 'cpm';
             logger.dsp.info(`Partner ${partnerId} is an SSP. Revenue Share: ${partnerRevenueShare}, Bid Format: ${sspBidPriceFormat}`);
           } else {
             logger.dsp.info(`Partner ${partnerId} is not an SSP (Type: ${partner.type || 'unknown'}).`);
           }
        } else {
          logger.dsp.warn(`Partner with ID ${partnerId} not found or not active.`);
        }
      } else {
         logger.dsp.warn(`Invalid partner_id query parameter: ${partnerId}`);
      }
    }

    // Get global publisher revenue share as fallback
    let globalRevenueShare = 20; // Default fallback - 20% as specified
    if (isSSPPartner && partnerRevenueShare === null) {
      // Use cache for platform settings
      const revenueShareSetting = await CacheHelper.getOrSet<
        string | null
      >(
        CACHE_KEYS.PUBLISHER_REVENUE_SHARE,
        async () => {
          logger.app.info('Platform Settings: Cache miss for publisher revenue share, querying DB...');
          const revenueShareResult = await clickhouse.query({
            query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
          });
          const revenueShareData = await revenueShareResult.json();
          // Ensure returned data is accessed safely and is a string or null
          const settingValue = revenueShareData.data.length > 0 ? String((revenueShareData.data[0] as any).setting_value) : null;
          logger.app.info(`Platform Settings: DB query returned revenue share: ${settingValue}`);
          return settingValue;
        },
        CACHE_TTL.SETTINGS
      );
      // Safely parse the revenue share setting, defaulting if null or invalid
      globalRevenueShare = revenueShareSetting !== null ? parseFloat(revenueShareSetting) || 20 : 20;
       logger.app.info(`Using global revenue share: ${globalRevenueShare}`);
    }

    // Track incoming RTB bid request from external DSP/SSP
    await RequestTracker.trackRequest({
      sourceType: 'dsp_incoming', // This endpoint receives bids from external DSPs
      partnerEndpointId: partnerId ? parseInt(partnerId) : 0,
      requestCount: 1, // This endpoint handles one bid request at a time
      winCount: 0,
    });

    // Process each impression in the bid request - Assuming for simplicity only the first impression is processed for now.
    const imp = bidRequest.imp[0];
    const adFormat = imp.banner ? 'banner' : imp.native ? 'native' : 'unknown';
    const width = imp.banner?.w || 0;
    const height = imp.banner?.h || 0;

    if (adFormat === 'unknown') {
        logger.dsp.warn(`RTB bid request ${bidRequest.id}: Unsupported ad format in impression ${imp.id}`);
         // No eligible impression to process, return no bid
         const endTime = process.hrtime.bigint();
         const totalDurationMs = Number(endTime - startTime) / 1e6;
         logger.dsp.info(`Auction for request ${bidRequest.id} finished in ${totalDurationMs.toFixed(2)} ms. Result: No bid (unsupported format)`);
         return NextResponse.json({
             id: bidRequest.id,
             nbr: 2, // Invalid request
           } as BidResponse);
    }

    logger.dsp.info(`RTB bid request ${bidRequest.id}: Processing impression ${imp.id} (${adFormat}, ${width}x${height}) for zone ${zoneId}`);

    // Get eligible local campaigns for this impression
    // Note: This part seems misplaced if this endpoint is for receiving DSP bids for our inventory.
    // The AuctionService.runAuction is likely where local campaigns and outbound DSP calls should happen.
    // However, adhering to the existing code structure for now.
    const localCampaignBids: AuctionBid[] = []; // Typed array
    const campaigns = await CacheHelper.getOrSet<
       Campaign[] | null
    >(
        CACHE_KEYS.ACTIVE_CAMPAIGNS(adFormat),
        async () => {
        logger.auction.info(`Local Campaigns (${adFormat}): Cache miss, querying DB...`);
        const dbStartTime = process.hrtime.bigint();
          const campaignsResult = await clickhouse.query({
            query: `
              SELECT c.*, u.balance as advertiser_balance, u.role as user_role
              FROM campaigns c
              LEFT JOIN users u ON c.user_id = u.id
              WHERE c.status = 'active'
              AND c.type = {adFormat:String}
              AND c.start_date <= now()
              AND (c.end_date IS NULL OR (c.end_date >= now()))
              AND c.cpm_bid >= {bidfloor:Decimal(18,8)}
            AND c.creative_type = 1
              AND (
                u.role = 'dsp' OR
                (
                  u.role = 'advertiser'
                  AND u.balance >= (c.cpm_bid / 1000)
                  AND (c.daily_budget = 0 OR (c.daily_spent + (c.cpm_bid / 1000)) <= c.daily_budget)
                  AND (c.total_budget = 0 OR (c.total_spent + (c.cpm_bid / 1000)) <= c.total_budget)
                )
              )
              ORDER BY c.cpm_bid DESC
              LIMIT 10
            `,
            query_params: {
              adFormat,
              bidfloor: imp.bidfloor || 0
            },
          });
          const result = await campaignsResult.json();
        const dbEndTime = process.hrtime.bigint();
        const dbDurationMs = Number(dbEndTime - dbStartTime) / 1e6;
        logger.auction.info(`Local Campaigns (${adFormat}): DB query took ${dbDurationMs.toFixed(2)} ms, returned ${result.data.length} campaigns.`);
        return result.data as Campaign[]; // Explicitly cast to Campaign[]
        },
        CACHE_TTL.CAMPAIGNS
      );

    // Populate localCampaignBids from cached campaigns
    if (campaigns && campaigns.length > 0) {
       for (const campaign of campaigns as Campaign[]) { // Assert type here
           // Assuming campaigns structure is compatible with AuctionBid partially
           localCampaignBids.push({
               source: 'local',
               campaignId: campaign.id,
               bidPrice: campaign.cpm_bid / 1000, // Corrected from cpm_bid to cpm_bid
               adMarkup: '', // Placeholder - adMarkup should be generated later
               landingUrl: campaign.landing_url,
               creativeType: String(campaign.creative_type), // Ensure creative_type is string
               data: campaign,
           });
       }
       logger.auction.info(`Added ${localCampaignBids.length} local campaign bids.`);
    }


    // Get available SSP inventory for this impression (assuming this represents our direct SSP supply)
    const sspInventory = await CacheHelper.getOrSet<
        CachedSSPInventoryData[] | null
    >(
      CACHE_KEYS.SSP_INVENTORY(zoneId, adFormat), // Use zoneId as number and adFormat as string
      async () => {
        logger.ssp.info(`SSP Inventory (${zoneId}:${adFormat}): Cache miss, querying DB...`); // Use ssp logger
        const dbStartTime = process.hrtime.bigint();
      const sspInventoryResult = await clickhouse.query({
        query: `
            SELECT
              i.id,
              i.ssp_id,
              i.format,
              i.size,
              i.floor_price,
              i.auction_type,
              s.revenue_share, -- Fetch revenue_share from joined partner_endpoints (SSP)
              s.name as ssp_name -- Fetch SSP name
            FROM ssp_inventory i
            JOIN partner_endpoints s ON i.ssp_id = s.id
            WHERE i.zone_id = {zoneId:UInt32}
            AND i.format = {format:String}
            AND i.status = 'active'
            AND s.status = 'active'
            -- Add targeting filters here if needed
        `,
        query_params: {
            zoneId: zoneId,
            format: adFormat,
          },
        });
        const data = await sspInventoryResult.json();
        const dbEndTime = process.hrtime.bigint();
        const dbDurationMs = Number(dbEndTime - dbStartTime) / 1e6;
        logger.ssp.info(`SSP Inventory (${zoneId}:${adFormat}): DB query took ${dbDurationMs.toFixed(2)} ms, returned ${data.data.length} items.`); // Use ssp logger
        return data.data as CachedSSPInventoryData[]; // Explicitly cast to CachedSSPInventoryData[]
      },
      CACHE_TTL.INVENTORY
    );

    const eligibleSspInventoryBids: AuctionBid[] = []; // Typed array

    if (sspInventory && sspInventory.length > 0) {
       // Filter SSP inventory by size (if applicable) and create bids
       for (const inventoryItem of sspInventory as CachedSSPInventoryData[]) { // Assert type
           if (inventoryItem.format === 'banner' && inventoryItem.size !== `${width}x${height}`) {
             logger.ssp.debug(`SSP Inventory ${inventoryItem.id}: Filtered out by size ${inventoryItem.size} (requested ${width}x${height})`);
             continue; // Skip if banner size does not match
           }

           // For SSP inventory, we are acting as the seller. The bid price is the floor price.
           // We might add a small margin or use auction type, but let's use floor for now.
           eligibleSspInventoryBids.push({
               source: 'ssp',
               partnerId: inventoryItem.ssp_id,
               partnerName: inventoryItem.ssp_name, // Include SSP name
               bidPrice: inventoryItem.floor_price, // This is our floor/asking price for this inventory
               adMarkup: '', // Markup will come from the winning DSP
               landingUrl: '', // Landing URL will come from the winning DSP
               creativeType: inventoryItem.format,
               data: inventoryItem,
               sspRevenueShare: inventoryItem.revenue_share // Keep track of SSP revenue share
           });
       }
       logger.ssp.info(`Found ${eligibleSspInventoryBids.length}/${sspInventory.length} eligible SSP inventory bids.`);
    }

    // Combine bids from all internal supply sources (local campaigns and SSP inventory)
    const internalSupplyBids = [
        ...localCampaignBids,
        ...eligibleSspInventoryBids,
    ];

    // Find the winning bid from internal supply based on price
    let winningInternalBid: AuctionBid | null = null;
    if (internalSupplyBids.length > 0) {
        internalSupplyBids.sort((a, b) => b.bidPrice - a.bidPrice);
        winningInternalBid = internalSupplyBids[0];
        logger.auction.info(`Highest internal supply bid for imp ${imp.id}: ${winningInternalBid.source} with bid $${winningInternalBid.bidPrice}`);
    } else {
        logger.auction.info(`No internal supply bids for impression ${imp.id}`);
    }

    // Let's assume this endpoint receives bid requests *from* DSPs for *our* inventory.
    // In this model, we find the best bid *from our internal supply* that meets the DSP's criteria (like bidfloor)
    // and prepare a response based on that.

    let ourWinningBid: AuctionBid | null = null;

    if (winningInternalBid && winningInternalBid.bidPrice >= (imp.bidfloor || 0)) {
        // Our best internal bid meets or exceeds the DSP's bidfloor
        ourWinningBid = winningInternalBid;
        logger.auction.info(`Our winning bid for imp ${imp.id} is from ${ourWinningBid.source} with bid $${ourWinningBid.bidPrice} (meets DSP bidfloor $${imp.bidfloor || 0})`);
    } else if (winningInternalBid) {
        logger.auction.info(`Our best internal bid for imp ${imp.id} ($${winningInternalBid.bidPrice}) is below DSP bidfloor $${imp.bidfloor || 0}`);
    } else {
        logger.auction.info(`No internal bid meets DSP bidfloor for imp ${imp.id}`);
    }

    // Prepare the OpenRTB bid response based on our winning bid
    const bidResponse: BidResponse = {
      id: bidRequest.id,
      seatbid: [],
      cur: (ourWinningBid && ourWinningBid.bidPrice > 0) ? 'USD' : undefined, // Explicitly check ourWinningBid
      nbr: ourWinningBid ? undefined : 1, // No bid reason: 1 = Unknown Error, 2 = Invalid Request (if no bid)
    };

    if (ourWinningBid) {
       // Determine the final bid price to send back to the DSP.
       // CRITICAL: Deduct platform revenue share from bid BEFORE sending to DSP/SSP
       // This implements the requirement: Publisher/SSP Bid Response = DSP/Advertiser bid * (Publisher/SSP Revenue Share / 100)
       let effectiveRevenueShare = globalRevenueShare;

       // Use SSP-specific revenue share if available
       if (ourWinningBid.source === 'ssp' && ourWinningBid.sspRevenueShare) {
         effectiveRevenueShare = ourWinningBid.sspRevenueShare;
       }

       // Calculate reduced bid price (what SSP/Publisher sees)
       let priceToDSP = ourWinningBid.bidPrice * (effectiveRevenueShare / 100);

       logger.auction.info(`Bid price reduction: Original $${ourWinningBid.bidPrice} -> Reduced $${priceToDSP} (${effectiveRevenueShare}% revenue share)`);

       // Determine ad markup and tracking URLs based on the winning internal bid source
       let adMarkup = '';
       let winNoticeUrl = '';
       let creativeId = ourWinningBid.creativeType?.toString() || '';
       let campaignOrAdId = ourWinningBid.campaignId?.toString() || ourWinningBid.partnerId?.toString() || '';
       let advertiserDomains: string[] = [];
       let width = imp.banner?.w || 0;
       let height = imp.banner?.h || 0;

       if (ourWinningBid.source === 'local') {
           // Winning bid is a local campaign
           const campaign = ourWinningBid.data as Campaign; // Assert type
           // Generate ad markup for local campaign
            if (ourWinningBid.creativeType === 'banner' && campaign.banner_image_url) {
              adMarkup = `<a href="${campaign.landing_url}" target="_blank"><img src="${campaign.banner_image_url}" alt="Ad" style="width:100%;height:100%;"/></a>`;
            } else if (ourWinningBid.creativeType === 'js' && campaign.js_tag) {
              adMarkup = campaign.js_tag;
    } else {
               // Fallback or other formats
               adMarkup = `<a href="${campaign.landing_url}" target="_blank">Click Here</a>`;
            }

           // Generate win notice URL for tracking
           // When a DSP receives our bid and decides to serve it (implicitly by sending a win notice),
           // we need to track that win for the local campaign.
           winNoticeUrl = `${process.env.NEXTAUTH_URL}/api/rtb/win?source=local&campaign_id=${ourWinningBid.campaignId}&price=\${AUCTION_PRICE}&request_id=${bidRequest.id}&impression_id=${imp.id}`;
           advertiserDomains = campaign.landing_url ? [new URL(campaign.landing_url).hostname] : [];
           creativeId = ourWinningBid.creativeType?.toString() || ''; // Use creative type as creative ID fallback
           campaignOrAdId = ourWinningBid.campaignId?.toString() || ''; // Use campaign ID
           width = ourWinningBid.data?.width || (ourWinningBid.creativeType === 'banner' ? (imp.banner?.w || 0) : undefined);
           height = ourWinningBid.data?.height || (ourWinningBid.creativeType === 'banner' ? (imp.banner?.h || 0) : undefined);

       } else if (ourWinningBid.source === 'ssp') {
          // Winning bid is from our SSP inventory
          const inventoryItem = ourWinningBid.data as CachedSSPInventoryData; // Assert type
          // For SSP inventory, the DSP will provide the ad markup in their win notice.
          // We provide a placeholder or mechanism for the DSP to load their ad.
          // This might be an impression tracking pixel or a URL for them to call.
          // A common approach is to provide an impression tracking pixel that triggers the SSP's ad serving.
          // The DSP will replace {AUCTION_PRICE} macro in the win notice URL they send back to us.
          winNoticeUrl = `${process.env.NEXTAUTH_URL}/api/rtb/win?source=ssp&inventory_id=${ourWinningBid.data.id}&price=\${AUCTION_PRICE}&request_id=${bidRequest.id}&impression_id=${imp.id}&ssp_id=${ourWinningBid.partnerId}`;
          adMarkup = `<!-- Placeholder for SSP Ad for Inventory ID ${inventoryItem.id} -->`; // DSP will replace this
           // We might include a URL here that the DSP's creative calls to fetch the actual ad.
           // For simplicity now, just a placeholder.
          advertiserDomains = ['ssp.example.com']; // Placeholder
          creativeId = inventoryItem.id?.toString() || ''; // Use inventory ID as creative ID
          campaignOrAdId = ourWinningBid.partnerId?.toString() || ''; // Use SSP ID as campaign/ad ID
          width = inventoryItem.size?.split('x')[0] ? parseInt(inventoryItem.size.split('x')[0]) : imp.banner?.w || 0;
          height = inventoryItem.size?.split('x')[1] ? parseInt(inventoryItem.size.split('x')[1]) : imp.banner?.h || 0;

       } // else if (ourWinningBid.source === 'dsp') { /* This case is less likely if we are receiving DSP bids */ }

       // Add the winning bid to the response seatbid array
       bidResponse.seatbid!.push({ // Add non-null assertion
         seat: 'global-ads-media', // Our exchange seat ID
         bid: [{
           id: ourWinningBid.data?.id?.toString() || ourWinningBid.campaignId?.toString() || ourWinningBid.partnerId?.toString() || `bid_${Date.now()}`,
           impid: imp.id,
           price: priceToDSP, // The price we are bidding to the DSP
           nurl: winNoticeUrl, // Win notice URL for the DSP to call
           adm: adMarkup, // Ad creative markup (or placeholder)
           adomain: advertiserDomains,
           cid: campaignOrAdId,
           crid: creativeId,
           w: width,
           h: height,
         }]
       });
    }

  // Log the auction outcome
  const endTime = process.hrtime.bigint();
  const totalDurationMs = Number(endTime - startTime) / 1e6;
  logger.auction.info(`Auction for request ${bidRequest.id} finished in ${totalDurationMs.toFixed(2)} ms. Winner: ${ourWinningBid?.source || 'None'} with bid $${ourWinningBid?.bidPrice || 0}`);

  return NextResponse.json(bidResponse);

  } catch (error) {
  const endTime = process.hrtime.bigint();
  const totalDurationMs = Number(endTime - startTime) / 1e6;
  logger.dsp.error(`RTB bid request handler error after ${totalDurationMs.toFixed(2)} ms:`, { error, bidRequestId: bidRequest?.id || 'unknown' }); // Combine error and bidRequestId into a single object
    return NextResponse.json({
    id: bidRequest?.id || 'internal_error',
    nbr: 1, // Unknown Error
  } as BidResponse);
}
}

// The GET function likely serves documentation or a test endpoint, less critical for QPS optimization.
export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'RTB Bid Endpoint' });
}
