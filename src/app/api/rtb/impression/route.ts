import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { CostProcessor } from '@/lib/cost-processor';
import { clickHouseBatchWriter } from '@/lib/clickhouse-batch-writer';
import { Campaign } from '@/lib/auction-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const campaignId = searchParams.get('campaign_id');
    const impId = searchParams.get('imp_id');
    const partnerId = searchParams.get('partner_id');

    if (!campaignId || !impId) {
      return new NextResponse('Missing parameters', { status: 400 });
    }

    // Get client information
    const userAgent = request.headers.get('user-agent') || '';
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               '127.0.0.1';

    // Generate impression ID
    const impressionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Get campaign details for cost processing
    const campaignResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns: { data: Campaign[] } = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return new NextResponse('Campaign not found', { status: 404 });
    }

    const campaign: Campaign = campaigns.data[0];

    // Log RTB impression with cost tracking
    await clickHouseBatchWriter.write('impressions', {
        id: impressionId,
        campaign_id: parseInt(campaignId),
        website_id: 0, // RTB impressions don't have specific website/zone
        zone_id: 0,
        user_agent: userAgent,
        ip_address: ip,
        country: 'Unknown',
        region: 'Unknown',
        city: 'Unknown',
        device_type: 'Unknown',
        os: 'Unknown',
        browser: 'Unknown',
        cost_deducted: 0, // Will be updated by cost processor
        publisher_revenue: 0, // Will be updated by cost processor
        bid_type: 'cpm', // Default to CPM for RTB
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
    });

    // Process cost deduction for RTB impression
    const costResult = await CostProcessor.processImpressionCost({
      campaignId: parseInt(campaignId),
      websiteId: 0, // RTB doesn't have specific website
      zoneId: 0, // RTB doesn't have specific zone
      impressionId: impressionId,
      bidType: 'cpm', // RTB typically uses CPM
      bidAmount: campaign.cpm_bid,
      partnerId: partnerId ? parseInt(partnerId) : undefined,
    });

    if (!costResult.success) {
      console.error('Failed to process RTB impression cost:', costResult.error);
    }

    // Return 1x1 transparent pixel
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    return new NextResponse(pixel, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('RTB impression tracking error:', error);
    return new NextResponse('Error', { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  // Handle POST requests for impression tracking
  return GET(request);
}
