import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    let whereClause = 'tr.user_id = {userId:UInt32}';
    let queryParams: any = { userId: parseInt(session.user.id) };

    if (type && ['whitelist', 'blacklist'].includes(type)) {
      whereClause += ' AND tr.type = {type:String}';
      queryParams.type = type;
    }

    const result = await clickhouse.query({
      query: `
        SELECT 
          tr.id,
          tr.type,
          tr.target_type,
          tr.target_id,
          tr.target_name,
          tr.created_at
        FROM targeting_rules tr
        WHERE ${whereClause}
        ORDER BY tr.created_at DESC
      `,
      query_params: queryParams,
    });

    const rules = await result.json();
    return NextResponse.json(rules.data);

  } catch (error) {
    console.error('Targeting rules fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { type, target_type, target_id } = await request.json();

    if (!type || !target_type || !target_id) {
      return NextResponse.json(
        { message: 'Type, target_type, and target_id are required' },
        { status: 400 }
      );
    }

    if (!['whitelist', 'blacklist'].includes(type)) {
      return NextResponse.json(
        { message: 'Invalid type. Must be whitelist or blacklist' },
        { status: 400 }
      );
    }

    if (!['publisher', 'website', 'zone'].includes(target_type)) {
      return NextResponse.json(
        { message: 'Invalid target_type. Must be publisher, website, or zone' },
        { status: 400 }
      );
    }

    // Verify target exists and get name
    let targetName = '';
    let targetQuery = '';
    
    switch (target_type) {
      case 'publisher':
        targetQuery = 'SELECT full_name as name FROM users WHERE id = {targetId:UInt32} AND role = \'publisher\'';
        break;
      case 'website':
        targetQuery = 'SELECT name FROM websites WHERE id = {targetId:UInt32}';
        break;
      case 'zone':
        targetQuery = 'SELECT name FROM ad_zones WHERE id = {targetId:UInt32}';
        break;
    }

    const targetResult = await clickhouse.query({
      query: targetQuery,
      query_params: { targetId: target_id },
    });

    const targetData = await targetResult.json();
    if (targetData.data.length === 0) {
      return NextResponse.json(
        { message: `${target_type} not found` },
        { status: 404 }
      );
    }

    targetName = targetData.data[0].name;

    // Check if rule already exists
    const existingResult = await clickhouse.query({
      query: `
        SELECT id FROM targeting_rules 
        WHERE user_id = {userId:UInt32} 
        AND target_type = {targetType:String} 
        AND target_id = {targetId:UInt32}
      `,
      query_params: {
        userId: parseInt(session.user.id),
        targetType: target_type,
        targetId: target_id,
      },
    });

    const existing = await existingResult.json();
    if (existing.data.length > 0) {
      return NextResponse.json(
        { message: 'Targeting rule for this target already exists' },
        { status: 400 }
      );
    }

    // Generate rule ID
    const ruleId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create targeting rule
    await clickhouse.insert({
      table: 'targeting_rules',
      values: [{
        id: ruleId,
        user_id: parseInt(session.user.id),
        type,
        target_type,
        target_id,
        target_name: targetName,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Targeting rule created successfully',
      rule_id: ruleId,
    });

  } catch (error) {
    console.error('Targeting rule creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
