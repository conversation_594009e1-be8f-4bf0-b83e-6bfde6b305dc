import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);

    // Get active campaigns count
    const campaignsResult = await clickhouse.query({
      query: 'SELECT COUNT(*) as count FROM campaigns WHERE user_id = {userId:UInt32} AND status = \'active\'',
      query_params: { userId },
    });
    const campaignsData = await campaignsResult.json();
    const activeCampaigns = campaignsData.data[0]?.count || 0;

    // Get total impressions for this advertiser's campaigns
    const impressionsResult = await clickhouse.query({
      query: `
        SELECT COUNT(DISTINCT i.id) as count
        FROM impressions i
        INNER JOIN campaigns c ON i.campaign_id = c.id
        WHERE c.user_id = {userId:UInt32}
      `,
      query_params: { userId },
    });
    const impressionsData = await impressionsResult.json();
    const totalImpressions = parseInt(impressionsData.data[0]?.count || 0);

    // Get total clicks for this advertiser's campaigns
    const clicksResult = await clickhouse.query({
      query: `
        SELECT SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as count
        FROM impressions i
        INNER JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        WHERE c.user_id = {userId:UInt32}
      `,
      query_params: { userId },
    });
    const clicksData = await clicksResult.json();
    const totalClicks = parseInt(clicksData.data[0]?.count || 0);

    // Get total spend for this advertiser
    const spendResult = await clickhouse.query({
      query: `
        SELECT SUM(i.cost_deducted) as total_spend
        FROM impressions i
        INNER JOIN campaigns c ON i.campaign_id = c.id
        WHERE c.user_id = {userId:UInt32}
      `,
      query_params: { userId },
    });
    const spendData = await spendResult.json();
    const totalSpend = parseFloat(spendData.data[0]?.total_spend || 0);

    // Get account balance
    const userResult = await clickhouse.query({
      query: 'SELECT balance FROM users WHERE id = {userId:UInt32}',
      query_params: { userId },
    });
    const userData = await userResult.json();
    const accountBalance = parseFloat(userData.data[0]?.balance || 0);

    const stats = {
      activeCampaigns,
      totalImpressions,
      totalClicks,
      totalSpend,
      accountBalance,
      ctr: totalImpressions > 0 ? ((totalClicks / totalImpressions) * 100).toFixed(2) : '0.00',
      avgCpm: totalImpressions > 0 ? ((totalSpend / totalImpressions) * 1000).toFixed(2) : '0.00',
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Advertiser dashboard stats error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
