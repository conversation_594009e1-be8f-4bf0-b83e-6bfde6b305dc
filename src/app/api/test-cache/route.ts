import { NextRequest, NextResponse } from 'next/server';
import { cache } from '@/lib/cache';

export async function GET(request: NextRequest) {
  try {
    // Test Redis connection first
    const connectionTest = await cache.testConnection();
    
    // Simple cache test
    const testKey = 'simple-test';
    const testValue = { message: 'Cache is working!', timestamp: new Date().toISOString() };
    
    await cache.set(testKey, testValue, 60);
    const retrieved = await cache.get(testKey);
    
    // Clean up test key
    await cache.del(testKey);
    
    return NextResponse.json({
      status: 'success',
      message: 'Enhanced cache test endpoint',
      connection: connectionTest,
      test: {
        stored: testValue,
        retrieved: retrieved,
        working: retrieved !== null
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Warm up basic cache entries
    const results = [];
    
    // Test basic cache operations
    await cache.set('test-partners-dsp', [{ id: 1, name: 'Test DSP' }], 300);
    await cache.set('test-partners-ssp', [{ id: 2, name: 'Test SSP' }], 300);
    await cache.set('test-settings', { revenue_share: 70 }, 600);
    
    results.push({ key: 'test-partners-dsp', success: true });
    results.push({ key: 'test-partners-ssp', success: true });
    results.push({ key: 'test-settings', success: true });
    
    return NextResponse.json({
      status: 'success',
      message: 'Basic cache warmup completed',
      results: results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
