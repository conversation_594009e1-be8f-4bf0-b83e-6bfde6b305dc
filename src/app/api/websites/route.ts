import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, url, category, verificationMethod } = body;

    // Validate required fields
    if (!name || !url || !category || !verificationMethod) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if website already exists
    const existingWebsiteResult = await clickhouse.query({
      query: 'SELECT id FROM websites WHERE url = {url:String}',
      query_params: { url },
    });

    const existingWebsites = await existingWebsiteResult.json();
    if (existingWebsites.data.length > 0) {
      return NextResponse.json(
        { message: 'Website with this URL already exists' },
        { status: 400 }
      );
    }

    // Get the next website ID
    const websiteIdStart = parseInt(process.env.WEBSITE_ID_START || '70589');
    
    const maxIdResult = await clickhouse.query({
      query: 'SELECT MAX(id) as max_id FROM websites',
    });

    const maxIdData = await maxIdResult.json();
    let nextId = websiteIdStart;
    if (maxIdData.data.length > 0 && maxIdData.data[0].max_id) {
      nextId = Math.max(websiteIdStart, maxIdData.data[0].max_id + 1);
    }

    // Generate verification code
    const verificationCode = crypto.randomBytes(16).toString('hex');

    // Insert new website
    await clickhouse.insert({
      table: 'websites',
      values: [{
        id: nextId,
        user_id: parseInt(session.user.id),
        name,
        url,
        category,
        status: 'pending',
        verification_method: verificationMethod,
        verification_code: verificationCode,
        verification_status: 'pending',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json(
      { 
        message: 'Website added successfully', 
        websiteId: nextId,
        verificationCode,
        verificationMethod 
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Website creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    let query = 'SELECT * FROM websites';
    let queryParams = {};

    if (session.user?.role === 'publisher') {
      query += ' WHERE user_id = {userId:UInt32}';
      queryParams = { userId: parseInt(session.user.id) };
    } else if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      );
    }

    query += ' ORDER BY created_at DESC';

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const websites = await result.json();

    return NextResponse.json(websites.data);
  } catch (error) {
    console.error('Websites fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
