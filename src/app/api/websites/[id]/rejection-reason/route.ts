import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const websiteId = parseInt(id);

    // First verify the website belongs to the user (unless admin)
    if (session.user.role !== 'admin') {
      const websiteResult = await clickhouse.query({
        query: 'SELECT user_id FROM websites WHERE id = {websiteId:UInt32}',
        query_params: { websiteId },
      });

      const websiteData = await websiteResult.json();
      if (!websiteData.data.length || websiteData.data[0].user_id !== parseInt(session.user.id)) {
        return NextResponse.json(
          { message: 'Website not found or unauthorized' },
          { status: 404 }
        );
      }
    }

    // Get the most recent rejection reason for this website
    const rejectionResult = await clickhouse.query({
      query: `
        SELECT 
          aa.reason,
          aa.timestamp,
          u.full_name as admin_name
        FROM admin_actions aa
        LEFT JOIN users u ON aa.admin_id = u.id
        WHERE aa.target_id = {websiteId:UInt32} 
          AND aa.target_type = 'website' 
          AND aa.action_type = 'website_reject'
          AND aa.reason != ''
        ORDER BY aa.timestamp DESC
        LIMIT 1
      `,
      query_params: { websiteId },
    });

    const rejectionData = await rejectionResult.json();
    
    if (rejectionData.data.length === 0) {
      return NextResponse.json(
        { message: 'No rejection reason found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      reason: rejectionData.data[0].reason,
      timestamp: rejectionData.data[0].timestamp,
      admin_name: rejectionData.data[0].admin_name,
    });

  } catch (error) {
    console.error('Error fetching rejection reason:', error);
    return NextResponse.json(
      { message: 'Failed to fetch rejection reason' },
      { status: 500 }
    );
  }
}
