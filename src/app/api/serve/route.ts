import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { CostProcessor } from '@/lib/cost-processor';
import { RequestTracker } from '@/lib/request-tracker';
import deviceGeoDetector from '@/lib/device-geo-detector';
import { AuctionService } from '@/lib/auction-service';
import FraudDetection from '@/lib/fraud-detection';
import FallbackManager from '@/lib/fallback-manager';
import logger from '@/lib/logger';


// Type definitions for database responses
interface Zone {
  id: number;
  website_id: number;
  name: string;
  format: string;
  size: string;
  status: string;
  user_id: number;
}





interface PlatformSetting {
  setting_value: string;
}

// Helper function to convert creative type (handles both number and string)
function getCreativeTypeString(creativeTypeValue: number | string): string {
  if (typeof creativeTypeValue === 'string') {
    return creativeTypeValue; // Already a string, return as-is
  }

  // Handle legacy numeric values
  switch (creativeTypeValue) {
    case 1: return 'image';
    case 2: return 'js';
    case 3: return 'native';
    case 4: return 'push';
    case 5: return 'popup';
    default: return 'image';
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const zoneId = searchParams.get('zone_id');
    const impressionId = searchParams.get('impression_id');
    const sspPartnerId = searchParams.get('ssp_partner_id');
    const format = searchParams.get('format') || 'banner';
    const size = format === 'banner' ? (searchParams.get('size') || '300x250') : '';
    const redirect = searchParams.get('redirect') === '1';
    const noTracking = searchParams.get('no_tracking') === '1'; // For popup ad preparation

    // Get client information for logging
    const userAgent = request.headers.get('user-agent') || '';
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               '127.0.0.1';

    logger.serve.info('Serve request received:', {
      zoneId,
      impressionId,
      sspPartnerId,
      format,
      size,
      redirect,
      noTracking,
      ip,
      userAgent: userAgent.substring(0, 100), // Truncate for readability
    });

    // Check if this is an SSP tracking request (has impression_id and ssp_partner_id but no zone_id)
    const isSSPTrackingRequest = !zoneId && impressionId && sspPartnerId;

    if (isSSPTrackingRequest) {
      // Handle SSP tracking request - redirect using the redirect_url parameter
      logger.serve.info('Processing SSP tracking request', { impressionId, sspPartnerId });

      const redirectUrl = searchParams.get('redirect_url');
      const campaignId = searchParams.get('campaign_id');

      // Track the impression for SSP using SmartImpressionTracker (regardless of redirect URL)
      try {
        // Check if impression already exists to avoid duplicates
        const existingImpressionResult = await clickhouse.query({
          query: 'SELECT id FROM impressions WHERE id = {impressionId:UInt64} LIMIT 1',
          query_params: { impressionId: parseInt(impressionId) }
        });

        const existingImpressions = await existingImpressionResult.json();

        if (existingImpressions.data.length === 0) {
          // Get device and geo info for SSP tracking
          const deviceGeoInfo = await deviceGeoDetector.detectDeviceAndGeo(ip, userAgent);

          // Impression doesn't exist, create new record using SmartImpressionTracker
          const { SmartImpressionTracker } = await import('@/lib/smart-impression-tracker');
          await SmartImpressionTracker.trackImpression({
            auctionId: parseInt(impressionId), // Use impressionId as auctionId for SSP tracking
            campaignId: parseInt(campaignId || '0'),
            sspPartnerId: parseInt(sspPartnerId),
            websiteId: 0, // SSP tracking doesn't have website context
            zoneId: 0, // SSP tracking doesn't have zone context
            userAgent: userAgent,
            ipAddress: ip,
            country: deviceGeoInfo.country || 'Unknown',
            region: deviceGeoInfo.state || deviceGeoInfo.region || 'Unknown',
            city: deviceGeoInfo.city || 'Unknown',
            deviceType: deviceGeoInfo.device_type || 'Unknown',
            os: deviceGeoInfo.os || 'Unknown',
            browser: deviceGeoInfo.browser || 'Unknown',
            costDeducted: 0,
            publisherRevenue: 0,
            sourceType: 'ssp_inbound', // SSP traffic source
          });
          logger.serve.info('SSP impression tracked successfully using SmartImpressionTracker', {
            impressionId,
            sspPartnerId,
            deviceGeoInfo
          });
        } else {
          logger.serve.info('SSP impression already exists, skipping insert', { impressionId });
        }
      } catch (trackingError) {
        logger.serve.warn('Failed to track SSP impression:', trackingError);
        // Continue with response even if tracking fails
      }

      if (redirectUrl) {
        // Decode the redirect URL and redirect to it
        const decodedRedirectUrl = decodeURIComponent(redirectUrl);
        logger.serve.info('SSP tracking: Redirecting to', {
          redirectUrl: decodedRedirectUrl,
          impressionId,
          campaignId,
          sspPartnerId
        });
        return NextResponse.redirect(decodedRedirectUrl, 302);
      } else {
        // No redirect URL - return success response for impression tracking
        logger.serve.info('SSP tracking: Impression tracked without redirect', { impressionId, sspPartnerId });
        return NextResponse.json(
          { success: true, message: 'Impression tracked', impressionId },
          { status: 200 }
        );
      }
    }

    if (!zoneId) {
      logger.serve.warn('Missing zone_id parameter');
      return NextResponse.json(
        { error: 'Missing zone_id parameter' },
        { status: 400 }
      );
    }

    logger.serve.info('Fetching zone information', { zoneId });

    // Get zone information
    const zoneResult = await clickhouse.query({
      query: 'SELECT * FROM ad_zones WHERE id = {zoneId:UInt32} AND status = \'active\'',
      query_params: { zoneId: parseInt(zoneId) },
    });

    const zones = await zoneResult.json();
    logger.serve.info('Zone query completed', { zoneId, found: zones.data.length > 0 });

    if (zones.data.length === 0) {
      return NextResponse.json(
        { error: 'Zone not found or inactive' },
        { status: 404 }
      );
    }

    const zone = zones.data[0] as Zone;
    logger.serve.info('Fetching website information', { websiteId: zone.website_id });

    // Get website information
    const websiteResult = await clickhouse.query({
      query: 'SELECT * FROM websites WHERE id = {websiteId:UInt32} AND status = \'approved\'',
      query_params: { websiteId: zone.website_id },
    });

    const websites = await websiteResult.json();
    if (websites.data.length === 0) {
      return NextResponse.json(
        { error: 'Website not found or not approved' },
        { status: 404 }
      );
    }

    const website = websites.data[0] as any;
    const websiteUserId = website.user_id || 0;

    // Note: Minimum bid enforcement is only for campaign creation, not ad serving
    // If admin approved a campaign with lower bid (special deal), it should still serve

    // Note: Website owner role check removed as it's not currently used in auction logic
    // Previously checked if website owner is publisher or SSP, but unified auction handles both

    // Old campaign selection logic removed - now using unified auction system

    // Client information already extracted above for logging

    // Get device and geo info for fraud detection
    const deviceGeoInfo = await deviceGeoDetector.detectDeviceAndGeo(ip, userAgent);

    // Debug device/geo detection
    logger.serve.info('Device/Geo detection result:', {
      ip,
      userAgent: userAgent.substring(0, 100),
      deviceGeoInfo
    });

    // Fraud detection for impressions with full context - RE-ENABLED
    const fraudCheck = await FraudDetection.checkImpression(
      ip,
      userAgent,
      0, // campaignId - will be set after campaign selection
      'publisher_direct',
      zone.user_id, // publisherId from zone's website
      zone.website_id,
      parseInt(zoneId),
      undefined, // partnerId
      deviceGeoInfo.country,
      deviceGeoInfo.device_type
    );

    if (!fraudCheck.isValid) {
      logger.fraud.warn(`Blocked impression: ${fraudCheck.reason} (Risk: ${fraudCheck.riskScore})`);
      return NextResponse.json({
        ad: {
          type: 'house',
          html: '<div style="display:none;">Blocked</div>',
        }
      });
    }

    // Generate unique auction ID for tracking (no impression record created yet)
    const auctionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    logger.serve.info('Starting auction process', { zoneId, auctionId });

    // 🎯 RUN UNIFIED AUCTION WITH LOCAL CAMPAIGNS AND DSP PARTNERS
    const auctionResult = await AuctionService.runAuction({
      zoneId: parseInt(zoneId),
      websiteId: zone.website_id,
      format: format,
      size: size,
      userAgent: userAgent,
      ipAddress: ip,
      country: deviceGeoInfo.country,
      state: deviceGeoInfo.state || deviceGeoInfo.region || 'Unknown', // Use state, fallback to region for compatibility
      city: deviceGeoInfo.city || 'Unknown',
      deviceType: deviceGeoInfo.device_type,
      os: deviceGeoInfo.os,
      browser: deviceGeoInfo.browser,
    });

    logger.serve.info('Auction completed', {
      hasWinner: !!auctionResult.winner,
      winnerSource: auctionResult.winner?.source,
      auctionId: auctionId
    });

    if (!auctionResult.winner) {
      // No bids from local campaigns or DSP partners
      const response = {
        ad: {
          type: 'house',
          html: '<div style="width:300px;height:250px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;">No ads available</div>',
        }
      };

      const callback = searchParams.get('callback');
      if (callback) {
        return new NextResponse(
          `${callback}(${JSON.stringify(response)});`,
          {
            headers: {
              'Content-Type': 'application/javascript',
              'Access-Control-Allow-Origin': '*',
            },
          }
        );
      }

      return NextResponse.json(response, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    }

    const winner = auctionResult.winner;
    logger.auction.info(`🏆 Auction winner: ${winner.source} with bid $${winner.bidPrice}`);

    // Get publisher revenue share to calculate what publisher should see
    const revenueShareResult = await clickhouse.query({
      query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
    });

    const revenueShareData = await revenueShareResult.json();
    const publisherRevenueShare = revenueShareData.data.length > 0
      ? parseFloat((revenueShareData.data[0] as any).setting_value)
      : 20; // Default fallback - 20% as specified

    // Calculate publisher-facing CPM (what they see and earn)
    const publisherCpm = winner.bidPrice * (publisherRevenueShare / 100);

    // Handle different winner types (local campaign vs DSP)
    let campaignId = 0;
    let winnerId = 0;
    let winType: 'publisher_direct' | 'dsp_rtb' = 'publisher_direct';

    if (winner.source === 'local' && winner.campaignId) {
      campaignId = winner.campaignId;
      winnerId = winner.data?.user_id || 0;
      winType = 'publisher_direct';
    } else if (winner.source === 'dsp' && winner.partnerId) {
      campaignId = 0; // DSP wins don't have local campaign IDs
      winnerId = winner.partnerId;
      winType = 'dsp_rtb';
    }

    // Check if this is an impression tracking request (has auction_id parameter from click)
    const isImpressionTracking = searchParams.get('auction_id') !== null;

    // For popup ads, skip billing and tracking when just preparing (no_tracking=1)
    // Only do billing and tracking when popup is actually triggered OR when tracking impression
    let costResult: { success: boolean; costDeducted: number; publisherRevenue: number; error?: string } = { success: true, costDeducted: 0, publisherRevenue: 0 };

    if (isImpressionTracking || (!noTracking || format !== 'popup')) {
      // Process cost deduction and publisher credit ONLY when impression is actually tracked
      // This happens when:
      // 1. User clicks/views ad (impression_id parameter present) - IMPRESSION TRACKING
      // 2. Initial ad serving for non-popup formats (immediate impression) - INITIAL SERVING

      if (isImpressionTracking) {
        // This is impression tracking - process cost and redirect user
        const trackingAuctionId = searchParams.get('auction_id');
        logger.app.info(`Processing impression tracking for auction ${trackingAuctionId}`);

        // Execute cost processing with automatic fallback
        await FallbackManager.executeWithFallback('process-cost', {
          campaignId: campaignId,
          websiteId: zone.website_id,
          zoneId: parseInt(zoneId),
          auctionId: trackingAuctionId,
          bidType: 'cpm',
          bidAmount: winner.bidPrice,
          publisherCpm: publisherCpm,
          dspPartnerId: winner.partnerId || undefined,
          sspPartnerId: parseInt(searchParams.get('ssp_partner_id') || '0') || undefined,
          isWinConfirmation: true,
        }, 10); // High priority

        // Set default cost result for immediate response
        costResult = {
          success: true,
          costDeducted: winner.bidPrice / 1000, // Estimate for response
          publisherRevenue: (winner.bidPrice / 1000) * 0.2, // Estimate
        };

        if (!costResult.success) {
          logger.app.error('Failed to process impression cost:', costResult.error);
        }

        logger.app.info(`Impression tracked and cost processed: Cost deducted $${costResult.costDeducted}, Publisher revenue $${costResult.publisherRevenue}`);

        // Track impression using SmartImpressionTracker with auction_id
        const { SmartImpressionTracker } = await import('@/lib/smart-impression-tracker');
        await SmartImpressionTracker.trackImpression({
          auctionId: parseInt(trackingAuctionId || '0'),
          campaignId: campaignId,
          dspPartnerId: winner.partnerId || 0,
          sspPartnerId: parseInt(searchParams.get('ssp_partner_id') || '0') || 0,
          websiteId: zone.website_id,
          zoneId: parseInt(zoneId),
          userAgent: userAgent,
          ipAddress: ip,
          country: deviceGeoInfo.country,
          region: deviceGeoInfo.region || 'Unknown',
          city: deviceGeoInfo.city || 'Unknown',
          deviceType: deviceGeoInfo.device_type,
          os: deviceGeoInfo.os,
          browser: deviceGeoInfo.browser,
          costDeducted: costResult.costDeducted || 0,
          publisherRevenue: costResult.publisherRevenue || 0,
          sourceType: winner.source === 'dsp' ? 'dsp' : 'local',
        });

        // Queue win notification tracking asynchronously
        const { publishToWinNotificationQueue } = await import('@/lib/queue-manager');
        await publishToWinNotificationQueue({
          winType: winType,
          campaignId: campaignId,
          winnerId: winnerId,
          supplierId: zone.website_id,
          websiteId: zone.website_id,
          zoneId: parseInt(zoneId),
          partnerEndpointId: winner.partnerId || 0,
          winPrice: winner.bidPrice / 1000,
          platformRevenue: (costResult.costDeducted || 0) - (costResult.publisherRevenue || 0),
          supplierRevenue: costResult.publisherRevenue || 0,
        }, 7); // Medium-high priority

        // For DSP wins, track win in request_stats (impression tracked)
        if (winner.source === 'dsp' && winner.partnerId) {
          try {
            // Queue DSP win tracking asynchronously
            const { publishToRequestStatsQueue } = await import('@/lib/queue-manager');
            await publishToRequestStatsQueue({
              sourceType: 'dsp_outgoing',
              partnerEndpointId: winner.partnerId,
              websiteId: zone.website_id,
              dspId: winner.data?.user_id || 0,
              publisherId: websiteUserId || 0,
              requestCount: 0,
              winCount: 1,
            }, 5); // Medium priority

            logger.dsp.info(`DSP win tracked: Partner ${winner.partnerId}`);
          } catch (trackingError) {
            logger.dsp.error('Failed to track DSP win:', trackingError);
          }
        }

        // For local campaign wins, track win in request_stats (impression tracked)
        if (winner.source === 'local' && winner.campaignId) {
          try {
            // Queue local campaign win tracking asynchronously
            const { publishToRequestStatsQueue } = await import('@/lib/queue-manager');
            await publishToRequestStatsQueue({
              sourceType: 'advertiser_outgoing',
              campaignId: winner.campaignId,
              websiteId: zone.website_id,
              advertiserId: winner.data?.user_id || 0,
              publisherId: websiteUserId || 0,
              requestCount: 0,
              winCount: 1,
            }, 5); // Medium priority

            logger.app.info(`Local campaign win tracked: Campaign ${winner.campaignId}`);
          } catch (trackingError) {
            logger.app.error('Failed to track local campaign win:', trackingError);
          }
        }

        // Handle redirect for impression tracking
        const redirectUrl = searchParams.get('redirect_url');
        if (redirectUrl) {
          // DSP win - redirect to original advertiser URL
          return NextResponse.redirect(decodeURIComponent(redirectUrl), 302);
        } else {
          // Local campaign - redirect to campaign landing URL
          const campaign = winner.data;
          if (campaign && campaign.landing_url) {
            return NextResponse.redirect(campaign.landing_url, 302);
          }
        }
      } else {
        // This is initial ad serving - don't process cost yet, just prepare the ad
        logger.app.info(`Serving initial ad for auction ${auctionId} - cost will be processed on impression tracking`);
      }
    } else {
      logger.auction.info(`Popup ad preparation: Skipping billing and tracking for auction ${auctionId}`);
    }

    // Send win notification to DSP if available
    // For DSP wins, send notification immediately but cost processing happens on win confirmation
    if (winner.source === 'dsp' && winner.winUrl) {
      try {
        // Replace macros in win URL
        let dspWinUrl = winner.winUrl.replace('${AUCTION_PRICE}', winner.bidPrice.toFixed(4));
        dspWinUrl = dspWinUrl.replace('${AUCTION_ID}', auctionId.toString());

        // Use a non-blocking fetch to send the win notification
        fetch(dspWinUrl, { method: 'GET', mode: 'no-cors' }).catch(error => {
          logger.dsp.error(`Failed to send win notification to DSP ${winner.partnerName} (${winner.partnerId}):`, error);
        });
        logger.dsp.info(`Sent win notification to DSP ${winner.partnerName} (${winner.partnerId}): ${dspWinUrl}`);
      } catch (error) {
        logger.dsp.error(`Error sending win notification to DSP ${winner.partnerName} (${winner.partnerId}):`, error);
      }
    }

    // Handle popup redirect (direct traffic)
    if (format === 'popup' && redirect) {
      logger.serve.info('Processing popup redirect request');

      // For popup redirect, send user directly to landing page
      // Ensure landingUrl doesn't contain placeholder text
      let landingUrl = winner.landingUrl || 'https://example.com';

      // If landingUrl contains placeholder text, use the original landing URL from DSP data
      if (landingUrl.includes('PLACEHOLDER_SERVE_URL')) {
        landingUrl = winner.data?.originalLandingUrl || winner.data?.landing_url || 'https://example.com';
        logger.serve.warn('Fixed placeholder URL in popup redirect:', {
          originalUrl: winner.landingUrl,
          fixedUrl: landingUrl
        });
      }

      logger.serve.info('Redirecting to:', { landingUrl });
      return NextResponse.redirect(landingUrl, 302);
    }

    // Generate ad HTML based on winner source
    let adHtml = '';
    let clickUrl = '';

    if (winner.source === 'dsp' && winner.adMarkup) {
      // DSP provided ad markup - replace placeholder URLs with actual tracking URLs
      clickUrl = `${process.env.PLATFORM_URL}/api/serve?auction_id=${auctionId}&campaign_id=${campaignId}&redirect_url=${encodeURIComponent(winner.data?.originalLandingUrl || winner.landingUrl || '')}`;

      // Replace placeholder serve URLs in DSP markup with actual tracking URL
      adHtml = winner.adMarkup.replace(/PLACEHOLDER_SERVE_URL/g, clickUrl);

      logger.auction.info(`DSP ad markup processed: Original landing URL ${winner.data?.originalLandingUrl || winner.landingUrl} -> Tracking URL ${clickUrl}`);
    } else if (winner.source === 'local' && winner.data) {
      // Local campaign - generate ad HTML
      clickUrl = `${process.env.PLATFORM_URL}/api/serve?auction_id=${auctionId}&campaign_id=${campaignId}`;
      const campaign = winner.data;

      if (format === 'banner') {
        // Check if this is a JS tag campaign or image campaign (creative_type: 'js' or 'image')
        if (campaign.creative_type === 'js' && campaign.js_tag && campaign.js_tag.trim()) {
          // JS Tag Campaign - deliver the actual JavaScript code directly
          adHtml = campaign.js_tag;
        } else if (campaign.creative_type === 'image' && campaign.banner_image_url && campaign.banner_image_url.trim()) {
          // Image Campaign - deliver image banner with complete URL
          const baseUrl = process.env.NEXTAUTH_URL || process.env.PLATFORM_URL || 'http://localhost:3000';
          const imageUrl = campaign.banner_image_url.startsWith('http')
            ? campaign.banner_image_url
            : `${baseUrl}${campaign.banner_image_url}`;

          adHtml = `
            <div style="width:${size.split('x')[0]}px;height:${size.split('x')[1]}px;">
              <a href="${clickUrl}" target="_blank" style="display:block;width:100%;height:100%;">
                <img src="${imageUrl}"
                     style="width:100%;height:100%;object-fit:cover;"
                     alt="Advertisement" />
              </a>
            </div>
          `;
        } else {
          // No image or JS tag available - return empty/no ad
          logger.auction.warn('No banner creative available for campaign', { campaignId: campaign.id, creativeType: campaign.creative_type, hasJsTag: !!campaign.js_tag, hasBannerImage: !!campaign.banner_image_url });
          adHtml = `<div style="width:${size.split('x')[0]}px;height:${size.split('x')[1]}px;"><!-- No banner creative available --></div>`;
        }
      } else if (format === 'native') {
        const title = campaign.native_title || campaign.push_title || campaign.name || 'Sponsored Content';
        const description = campaign.native_description || campaign.push_description || 'Click to learn more about this offer.';

        adHtml = `
          <div class="native-ad" style="border:1px solid #ddd;padding:15px;background:#fff;">
            <a href="${clickUrl}" target="_blank" style="text-decoration:none;color:inherit;">
              <h4 style="margin:0 0 10px 0;color:#333;">${title}</h4>
              <p style="margin:0;color:#666;line-height:1.4;">${description}</p>
              <span style="font-size:12px;color:#999;margin-top:10px;display:block;">Sponsored</span>
            </a>
          </div>
        `;
      } else if (format === 'in_page_push') {
        const title = campaign.push_title || campaign.native_title || campaign.name || 'Notification';
        const description = campaign.push_description || campaign.native_description || 'Click to learn more about this offer.';

        adHtml = `
          <div class="push-notification" style="background:#fff;border:1px solid #ddd;border-radius:8px;padding:15px;box-shadow:0 2px 8px rgba(0,0,0,0.1);max-width:300px;">
            <div style="display:flex;align-items:center;margin-bottom:10px;">
              <div style="width:40px;height:40px;background:#007bff;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:10px;">
                <span style="color:#fff;font-size:18px;">📢</span>
              </div>
              <div>
                <h5 style="margin:0;font-size:14px;font-weight:bold;color:#333;">${title}</h5>
                <p style="margin:0;font-size:12px;color:#666;">Advertisement</p>
              </div>
            </div>
            <a href="${clickUrl}" target="_blank" style="text-decoration:none;color:inherit;">
              <p style="margin:0;color:#333;font-size:13px;line-height:1.4;">${description}</p>
            </a>
          </div>
        `;
      } else if (format === 'popup') {
        // Popup ads don't need HTML content - they just redirect to landing URL
        // The ad tag script will handle opening the click URL in a new tab
        adHtml = '<!-- Popup ad - will redirect to landing URL -->';
      }
    }

    // Note: Impression tracking now happens only when user clicks (using auction_id)
    // This prevents counting impressions for ads that are served but never viewed

    const response = {
      ad: {
        type: format,
        creative_type: winner.source === 'local' ? getCreativeTypeString(winner.data?.creative_type) : 'dsp',
        html: adHtml,
        auction_id: auctionId, // Use auction_id instead of impression_id
        campaign_id: campaignId,
        dsp_partner_id: winner.partnerId || 0, // For DSP wins
        ssp_partner_id: parseInt(searchParams.get('ssp_partner_id') || '0') || 0, // For SSP requests
        website_id: zone.website_id, // For popup impression tracking
        landing_url: clickUrl, // Send tracking URL to SSP/Publisher for all formats
        cpm: publisherCpm, // Publisher sees their earning rate, not original bid
        original_bid: winner.bidPrice, // Original bid price for billing
        currency: 'USD',
        source: winner.source, // Track whether this came from local campaign or DSP
        partner_name: winner.partnerName || undefined, // Include DSP partner name if applicable
      }
    };

    // Support JSONP callback
    const callback = searchParams.get('callback');
    if (callback) {
      return new NextResponse(
        `${callback}(${JSON.stringify(response)});`,
        {
          headers: {
            'Content-Type': 'application/javascript',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    return NextResponse.json(response, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    // Enhanced error logging to capture all error details
    const errorDetails = {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      code: (error as any)?.code,
      type: (error as any)?.type,
      url: request.url,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    };

    logger.serve.error('Ad serving error details:', errorDetails);
    logger.serve.error('Raw error object:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
