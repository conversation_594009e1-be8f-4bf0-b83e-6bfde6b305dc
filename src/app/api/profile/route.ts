import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import bcrypt from 'bcryptjs';

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const result = await clickhouse.query({
      query: 'SELECT * FROM users WHERE id = {id:UInt32}',
      query_params: { id: parseInt(session.user.id) },
    });

    const users = await result.json();

    if (!users.data || users.data.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const user = users.data[0];

    // Remove sensitive data
    const { password, ...userProfile } = user;

    return NextResponse.json(userProfile);
  } catch (error) {
    console.error('Profile fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { fullName, address, city, state, zip, country } = body;

    // Validate required fields
    if (!fullName?.trim()) {
      return NextResponse.json({ error: 'Full name is required' }, { status: 400 });
    }

    await clickhouse.command({
      query: `
        ALTER TABLE users
        UPDATE
          full_name = {fullName:String},
          address = {address:String},
          city = {city:String},
          state = {state:String},
          zip = {zip:String},
          country = {country:String},
          updated_at = now()
        WHERE id = {id:UInt32}
      `,
      query_params: {
        id: parseInt(session.user.id),
        fullName: fullName.trim(),
        address: address?.trim() || '',
        city: city?.trim() || '',
        state: state?.trim() || '',
        zip: zip?.trim() || '',
        country: country?.trim() || '',
      },
    });

    return NextResponse.json({ message: 'Profile updated successfully' });
  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
