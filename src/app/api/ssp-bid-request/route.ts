import { NextRequest, NextResponse } from 'next/server';
import { AuctionService } from '@/lib/auction-service';
import deviceGeoDetector from '@/lib/device-geo-detector';
import logger from '@/lib/logger';
import clickhouse from '@/lib/clickhouse';

interface SSPPartner {
  id: number;
  name: string;
  type: string;
  status: string;
  protocol: string;
  openrtb_version?: string;
  bid_price_format?: string;
  revenue_share: number;
  timeout_ms?: number;
  endpoint_url?: string;
  api_key?: string;
  auth_type?: string;
  auth_credentials?: string;
  seat_id?: string;
  targeting?: string;
}

export async function POST(request: NextRequest) {
  try {
    const partnerId = request.headers.get('x-ssp-partner-id') || request.nextUrl.searchParams.get('partner_id');

    if (!partnerId) {
      // For OpenRTB, a 204 No Content or specific error response might be expected.
      // For XML, an error XML response.
      // For now, return a generic error.
      logger.ssp.warn('SSP Bid Request: Missing partner ID');
      return NextResponse.json({ error: 'Missing partner ID' }, { status: 400 });
    }

    // Authenticate SSP partner and get configuration
    const partnerResult = await clickhouse.query({
      query: `
        SELECT id, name, type, status, protocol, openrtb_version,
               bid_price_format, revenue_share, timeout_ms,
               endpoint_url, api_key, auth_type, auth_credentials, seat_id, targeting
        FROM partner_endpoints
        WHERE id = {partnerId:UInt32} AND type = 'ssp' AND status = 'active'
      `,
      query_params: { partnerId: parseInt(partnerId) },
    });

    const partners = await partnerResult.json();
    if (partners.data.length === 0) {
      logger.ssp.warn(`SSP Bid Request: Invalid or inactive SSP partner ID: ${partnerId}`);
      return NextResponse.json({ error: 'Invalid or inactive SSP partner' }, { status: 403 });
    }
    const sspPartner = partners.data[0] as SSPPartner;

    // Determine request format (OpenRTB JSON or XML)
    const contentType = request.headers.get('content-type')?.toLowerCase() || '';
    logger.ssp.debug(`SSP Bid Request from ${sspPartner.name}: Incoming Content-Type: ${contentType}`);
    let auctionRequestData: any;
    let requestFormat: 'openrtb' | 'xml';

    // Prioritize XML parsing if Content-Type is XML
    if (contentType.includes('application/xml') || contentType.includes('text/xml')) {
      requestFormat = 'xml';
      const xmlBody = await request.text();
      auctionRequestData = parseXmlSspBidRequest(xmlBody);
      logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: Received XML request`);
    } else if (contentType.includes('application/json') || contentType.includes('application/openrtb+json')) {
      requestFormat = 'openrtb';
      auctionRequestData = await request.json();
      logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: Received OpenRTB JSON request`);
    } else {
      logger.ssp.warn(`SSP Bid Request from ${sspPartner.name}: Unsupported content type: ${contentType}`);
      return NextResponse.json({ error: 'Unsupported Content-Type' }, { status: 415 });
    }

    // Extract necessary info for AuctionService
    const userAgent = request.headers.get('user-agent') || auctionRequestData.device?.ua || '';
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || auctionRequestData.device?.ip || '127.0.0.1';
    const country = auctionRequestData.device?.geo?.country || auctionRequestData.site?.geo?.country || '';
    const region = auctionRequestData.device?.geo?.region || auctionRequestData.site?.geo?.region || '';
    const city = auctionRequestData.device?.geo?.city || auctionRequestData.site?.geo?.city || '';
    const deviceType = auctionRequestData.device?.devicetype === 1 ? 'Mobile' : (auctionRequestData.device?.devicetype === 2 ? 'Desktop' : (auctionRequestData.device?.devicetype === 5 ? 'Tablet' : 'Unknown'));
    const os = auctionRequestData.device?.os || '';
    const browser = auctionRequestData.device?.browser || '';

    // Attempt to detect geo and device if not provided in bid request
    const deviceGeoInfo = await deviceGeoDetector.detectDeviceAndGeo(ipAddress, userAgent);
    const finalCountry = country || deviceGeoInfo.country;
    const finalState = region || deviceGeoInfo.state || deviceGeoInfo.region || 'Unknown';
    const finalCity = city || deviceGeoInfo.city || 'Unknown';
    const finalDeviceType = deviceType !== 'Unknown' ? deviceType : deviceGeoInfo.device_type;
    const finalOs = os || deviceGeoInfo.os;
    const finalBrowser = browser || deviceGeoInfo.browser;

    // Derive format and size from impression object
    const impression = auctionRequestData.imp?.[0] || {};
    let format = 'unknown';
    let size = '';

    if (impression.banner) {
      format = 'banner';
      size = `${impression.banner.w || 0}x${impression.banner.h || 0}`;
    } else if (impression.native) {
      format = 'native';
    } else if (impression.video) {
      format = 'video';
      size = `${impression.video.w || 0}x${impression.video.h || 0}`;
    } else if (impression.audio) {
      format = 'audio';
    } else if (auctionRequestData.ext?.format === 'popup' || impression.ext?.format === 'popup' || auctionRequestData.format === 'popup') {
      format = 'popup';
    } else if (auctionRequestData.ext?.format === 'in_page_push' || impression.ext?.format === 'in_page_push' || auctionRequestData.format === 'in_page_push') {
      format = 'in_page_push';
    }

    // Determine site/app ID more robustly
    const siteId = auctionRequestData.site?.id;
    const websiteId = siteId ? (isNaN(parseInt(siteId)) ? 0 : parseInt(siteId)) : 0;
    const zoneId = impression.id ? (isNaN(parseInt(impression.id)) ? 0 : parseInt(impression.id)) : 0;

    const auctionRequest = {
      zoneId: zoneId,
      websiteId: websiteId,
      format: format,
      size: size,
      userAgent: userAgent,
      ipAddress: ipAddress,
      country: finalCountry,
      state: finalState,
      city: finalCity,
      deviceType: finalDeviceType,
      os: finalOs,
      browser: finalBrowser,
    };

    // Basic validation of auction request data
    if (!auctionRequest.format || auctionRequest.websiteId === 0 || auctionRequest.zoneId === 0) {
      logger.ssp.warn(`SSP Bid Request from ${sspPartner.name}: Invalid auction request data. Format: ${auctionRequest.format}, Website ID: ${auctionRequest.websiteId}, Zone ID: ${auctionRequest.zoneId}`);
      return NextResponse.json({ error: 'Invalid request structure' }, { status: 400 });
    }

    // Run unified auction
    const auctionResult = await AuctionService.runAuction(auctionRequest);

    if (!auctionResult.winner) {
      logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: No winner for auction. Returning No Bid.`);
      // Return 204 No Content for OpenRTB no bid
      return new NextResponse(null, { status: 204 });
    }

    const winner = auctionResult.winner;
    logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: Winner found: ${winner.source} with bid $${winner.bidPrice}`);

    // Construct response back to SSP
    let sspResponse: any;
    if (requestFormat === 'openrtb') {
      sspResponse = constructOpenRtbBidResponse(sspPartner, auctionRequestData, winner);
    } else { // XML
      sspResponse = constructXmlBidResponse(sspPartner, auctionRequestData, winner);
    }

    // For JS tag campaigns, we need to decide how to deliver them to SSPs.
    // OpenRTB has 'adm' (ad markup). If it's a JS tag, we might wrap it in an iframe.
    // SSPs typically expect HTML or a URL.
    if (winner.source === 'local' && winner.creativeType === 'js' && winner.adMarkup) {
      winner.adMarkup = `<iframe srcdoc="${encodeURIComponent(winner.adMarkup)}" width="${auctionRequest.size.split('x')[0]}" height="${auctionRequest.size.split('x')[1]}" frameborder="0" scrolling="no" style="border:0;"></iframe>`;
      logger.ssp.info(`SSP Bid Request: Wrapped JS tag creative in iframe for delivery to SSP.`);
    }

    // Placeholder functions for bid response construction and XML parsing
    // These will be implemented in subsequent steps.
    function constructOpenRtbBidResponse(partner: any, originalRequest: any, winner: any): any {
      // Implement OpenRTB bid response construction here
      const bidResponse: any = {
        id: originalRequest.id,
        seatbid: [{
          bid: [{
            id: winner.data?.id || `bid_${Date.now()}`,
            impid: originalRequest.imp?.[0]?.id || '1',
            price: winner.bidPrice / (partner.bid_price_format === 'cpm' ? 1000 : 1), // Convert CPM to price per impression for OpenRTB
            adm: winner.adMarkup,
            crid: winner.campaignId || winner.partnerId,
            adomain: [winner.landingUrl ? new URL(winner.landingUrl).hostname : ''],
            nurl: `${process.env.PLATFORM_URL}/api/ssp/win?ssp_imp_id=${originalRequest.imp?.[0]?.id || '1'}&ssp_partner_id=${sspPartner.id}&bid_price=${winner.bidPrice}&winner_source=${winner.source}&winner_id=${winner.source === 'local' ? winner.campaignId : winner.partnerId}`,
          }]
        }],
        bidid: auctionResult.auctionId,
        cur: 'USD',
      };

      // Add banner dimensions if it's a banner ad
      if (originalRequest.imp?.[0]?.banner) {
        bidResponse.seatbid[0].bid[0].w = originalRequest.imp[0].banner.w;
        bidResponse.seatbid[0].bid[0].h = originalRequest.imp[0].banner.h;
      }
      return bidResponse;
    }

    function constructXmlBidResponse(partner: any, originalRequest: any, winner: any): string {
      // Implement XML bid response construction here
      let xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>\n<response>\n  <status>success</status>\n  <id>${originalRequest.id || auctionResult.auctionId}</id>\n  <bid>\n    <price>${winner.bidPrice / (partner.bid_price_format === 'cpm' ? 1000 : 1)}</price>\n    <markup><![CDATA[${winner.adMarkup}]]></markup>\n    <nurl><![CDATA[${process.env.PLATFORM_URL}/api/ssp/win?ssp_imp_id=${originalRequest.imp?.[0]?.id || '1'}&ssp_partner_id=${sspPartner.id}&bid_price=${winner.bidPrice}&winner_source=${winner.source}&winner_id=${winner.source === 'local' ? winner.campaignId : winner.partnerId}]]></nurl>\n    <url><![CDATA[${winner.landingUrl}]]></url>\n  </bid>\n</response>`;
      return xmlResponse;
    }

    function parseXmlSspBidRequest(xml: string): any {
        const requestData: any = {};

        // Extract ID
        const idMatch = xml.match(/<id>(.*?)<\/id>/);
        if (idMatch) requestData.id = idMatch[1];

        // Extract Imp (Impression) details - simplified for one impression
        const impMatch = xml.match(/<imp>(.*?)<\/imp>/s);
        if (impMatch) {
            const impXml = impMatch[1];
            const imp: any = {};

            const impIdMatch = impXml.match(/<id>(.*?)<\/id>/);
            if (impIdMatch) imp.id = impIdMatch[1];

            const bannerMatch = impXml.match(/<banner.*?w="(\d+)".*?h="(\d+)".*?\/>/);
            if (bannerMatch) {
                imp.banner = { w: parseInt(bannerMatch[1]), h: parseInt(bannerMatch[2]) };
            }
            const formatMatch = impXml.match(/<format>(.*?)<\/format>/);
            if (formatMatch) imp.format = formatMatch[1];

            requestData.imp = [imp];
        }

        // Extract Site details
        const siteMatch = xml.match(/<site>(.*?)<\/site>/s);
        if (siteMatch) {
            const siteXml = siteMatch[1];
            const site: any = {};
            const siteIdMatch = siteXml.match(/<id>(.*?)<\/id>/);
            if (siteIdMatch) site.id = siteIdMatch[1];
            requestData.site = site;
        }

        // Extract Device details
        const deviceMatch = xml.match(/<device>(.*?)<\/device>/s);
        if (deviceMatch) {
            const deviceXml = deviceMatch[1];
            const device: any = {};

            const uaMatch = deviceXml.match(/<ua><!\[CDATA\[([^\]]+)\]\]><\/ua>/);
            if (uaMatch) device.ua = uaMatch[1];

            const ipMatch = deviceXml.match(/<ip>(.*?)<\/ip>/);
            if (ipMatch) device.ip = ipMatch[1];

            const geoMatch = deviceXml.match(/<geo>(.*?)<\/geo>/s);
            if (geoMatch) {
                const geoXml = geoMatch[1];
                const geo: any = {};
                const countryMatch = geoXml.match(/<country>(.*?)<\/country>/);
                if (countryMatch) geo.country = countryMatch[1];
                const regionMatch = geoXml.match(/<region>(.*?)<\/region>/);
                if (regionMatch) geo.region = regionMatch[1];
                const cityMatch = geoXml.match(/<city>(.*?)<\/city>/);
                if (cityMatch) geo.city = cityMatch[1];
                device.geo = geo;
            }
            const deviceTypeMatch = deviceXml.match(/<devicetype>(.*?)<\/devicetype>/);
            if (deviceTypeMatch) device.devicetype = parseInt(deviceTypeMatch[1]);
            const osMatch = deviceXml.match(/<os>(.*?)<\/os>/);
            if (osMatch) device.os = osMatch[1];
            const browserMatch = deviceXml.match(/<browser>(.*?)<\/browser>/);
            if (browserMatch) device.browser = browserMatch[1];

            requestData.device = device;
        }

        // Extract tmax
        const tmaxMatch = xml.match(/<tmax>(.*?)<\/tmax>/);
        if (tmaxMatch) requestData.tmax = parseInt(tmaxMatch[1]);

        return requestData;
    }

    const headers: { [key: string]: string } = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    if (requestFormat === 'xml') {
        headers['Content-Type'] = 'application/xml';
    } else {
        headers['Content-Type'] = 'application/json';
    }

    return new NextResponse(
      requestFormat === 'xml' ? sspResponse : JSON.stringify(sspResponse),
      { headers: headers }
    );

  } catch (error) {
    logger.ssp.error('SSP Bid Request Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
} 