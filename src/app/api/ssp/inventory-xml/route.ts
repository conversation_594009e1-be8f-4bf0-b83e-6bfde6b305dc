import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { AuctionService, AuctionBid } from '@/lib/auction-service';
import deviceGeoDetector from '@/lib/device-geo-detector';
import logger from '@/lib/logger';
import { RequestTracker } from '@/lib/request-tracker';
const { CounterManager } = require('@/lib/counter-manager');

interface SSPPartner {
  id: number;
  name: string;
  type: string;
  status: string;
  protocol: string;
  openrtb_version?: string;
  bid_price_format?: string;
  revenue_share: number;
  timeout_ms?: number;
  endpoint_url?: string;
  api_key?: string;
  auth_type?: string;
  auth_credentials?: string;
  seat_id?: string;
  targeting?: string;
}

// Interface for platform settings data
interface PlatformSetting {
  setting_value: string;
}

// Interface for website publisher data
interface WebsitePublisherInfo {
  user_id: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const partnerId = searchParams.get('partner_id');

    if (!partnerId) {
      logger.ssp.warn('SSP XML Bid Request: Missing partner ID');
      return new NextResponse(
        '<?xml version="1.0" encoding="UTF-8"?><response><status>error</status><message>Missing partner ID</message></response>',
        { status: 400, headers: { 'Content-Type': 'application/xml' } }
      );
    }

    const partnerResult = await clickhouse.query({
      query: `
        SELECT id, name, type, status, protocol, openrtb_version,
               bid_price_format, revenue_share, timeout_ms,
               endpoint_url, api_key, auth_type, auth_credentials, seat_id, targeting
        FROM partner_endpoints
        WHERE id = {partnerId:UInt32} AND type = 'ssp' AND status = 'active' AND protocol = 'xml'
      `,
      query_params: { partnerId: parseInt(partnerId) },
    });

    const partners = await partnerResult.json();
    if (partners.data.length === 0) {
      logger.ssp.warn(`SSP XML Bid Request: Invalid or inactive SSP partner ID: ${partnerId}`);
      return new NextResponse(
        '<?xml version="1.0" encoding="UTF-8"?><response><status>error</status><message>Invalid or inactive SSP partner</message></response>',
        { status: 403, headers: { 'Content-Type': 'application/xml' } }
      );
    }
    const sspPartner = partners.data[0] as SSPPartner;

    // Get global publisher revenue share as fallback
    let globalRevenueShare = 20; // Default fallback - 20% as specified
    const revenueShareSetting = await clickhouse.query({
      query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
    });
    const revenueShareData = await revenueShareSetting.json();
    if (revenueShareData.data.length > 0) {
      const setting = revenueShareData.data[0] as PlatformSetting;
      globalRevenueShare = parseFloat(setting.setting_value);
    }
    logger.ssp.info(`SSP XML Bid Request from ${sspPartner.name}: Received GET request`);

    // Extract info for AuctionService from URL parameters
    const userAgent = searchParams.get('ua') || '';
    const ipAddress = searchParams.get('ip') || '';
    const country = searchParams.get('country') || '';
    const region = searchParams.get('region') || '';
    const city = searchParams.get('city') || '';
    const deviceType = searchParams.get('device') || '';
    const os = searchParams.get('os_ver') || '';
    const browser = searchParams.get('browser') || '';
    const format = searchParams.get('format') || 'unknown'; // Default to 'unknown' instead of 'banner'
    const size = searchParams.get('size') || ''; // No default size for flexible ad formats like popup
    const zoneIdParam = searchParams.get('subid') || searchParams.get('zone_id') || '0';
    
    // Generate a unique numeric impression ID for tracking purposes using CounterManager
    const impressionId = await CounterManager.getNextId('impression');

    let websiteId = 0; // Initialize as a number
    const refParam = searchParams.get('ref');
    if (refParam) {
      try {
        const url = new URL(refParam);
        // Attempt to parse websiteId from hostname as an integer. If not a number, it remains 0.
        websiteId = parseInt(url.hostname) || 0;
      } catch (e) {
        logger.ssp.warn(`SSP XML Bid Request: Invalid URL in \'ref\' parameter: ${refParam}`);
        websiteId = 0; // Default to 0 for invalid URLs
      }
    }

    // Attempt to detect geo and device if not provided in bid request
    const deviceGeoInfo = await deviceGeoDetector.detectDeviceAndGeo(ipAddress, userAgent);
    const finalCountry = country || deviceGeoInfo.country;
    const finalState = region || deviceGeoInfo.state || deviceGeoInfo.region || 'Unknown';
    const finalCity = city || deviceGeoInfo.city || 'Unknown';
    const finalDeviceType = deviceType !== '' ? deviceType : deviceGeoInfo.device_type;
    const finalOs = os || deviceGeoInfo.os;
    const finalBrowser = browser || deviceGeoInfo.browser;
    const finalConnectionType = deviceGeoInfo.connection_type || 'Unknown'; // Get connection type
    const referrerUrl = refParam || ''; // Capture the full referrer URL

    // Fetch publisher ID associated with the website
    let publisherId: number | undefined;
    if (websiteId) {
      try {
        const publisherResult = await clickhouse.query({
          query: `SELECT user_id FROM websites WHERE id = {websiteId:UInt32}`,
          query_params: { websiteId: websiteId },
        });
        const publisherData = await publisherResult.json();
        if (publisherData.data.length > 0) {
          const publisherInfo = publisherData.data[0] as WebsitePublisherInfo;
          publisherId = publisherInfo.user_id; // Assuming user_id is the publisher ID
        }
      } catch (e) {
        logger.ssp.warn(`SSP XML Bid Request: Error fetching publisher ID for website ${websiteId}:`, e);
      }
    }

    const auctionRequest = {
      zoneId: parseInt(zoneIdParam) || 0, // Keep this as is for internal auction filtering by zone, if needed
      websiteId: websiteId, // Now correctly a number
      format: format,
      size: size,
      userAgent: userAgent,
      ipAddress: ipAddress,
      country: finalCountry,
      state: finalState,
      city: finalCity,
      deviceType: finalDeviceType,
      os: finalOs,
      browser: finalBrowser,
      referrerUrl: referrerUrl, // Pass the referrer URL
      connectionType: finalConnectionType, // Pass connection type
      publisherId: publisherId, // Pass publisher ID
    };

    // Basic validation of auction request data
    // For XML, we are being more lenient with websiteId, as it's often not a numerical ID but a domain.
    if (!auctionRequest.format /* || auctionRequest.zoneId === 0 */) {
      logger.ssp.warn(`SSP XML Bid Request from ${sspPartner.name}: Invalid auction request data. Format: ${auctionRequest.format}, Website ID: ${auctionRequest.websiteId}, Zone ID: ${auctionRequest.zoneId}`);
      return new NextResponse(
        '<?xml version="1.0" encoding="UTF-8"?><response><status>error</status><message>Invalid request parameters</message></response>',
        { status: 400, headers: { 'Content-Type': 'application/xml' } }
      );
    }

    // Track incoming SSP XML request
    await RequestTracker.trackRequest({
      sourceType: 'ssp_incoming',
      partnerEndpointId: sspPartner.id,
      websiteId: auctionRequest.websiteId,
      sspId: sspPartner.id,
      publisherId: publisherId || 0,
      requestCount: 1,
      winCount: 0, // Will be updated if there's a winner
    });

    // Run unified auction
    const auctionResult = await AuctionService.runAuction(auctionRequest);

    if (!auctionResult.winner) {
      logger.ssp.info(`SSP XML Bid Request from ${sspPartner.name}: No winner for auction. Returning No Bid.`);
      return new NextResponse(
        '<?xml version="1.0" encoding="UTF-8"?><response><status>no_bid</status></response>',
        { status: 204, headers: { 'Content-Type': 'application/xml' } }
      );
    }

    const winner: AuctionBid = auctionResult.winner;
    logger.ssp.info(`SSP XML Bid Request from ${sspPartner.name}: Winner found: ${winner.source} with bid $${winner.bidPrice}`);

    // Track outgoing SSP XML response (bid response sent to SSP)
    await RequestTracker.trackRequest({
      sourceType: 'ssp_outgoing',
      partnerEndpointId: sspPartner.id,
      campaignId: winner.source === 'local' ? winner.campaignId : 0,
      websiteId: auctionRequest.websiteId,
      sspId: sspPartner.id,
      publisherId: publisherId || 0,
      advertiserId: winner.source === 'local' ? (winner.data?.user_id || 0) : 0,
      dspId: winner.source === 'dsp' ? (winner.data?.user_id || 0) : 0,
      requestCount: 0, // This is a response, not a request
      winCount: 0, // Win will be counted when impression is tracked
    });

    // Construct XML response back to SSP
    const xmlResponse = await constructXmlBidResponse(sspPartner, auctionRequest, winner, globalRevenueShare, impressionId);

    return new NextResponse(
      xmlResponse,
      { status: 200, headers: { 'Content-Type': 'application/xml' } }
    );

  } catch (error) {
    logger.ssp.error('SSP XML Bid Request Error:', error);
    return new NextResponse(
      '<?xml version="1.0" encoding="UTF-8"?><response><status>error</status><message>Internal server error</message></response>',
      { status: 500, headers: { 'Content-Type': 'application/xml' } }
    );
  }
}

// Helper function to construct XML Bid Response
async function constructXmlBidResponse(partner: SSPPartner, originalRequest: any, winner: AuctionBid, globalRevenueShare: number, impressionId: number): Promise<string> {
  const adMarkup = winner.adMarkup || ''; // Default to empty string if undefined or null
  const landingUrl = winner.landingUrl || ''; // Default to empty string

  // Determine the effective revenue share for the SSP
  const revenueShare = partner.revenue_share > 0 ? partner.revenue_share : globalRevenueShare;

  // Calculate the bid price after deducting platform share, ensuring it's in CPM first
  let sspAdjustedBidPriceCpm = winner.bidPrice * (revenueShare / 100);

  // Convert the adjusted bid price to SSP's preferred format
  let finalSspBidPrice = sspAdjustedBidPriceCpm;
  if (partner.bid_price_format === 'cpv' || partner.bid_price_format === 'cpc') {
    // Convert CPM to CPV/CPC (divide by 1000)
    finalSspBidPrice = sspAdjustedBidPriceCpm / 1000;
  }
  // If sspBidPriceFormat is 'cpm' or 'ecpm', keep as-is (already in CPM)

  // Ensure price is non-negative
  finalSspBidPrice = Math.max(0, finalSspBidPrice);

  // Generate tracking URLs using /api/serve endpoint with SSP partner ID
  // Include redirect_url for both local campaigns and DSP wins
  let redirectUrl = '';
  if (winner.source === 'local' && winner.data?.landing_url) {
    redirectUrl = winner.data.landing_url;
  } else if (winner.source === 'dsp' && winner.data?.originalLandingUrl) {
    redirectUrl = winner.data.originalLandingUrl;
  }

  const trackingUrl = `${process.env.PLATFORM_URL}/api/serve?impression_id=${impressionId}&campaign_id=${winner.source === 'local' ? winner.campaignId : 0}&ssp_partner_id=${partner.id}${redirectUrl ? `&redirect_url=${encodeURIComponent(redirectUrl)}` : ''}`;

  let xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>\n<response>\n  <status>success</status>\n  <id>${originalRequest.id || originalRequest.zoneId}</id>\n  <bid>\n    <price>${finalSspBidPrice.toFixed(6)}</price>\n    <markup><![CDATA[${adMarkup}]]></markup>\n    <nurl><![CDATA[${process.env.PLATFORM_URL}/api/ssp/win?ssp_imp_id=${impressionId}&ssp_partner_id=${partner.id}&bid_price=${finalSspBidPrice.toFixed(6)}&gross_bid_cpm=${winner.bidPrice.toFixed(6)}&winner_source=${winner.source}&winner_id=${winner.source === 'local' ? winner.campaignId : winner.partnerId}]]></nurl>\n    <url><![CDATA[${trackingUrl}]]></url>\n  </bid>\n</response>`;

  // For popup or in_page_push, if adMarkup is a URL, use it directly instead of CDATA
  if ((originalRequest.format === 'popup' || originalRequest.format === 'in_page_push') && isUrl(adMarkup)) {
    xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>\n<response>\n  <status>success</status>\n  <id>${originalRequest.id || originalRequest.zoneId}</id>\n  <bid>\n    <price>${finalSspBidPrice.toFixed(6)}</price>\n    <markup>${adMarkup}</markup>\n    <nurl><![CDATA[${process.env.PLATFORM_URL}/api/ssp/win?ssp_imp_id=${impressionId}&ssp_partner_id=${partner.id}&bid_price=${finalSspBidPrice.toFixed(6)}&gross_bid_cpm=${winner.bidPrice.toFixed(6)}&winner_source=${winner.source}&winner_id=${winner.source === 'local' ? winner.campaignId : winner.partnerId}]]></nurl>\n    <url><![CDATA[${trackingUrl}]]></url>\n  </bid>\n</response>`;
  }

  return xmlResponse;
}

function isUrl(str: string): boolean {
  try {
    new URL(str);
    return true;
  } catch (_) {
    return false;
  }
}

// Placeholder for OpenRTB JSON bid request parsing (not needed here, but kept for clarity)
// This file will only handle XML GET requests for bid requests.
// The main /api/ssp/inventory endpoint will handle OpenRTB POST requests for bid requests.

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
