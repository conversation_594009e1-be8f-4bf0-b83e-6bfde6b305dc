import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { RequestTracker } from '@/lib/request-tracker';
import { CostProcessor } from '@/lib/cost-processor';
import logger from '@/lib/logger';
import { clickHouseBatchWriter } from '@/lib/clickhouse-batch-writer';

interface SSPPartner {
  id: number;
  user_id: number;
  name: string;
  revenue_share: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sspImpId = searchParams.get('ssp_imp_id');
    const sspPartnerId = searchParams.get('ssp_partner_id');
    const bidPriceStr = searchParams.get('bid_price');
    const grossBidCpmStr = searchParams.get('gross_bid_cpm');
    const winnerSource = searchParams.get('winner_source');
    const winnerIdStr = searchParams.get('winner_id');

    if (!sspImpId || !sspPartnerId || !bidPriceStr || !grossBidCpmStr || !winnerSource || !winnerIdStr) {
      logger.ssp.warn('SSP Win: Missing required parameters', { sspImpId, sspPartnerId, bidPriceStr, grossBidCpmStr, winnerSource, winnerIdStr });
      return new NextResponse('Missing parameters', { status: 400 });
    }

    const bidPrice = parseFloat(bidPriceStr);
    const grossBidCpm = parseFloat(grossBidCpmStr);
    const sspPartnerIdInt = parseInt(sspPartnerId);
    const winnerId = parseInt(winnerIdStr);
    const sspImpIdInt = parseInt(sspImpId);

    if (isNaN(bidPrice) || isNaN(grossBidCpm) || isNaN(sspPartnerIdInt) || isNaN(winnerId) || isNaN(sspImpIdInt)) {
      logger.ssp.warn('SSP Win: Invalid numeric parameters', { bidPriceStr, grossBidCpmStr, sspPartnerId, winnerIdStr, sspImpId });
      return new NextResponse('Invalid numeric parameters', { status: 400 });
    }

    // Get SSP partner details (no longer needed for revenue share calculation here, but for user_id and name)
    const sspPartnerResult = await clickhouse.query({
      query: `
        SELECT id, user_id, name, revenue_share
        FROM partner_endpoints
        WHERE id = {sspPartnerId:UInt32} AND type = 'ssp' AND status = 'active'
      `,
      query_params: { sspPartnerId: sspPartnerIdInt },
    });

    const sspPartners = await sspPartnerResult.json();
    if (sspPartners.data.length === 0) {
      logger.ssp.warn(`SSP Win: SSP partner not found or inactive: ${sspPartnerId}`);
      return new NextResponse('SSP Partner not found or inactive', { status: 404 });
    }

    const sspPartner = sspPartners.data[0] as SSPPartner;
    const sspUserAccountId = sspPartner.user_id; // The user account ID associated with this SSP partner
    
    // Calculate actual revenue split based on gross bid and net bid to SSP
    const platformRevenue = grossBidCpm - bidPrice; // Platform's cut
    const supplierRevenue = bidPrice; // SSP's actual earnings (already net of platform's share)

    // NOTE: ALL BALANCE PROCESSING (advertiser/DSP debit, publisher/SSP credit) is handled
    // during impression tracking in /api/serve, NOT here in SSP win notification.
    // This endpoint ONLY records the win notification event for tracking purposes.

    logger.ssp.info(`SSP Win: Recording win notification - Gross: $${grossBidCpm}, SSP should get: $${supplierRevenue}, Platform should keep: $${platformRevenue}`);

    // Track SSP inventory win (using our new RequestTracker.trackWin for SSP inbound)
    await RequestTracker.trackWin({
      winType: 'ssp_inventory',
      campaignId: winnerSource === 'local' ? winnerId : 0, // Our internal campaign ID if local
      partnerEndpointId: winnerSource === 'dsp' ? winnerId : 0, // Our internal DSP ID if DSP
      winnerId: winnerId, // DSP/Advertiser who won (our internal ID)
      supplierId: sspUserAccountId, // SSP User ID who supplied inventory
      websiteId: 0, // Not applicable for SSP inventory
      zoneId: 0, // Not applicable for SSP inventory
      winPrice: grossBidCpm, // The gross price paid by the winner
      platformRevenue: platformRevenue,
      supplierRevenue: supplierRevenue,
      sspImpressionId: sspImpId, // SSP's original impression ID
    });
    logger.ssp.info(`SSP Win: Tracked win for SSP ${sspPartner.name} (ID: ${sspPartnerId}) from winner ${winnerSource}:${winnerId} - Gross: $${grossBidCpm.toFixed(4)}, Net: $${supplierRevenue.toFixed(4)}`);

    // NOTE: No balance changes or transaction logging here
    // All financial transactions happen during impression tracking in /api/serve

    // No need to update transaction user_id separately if set correctly initially

    return new NextResponse('OK', { status: 200 });

  } catch (error) {
    logger.ssp.error('SSP win notification error:', error);
    return new NextResponse('Error', { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  // Handle POST requests for win notifications
  return GET(request);
}
