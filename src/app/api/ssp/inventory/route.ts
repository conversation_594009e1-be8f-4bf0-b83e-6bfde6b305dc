import { NextRequest, NextResponse } from 'next/server';
import { AuctionService } from '@/lib/auction-service';
import deviceGeoDetector from '@/lib/device-geo-detector';
import logger from '@/lib/logger';
import clickhouse from '@/lib/clickhouse';
import { RequestTracker } from '@/lib/request-tracker';

interface SSPPartner {
  id: number;
  name: string;
  type: string;
  status: string;
  protocol: string;
  openrtb_version?: string;
  bid_price_format?: string;
  revenue_share: number;
  timeout_ms?: number;
  endpoint_url?: string;
  api_key?: string;
  auth_type?: string;
  auth_credentials?: string;
  seat_id?: string;
  targeting?: string;
}

// Interface for website publisher data
interface WebsitePublisherInfo {
  user_id: number;
}

export async function POST(request: NextRequest) {
  try {
    const partnerId = request.headers.get('x-ssp-partner-id') || request.nextUrl.searchParams.get('partner_id');

    if (!partnerId) {
      logger.ssp.warn('SSP Bid Request: Missing partner ID');
      return NextResponse.json({ error: 'Missing partner ID' }, { status: 400 });
    }

    const partnerResult = await clickhouse.query({
      query: `
        SELECT id, name, type, status, protocol, openrtb_version,
               bid_price_format, revenue_share, timeout_ms,
               endpoint_url, api_key, auth_type, auth_credentials, seat_id, targeting
        FROM partner_endpoints
        WHERE id = {partnerId:UInt32} AND type = 'ssp' AND status = 'active'
      `,
      query_params: { partnerId: parseInt(partnerId) },
    });

    const partners = await partnerResult.json();
    if (partners.data.length === 0) {
      logger.ssp.warn(`SSP Bid Request: Invalid or inactive SSP partner ID: ${partnerId}`);
      return NextResponse.json({ error: 'Invalid or inactive SSP partner' }, { status: 403 });
    }
    const sspPartner = partners.data[0] as SSPPartner;

    const contentType = request.headers.get('content-type')?.toLowerCase() || '';
    logger.ssp.debug(`SSP Bid Request from ${sspPartner.name}: Incoming Content-Type: ${contentType}`);
    let auctionRequestData: any;

    if (contentType.includes('application/json') || contentType.includes('application/openrtb+json')) {
      auctionRequestData = await request.json();
      logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: Received OpenRTB JSON request`);
    } else {
      logger.ssp.warn(`SSP Bid Request from ${sspPartner.name}: Unsupported content type: ${contentType}`);
      return NextResponse.json({ error: 'Unsupported Content-Type' }, { status: 415 });
    }

    const userAgent = request.headers.get('user-agent') || auctionRequestData.device?.ua || '';
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || auctionRequestData.device?.ip || '127.0.0.1';
    const country = auctionRequestData.device?.geo?.country || auctionRequestData.site?.geo?.country || '';
    const region = auctionRequestData.device?.geo?.region || auctionRequestData.site?.geo?.region || '';
    const city = auctionRequestData.device?.geo?.city || auctionRequestData.site?.geo?.city || '';
    const deviceType = auctionRequestData.device?.devicetype === 1 ? 'Mobile' : (auctionRequestData.device?.devicetype === 2 ? 'Desktop' : (auctionRequestData.device?.devicetype === 5 ? 'Tablet' : 'Unknown'));
    const os = auctionRequestData.device?.os || '';
    const browser = auctionRequestData.device?.browser || '';

    const deviceGeoInfo = await deviceGeoDetector.detectDeviceAndGeo(ipAddress, userAgent);
    const finalCountry = country || deviceGeoInfo.country;
    const finalState = region || deviceGeoInfo.state || deviceGeoInfo.region || 'Unknown';
    const finalCity = city || deviceGeoInfo.city || 'Unknown';
    const finalDeviceType = deviceType !== 'Unknown' ? deviceType : deviceGeoInfo.device_type;
    const finalOs = os || deviceGeoInfo.os;
    const finalBrowser = browser || deviceGeoInfo.browser;
    const finalConnectionType = deviceGeoInfo.connection_type || 'Unknown';

    const impression = auctionRequestData.imp?.[0] || {};
    let format = 'unknown';
    let size = '';
    const referrerUrl = auctionRequestData.site?.page || request.nextUrl.searchParams.get('ref') || '';

    if (impression.banner) {
      format = 'banner';
      size = `${impression.banner.w || 0}x${impression.banner.h || 0}`;
    } else if (impression.native) {
      format = 'native';
    } else if (impression.video) {
      format = 'video';
      size = `${impression.video.w || 0}x${impression.video.h || 0}`;
    } else if (impression.audio) {
      format = 'audio';
    } else if (auctionRequestData.ext?.format === 'popup' || impression.ext?.format === 'popup' || auctionRequestData.format === 'popup') {
      format = 'popup';
    } else if (auctionRequestData.ext?.format === 'in_page_push' || impression.ext?.format === 'in_page_push' || auctionRequestData.format === 'in_page_push') {
      format = 'in_page_push';
    }

    const siteId = auctionRequestData.site?.id;
    const websiteId = siteId ? (isNaN(parseInt(siteId)) ? 0 : parseInt(siteId)) : 0;
    const zoneId = impression.id ? (isNaN(parseInt(impression.id)) ? 0 : parseInt(impression.id)) : 0;

    // Fetch publisher ID associated with the website
    let publisherId: number | undefined;
    if (websiteId) {
      try {
        const publisherResult = await clickhouse.query({
          query: `SELECT user_id FROM websites WHERE id = {websiteId:UInt32}`,
          query_params: { websiteId: websiteId },
        });
        const publisherData = await publisherResult.json();
        if (publisherData.data.length > 0) {
          const publisherInfo = publisherData.data[0] as WebsitePublisherInfo;
          publisherId = publisherInfo.user_id;
        }
      } catch (e) {
        logger.ssp.warn(`SSP Bid Request: Error fetching publisher ID for website ${websiteId}:`, e);
      }
    }

    const auctionRequest = {
      zoneId: zoneId,
      websiteId: websiteId,
      format: format,
      size: size,
      userAgent: userAgent,
      ipAddress: ipAddress,
      country: finalCountry,
      state: finalState,
      city: finalCity,
      deviceType: finalDeviceType,
      os: finalOs,
      browser: finalBrowser,
      referrerUrl: referrerUrl,
      connectionType: finalConnectionType,
      publisherId: publisherId,
    };

    if (!auctionRequest.format /* || auctionRequest.websiteId === 0 || auctionRequest.zoneId === 0 */) {
      logger.ssp.warn(`SSP Bid Request from ${sspPartner.name}: Invalid auction request data. Format: ${auctionRequest.format}, Website ID: ${auctionRequest.websiteId}, Zone ID: ${auctionRequest.zoneId}`);
      return NextResponse.json({ error: 'Invalid request structure' }, { status: 400 });
    }

    // Track incoming SSP request
    await RequestTracker.trackRequest({
      sourceType: 'ssp_incoming',
      partnerEndpointId: sspPartner.id,
      websiteId: websiteId,
      sspId: sspPartner.id, // SSP partner endpoint ID
      publisherId: publisherId || 0, // Publisher user ID
      requestCount: 1,
      winCount: 0, // Will be updated if there's a winner
    });

    const auctionResult = await AuctionService.runAuction(auctionRequest);

    if (!auctionResult.winner) {
      logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: No winner for auction. Returning No Bid.`);
      return new NextResponse(null, { status: 204 });
    }

    const winner = auctionResult.winner;
    logger.ssp.info(`SSP Bid Request from ${sspPartner.name}: Winner found: ${winner.source} with bid $${winner.bidPrice}`);

    // Track outgoing SSP response (bid response sent to SSP)
    await RequestTracker.trackRequest({
      sourceType: 'ssp_outgoing',
      partnerEndpointId: sspPartner.id,
      campaignId: winner.source === 'local' ? winner.campaignId : 0,
      websiteId: websiteId,
      sspId: sspPartner.id,
      publisherId: publisherId || 0,
      advertiserId: winner.source === 'local' ? (winner.data?.user_id || 0) : 0,
      dspId: winner.source === 'dsp' ? (winner.data?.user_id || 0) : 0,
      requestCount: 0, // This is a response, not a request
      winCount: 0, // Win will be counted when impression is tracked
    });

    const sspResponse = constructOpenRtbBidResponse(sspPartner, auctionRequestData, winner);

    if (winner.source === 'local' && winner.creativeType === 'js' && winner.adMarkup) {
      winner.adMarkup = `<iframe srcdoc="${encodeURIComponent(winner.adMarkup)}" width="${auctionRequest.size.split('x')[0]}" height="${auctionRequest.size.split('x')[1]}" frameborder="0" scrolling="no" style="border:0;"></iframe>`;
      logger.ssp.info(`SSP Bid Request: Wrapped JS tag creative in iframe for delivery to SSP.`);
    }

    function constructOpenRtbBidResponse(partner: any, originalRequest: any, winner: any): any {
      logger.ssp.debug(`Constructing OpenRTB response for winner.landingUrl: ${winner.landingUrl}`);
      const bidResponse: any = {
        id: originalRequest.id,
        seatbid: [{
          bid: [{
            id: winner.data?.id || `bid_${Date.now()}`,
            impid: originalRequest.imp?.[0]?.id || '1',
            price: winner.bidPrice / (partner.bid_price_format === 'cpm' ? 1000 : 1),
            // The 'adm' (ad markup/creative content) is populated from winner.adMarkup.
            // Ensure `creative_content` is populated in your 'campaigns' table for local bids.
            adm: winner.adMarkup,
            crid: winner.campaignId || winner.partnerId,
            // 'adomain' is populated from winner.landingUrl.
            // Ensure `landing_url` is populated in your 'campaigns' table for local bids.
            adomain: [],
            nurl: `${process.env.PLATFORM_URL}/api/ssp/win?ssp_imp_id=${originalRequest.imp?.[0]?.id || '1'}&ssp_partner_id=${sspPartner.id}&bid_price=${winner.bidPrice}&winner_source=${winner.source}&winner_id=${winner.source === 'local' ? winner.campaignId : winner.partnerId}`,
          }]
        }],
        bidid: auctionResult.auctionId,
        cur: 'USD',
      };

      if (winner.landingUrl) {
        try {
          const url = new URL(winner.landingUrl);
          bidResponse.seatbid[0].bid[0].adomain.push(url.hostname);
        } catch (e) {
          logger.ssp.warn(`SSP Bid Response: Invalid landing URL for adomain: ${winner.landingUrl}`);
        }
      }

      // Populate 'w' and 'h' from the original request's impression or from the winner's data
      if (originalRequest.imp?.[0]?.banner) {
        bidResponse.seatbid[0].bid[0].w = originalRequest.imp[0].banner.w;
        bidResponse.seatbid[0].bid[0].h = originalRequest.imp[0].banner.h;
      } else if (winner.source === 'local' && winner.data?.banner_size) {
        // For local campaigns, try to get dimensions from banner_size if available
        const [width, height] = winner.data.banner_size.split('x').map(Number);
        if (!isNaN(width) && !isNaN(height)) {
          bidResponse.seatbid[0].bid[0].w = width;
          bidResponse.seatbid[0].bid[0].h = height;
        }
      }
      return bidResponse;
    }

    // XML response constructor and parser are not needed here anymore

    const headers: { [key: string]: string } = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    headers['Content-Type'] = 'application/json';

    return new NextResponse(
      JSON.stringify(sspResponse),
      { headers: headers }
    );

  } catch (error) {
    logger.ssp.error('SSP Bid Request Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'ok',
    message: 'SSP Inventory Submission Endpoint',
    timestamp: new Date().toISOString(),
    methods: ['POST'],
  });
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

// Placeholder for XML parsing functions - will move to inventory-xml route
function parseXmlSspBidRequest(xml: string): any {
  return {}; // This will be handled in the new inventory-xml route
}

function constructXmlBidResponse(partner: any, originalRequest: any, winner: any): string {
  return ''; // This will be handled in the new inventory-xml route
}
