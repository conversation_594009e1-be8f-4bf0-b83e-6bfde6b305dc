import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const inventoryId = searchParams.get('inventory_id');
    const impId = searchParams.get('imp_id');

    if (!inventoryId) {
      return new NextResponse('Missing inventory_id', { status: 400 });
    }

    // Get user agent and IP for tracking
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'Unknown';

    // Generate impression ID
    const impressionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Get SSP inventory details
    const inventoryResult = await clickhouse.query({
      query: `
        SELECT si.*, pe.name as ssp_name
        FROM ssp_inventory si
        LEFT JOIN partner_endpoints pe ON si.ssp_partner_id = pe.id
        WHERE si.id = {inventoryId:UInt64}
      `,
      query_params: { inventoryId: parseInt(inventoryId) },
    });

    const inventoryData = await inventoryResult.json();
    if (inventoryData.data.length === 0) {
      return new NextResponse('Inventory not found', { status: 404 });
    }

    const inventory = inventoryData.data[0];

    // Log SSP impression with tracking data
    await clickhouse.insert({
      table: 'impressions',
      values: [{
        id: impressionId,
        campaign_id: 0, // SSP inventory doesn't have campaign ID
        website_id: 0, // SSP impressions don't have specific website/zone
        zone_id: 0,
        user_agent: userAgent,
        ip_address: ip,
        country: 'Unknown',
        region: 'Unknown',
        city: 'Unknown',
        device_type: 'Unknown',
        os: 'Unknown',
        browser: 'Unknown',
        cost_deducted: 0, // Will be updated when win notification comes
        publisher_revenue: 0, // Will be updated when win notification comes
        bid_type: 'cpm', // Default to CPM for SSP
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Return 1x1 transparent pixel
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    return new NextResponse(pixel, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('SSP impression tracking error:', error);
    
    // Return pixel even on error to avoid breaking ad display
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    return new NextResponse(pixel, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'no-cache',
      },
    });
  }
}
