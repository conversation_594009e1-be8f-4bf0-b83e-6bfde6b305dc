import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth'
import clickhouse from '@/lib/clickhouse';

interface PartnerData {
  id: number;
}

interface Totals {
  total_requests: number;
  total_wins: number;
  total_revenue: number;
  avg_win_rate: number;
}

interface EndpointBreakdown {
  partner_id: number;
  partner_name: string;
  total_requests: number;
  total_wins: number;
  total_revenue: number;
}

interface DimensionBreakdown {
  total_requests: number;
  total_wins: number;
  total_revenue: number;
}

interface CountryBreakdown extends DimensionBreakdown {
  country: string;
}

interface OSBreakdown extends DimensionBreakdown {
  os: string;
}

interface BrowserBreakdown extends DimensionBreakdown {
  browser: string;
}

interface DeviceBreakdown extends DimensionBreakdown {
  device_type: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const apiKey = searchParams.get('api_key');

    let session = null;
    let userId = null;

    // Check for API key in query parameters
    if (!apiKey) {
      // Fallback to session auth for browser requests
      session = await auth();
      if (!session || session.user?.role !== 'ssp') {
        return NextResponse.json(
          { message: 'Unauthorized - Missing API key' },
          { status: 401 }
        );
      }
      userId = parseInt(session.user.id);
    } else {
      // Validate API key format
      if (!apiKey.startsWith('ssp_')) {
        return NextResponse.json(
          { message: 'Unauthorized - Invalid API key format' },
          { status: 401 }
        );
      }

      // TODO: Validate API key against database and get user ID
      // For now, we'll use a placeholder user ID
      userId = 1; // This should be retrieved from the API key validation
    }


    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const groupBy = searchParams.get('group_by') || 'day';

    console.log('🔍 SSP Statistics API - User ID:', userId, 'Auth Type:', apiKey ? 'API Key' : 'Session');

    // Get SSP partner endpoint ID for this user
    const partnerResult = await clickhouse.query({
      query: `
        SELECT id FROM partner_endpoints
        WHERE user_id = {userId:UInt32} AND type = 'ssp'
        LIMIT 1
      `,
      query_params: { userId },
    });
    const partnerData = await partnerResult.json();
    const partnerId = (partnerData.data[0] as PartnerData)?.id || 0;

    let stats: any = {};

    if (groupBy === 'day') {
      // Daily statistics for SSP inventory requests - simplified query
      const dailyResult = await clickhouse.query({
        query: `
          SELECT
            date,
            sum(total_requests) as total_requests,
            sum(total_wins) as total_wins,
            sum(total_revenue) as total_revenue
          FROM request_stats
          WHERE source_type = 'ssp_inbound'
            AND partner_id = {partnerId:UInt32}
            AND date >= {dateFrom:String}
            AND date <= {dateTo:String}
          GROUP BY date
          ORDER BY date
        `,
        query_params: { partnerId, dateFrom, dateTo },
      });

      const dailyData = await dailyResult.json();
      // Calculate win rate and avg win price in JavaScript
      const processedData = (dailyData.data || []).map((row: any) => ({
        ...row,
        win_rate: row.total_requests > 0 ? Math.round((row.total_wins / row.total_requests) * 100 * 100) / 100 : 0,
        avg_win_price: row.total_wins > 0 ? row.total_revenue / row.total_wins : 0
      }));
      stats.daily_breakdown = processedData;

      // Get win notifications for SSP inventory
      const winsResult = await clickhouse.query({
        query: `
          SELECT
            toDate(timestamp) as date,
            COUNT(*) as wins,
            SUM(supplier_revenue) as total_revenue,
            AVG(supplier_revenue) as avg_revenue_per_win
          FROM win_notifications
          WHERE win_type = 'ssp_inventory'
            AND supplier_id = {userId:UInt32}
            AND toDate(timestamp) >= {dateFrom:String}
            AND toDate(timestamp) <= {dateTo:String}
          GROUP BY date
          ORDER BY date
        `,
        query_params: { userId, dateFrom, dateTo },
      });

      const winsData = await winsResult.json();
      stats.wins_breakdown = winsData.data || [];

    } else if (groupBy === 'endpoint') {
      // Endpoint breakdown
      const endpointResult = await clickhouse.query({
        query: `
          SELECT
            partner_id,
            partner_name,
            sum(total_requests) as total_requests,
            sum(total_wins) as total_wins,
            sum(total_revenue) as total_revenue
          FROM request_stats
          WHERE source_type = 'ssp_inbound'
            AND partner_id = {partnerId:UInt32}
            AND date >= {dateFrom:String}
            AND date <= {dateTo:String}
          GROUP BY partner_id, partner_name
          ORDER BY total_requests DESC
        `,
        query_params: { partnerId, dateFrom, dateTo },
      });

      const endpointData = await endpointResult.json();
      // Calculate win rate and avg win price for endpoint breakdown
      const processedEndpointData = (endpointData.data || []).map((row: any) => ({
        ...row,
        win_rate: row.total_requests > 0 ? Math.round((row.total_wins / row.total_requests) * 100 * 100) / 100 : 0,
        avg_win_price: row.total_wins > 0 ? row.total_revenue / row.total_wins : 0
      }));
      stats.endpoint_breakdown = processedEndpointData;

    } else if (groupBy === 'country') {
      // Country breakdown
      const countryResult = await clickhouse.query({
        query: `
          SELECT
            country,
            COUNT(*) as total_requests,
            SUM(CASE WHEN publisher_revenue > 0 THEN 1 ELSE 0 END) as total_wins,
            SUM(publisher_revenue) as total_revenue
          FROM impressions
          WHERE source_type = 3
            AND partner_endpoint_id = {partnerId:UInt32}
            AND toDate(timestamp) >= {dateFrom:String}
            AND toDate(timestamp) <= {dateTo:String}
          GROUP BY country
          ORDER BY total_requests DESC
        `,
        query_params: { partnerId, dateFrom, dateTo },
      });

      const countryData = await countryResult.json();
      // Calculate avg win price for country breakdown
      const processedCountryData = (countryData.data || []).map((row: any) => ({
        ...row,
        avg_win_price: row.total_wins > 0 ? row.total_revenue / row.total_wins : 0
      }));
      stats.country_breakdown = processedCountryData;

    } else if (groupBy === 'os') {
      // OS breakdown
      const osResult = await clickhouse.query({
        query: `
          SELECT
            os,
            COUNT(*) as total_requests,
            SUM(CASE WHEN publisher_revenue > 0 THEN 1 ELSE 0 END) as total_wins,
            SUM(publisher_revenue) as total_revenue
          FROM impressions
          WHERE source_type = 3
            AND partner_endpoint_id = {partnerId:UInt32}
            AND toDate(timestamp) >= {dateFrom:String}
            AND toDate(timestamp) <= {dateTo:String}
          GROUP BY os
          ORDER BY total_requests DESC
        `,
        query_params: { partnerId, dateFrom, dateTo },
      });

      const osData = await osResult.json();
      // Calculate avg win price for OS breakdown
      const processedOSData = (osData.data || []).map((row: any) => ({
        ...row,
        avg_win_price: row.total_wins > 0 ? row.total_revenue / row.total_wins : 0
      }));
      stats.os_breakdown = processedOSData;

    } else if (groupBy === 'browser') {
      // Browser breakdown
      const browserResult = await clickhouse.query({
        query: `
          SELECT
            browser,
            COUNT(*) as total_requests,
            SUM(CASE WHEN publisher_revenue > 0 THEN 1 ELSE 0 END) as total_wins,
            SUM(publisher_revenue) as total_revenue
          FROM impressions
          WHERE source_type = 3
            AND partner_endpoint_id = {partnerId:UInt32}
            AND toDate(timestamp) >= {dateFrom:String}
            AND toDate(timestamp) <= {dateTo:String}
          GROUP BY browser
          ORDER BY total_requests DESC
        `,
        query_params: { partnerId, dateFrom, dateTo },
      });

      const browserData = await browserResult.json();
      // Calculate avg win price for browser breakdown
      const processedBrowserData = (browserData.data || []).map((row: any) => ({
        ...row,
        avg_win_price: row.total_wins > 0 ? row.total_revenue / row.total_wins : 0
      }));
      stats.browser_breakdown = processedBrowserData;

    } else if (groupBy === 'device') {
      // Device breakdown
      const deviceResult = await clickhouse.query({
        query: `
          SELECT
            device_type,
            COUNT(*) as total_requests,
            SUM(CASE WHEN publisher_revenue > 0 THEN 1 ELSE 0 END) as total_wins,
            SUM(publisher_revenue) as total_revenue
          FROM impressions
          WHERE source_type = 3
            AND partner_endpoint_id = {partnerId:UInt32}
            AND toDate(timestamp) >= {dateFrom:String}
            AND toDate(timestamp) <= {dateTo:String}
          GROUP BY device_type
          ORDER BY total_requests DESC
        `,
        query_params: { partnerId, dateFrom, dateTo },
      });

      const deviceData = await deviceResult.json();
      // Calculate avg win price for device breakdown
      const processedDeviceData = (deviceData.data || []).map((row: any) => ({
        ...row,
        avg_win_price: row.total_wins > 0 ? row.total_revenue / row.total_wins : 0
      }));
      stats.device_breakdown = processedDeviceData;
    }

    // Calculate totals from the filtered breakdown data
    let totals = {
      total_requests: 0,
      total_wins: 0,
      total_revenue: 0,
      avg_win_rate: 0
    };

    // Get totals from the appropriate breakdown data
    if (stats.daily_breakdown && stats.daily_breakdown.length > 0) {
      totals.total_requests = stats.daily_breakdown.reduce((sum: number, row: any) => sum + (parseInt(row.total_requests) || 0), 0);
      totals.total_wins = stats.daily_breakdown.reduce((sum: number, row: any) => sum + (parseInt(row.total_wins) || 0), 0);
      totals.total_revenue = stats.daily_breakdown.reduce((sum: number, row: any) => sum + (parseFloat(row.total_revenue) || 0), 0);
    } else if (stats[`${groupBy}_breakdown`] && stats[`${groupBy}_breakdown`].length > 0) {
      const breakdownData = stats[`${groupBy}_breakdown`];
      totals.total_requests = breakdownData.reduce((sum: number, row: any) => sum + (parseInt(row.total_requests) || 0), 0);
      totals.total_wins = breakdownData.reduce((sum: number, row: any) => sum + (parseInt(row.total_wins) || 0), 0);
      totals.total_revenue = breakdownData.reduce((sum: number, row: any) => sum + (parseFloat(row.total_revenue) || 0), 0);
    }

    // Calculate win rate from the totals
    totals.avg_win_rate = totals.total_requests > 0 ? Math.round((totals.total_wins / totals.total_requests) * 100 * 100) / 100 : 0;

    return NextResponse.json({
      success: true,
      data: {
        totals,
        ...stats
      }
    });

  } catch (error) {
    console.error('SSP statistics error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
