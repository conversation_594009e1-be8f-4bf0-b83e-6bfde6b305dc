import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { CostProcessor } from '@/lib/cost-processor';
import { RequestTracker } from '@/lib/request-tracker';
import deviceGeoDetector from '@/lib/device-geo-detector';

/**
 * Track popup impression when popup is actually triggered/opened
 * This is called when user clicks and popup opens, not when JS tag loads
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { impression_id, campaign_id, dsp_partner_id, zone_id, website_id, bid_price, publisher_cpm, source_type } = body;

    if (!impression_id || !zone_id || !website_id || !bid_price) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Get client information
    const userAgent = request.headers.get('user-agent') || '';
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               '127.0.0.1';

    // Get device and geo info
    const deviceGeoInfo = await deviceGeoDetector.detectDeviceAndGeo(ip, userAgent);

    console.log(`🎯 Popup triggered: Processing impression ${impression_id} for ${source_type === 'local' ? 'campaign' : 'DSP partner'} ${campaign_id || dsp_partner_id}`);

    // Determine win type and IDs
    let campaignId = 0;
    let winnerId = 0;
    let winType: 'publisher_direct' | 'dsp_rtb' = 'publisher_direct';

    if (source_type === 'local' && campaign_id) {
      campaignId = campaign_id;
      winnerId = campaign_id; // For local campaigns, winner ID is campaign ID
      winType = 'publisher_direct';
    } else if (source_type === 'dsp' && dsp_partner_id) {
      campaignId = 0; // DSP wins don't have local campaign IDs
      winnerId = dsp_partner_id;
      winType = 'dsp_rtb';
    }

    // Process cost deduction and publisher credit
    const costResult = await CostProcessor.processImpressionCost({
      campaignId: campaignId,
      websiteId: website_id,
      zoneId: zone_id,
      impressionId: impression_id,
      bidType: 'cpm',
      bidAmount: bid_price,
      publisherCpm: publisher_cpm,
      dspPartnerId: dsp_partner_id || undefined,
    });

    if (!costResult.success) {
      console.error('Failed to process popup impression cost:', costResult.error);
      return NextResponse.json(
        { error: 'Failed to process impression cost' },
        { status: 500 }
      );
    }

    // Track impression
    const { SmartImpressionTracker } = await import('@/lib/smart-impression-tracker');
    await SmartImpressionTracker.trackImpression({
      impressionId: impression_id,
      campaignId: campaignId,
      dspPartnerId: dsp_partner_id || 0,
      websiteId: website_id,
      zoneId: zone_id,
      userAgent: userAgent,
      ipAddress: ip,
      country: deviceGeoInfo.country,
      region: deviceGeoInfo.region || 'Unknown',
      city: deviceGeoInfo.city || 'Unknown',
      deviceType: deviceGeoInfo.device_type,
      os: deviceGeoInfo.os,
      browser: deviceGeoInfo.browser,
      costDeducted: costResult.costDeducted || 0,
      publisherRevenue: costResult.publisherRevenue || 0,
      sourceType: source_type,
    });

    // Track win
    await RequestTracker.trackWin({
      winType: winType,
      campaignId: campaignId,
      winnerId: winnerId,
      supplierId: website_id,
      websiteId: website_id,
      zoneId: zone_id,
      partnerEndpointId: dsp_partner_id || 0,
      winPrice: bid_price / 1000, // Convert CPM to cost per impression
      platformRevenue: (costResult.costDeducted || 0) - (costResult.publisherRevenue || 0),
      supplierRevenue: costResult.publisherRevenue || 0,
    });

    console.log(`✅ Popup impression tracked: $${costResult.costDeducted} deducted, $${costResult.publisherRevenue} credited to publisher`);

    return NextResponse.json({
      success: true,
      impression_id: impression_id,
      cost_deducted: costResult.costDeducted,
      publisher_revenue: costResult.publisherRevenue,
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Popup impression tracking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
