import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import FraudDetection from '@/lib/fraud-detection';
import { clickHouseBatchWriter } from '@/lib/clickhouse-batch-writer';
import { Campaign } from '@/lib/auction-service';
import { CostProcessor } from '@/lib/cost-processor';

interface Impression {
  id: number;
  campaign_id: number;
  dsp_partner_id?: number;
  ssp_partner_id?: number;
  website_id: number;
  zone_id: number;
  user_agent: string;
  ip_address: string;
  country: string;
  region: string;
  city: string;
  device_type: string;
  os: string;
  browser: string;
  source_type: number;
  page_url?: string;
  os_version?: string;
  user_lang?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const impressionId = searchParams.get('impression_id');
    const campaignId = searchParams.get('campaign_id');
    const redirectUrlFromQuery = searchParams.get('redirect_url');
    const trackOnly = searchParams.get('track_only') === '1';

    console.log('Click handler - Received:', { impressionId, campaignId, trackOnly });

    if (!impressionId || !campaignId) {
      console.log('Click handler - Missing parameters');
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Get impression details
    const impressionResult = await clickhouse.query({
      query: 'SELECT * FROM impressions WHERE id = {impressionId:UInt64}',
      query_params: { impressionId: parseInt(impressionId) },
    });

    const impressions: { data: Impression[] } = await impressionResult.json();
    console.log('Click handler - Found impressions:', impressions.data.length);

    if (impressions.data.length === 0) {
      console.log('Click handler - No impression found for ID:', impressionId);
      return NextResponse.redirect(new URL('/', request.url));
    }

    const impression: Impression = impressions.data[0];

    // Get campaign details
    const campaignResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns: { data: Campaign[] } = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    const campaign: Campaign = campaigns.data[0];

    // Get client information
    const userAgent = request.headers.get('user-agent') || '';
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               '127.0.0.1';

    // Fraud detection RE-ENABLED
    const fraudCheck = await FraudDetection.checkClick(ip, userAgent, parseInt(campaignId), impression.zone_id);
    if (!fraudCheck.isValid) {
      console.log(`Blocked click: ${fraudCheck.reason} (Risk: ${fraudCheck.riskScore})`);
      // Still redirect but don't log the click or charge
      return NextResponse.redirect(campaign.landing_url);
    }

    // Generate click ID
    const clickId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Log click
    await clickHouseBatchWriter.write('clicks', {
        id: clickId,
        impression_id: parseInt(impressionId),
        campaign_id: parseInt(campaignId),
        partner_endpoint_id: impression.partner_endpoint_id || 0, // Track partner endpoint ID from impression
        website_id: impression.website_id,
        zone_id: impression.zone_id,
        user_agent: userAgent,
        ip_address: ip,
        country: impression.country,
      state: impression.region, // Use region for compatibility
        city: impression.city,
        device_type: impression.device_type,
        os: impression.os,
        browser: impression.browser,
        source_type: impression.source_type || 1, // Copy source type from impression (local=1, dsp=2)
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
    });

    // Note: Cost processing for DSP wins is handled in /api/serve (for direct publisher requests)
    // or /api/ssp/win (for SSP requests). Click tracking only records the click event.
    // This prevents double cost processing.

    const isDspWin = impression.partner_endpoint_id && impression.partner_endpoint_id > 0 && parseInt(campaignId) === 0;

    if (isDspWin) {
      console.log(`DSP win click tracked for partner ${impression.partner_endpoint_id} - Cost already processed in serve/ssp-win`);
    }

    // If this is a tracking-only request (for popup ads), return success without redirecting
    if (trackOnly) {
      console.log('Click handler - Track only request, returning success');
      return NextResponse.json({ success: true, click_id: clickId });
    }

    // Determine the landing URL
    let landingUrl = redirectUrlFromQuery || campaign.landing_url;
    console.log('Click handler - Initial landing URL (from query or campaign):', landingUrl);

    // Use fallback if no landing URL is found
    if (!landingUrl || landingUrl.trim() === '') {
      landingUrl = `https://www.google.com/search?q=test+ad+campaign+${campaignId}`;
      console.log('Click handler - Using fallback URL:', landingUrl);
    }

    // Replace macros in landing URL
    console.log('Click handler - Landing URL before macro replacement:', landingUrl);
    const macros = {
      '{click_id}': clickId.toString(),
      '{campaign_id}': campaignId,
      '{zone_id}': impression.zone_id.toString(),
      '{website_id}': impression.website_id.toString(),
      '{country}': impression.country,
      '{region}': impression.region,
      '{city}': impression.city,
      '{device_type}': impression.device_type,
      '{os}': impression.os,
      '{browser}': impression.browser,
      '{ip_address}': ip,
      '{timestamp}': Date.now().toString(),
      '{user_agent}': encodeURIComponent(userAgent),
      '{user_lang}': impression.user_lang || 'en',
      '{ad_format}': campaign.type || 'banner',
      '{page_url}': encodeURIComponent(impression.page_url || ''),
      '{os_version}': impression.os_version || 'Unknown',
    };

    // Replace all macros
    Object.entries(macros).forEach(([macro, value]) => {
      landingUrl = landingUrl.replace(new RegExp(macro.replace(/[{}]/g, '\\$&'), 'g'), value);
    });

    // Redirect to landing page with macros replaced
    console.log('Click handler - Final redirect URL:', landingUrl);
    return NextResponse.redirect(landingUrl);

  } catch (error) {
    console.error('Click tracking error:', error);
    return NextResponse.redirect(new URL('/', request.url));
  }
}
