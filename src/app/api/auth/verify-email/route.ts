import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { EmailService } from '@/lib/email-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { message: 'Verification token is required' },
        { status: 400 }
      );
    }

    // Find user by verification token
    const userResult = await clickhouse.query({
      query: 'SELECT * FROM users WHERE email_verification_token = {token:String} AND email_verified = 0',
      query_params: { token },
    });

    const userData = await userResult.json();
    if (userData.data.length === 0) {
      return NextResponse.json(
        { message: 'Invalid or expired verification token' },
        { status: 400 }
      );
    }

    const user = userData.data[0];

    // Update user as verified
    await clickhouse.command({
      query: `
        ALTER TABLE users 
        UPDATE 
          email_verified = 1,
          email_verification_token = '',
          status = 'active',
          updated_at = now()
        WHERE id = ${user.id}
      `,
    });

    // Send welcome email based on user type
    await EmailService.sendWelcomeEmail(user);

    // Redirect to success page
    return NextResponse.redirect(new URL('/auth/verification-success', request.url));

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    // Find user by email
    const userResult = await clickhouse.query({
      query: 'SELECT * FROM users WHERE email = {email:String}',
      query_params: { email },
    });

    const userData = await userResult.json();
    if (userData.data.length === 0) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    const user = userData.data[0];

    if (user.email_verified === 1) {
      return NextResponse.json(
        { message: 'Email is already verified' },
        { status: 400 }
      );
    }

    // Generate new verification token
    const verificationToken = Math.random().toString(36).substring(2) + Date.now().toString(36);

    // Update user with new token
    await clickhouse.command({
      query: `
        ALTER TABLE users 
        UPDATE 
          email_verification_token = '${verificationToken}',
          updated_at = now()
        WHERE id = ${user.id}
      `,
    });

    // Send verification email
    await EmailService.sendVerificationEmail({
      ...user,
      email_verification_token: verificationToken
    });

    return NextResponse.json({
      message: 'Verification email sent successfully'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
