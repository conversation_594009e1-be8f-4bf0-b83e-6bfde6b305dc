import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const userResult = await clickhouse.query({
      query: 'SELECT id, email FROM users WHERE email = {email:String}',
      query_params: { email },
    });

    const users = await userResult.json();
    
    // Always return success to prevent email enumeration
    if (users.data.length === 0) {
      return NextResponse.json({
        message: 'If an account with that email exists, we have sent password reset instructions.',
      });
    }

    const user = users.data[0];

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour

    // Store reset token (in a real app, you'd store this in database)
    // For demo purposes, we'll just log it
    console.log(`Password reset token for ${email}: ${resetToken}`);
    console.log(`Reset link: ${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`);

    // In a real application, you would:
    // 1. Store the reset token in the database with expiry
    // 2. Send an email with the reset link
    // 3. Use a proper email service like SendGrid, AWS SES, etc.

    return NextResponse.json({
      message: 'If an account with that email exists, we have sent password reset instructions.',
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
