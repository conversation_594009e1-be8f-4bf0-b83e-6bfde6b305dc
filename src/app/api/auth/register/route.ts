import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { EmailService } from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      fullName,
      email,
      address,
      city,
      zip,
      state,
      country,
      password,
      role,
    } = body;

    // Validate required fields
    if (!fullName || !email || !address || !city || !zip || !state || !country || !password || !role) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUserResult = await clickhouse.query({
      query: 'SELECT id FROM users WHERE email = {email:String}',
      query_params: { email },
    });

    const existingUsers = await existingUserResult.json();
    if (existingUsers.data.length > 0) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Get the next user ID based on role
    let nextId: number;
    if (role === 'admin') {
      nextId = parseInt(process.env.ADMIN_ID_START || '1');
    } else {
      nextId = parseInt(process.env.USER_ID_START || '10340');
    }

    // Get the highest existing ID for this role type
    const maxIdResult = await clickhouse.query({
      query: role === 'admin'
        ? 'SELECT MAX(id) as max_id FROM users WHERE role = \'admin\''
        : 'SELECT MAX(id) as max_id FROM users WHERE role != \'admin\'',
    });

    const maxIdData = await maxIdResult.json();
    if (maxIdData.data.length > 0 && maxIdData.data[0].max_id) {
      nextId = Math.max(nextId, maxIdData.data[0].max_id + 1);
    }

    // Check if email verification is needed (only for advertiser and publisher)
    const needsVerification = ['advertiser', 'publisher'].includes(role);

    let verificationToken = '';
    let userStatus = 'active';
    let emailVerified = 1;

    if (needsVerification) {
      verificationToken = Math.random().toString(36).substring(2) + Date.now().toString(36);
      userStatus = 'pending_verification';
      emailVerified = 0;
    }

    // Insert new user
    await clickhouse.insert({
      table: 'users',
      values: [{
        id: nextId,
        email,
        password,
        full_name: fullName,
        address,
        city,
        zip,
        state,
        country,
        role,
        status: userStatus,
        balance: 0,
        email_verified: emailVerified,
        email_verification_token: verificationToken,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Send verification email only if needed
    if (needsVerification) {
      try {
        await EmailService.sendVerificationEmail({
          id: nextId,
          email,
          full_name: fullName,
          role,
          email_verification_token: verificationToken
        });
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // Don't fail registration if email fails
      }

      return NextResponse.json(
        {
          message: 'Registration successful! Please check your email to verify your account before signing in.',
          userId: nextId,
          requiresVerification: true
        },
        { status: 201 }
      );
    } else {
      // For admin users, send welcome email directly
      try {
        await EmailService.sendWelcomeEmail({
          id: nextId,
          email,
          full_name: fullName,
          role
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
      }

      return NextResponse.json(
        {
          message: 'Registration successful! You can now sign in.',
          userId: nextId,
          requiresVerification: false
        },
        { status: 201 }
      );
    }
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
