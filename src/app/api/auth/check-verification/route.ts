import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    // Check user verification status
    const userResult = await clickhouse.query({
      query: 'SELECT email_verified, role, full_name FROM users WHERE email = {email:String}',
      query_params: { email },
    });

    const userData = await userResult.json();
    if (userData.data.length === 0) {
      return NextResponse.json(
        { exists: false },
        { status: 200 }
      );
    }

    const user = userData.data[0];
    
    // Check if this user type needs verification
    const needsVerification = ['advertiser', 'publisher'].includes(user.role);
    
    return NextResponse.json({
      exists: true,
      needsVerification,
      isVerified: user.email_verified === 1,
      role: user.role,
      name: user.full_name
    });

  } catch (error) {
    console.error('Check verification error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
