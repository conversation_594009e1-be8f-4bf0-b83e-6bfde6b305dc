import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    if (isNaN(campaignId)) {
      return NextResponse.json(
        { message: 'Invalid campaign ID' },
        { status: 400 }
      );
    }

    // Check if campaign exists and belongs to the user
    const campaignResult = await clickhouse.query({
      query: `
        SELECT id, user_id, status, name
        FROM campaigns 
        WHERE id = {campaignId:UInt32} AND user_id = {userId:UInt32}
      `,
      query_params: { 
        campaignId,
        userId: parseInt(session.user.id)
      },
    });

    const campaignData = await campaignResult.json();
    if (campaignData.data.length === 0) {
      return NextResponse.json(
        { message: 'Campaign not found' },
        { status: 404 }
      );
    }

    const campaign = campaignData.data[0];

    // Check if campaign is rejected
    if (campaign.status !== 'rejected') {
      return NextResponse.json(
        { message: 'Only rejected campaigns can be resubmitted' },
        { status: 400 }
      );
    }

    // Update campaign status to pending
    await clickhouse.command({
      query: `
        ALTER TABLE campaigns 
        UPDATE 
          status = 'pending',
          updated_at = {updatedAt:String}
        WHERE id = {campaignId:UInt32}
      `,
      query_params: {
        campaignId,
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
      },
    });

    // Log the resubmission action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'campaign_resubmit',
        target_id: campaignId,
        target_type: 'campaign',
        reason: `Campaign "${campaign.name}" resubmitted for review by advertiser`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Campaign resubmitted for review successfully',
      campaign_id: campaignId,
    });

  } catch (error) {
    console.error('Campaign resubmit error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
