import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import { uploadService } from '@/lib/upload';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    const campaignResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32} AND user_id = {userId:UInt32}',
      query_params: {
        campaignId,
        userId: parseInt(session.user.id)
      },
    });

    const campaignData = await campaignResult.json();
    if (!campaignData.data.length) {
      return NextResponse.json(
        { success: false, message: 'Campaign not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      campaign: campaignData.data[0]
    });

  } catch (error) {
    console.error('Error fetching campaign:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch campaign' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);
    const body = await request.json();
    const { status } = body;

    // Validate status - ClickHouse enum: active=1, paused=2, pending=3, rejected=4, archived=5
    const validStatuses = ['active', 'paused', 'archived'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { message: 'Invalid status' },
        { status: 400 }
      );
    }

    // Check if campaign belongs to user
    const campaignResult = await clickhouse.query({
      query: 'SELECT user_id FROM campaigns WHERE id = {campaignId:UInt32}',
      query_params: { campaignId },
    });

    const campaignData = await campaignResult.json();
    if (!campaignData.data.length || campaignData.data[0].user_id !== parseInt(session.user.id)) {
      return NextResponse.json(
        { message: 'Campaign not found or unauthorized' },
        { status: 404 }
      );
    }

    // For ClickHouse, we'll use a different approach
    // Since ClickHouse doesn't support traditional UPDATE, we'll use mutations
    try {
      await clickhouse.command({
        query: `ALTER TABLE campaigns UPDATE status = '${status}', updated_at = now() WHERE id = ${campaignId}`,
      });
    } catch (updateError) {
      console.log('Direct update failed, trying alternative approach:', updateError);

      // Alternative: Get full campaign data, delete, and reinsert
      const fullCampaignResult = await clickhouse.query({
        query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32}',
        query_params: { campaignId },
      });

      const fullCampaignData = await fullCampaignResult.json();
      const campaign = fullCampaignData.data[0];

      // Delete the old record
      await clickhouse.command({
        query: `ALTER TABLE campaigns DELETE WHERE id = ${campaignId}`,
      });

      // Wait a moment for the delete to process
      await new Promise(resolve => setTimeout(resolve, 100));

      // Insert updated record
      await clickhouse.insert({
        table: 'campaigns',
        values: [{
          ...campaign,
          status: status,
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Campaign updated successfully'
    });

  } catch (error) {
    console.error('Error updating campaign:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update campaign' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'advertiser') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    // Check if campaign belongs to user
    const campaignResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32} AND user_id = {userId:UInt32}',
      query_params: {
        campaignId,
        userId: parseInt(session.user.id)
      },
    });

    const campaignData = await campaignResult.json();
    if (!campaignData.data.length) {
      return NextResponse.json(
        { message: 'Campaign not found or unauthorized' },
        { status: 404 }
      );
    }

    const existingCampaign = campaignData.data[0];

    // Parse form data
    const formData = await request.formData();

    // Extract form fields
    const name = formData.get('name') as string;
    const cpmBid = formData.get('cpmBid') as string;
    const dailyBudget = formData.get('dailyBudget') as string;
    const totalBudget = formData.get('totalBudget') as string;
    const startDate = formData.get('startDate') as string;
    const endDate = formData.get('endDate') as string;
    const landingUrl = formData.get('landingUrl') as string;
    const jsTag = formData.get('jsTag') as string;
    const creativeType = formData.get('creativeType') as string;

    // Native ad fields
    const nativeTitle = formData.get('nativeTitle') as string;
    const nativeDescription = formData.get('nativeDescription') as string;

    // In-page push fields
    const pushTitle = formData.get('pushTitle') as string;
    const pushDescription = formData.get('pushDescription') as string;

    // Image files
    const bannerImage = formData.get('bannerImage') as File | null;
    const nativeIcon = formData.get('nativeIcon') as File | null;
    const nativeImage = formData.get('nativeImage') as File | null;
    const pushImage = formData.get('pushImage') as File | null;

    // Targeting fields
    const targetingCountries = JSON.parse(formData.get('targetingCountries') as string || '[]');
    const targetingStates = JSON.parse(formData.get('targetingStates') as string || '[]');
    const targetingDevices = JSON.parse(formData.get('targetingDevices') as string || '[]');
    const targetingOs = JSON.parse(formData.get('targetingOs') as string || '[]');
    const targetingBrowsers = JSON.parse(formData.get('targetingBrowsers') as string || '[]');
    const targetingConnectionTypes = JSON.parse(formData.get('targetingConnectionTypes') as string || '[]');

    // Schedule fields
    const dailySchedule = JSON.parse(formData.get('dailySchedule') as string || '[]');
    const hourlySchedule = JSON.parse(formData.get('hourlySchedule') as string || '[]');

    // Frequency cap fields
    const frequencyCapValue = formData.get('frequencyCapValue') as string;
    const frequencyCapPeriod = formData.get('frequencyCapPeriod') as string;

    // Whitelist/Blacklist fields
    const whitelistPublishers = formData.get('whitelistPublishers') as string;
    const whitelistWebsites = formData.get('whitelistWebsites') as string;
    const whitelistZones = formData.get('whitelistZones') as string;
    const blacklistPublishers = formData.get('blacklistPublishers') as string;
    const blacklistWebsites = formData.get('blacklistWebsites') as string;
    const blacklistZones = formData.get('blacklistZones') as string;

    // Basic validation
    if (!name?.trim()) {
      return NextResponse.json({ message: 'Campaign name is required' }, { status: 400 });
    }

    if (!cpmBid || parseFloat(cpmBid) <= 0) {
      return NextResponse.json({ message: 'Valid CPM bid is required' }, { status: 400 });
    }

    if (!startDate) {
      return NextResponse.json({ message: 'Start date is required' }, { status: 400 });
    }

    // Handle image uploads and deletions
    let bannerImageUrl = existingCampaign.banner_image_url || '';
    let nativeIconUrl = existingCampaign.native_icon_url || '';
    let nativeImageUrl = existingCampaign.native_image_url || '';
    let pushImageUrl = existingCampaign.push_image_url || '';

    // Track old images for deletion
    const imagesToDelete: string[] = [];

    // Upload new images if provided
    const imageFiles: any = {};
    if (bannerImage && typeof bannerImage === 'object' && bannerImage.size > 0) {
      imageFiles.bannerImage = bannerImage;
      if (existingCampaign.banner_image_url) {
        imagesToDelete.push(existingCampaign.banner_image_url);
      }
    }
    if (nativeIcon && typeof nativeIcon === 'object' && nativeIcon.size > 0) {
      imageFiles.nativeIcon = nativeIcon;
      if (existingCampaign.native_icon_url) {
        imagesToDelete.push(existingCampaign.native_icon_url);
      }
    }
    if (nativeImage && typeof nativeImage === 'object' && nativeImage.size > 0) {
      imageFiles.nativeImage = nativeImage;
      if (existingCampaign.native_image_url) {
        imagesToDelete.push(existingCampaign.native_image_url);
      }
    }
    if (pushImage && typeof pushImage === 'object' && pushImage.size > 0) {
      imageFiles.pushImage = pushImage;
      if (existingCampaign.push_image_url) {
        imagesToDelete.push(existingCampaign.push_image_url);
      }
    }

    // Upload new images
    if (Object.keys(imageFiles).length > 0) {
      const uploadResults = await uploadService.uploadCampaignImages(imageFiles, campaignId);
      if (uploadResults.bannerImageUrl) bannerImageUrl = uploadResults.bannerImageUrl;
      if (uploadResults.nativeIconUrl) nativeIconUrl = uploadResults.nativeIconUrl;
      if (uploadResults.nativeImageUrl) nativeImageUrl = uploadResults.nativeImageUrl;
      if (uploadResults.pushImageUrl) pushImageUrl = uploadResults.pushImageUrl;
    }

    // Convert frequency cap period to enum value
    const frequencyCapPeriodEnum = frequencyCapPeriod === 'hour' ? 1 :
                                   frequencyCapPeriod === 'week' ? 3 : 2; // default to day

    // Delete the old record
    await clickhouse.command({
      query: `ALTER TABLE campaigns DELETE WHERE id = ${campaignId}`,
    });

    // Wait a moment for the delete to process
    await new Promise(resolve => setTimeout(resolve, 100));

    // Determine new status - if campaign was rejected, resubmit for review
    const newStatus = existingCampaign.status === 'rejected' ? 'pending' : existingCampaign.status;

    // Insert updated record with preserved ad format fields
    await clickhouse.insert({
      table: 'campaigns',
      values: [{
        id: campaignId,
        user_id: parseInt(session.user.id),
        name,
        type: existingCampaign.type, // Preserve original type
        status: newStatus, // Resubmit rejected campaigns for review
        cpm_bid: parseFloat(cpmBid),
        daily_budget: dailyBudget ? parseFloat(dailyBudget) : 0,
        total_budget: totalBudget ? parseFloat(totalBudget) : 0,

        // Preserve original creative fields (cannot be changed)
        banner_size: existingCampaign.banner_size,
        creative_type: creativeType,

        // Editable creative fields
        banner_image_url: bannerImageUrl,
        landing_url: landingUrl || existingCampaign.landing_url,
        js_tag: jsTag || existingCampaign.js_tag,
        native_title: nativeTitle || existingCampaign.native_title,
        native_description: nativeDescription || existingCampaign.native_description,
        native_icon_url: nativeIconUrl,
        native_image_url: nativeImageUrl,
        push_title: pushTitle || existingCampaign.push_title,
        push_description: pushDescription || existingCampaign.push_description,
        push_image_url: pushImageUrl,

        // Targeting fields
        targeting_countries: targetingCountries,
        targeting_states: targetingStates,
        targeting_devices: targetingDevices,
        targeting_os: targetingOs,
        targeting_browsers: targetingBrowsers,
        targeting_connection_types: targetingConnectionTypes,

        // Schedule fields
        start_date: new Date(startDate).toISOString().slice(0, 19).replace('T', ' '),
        end_date: endDate ? new Date(endDate).toISOString().slice(0, 19).replace('T', ' ') : null,
        daily_schedule: dailySchedule,
        hourly_schedule: hourlySchedule,

        // Frequency cap fields
        frequency_cap_value: parseInt(frequencyCapValue) || 0,
        frequency_cap_period: frequencyCapPeriodEnum,

        // Whitelist/Blacklist fields
        whitelist_publishers: whitelistPublishers || '',
        whitelist_websites: whitelistWebsites || '',
        whitelist_zones: whitelistZones || '',
        blacklist_publishers: blacklistPublishers || '',
        blacklist_websites: blacklistWebsites || '',
        blacklist_zones: blacklistZones || '',

        created_at: existingCampaign.created_at, // Preserve original
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Delete old images after successful database update
    if (imagesToDelete.length > 0) {
      await uploadService.deleteCampaignImages(imagesToDelete);
    }

    // Log automatic resubmission if campaign was rejected
    if (existingCampaign.status === 'rejected') {
      await clickhouse.insert({
        table: 'admin_actions',
        values: [{
          id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
          admin_id: parseInt(session.user.id),
          action_type: 'campaign_auto_resubmit',
          target_id: campaignId,
          target_type: 'campaign',
          reason: `Campaign "${name}" automatically resubmitted for review after edit`,
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });
    }

    return NextResponse.json({
      success: true,
      message: existingCampaign.status === 'rejected'
        ? 'Campaign updated and resubmitted for review successfully'
        : 'Campaign updated successfully'
    });

  } catch (error) {
    console.error('Error updating campaign:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update campaign' },
      { status: 500 }
    );
  }
}