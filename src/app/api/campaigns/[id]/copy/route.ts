import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    // Get original campaign
    const campaignResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32} AND user_id = {userId:UInt32}',
      query_params: {
        campaignId,
        userId: parseInt(session.user.id)
      },
    });

    const campaignData = await campaignResult.json();
    if (!campaignData.data.length) {
      return NextResponse.json(
        { message: 'Campaign not found or unauthorized' },
        { status: 404 }
      );
    }

    const originalCampaign = campaignData.data[0];

    // Generate new campaign ID using proper counter
    const campaignIdStart = parseInt(process.env.CAMPAIGN_ID_START || '171288');

    const maxIdResult = await clickhouse.query({
      query: 'SELECT MAX(id) as max_id FROM campaigns',
    });

    const maxIdData = await maxIdResult.json();
    console.log('Max ID query result:', maxIdData);

    let newCampaignId = campaignIdStart;
    if (maxIdData.data && maxIdData.data.length > 0 && maxIdData.data[0].max_id !== null) {
      newCampaignId = Math.max(campaignIdStart, parseInt(maxIdData.data[0].max_id) + 1);
    }

    console.log('Generated new campaign ID:', newCampaignId);

    // Create copy with modified name and pending status
    const newCampaign = {
      ...originalCampaign,
      id: newCampaignId,
      name: `${originalCampaign.name} (Copy)`,
      status: 'pending',
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
    };

    // Insert the copied campaign
    await clickhouse.insert({
      table: 'campaigns',
      values: [newCampaign],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      success: true,
      message: 'Campaign copied successfully',
      campaign: newCampaign
    });

  } catch (error) {
    console.error('Error copying campaign:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to copy campaign' },
      { status: 500 }
    );
  }
}
