import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import client from '@/lib/clickhouse';

interface PostbackConfig {
  id?: string;
  campaign_id: string;
  postback_url: string;
  method: 'GET' | 'POST';
  parameters: { [key: string]: string };
  is_active: boolean;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: campaignId } = await params;

    // Verify campaign belongs to user
    const campaignResult = await client.query({
      query: 'SELECT id, user_id FROM campaigns WHERE id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    const campaign = campaigns.data[0];
    if (campaign.user_id !== parseInt(session.user.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get postback configuration
    const postbackResult = await client.query({
      query: 'SELECT * FROM postback_configs WHERE campaign_id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const postbacks = await postbackResult.json();
    if (postbacks.data.length === 0) {
      return NextResponse.json({ error: 'Postback configuration not found' }, { status: 404 });
    }

    const config = postbacks.data[0];

    // Parse parameters JSON
    let parameters = {};
    try {
      parameters = config.parameters ? JSON.parse(config.parameters) : {};
    } catch (error) {
      console.error('Failed to parse postback parameters:', error);
    }

    return NextResponse.json({
      id: config.id,
      campaign_id: config.campaign_id,
      postback_url: config.postback_url,
      method: config.method,
      parameters,
      is_active: config.is_active,
    });

  } catch (error) {
    console.error('Failed to get postback configuration:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: campaignId } = await params;
    const body = await request.json();

    // Verify campaign belongs to user
    const campaignResult = await client.query({
      query: 'SELECT id, user_id FROM campaigns WHERE id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    const campaign = campaigns.data[0];
    if (campaign.user_id !== parseInt(session.user.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Validate required fields
    if (!body.postback_url || !body.method) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate URL format
    try {
      new URL(body.postback_url);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid postback URL format' }, { status: 400 });
    }

    // Check if configuration already exists
    const existingResult = await client.query({
      query: 'SELECT id FROM postback_configs WHERE campaign_id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const existing = await existingResult.json();
    if (existing.data.length > 0) {
      return NextResponse.json({ error: 'Postback configuration already exists. Use PUT to update.' }, { status: 409 });
    }

    // Generate configuration ID
    const configId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Insert postback configuration
    await client.insert({
      table: 'postback_configs',
      values: [{
        id: configId,
        campaign_id: parseInt(campaignId),
        postback_url: body.postback_url,
        method: body.method,
        parameters: JSON.stringify(body.parameters || {}),
        is_active: body.is_active !== false,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      id: configId,
      campaign_id: parseInt(campaignId),
      postback_url: body.postback_url,
      method: body.method,
      parameters: body.parameters || {},
      is_active: body.is_active !== false,
      message: 'Postback configuration created successfully',
    });

  } catch (error) {
    console.error('Failed to create postback configuration:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: campaignId } = await params;
    const body = await request.json();

    // Verify campaign belongs to user
    const campaignResult = await client.query({
      query: 'SELECT id, user_id FROM campaigns WHERE id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    const campaign = campaigns.data[0];
    if (campaign.user_id !== parseInt(session.user.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Validate required fields
    if (!body.postback_url || !body.method) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate URL format
    try {
      new URL(body.postback_url);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid postback URL format' }, { status: 400 });
    }

    // Check if configuration exists
    const existingResult = await client.query({
      query: 'SELECT id FROM postback_configs WHERE campaign_id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const existing = await existingResult.json();
    if (existing.data.length === 0) {
      return NextResponse.json({ error: 'Postback configuration not found' }, { status: 404 });
    }

    const configId = existing.data[0].id;

    // Update postback configuration
    await client.command({
      query: `
        ALTER TABLE postback_configs
        UPDATE
          postback_url = {postback_url:String},
          method = {method:String},
          parameters = {parameters:String},
          is_active = {is_active:UInt8},
          updated_at = {updated_at:String}
        WHERE id = {configId:UInt64}
      `,
      query_params: {
        configId: parseInt(configId),
        postback_url: body.postback_url,
        method: body.method,
        parameters: JSON.stringify(body.parameters || {}),
        is_active: body.is_active !== false ? 1 : 0,
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      },
    });

    return NextResponse.json({
      id: configId,
      campaign_id: parseInt(campaignId),
      postback_url: body.postback_url,
      method: body.method,
      parameters: body.parameters || {},
      is_active: body.is_active !== false,
      message: 'Postback configuration updated successfully',
    });

  } catch (error) {
    console.error('Failed to update postback configuration:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: campaignId } = await params;

    // Verify campaign belongs to user
    const campaignResult = await client.query({
      query: 'SELECT id, user_id FROM campaigns WHERE id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    const campaigns = await campaignResult.json();
    if (campaigns.data.length === 0) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
    }

    const campaign = campaigns.data[0];
    if (campaign.user_id !== parseInt(session.user.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Delete postback configuration
    await client.command({
      query: 'ALTER TABLE postback_configs DELETE WHERE campaign_id = {campaignId:UInt64}',
      query_params: { campaignId: parseInt(campaignId) },
    });

    return NextResponse.json({
      message: 'Postback configuration deleted successfully',
    });

  } catch (error) {
    console.error('Failed to delete postback configuration:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
