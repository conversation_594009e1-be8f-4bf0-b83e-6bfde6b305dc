import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    // First verify the campaign belongs to the user (unless admin)
    if (session.user.role !== 'admin') {
      const campaignResult = await clickhouse.query({
        query: 'SELECT user_id FROM campaigns WHERE id = {campaignId:UInt32}',
        query_params: { campaignId },
      });

      const campaignData = await campaignResult.json();
      if (!campaignData.data.length || campaignData.data[0].user_id !== parseInt(session.user.id)) {
        return NextResponse.json(
          { message: 'Campaign not found or unauthorized' },
          { status: 404 }
        );
      }
    }

    // Get the most recent rejection reason for this campaign
    const rejectionResult = await clickhouse.query({
      query: `
        SELECT
          aa.reason,
          aa.timestamp,
          u.full_name as admin_name
        FROM admin_actions aa
        LEFT JOIN users u ON aa.admin_id = u.id
        WHERE aa.target_id = {campaignId:UInt32}
          AND aa.target_type = 'campaign'
          AND aa.action_type = 'campaign_reject'
          AND aa.reason != ''
        ORDER BY aa.timestamp DESC
        LIMIT 1
      `,
      query_params: { campaignId },
    });

    // Also get screenshot from ad monitoring if available
    const screenshotResult = await clickhouse.query({
      query: `
        SELECT screenshot_url, issues_found, region, checked_at
        FROM ad_monitoring
        WHERE campaign_id = {campaignId:UInt32}
          AND status = 'flagged'
          AND screenshot_url != ''
        ORDER BY checked_at DESC
        LIMIT 1
      `,
      query_params: { campaignId },
    });

    const rejectionData = await rejectionResult.json();
    const screenshotData = await screenshotResult.json();

    if (rejectionData.data.length === 0) {
      return NextResponse.json(
        { message: 'No rejection reason found' },
        { status: 404 }
      );
    }

    const rejection = rejectionData.data[0];
    const screenshot = screenshotData.data.length > 0 ? screenshotData.data[0] : null;

    return NextResponse.json({
      reason: rejection.reason,
      timestamp: rejection.timestamp,
      admin_name: rejection.admin_name,
      screenshot: screenshot ? {
        url: screenshot.screenshot_url,
        region: screenshot.region,
        issues_found: JSON.parse(screenshot.issues_found || '[]'),
        checked_at: screenshot.checked_at,
      } : null,
    });

  } catch (error) {
    console.error('Error fetching rejection reason:', error);
    return NextResponse.json(
      { message: 'Failed to fetch rejection reason' },
      { status: 500 }
    );
  }
}
