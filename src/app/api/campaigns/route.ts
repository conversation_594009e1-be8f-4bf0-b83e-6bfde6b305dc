import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import { uploadService } from '@/lib/upload';

// Helper function to get creative type value based on campaign type and creative type
function getCreativeTypeValue(campaignType: string, creativeType: string): number {
  switch (campaignType) {
    case 'banner':
      return creativeType === 'js' ? 2 : 1; // js = 2, image = 1
    case 'native':
      return 3; // native = 3
    case 'in_page_push':
      return 4; // push = 4
    case 'popup':
      return 5; // popup = 5
    default:
      return 1; // default to image
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const userId = parseInt(session.user.id);

    // Get campaigns first
    const campaignsResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE user_id = {userId:UInt32} ORDER BY created_at DESC',
      query_params: { userId },
    });

    const campaignsData = await campaignsResult.json();
    const campaigns = campaignsData.data || [];

    // Add real statistics to each campaign
    for (const campaign of campaigns) {
      const statsResult = await clickhouse.query({
        query: `
          SELECT
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.cost_deducted) as spent
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          WHERE i.campaign_id = {campaignId:UInt32}
        `,
        query_params: { campaignId: campaign.id },
      });

      const statsData = await statsResult.json();
      const stats = statsData.data[0] || {};

      // Add statistics to campaign object
      campaign.impressions = parseInt(stats.impressions) || 0;
      campaign.clicks = parseInt(stats.clicks) || 0;
      campaign.conversions = parseInt(stats.conversions) || 0;
      campaign.spent = parseFloat(stats.spent) || 0;
      campaign.budget = campaign.total_budget || campaign.daily_budget || 0;
    }

    return NextResponse.json({
      success: true,
      campaigns
    });

  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch campaigns' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Handle both JSON and FormData
    let body: any;
    const contentType = request.headers.get('content-type');

    if (contentType?.includes('multipart/form-data')) {
      const formData = await request.formData();
      body = {};

      // Convert FormData to object
      for (const [key, value] of formData.entries()) {
        if (typeof value === 'object' && value.constructor.name === 'File') {
          // Handle file uploads here if needed
          body[key] = value;
        } else if (key.includes('targeting') || key.includes('Schedule')) {
          // Parse JSON arrays
          try {
            body[key] = JSON.parse(value as string);
          } catch {
            body[key] = [];
          }
        } else {
          // Handle all other fields as strings
          body[key] = value as string;
        }
      }
    } else {
      body = await request.json();
    }

    const {
      name,
      type,
      cpmBid,
      dailyBudget,
      totalBudget,

      // Creative fields
      bannerSize = '',
      creativeType = 'image',
      landingUrl = '',
      jsTag = '',
      imageFile,

      // Native ad fields
      nativeTitle = '',
      nativeDescription = '',
      nativeIconFile,
      nativeImageFile,

      // In-page push fields
      pushTitle = '',
      pushDescription = '',
      pushImageFile,

      // Targeting fields
      targetingCountries = [],
      targetingStates = [],
      targetingDevices = [],
      targetingOs = [],
      targetingBrowsers = [],
      targetingConnectionTypes = [],

      // Schedule fields
      startDate,
      endDate,
      dailySchedule = [],
      hourlySchedule = [],

      // Frequency cap fields
      frequencyCapValue = 0,
      frequencyCapPeriod = 'day',

      // Whitelist/Blacklist fields
      whitelistPublishers = '',
      whitelistWebsites = '',
      whitelistZones = '',
      blacklistPublishers = '',
      blacklistWebsites = '',
      blacklistZones = '',
    } = body;



    // Validate required fields
    if (!name || !type || !cpmBid || !startDate) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate daily budget if provided (minimum $30)
    if (dailyBudget) {
      const dailyBudgetValue = parseFloat(dailyBudget);
      if (isNaN(dailyBudgetValue) || dailyBudgetValue <= 0) {
        return NextResponse.json(
          { message: 'Daily budget must be a valid positive number' },
          { status: 400 }
        );
      }
      if (dailyBudgetValue < 30) {
        return NextResponse.json(
          { message: 'Daily budget must be at least $30.00' },
          { status: 400 }
        );
      }
    }

    // Validate total budget if provided
    if (totalBudget) {
      const totalBudgetValue = parseFloat(totalBudget);
      if (isNaN(totalBudgetValue) || totalBudgetValue <= 0) {
        return NextResponse.json(
          { message: 'Total budget must be a valid positive number' },
          { status: 400 }
        );
      }
    }

    // Validate minimum CPM bid
    const bidValue = parseFloat(cpmBid);
    if (isNaN(bidValue) || bidValue <= 0) {
      return NextResponse.json(
        { message: 'CPM bid must be a valid positive number' },
        { status: 400 }
      );
    }

    // Fetch minimum bid setting for this campaign type
    const minBidResult = await clickhouse.query({
      query: `SELECT setting_value FROM platform_settings WHERE setting_key = {key:String}`,
      query_params: { key: `minimum_cpm_${type}` },
    });

    const minBidData = await minBidResult.json();
    let minimumBid = 0;

    if (minBidData.data.length > 0) {
      minimumBid = parseFloat(minBidData.data[0].setting_value);
    } else {
      // Default minimum bids if not found in settings
      const defaultMinimums: Record<string, number> = {
        banner: 0.50,
        native: 0.75,
        in_page_push: 0.25,
        popup: 0.10,
      };
      minimumBid = defaultMinimums[type] || 0.10;
    }

    if (bidValue < minimumBid) {
      return NextResponse.json(
        { message: `CPM bid must be at least $${minimumBid.toFixed(2)} for ${type} campaigns` },
        { status: 400 }
      );
    }

    // Type-specific validation
    if (type === 'banner') {
      if (!bannerSize || !creativeType) {
        return NextResponse.json(
          { message: 'Banner campaigns require banner size and creative type' },
          { status: 400 }
        );
      }
      if (creativeType === 'image' && !landingUrl) {
        return NextResponse.json(
          { message: 'Image banners require landing URL' },
          { status: 400 }
        );
      }
      if (creativeType === 'js' && !jsTag) {
        return NextResponse.json(
          { message: 'JS banners require JS tag' },
          { status: 400 }
        );
      }
    }

    if (type === 'native') {
      if (!nativeTitle || !nativeDescription || !landingUrl) {
        return NextResponse.json(
          { message: 'Native campaigns require title, description, and landing URL' },
          { status: 400 }
        );
      }
    }

    if (type === 'in_page_push') {
      if (!pushTitle || !pushDescription || !landingUrl) {
        return NextResponse.json(
          { message: 'In-page push campaigns require title, description, and landing URL' },
          { status: 400 }
        );
      }
    }

    if (type === 'popup') {
      if (!landingUrl) {
        return NextResponse.json(
          { message: 'Popup campaigns require landing URL' },
          { status: 400 }
        );
      }
    }

    // Handle file uploads for images
    let bannerImageUrl = '';
    let nativeIconUrl = '';
    let nativeImageUrl = '';
    let pushImageUrl = '';

    // Get the next campaign ID first (needed for upload paths)
    const campaignIdStart = parseInt(process.env.CAMPAIGN_ID_START || '171288');

    const maxIdResult = await clickhouse.query({
      query: 'SELECT MAX(id) as max_id FROM campaigns',
    });

    const maxIdData = await maxIdResult.json();
    console.log('Max ID query result for campaign creation:', maxIdData);

    let nextId = campaignIdStart;
    if (maxIdData.data && maxIdData.data.length > 0 && maxIdData.data[0].max_id !== null) {
      nextId = Math.max(campaignIdStart, parseInt(maxIdData.data[0].max_id) + 1);
    }

    console.log('Generated campaign ID:', nextId);

    // Upload images if provided
    const imageFiles: any = {};
    if (body.bannerImage && typeof body.bannerImage === 'object' && body.bannerImage.size > 0) {
      imageFiles.bannerImage = body.bannerImage;
    }
    if (body.nativeIcon && typeof body.nativeIcon === 'object' && body.nativeIcon.size > 0) {
      imageFiles.nativeIcon = body.nativeIcon;
    }
    if (body.nativeImage && typeof body.nativeImage === 'object' && body.nativeImage.size > 0) {
      imageFiles.nativeImage = body.nativeImage;
    }
    if (body.pushImage && typeof body.pushImage === 'object' && body.pushImage.size > 0) {
      imageFiles.pushImage = body.pushImage;
    }

    if (Object.keys(imageFiles).length > 0) {
      const uploadResults = await uploadService.uploadCampaignImages(imageFiles, nextId);
      bannerImageUrl = uploadResults.bannerImageUrl || '';
      nativeIconUrl = uploadResults.nativeIconUrl || '';
      nativeImageUrl = uploadResults.nativeImageUrl || '';
      pushImageUrl = uploadResults.pushImageUrl || '';
    }

    // Convert frequency cap period to enum value
    const frequencyCapPeriodEnum = frequencyCapPeriod === 'hour' ? 1 :
                                   frequencyCapPeriod === 'week' ? 3 : 2; // default to day

    // Insert new campaign
    await clickhouse.insert({
      table: 'campaigns',
      values: [{
        id: nextId,
        user_id: parseInt(session.user.id),
        name,
        type,
        status: 'pending',
        cpm_bid: parseFloat(cpmBid),
        daily_budget: dailyBudget ? parseFloat(dailyBudget) : 0,
        total_budget: totalBudget ? parseFloat(totalBudget) : 0,

        // Creative fields
        banner_size: bannerSize,
        creative_type: getCreativeTypeValue(type, creativeType), // Proper creative type mapping
        banner_image_url: bannerImageUrl,
        landing_url: landingUrl,
        js_tag: jsTag,

        // Native ad fields
        native_title: nativeTitle,
        native_description: nativeDescription,
        native_icon_url: nativeIconUrl,
        native_image_url: nativeImageUrl,

        // In-page push fields
        push_title: pushTitle,
        push_description: pushDescription,
        push_image_url: pushImageUrl,

        // Targeting fields
        targeting_countries: targetingCountries,
        targeting_states: targetingStates,
        targeting_devices: targetingDevices,
        targeting_os: targetingOs,
        targeting_browsers: targetingBrowsers,
        targeting_connection_types: targetingConnectionTypes,

        // Schedule fields
        start_date: new Date(startDate).toISOString().slice(0, 19).replace('T', ' '),
        end_date: endDate ? new Date(endDate).toISOString().slice(0, 19).replace('T', ' ') : null,
        daily_schedule: dailySchedule,
        hourly_schedule: hourlySchedule,

        // Frequency cap fields
        frequency_cap_value: parseInt(frequencyCapValue) || 0,
        frequency_cap_period: frequencyCapPeriodEnum,

        // Whitelist/Blacklist fields (stored as strings)
        whitelist_publishers: whitelistPublishers || '',
        whitelist_websites: whitelistWebsites || '',
        whitelist_zones: whitelistZones || '',
        blacklist_publishers: blacklistPublishers || '',
        blacklist_websites: blacklistWebsites || '',
        blacklist_zones: blacklistZones || '',

        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json(
      { message: 'Campaign created successfully', campaignId: nextId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Campaign creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}


