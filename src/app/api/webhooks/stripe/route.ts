import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import stripe from '@/lib/stripe';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = headers().get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { message: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    let event;

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { message: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;

        if (session.payment_status === 'paid') {
          const transactionId = session.metadata?.transaction_id;
          const userId = session.metadata?.user_id;
          const amount = session.amount_total ? session.amount_total / 100 : 0;

          if (transactionId && userId) {
            // Update transaction status
            await clickhouse.command({
              query: `
                ALTER TABLE transactions
                UPDATE status = 'completed',
                       payment_reference = {paymentId:String},
                       updated_at = {updatedAt:String}
                WHERE id = {transactionId:UInt64}
              `,
              query_params: {
                paymentId: session.id,
                updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
                transactionId: parseInt(transactionId),
              },
            });

            // Update user balance
            await clickhouse.command({
              query: `
                ALTER TABLE users
                UPDATE balance = balance + {amount:Decimal(10,2)},
                       updated_at = {updatedAt:String}
                WHERE id = {userId:UInt32}
              `,
              query_params: {
                amount,
                updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
                userId: parseInt(userId),
              },
            });

            console.log(`Payment completed for transaction ${transactionId}, amount: $${amount}`);
          }
        }
        break;

      case 'checkout.session.expired':
        const expiredSession = event.data.object;
        const expiredTransactionId = expiredSession.metadata?.transaction_id;

        if (expiredTransactionId) {
          // Update transaction status to failed
          await clickhouse.command({
            query: `
              ALTER TABLE transactions
              UPDATE status = 'failed',
                     updated_at = {updatedAt:String}
              WHERE id = {transactionId:UInt64}
            `,
            query_params: {
              updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
              transactionId: parseInt(expiredTransactionId),
            },
          });
        }
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Stripe webhook error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
