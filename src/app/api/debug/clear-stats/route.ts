import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    console.log('Clearing all statistics data...');

    // Clear impressions table
    await clickhouse.command({
      query: 'TRUNCATE TABLE impressions'
    });

    // Clear clicks table
    await clickhouse.command({
      query: 'TRUNCATE TABLE clicks'
    });

    // Clear conversions table
    await clickhouse.command({
      query: 'TRUNCATE TABLE conversions'
    });

    console.log('All statistics data cleared successfully');

    return NextResponse.json({ 
      message: 'All statistics data cleared successfully',
      cleared: ['impressions', 'clicks', 'conversions']
    });

  } catch (error) {
    console.error('Error clearing statistics data:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
