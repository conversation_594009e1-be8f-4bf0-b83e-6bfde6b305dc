import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    // Fetch minimum deposit settings from platform_settings
    const result = await clickhouse.query({
      query: `
        SELECT setting_key, setting_value
        FROM platform_settings
        WHERE setting_key IN ('minimum_deposit_stripe', 'minimum_deposit_paypal')
        ORDER BY setting_key
      `,
    });

    const settingsData = await result.json();

    // Convert to object format with default values
    const minimumDeposits = {
      stripe: '100',
      paypal: '100',
    };

    // Update with actual values from database
    settingsData.data.forEach((row: any) => {
      switch (row.setting_key) {
        case 'minimum_deposit_stripe':
          minimumDeposits.stripe = row.setting_value;
          break;
        case 'minimum_deposit_paypal':
          minimumDeposits.paypal = row.setting_value;
          break;
      }
    });

    return NextResponse.json({
      success: true,
      data: minimumDeposits
    });

  } catch (error) {
    console.error('Minimum deposits fetch error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch minimum deposit settings',
        data: {
          stripe: '100',
          paypal: '100',
        }
      },
      { status: 500 }
    );
  }
}
