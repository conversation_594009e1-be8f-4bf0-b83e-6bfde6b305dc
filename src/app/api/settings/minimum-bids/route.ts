import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    // Fetch minimum CPM bid settings from platform_settings
    const result = await clickhouse.query({
      query: `
        SELECT setting_key, setting_value
        FROM platform_settings
        WHERE setting_key IN ('minimum_cpm_banner', 'minimum_cpm_native', 'minimum_cpm_in_page_push', 'minimum_cpm_popup')
        ORDER BY setting_key
      `,
    });

    const settingsData = await result.json();

    // Convert to object format with default values
    const minimumBids = {
      banner: '0.50',
      native: '0.75',
      in_page_push: '0.25',
      popup: '0.10',
    };

    // Update with actual values from database
    settingsData.data.forEach((row: any) => {
      switch (row.setting_key) {
        case 'minimum_cpm_banner':
          minimumBids.banner = row.setting_value;
          break;
        case 'minimum_cpm_native':
          minimumBids.native = row.setting_value;
          break;
        case 'minimum_cpm_in_page_push':
          minimumBids.in_page_push = row.setting_value;
          break;
        case 'minimum_cpm_popup':
          minimumBids.popup = row.setting_value;
          break;
      }
    });

    return NextResponse.json({
      success: true,
      data: minimumBids
    });

  } catch (error) {
    console.error('Minimum bids fetch error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch minimum bid settings',
        data: {
          banner: '0.50',
          native: '0.75',
          in_page_push: '0.25',
          popup: '0.10',
        }
      },
      { status: 500 }
    );
  }
}
