import { NextRequest, NextResponse } from 'next/server';
import clickhouse from '@/lib/clickhouse';
import { clickHouseBatchWriter } from '@/lib/clickhouse-batch-writer';

interface Click {
  id: number;
  campaign_id: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clickId = searchParams.get('click_id');
    const conversionValue = searchParams.get('value') || '0';
    const conversionType = searchParams.get('type') || 'sale';

    if (!clickId) {
      return new NextResponse('Missing click_id parameter', { status: 400 });
    }

    // Verify click exists and get campaign info
    const clickResult = await clickhouse.query({
      query: 'SELECT id, campaign_id FROM clicks WHERE id = {clickId:UInt64}',
      query_params: { clickId: parseInt(clickId) },
    });

    const clicks: { data: Click[] } = await clickResult.json();
    if (clicks.data.length === 0) {
      return new NextResponse('Click not found', { status: 404 });
    }

    const click: Click = clicks.data[0];

    // Check if conversion already exists for this click
    const existingResult = await clickhouse.query({
      query: 'SELECT id FROM conversions WHERE click_id = {clickId:UInt64}',
      query_params: { clickId: parseInt(clickId) },
    });

    const existing = await existingResult.json();
    if (existing.data.length > 0) {
      // Return 1x1 transparent pixel for duplicate conversions
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );

      return new NextResponse(pixel, {
        headers: {
          'Content-Type': 'image/png',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      });
    }

    // Generate conversion ID
    const conversionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Log conversion
    await clickHouseBatchWriter.write('conversions', {
        id: conversionId,
        click_id: parseInt(clickId),
        campaign_id: click.campaign_id,
        conversion_value: parseFloat(conversionValue),
        conversion_type: conversionType,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
    });

    // Return 1x1 transparent pixel
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    return new NextResponse(pixel, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Conversion tracking error:', error);
    return new NextResponse('Error', {
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }
}

export async function POST(request: NextRequest) {
  // Handle POST requests for conversion tracking
  return GET(request);
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
