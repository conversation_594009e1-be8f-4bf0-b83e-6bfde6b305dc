import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const websiteId = parseInt(id);
    if (isNaN(websiteId)) {
      return NextResponse.json(
        { message: 'Invalid website ID' },
        { status: 400 }
      );
    }

    const { action, reason } = await request.json();

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { message: 'Invalid action. Must be approve or reject' },
        { status: 400 }
      );
    }

    // Update website status
    const newStatus = action === 'approve' ? 'approved' : 'rejected';

    await clickhouse.command({
      query: `
        ALTER TABLE websites
        UPDATE status = {status:String},
               updated_at = {updatedAt:String}
        WHERE id = {websiteId:UInt32}
      `,
      query_params: {
        status: newStatus,
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        websiteId,
      },
    });

    // Log the approval/rejection
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: `website_${action}`,
        target_id: websiteId,
        target_type: 'website',
        reason: reason || '',
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: `Website ${action}d successfully`,
      status: newStatus,
    });

  } catch (error) {
    console.error('Website approval error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
