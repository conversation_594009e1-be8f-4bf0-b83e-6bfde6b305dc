import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT
          w.id,
          w.name,
          w.url,
          w.category,
          w.status,
          w.verification_method,
          w.verification_status,
          w.created_at,
          w.updated_at,
          u.full_name as user_name,
          u.email as user_email
        FROM websites w
        LEFT JOIN users u ON w.user_id = u.id
        ORDER BY w.created_at DESC
      `,
    });

    const websites = await result.json();

    console.log('Admin websites query result:', websites);
    console.log('Number of websites found:', websites.data?.length || 0);

    return NextResponse.json(websites.data);

  } catch (error) {
    console.error('Admin websites fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
