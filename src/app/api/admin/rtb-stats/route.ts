import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import { RequestTracker } from '@/lib/request-tracker';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'today'; // today, week, month

    let dateFilter = '';
    switch (period) {
      case 'week':
        dateFilter = 'date >= today() - 7';
        break;
      case 'month':
        dateFilter = 'date >= today() - 30';
        break;
      default:
        dateFilter = 'date = today()';
    }

    // Get aggregated stats using unified tracker
    const stats = await RequestTracker.getStats(period);

    // Get hourly breakdown for today
    const hourlyStats = await RequestTracker.getHourlyStats();

    // Get top partners by volume
    const partnersResult = await clickhouse.query({
      query: `
        SELECT
          rs.partner_id,
          rs.source_type,
          CASE
            WHEN rs.source_type = 'publisher_direct' THEN w.name
            WHEN rs.source_type IN ('dsp_inbound', 'ssp_inventory') THEN pe.name
            ELSE 'Unknown'
          END as partner_name,
          sum(rs.total_requests) as total_requests,
          sum(rs.total_wins) as total_wins,
          round(sum(rs.total_wins) / sum(rs.total_requests) * 100, 2) as win_rate
        FROM request_stats rs
        LEFT JOIN websites w ON rs.partner_id = w.id AND rs.source_type = 'publisher_direct'
        LEFT JOIN partner_endpoints pe ON rs.partner_id = pe.id AND rs.source_type IN ('dsp_inbound', 'ssp_inventory')
        WHERE ${dateFilter}
        GROUP BY rs.partner_id, rs.source_type, partner_name
        HAVING total_requests > 0
        ORDER BY total_requests DESC
        LIMIT 20
      `,
    });

    const partners = await partnersResult.json();

    // Get SSP inventory stats
    const sspInventoryResult = await clickhouse.query({
      query: `
        SELECT
          COUNT(*) as total_inventory,
          COUNT(CASE WHEN status = 'available' THEN 1 END) as available_inventory,
          COUNT(CASE WHEN status = 'sold' THEN 1 END) as sold_inventory,
          COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_inventory,
          AVG(floor_price) as avg_floor_price
        FROM ssp_inventory
        WHERE created_at >= today()
      `,
    });

    const sspInventory = await sspInventoryResult.json();

    // Get recent wins from unified win notifications
    const winsResult = await clickhouse.query({
      query: `
        SELECT
          wn.win_type,
          wn.supplier_id,
          wn.winner_id,
          pe.name as supplier_name,
          COUNT(*) as win_count,
          sum(wn.win_price) as total_revenue,
          sum(wn.platform_revenue) as platform_revenue,
          sum(wn.supplier_revenue) as supplier_revenue
        FROM win_notifications wn
        LEFT JOIN partner_endpoints pe ON wn.supplier_id = pe.user_id
        WHERE wn.timestamp >= today()
        GROUP BY wn.win_type, wn.supplier_id, wn.winner_id, pe.name
        ORDER BY total_revenue DESC
        LIMIT 10
      `,
    });

    const wins = await winsResult.json();

    return NextResponse.json({
      period,
      overview: stats || [],
      hourly: hourlyStats || [],
      top_partners: partners.data || [],
      ssp_inventory: sspInventory.data[0] || {},
      recent_wins: wins.data || [],
    });

  } catch (error) {
    console.error('RTB stats fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
