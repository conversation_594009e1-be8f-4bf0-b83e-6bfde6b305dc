import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const country = searchParams.get('country');
    const state = searchParams.get('state');
    const region = searchParams.get('region'); // Keep for backward compatibility

    let whereConditions = ['1=1'];
    let queryParams: any = {};

    if (status) {
      whereConditions.push('am.status = {status:String}');
      queryParams.status = status;
    }

    if (country) {
      whereConditions.push('am.country = {country:String}');
      queryParams.country = country;
    }

    if (state) {
      whereConditions.push('am.state = {state:String}');
      queryParams.state = state;
    }

    // Backward compatibility: if region is provided, use it as state
    if (region && !state) {
      whereConditions.push('am.state = {region:String}');
      queryParams.region = region;
    }

    const whereClause = whereConditions.join(' AND ');

    const result = await clickhouse.query({
      query: `
        SELECT
          am.id,
          am.campaign_id,
          c.name as campaign_name,
          u.full_name as advertiser_name,
          am.country,
          am.state,
          am.city,
          am.status,
          am.issues_found,
          am.screenshot_url,
          am.landing_url,
          am.checked_at,
          am.created_at
        FROM ad_monitoring am
        LEFT JOIN campaigns c ON am.campaign_id = c.id
        LEFT JOIN users u ON c.user_id = u.id
        WHERE ${whereClause}
        ORDER BY am.created_at DESC
        LIMIT 1000
      `,
      query_params: queryParams,
    });

    const monitoring = await result.json();
    const formattedResults = monitoring.data.map((item: any) => ({
      ...item,
      issues_found: JSON.parse(item.issues_found || '[]'),
    }));

    return NextResponse.json(formattedResults);

  } catch (error) {
    console.error('Ad monitoring fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
