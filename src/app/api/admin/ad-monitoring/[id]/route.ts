import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import { EmailService } from '@/lib/email-service';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action } = await request.json();
    const resultId = parseInt(params.id);

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { message: 'Invalid action' },
        { status: 400 }
      );
    }

    // Get the monitoring result details with issues
    const resultQuery = await clickhouse.query({
      query: 'SELECT campaign_id, status, issues_found FROM ad_monitoring WHERE id = {resultId:UInt64}',
      query_params: { resultId },
    });

    const resultData = await resultQuery.json();
    if (resultData.data.length === 0) {
      return NextResponse.json(
        { message: 'Monitoring result not found' },
        { status: 404 }
      );
    }

    const monitoringResult = resultData.data[0];
    const campaignId = monitoringResult.campaign_id;
    const issues = JSON.parse(monitoringResult.issues_found || '[]');

    const newStatus = action === 'approve' ? 'approved' : 'rejected';

    // Update monitoring result status
    await clickhouse.command({
      query: `
        ALTER TABLE ad_monitoring
        UPDATE status = {status:String}
        WHERE id = {resultId:UInt64}
      `,
      query_params: {
        status: newStatus,
        resultId,
      },
    });

    // Update campaign status based on action
    const campaignStatus = action === 'approve' ? 'active' : 'rejected';
    let rejectionReason = null;

    if (action === 'reject') {
      rejectionReason = `Rejected by admin after ad monitoring review. Issues found: ${issues.join(', ')}`;

      // Update campaign with rejection reason
      await clickhouse.command({
        query: `
          ALTER TABLE campaigns
          UPDATE
            status = {status:String},
            rejection_reason = {rejectionReason:String}
          WHERE id = {campaignId:UInt32}
        `,
        query_params: {
          status: campaignStatus,
          campaignId,
          rejectionReason,
        },
      });
    } else {
      // Just update status for approval
      await clickhouse.command({
        query: `
          ALTER TABLE campaigns
          UPDATE status = {status:String}
          WHERE id = {campaignId:UInt32}
        `,
        query_params: {
          status: campaignStatus,
          campaignId,
        },
      });
    }

    // Send email notification
    try {
      await EmailService.sendCampaignStatusNotification(
        campaignId,
        campaignStatus,
        rejectionReason
      );
    } catch (emailError) {
      console.error('Failed to send campaign status email:', emailError);
    }

    // Log the admin action (use standard action types so they show up in rejection reasons)
    const actionType = action === 'approve' ? 'campaign_approve' : 'campaign_reject';
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: actionType,
        target_id: campaignId,
        target_type: 'campaign',
        reason: rejectionReason || `Approved campaign after ad monitoring review`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: `Monitoring result ${action}d successfully`,
    });

  } catch (error) {
    console.error('Monitoring result action error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
