import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import { EmailService } from '@/lib/email-service';
import { ScreenshotService } from '@/lib/screenshot-service';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get excluded advertisers from settings
    const excludedResult = await clickhouse.query({
      query: 'SELECT setting_value FROM platform_settings WHERE setting_key = \'monitoring_excluded_advertisers\'',
    });

    let excludedAdvertisers: number[] = [];
    const excludedData = await excludedResult.json();
    if (excludedData.data.length > 0) {
      try {
        excludedAdvertisers = JSON.parse(excludedData.data[0].setting_value || '[]');
      } catch (error) {
        console.error('Error parsing excluded advertisers:', error);
        excludedAdvertisers = [];
      }
    }

    console.log('Excluded advertisers from monitoring:', excludedAdvertisers);

    // Get active campaigns (excluding excluded advertisers)
    let campaignQuery = `
      SELECT
        c.id,
        c.user_id,
        c.name,
        c.type,
        c.creative_type,
        c.landing_url,
        c.js_tag,
        u.full_name as advertiser_name
      FROM campaigns c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.status = 'active'
      AND c.start_date <= now()
      AND (c.end_date IS NULL OR c.end_date >= now())
    `;

    let queryParams = {};

    // Add exclusion filter if there are excluded advertisers
    if (excludedAdvertisers.length > 0) {
      const placeholders = excludedAdvertisers.map((_, index) => `{excluded${index}:UInt32}`).join(', ');
      campaignQuery += ` AND c.user_id NOT IN (${placeholders})`;

      excludedAdvertisers.forEach((advertiserId, index) => {
        queryParams[`excluded${index}`] = advertiserId;
      });
    }

    campaignQuery += ' LIMIT 100';

    const campaignsResult = await clickhouse.query({
      query: campaignQuery,
      query_params: queryParams,
    });

    const campaigns = await campaignsResult.json();

    if (campaigns.data.length === 0) {
      return NextResponse.json({
        message: 'No active campaigns to monitor',
        scanned: 0,
      });
    }

    const regions = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'NO', 'DK'];
    const prohibitedKeywords = [
      // Adult/Pornographic content
      'porn', 'xxx', 'nude', 'naked', 'erotic', 'sexual', 'adult content',
      // Illegal drugs
      'cocaine', 'heroin', 'methamphetamine', 'ecstasy', 'lsd', 'illegal drugs',
      // Weapons/Violence
      'guns for sale', 'buy weapons', 'firearms', 'explosives', 'bomb making',
      // Illegal activities
      'money laundering', 'fake documents', 'identity theft', 'credit card fraud',
      'hacking services', 'illegal downloads', 'pirated software'
    ];

    let scannedCount = 0;

    // Simulate monitoring for each campaign in each region
    for (const campaign of campaigns.data) {
      for (const region of regions) {
        const issues = await scanCampaign(campaign, region, prohibitedKeywords);
        const status = issues.length > 0 ? 'flagged' : 'clean';
        let screenshotUrl = '';

        // Take screenshot only if issues are found and campaign has a landing URL
        // JS tag campaigns don't have landing URLs, so skip screenshots for them
        if (status === 'flagged') {
          if (campaign.creative_type === 'js') {
            console.log(`⚠️ Skipping screenshot for JS tag campaign ${campaign.id} (no landing URL)`);
          } else if (campaign.landing_url && campaign.landing_url.trim()) {
            console.log(`Taking screenshot for flagged campaign ${campaign.id} in ${region}...`);
            screenshotUrl = await ScreenshotService.takeScreenshot(
              campaign.landing_url,
              campaign.id,
              region
            ) || '';

            if (screenshotUrl) {
              console.log(`✅ Screenshot taken: ${screenshotUrl}`);
            } else {
              console.log(`❌ Screenshot failed for campaign ${campaign.id}`);
            }
          } else {
            console.log(`⚠️ Cannot take screenshot for campaign ${campaign.id}: missing landing URL`);
          }
        }

        // Insert monitoring result with geographic information
        // For now, use region as state and add placeholder country/city
        // TODO: Enhance to get actual geographic data based on campaign targeting
        await clickhouse.insert({
          table: 'ad_monitoring',
          values: [{
            id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
            campaign_id: campaign.id,
            country: 'US', // Default country - could be enhanced with campaign targeting data
            state: region, // Use region as state for now
            city: '', // Could be enhanced with more specific targeting
            status,
            issues_found: JSON.stringify(issues),
            screenshot_url: screenshotUrl,
            landing_url: campaign.landing_url,
            checked_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });

        scannedCount++;

        // If campaign is flagged, update campaign status with rejection reason
        if (status === 'flagged') {
          const rejectionReason = `Auto-rejected by ad monitoring: ${issues.join(', ')}`;

          // Update campaign status and rejection reason
          await clickhouse.command({
            query: `
              ALTER TABLE campaigns
              UPDATE
                status = 'rejected',
                rejection_reason = {rejectionReason:String}
              WHERE id = {campaignId:UInt32}
            `,
            query_params: {
              campaignId: campaign.id,
              rejectionReason
            },
          });

          // Send rejection email notification
          try {
            await EmailService.sendCampaignStatusNotification(
              campaign.id,
              'rejected',
              rejectionReason
            );
          } catch (emailError) {
            console.error('Failed to send rejection email:', emailError);
          }

          // Log admin action (use 'campaign_reject' so it shows up in rejection reasons)
          await clickhouse.insert({
            table: 'admin_actions',
            values: [{
              id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
              admin_id: parseInt(session.user.id),
              action_type: 'campaign_reject',
              target_id: campaign.id,
              target_type: 'campaign',
              reason: rejectionReason,
              timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
            }],
            format: 'JSONEachRow',
          });
        }
      }
    }

    return NextResponse.json({
      message: 'Ad monitoring scan completed',
      scanned: scannedCount,
      campaigns: campaigns.data.length,
      regions: regions.length,
    });

  } catch (error) {
    console.error('Ad monitoring scan error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function scanCampaign(campaign: any, region: string, prohibitedKeywords: string[]): Promise<string[]> {
  const issues: string[] = [];

  try {
    // Simulate content analysis
    const campaignName = campaign.name.toLowerCase();

    // Check for prohibited keywords in campaign name
    for (const keyword of prohibitedKeywords) {
      if (campaignName.includes(keyword)) {
        issues.push(`Prohibited keyword "${keyword}" found in campaign name`);
      }
    }

    // Handle different campaign types
    if (campaign.creative_type === 'js') {
      // For JS tag campaigns, analyze the JS tag content
      const jsTag = (campaign.js_tag || '').toLowerCase();

      if (!jsTag.trim()) {
        issues.push('Missing or empty JS tag content');
      } else {
        // Check JS tag for prohibited content (porn, illegal content only)
        if (jsTag.includes('porn') || jsTag.includes('sex') || jsTag.includes('nude') || jsTag.includes('xxx')) {
          issues.push('Adult/pornographic content detected in JS tag');
        }

        if (jsTag.includes('drug') || jsTag.includes('cocaine') || jsTag.includes('heroin') || jsTag.includes('marijuana')) {
          issues.push('Illegal drug content detected in JS tag');
        }

        if (jsTag.includes('gun') || jsTag.includes('weapon') || jsTag.includes('firearm')) {
          issues.push('Weapons/firearms content detected in JS tag');
        }

        if (jsTag.includes('eval(') || jsTag.includes('document.write(') || jsTag.includes('innerHTML')) {
          issues.push('Potentially malicious JavaScript code detected');
        }
      }
    } else {
      // For image campaigns, analyze the landing URL
      const landingUrl = (campaign.landing_url || '').toLowerCase();

      if (!landingUrl.trim()) {
        issues.push('Missing or empty landing URL');
      } else {
        // Check for redirect count (allow up to 3 redirects)
        const redirectCount = await checkRedirectCount(campaign.landing_url);
        if (redirectCount > 3) {
          issues.push(`Excessive redirects detected (${redirectCount} redirects, maximum 3 allowed)`);
        }

        // Check landing URL for prohibited content (porn, illegal content only)
        if (landingUrl.includes('porn') || landingUrl.includes('sex') || landingUrl.includes('nude') || landingUrl.includes('xxx')) {
          issues.push('Adult/pornographic content detected in landing URL');
        }

        if (landingUrl.includes('drug') || landingUrl.includes('cocaine') || landingUrl.includes('heroin') || landingUrl.includes('marijuana')) {
          issues.push('Illegal drug content detected in landing URL');
        }

        if (landingUrl.includes('gun') || landingUrl.includes('weapon') || landingUrl.includes('firearm')) {
          issues.push('Weapons/firearms content detected in landing URL');
        }
      }
    }

    // Region-specific checks for truly illegal content only
    // Note: Gambling, pharmacy, and marketing terms are now allowed platform-wide

    // Simulate random detection for demo purposes (reduced frequency and severity)
    if (Math.random() < 0.05) { // 5% chance of random issue (reduced from 10%)
      const randomIssues = [
        'Potential malware detected',
        'Copyright infringement suspected',
        'Suspicious domain reputation',
        'Possible phishing attempt detected',
      ];
      issues.push(randomIssues[Math.floor(Math.random() * randomIssues.length)]);
    }

  } catch (error) {
    console.error(`Error scanning campaign ${campaign.id} in ${region}:`, error);
    issues.push('Technical error during content analysis');
  }

  return issues;
}

// Function to check redirect count for a URL
async function checkRedirectCount(url: string): Promise<number> {
  try {
    let redirectCount = 0;
    let currentUrl = url;
    const maxRedirects = 10; // Safety limit to prevent infinite loops
    const visitedUrls = new Set<string>();

    while (redirectCount < maxRedirects) {
      // Prevent infinite loops
      if (visitedUrls.has(currentUrl)) {
        break;
      }
      visitedUrls.add(currentUrl);

      const response = await fetch(currentUrl, {
        method: 'HEAD',
        redirect: 'manual',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      // Check if it's a redirect response
      if (response.status >= 300 && response.status < 400) {
        const location = response.headers.get('location');
        if (location) {
          redirectCount++;
          // Handle relative URLs
          if (location.startsWith('/')) {
            const urlObj = new URL(currentUrl);
            currentUrl = `${urlObj.protocol}//${urlObj.host}${location}`;
          } else if (location.startsWith('http')) {
            currentUrl = location;
          } else {
            // Relative to current path
            const urlObj = new URL(currentUrl);
            currentUrl = new URL(location, urlObj.href).href;
          }
        } else {
          break;
        }
      } else {
        // Not a redirect, we're done
        break;
      }
    }

    return redirectCount;
  } catch (error) {
    console.error('Error checking redirects for URL:', url, error);
    // Return 0 on error to avoid false positives
    return 0;
  }
}
