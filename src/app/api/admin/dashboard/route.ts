import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get total users count
    const usersResult = await clickhouse.query({
      query: 'SELECT COUNT(*) as total FROM users',
    });
    const usersData = await usersResult.json();
    const totalUsers = usersData.data[0]?.total || 0;

    // Get active campaigns count
    const campaignsResult = await clickhouse.query({
      query: "SELECT COUNT(*) as total FROM campaigns WHERE status = 'active'",
    });
    const campaignsData = await campaignsResult.json();
    const activeCampaigns = campaignsData.data[0]?.total || 0;

    // Get active websites count
    const websitesResult = await clickhouse.query({
      query: "SELECT COUNT(*) as total FROM websites WHERE status = 'approved'",
    });
    const websitesData = await websitesResult.json();
    const activeWebsites = websitesData.data[0]?.total || 0;

    // Get platform revenue (advertiser spend - publisher revenue = platform commission)
    const revenueResult = await clickhouse.query({
      query: `
        SELECT
          SUM(cost_deducted) as total_spend,
          SUM(publisher_revenue) as total_publisher_revenue,
          SUM(cost_deducted) - SUM(publisher_revenue) as platform_revenue
        FROM impressions
        WHERE cost_deducted > 0
      `,
    });
    const revenueData = await revenueResult.json();
    const platformRevenue = revenueData.data[0]?.platform_revenue || 0;

    // Get recent activity (campaigns)
    const campaignActivityResult = await clickhouse.query({
      query: `
        SELECT
          'campaign' as type,
          c.name as title,
          c.status as status,
          c.created_at as timestamp,
          u.full_name as user_name
        FROM campaigns c
        LEFT JOIN users u ON c.user_id = u.id
        ORDER BY c.created_at DESC
        LIMIT 5
      `,
    });
    const campaignActivityData = await campaignActivityResult.json();
    const campaignActivity = campaignActivityData.data || [];

    // Get recent activity (websites)
    const websiteActivityResult = await clickhouse.query({
      query: `
        SELECT
          'website' as type,
          w.name as title,
          w.status as status,
          w.created_at as timestamp,
          u.full_name as user_name
        FROM websites w
        LEFT JOIN users u ON w.user_id = u.id
        ORDER BY w.created_at DESC
        LIMIT 5
      `,
    });
    const websiteActivityData = await websiteActivityResult.json();
    const websiteActivity = websiteActivityData.data || [];

    // Combine and sort activities
    const allActivity = [...campaignActivity, ...websiteActivity];
    const recentActivity = allActivity
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);

    // Get pending items counts
    const pendingCampaignsResult = await clickhouse.query({
      query: "SELECT COUNT(*) as total FROM campaigns WHERE status = 'pending'",
    });
    const pendingCampaignsData = await pendingCampaignsResult.json();
    const pendingCampaigns = pendingCampaignsData.data[0]?.total || 0;

    const pendingWebsitesResult = await clickhouse.query({
      query: "SELECT COUNT(*) as total FROM websites WHERE status = 'pending'",
    });
    const pendingWebsitesData = await pendingWebsitesResult.json();
    const pendingWebsites = pendingWebsitesData.data[0]?.total || 0;

    // Get today's stats
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayStartStr = todayStart.toISOString().slice(0, 19).replace('T', ' ');

    const todayImpressionsResult = await clickhouse.query({
      query: "SELECT COUNT(*) as total FROM impressions WHERE timestamp >= {todayStart:String}",
      query_params: { todayStart: todayStartStr },
    });
    const todayImpressionsData = await todayImpressionsResult.json();
    const todayImpressions = parseInt(todayImpressionsData.data[0]?.total || 0);

    const todayClicksResult = await clickhouse.query({
      query: "SELECT COUNT(*) as total FROM clicks WHERE timestamp >= {todayStart:String}",
      query_params: { todayStart: todayStartStr },
    });
    const todayClicksData = await todayClicksResult.json();
    const todayClicks = parseInt(todayClicksData.data[0]?.total || 0);

    // Get RTB statistics
    const rtbRequestsResult = await clickhouse.query({
      query: `
        SELECT
          SUM(total_requests) as total_requests,
          SUM(total_wins) as total_wins,
          SUM(total_revenue) as total_revenue
        FROM request_stats
        WHERE date >= today() - 30
      `,
    });
    const rtbRequestsData = await rtbRequestsResult.json();
    const rtbStats = rtbRequestsData.data[0] || {
      total_requests: 0,
      total_wins: 0,
      total_revenue: 0
    };

    // Get today's RTB stats
    const todayRtbResult = await clickhouse.query({
      query: `
        SELECT
          SUM(total_requests) as requests,
          SUM(total_wins) as wins
        FROM request_stats
        WHERE date = today()
      `,
    });
    const todayRtbData = await todayRtbResult.json();
    const todayRtb = todayRtbData.data[0] || {
      requests: 0,
      wins: 0
    };

    // Get win notifications stats
    const winNotificationsResult = await clickhouse.query({
      query: `
        SELECT
          COUNT(*) as total_wins,
          SUM(win_price) as total_win_price,
          SUM(platform_revenue) as total_platform_revenue
        FROM win_notifications
        WHERE timestamp >= now() - INTERVAL 30 DAY
      `,
    });
    const winNotificationsData = await winNotificationsResult.json();
    const winStats = winNotificationsData.data[0] || {
      total_wins: 0,
      total_win_price: 0,
      total_platform_revenue: 0
    };

    return NextResponse.json({
      success: true,
      data: {
        totalUsers,
        activeCampaigns,
        activeWebsites,
        platformRevenue: parseFloat(platformRevenue).toFixed(2),
        recentActivity,
        pending: {
          campaigns: pendingCampaigns,
          websites: pendingWebsites,
        },
        today: {
          impressions: todayImpressions,
          clicks: todayClicks,
          ctr: todayImpressions > 0 ? ((todayClicks / todayImpressions) * 100).toFixed(2) : '0.00',
          rtbRequests: todayRtb.requests,
          rtbWins: todayRtb.wins,
          rtbWinRate: todayRtb.requests > 0 ? ((todayRtb.wins / todayRtb.requests) * 100).toFixed(2) : '0.00',
        },
        rtb: {
          totalRequests: rtbStats.total_requests,
          totalWins: rtbStats.total_wins,
          totalRevenue: parseFloat(rtbStats.total_revenue || 0).toFixed(2),
          winRate: rtbStats.total_requests > 0 ? ((rtbStats.total_wins / rtbStats.total_requests) * 100).toFixed(2) : '0.00',
          winNotifications: winStats.total_wins,
          totalWinPrice: parseFloat(winStats.total_win_price || 0).toFixed(2),
          platformRevenue: parseFloat(winStats.total_platform_revenue || 0).toFixed(2),
        },
      },
    });

  } catch (error) {
    console.error('Admin dashboard stats error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
