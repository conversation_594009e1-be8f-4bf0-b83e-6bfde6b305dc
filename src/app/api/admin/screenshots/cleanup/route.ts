import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { ScreenshotService } from '@/lib/screenshot-service';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting screenshot cleanup...');

    // Get current stats before cleanup
    const statsBefore = await ScreenshotService.getScreenshotStats();

    // Clean up old screenshots from filesystem
    await ScreenshotService.cleanupOldScreenshots();

    // Clean up old screenshot URLs from database (older than 90 days)
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    await clickhouse.command({
      query: `
        ALTER TABLE ad_monitoring
        UPDATE screenshot_url = ''
        WHERE created_at < {cutoffDate:String}
        AND screenshot_url != ''
      `,
      query_params: {
        cutoffDate: ninetyDaysAgo.toISOString().slice(0, 19).replace('T', ' '),
      },
    });

    // Get stats after cleanup
    const statsAfter = await ScreenshotService.getScreenshotStats();

    // Calculate cleanup results
    const filesDeleted = statsBefore.totalFiles - statsAfter.totalFiles;
    const spaceFreed = statsBefore.totalSize - statsAfter.totalSize;

    // Log the cleanup action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'screenshot_cleanup',
        target_id: 0,
        target_type: 'system',
        reason: `Cleaned up ${filesDeleted} old screenshots, freed ${(spaceFreed / 1024 / 1024).toFixed(2)} MB`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Screenshot cleanup completed successfully',
      results: {
        filesDeleted,
        spaceFreedMB: Math.round(spaceFreed / 1024 / 1024 * 100) / 100,
        remainingFiles: statsAfter.totalFiles,
        remainingSizeMB: Math.round(statsAfter.totalSize / 1024 / 1024 * 100) / 100,
      }
    });

  } catch (error) {
    console.error('Screenshot cleanup error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current screenshot statistics
    const stats = await ScreenshotService.getScreenshotStats();

    // Get database screenshot count
    const dbResult = await clickhouse.query({
      query: `
        SELECT 
          COUNT(*) as total_records,
          COUNT(CASE WHEN screenshot_url != '' THEN 1 END) as with_screenshots,
          COUNT(CASE WHEN created_at < subtractDays(now(), 90) AND screenshot_url != '' THEN 1 END) as old_screenshots
        FROM ad_monitoring
      `,
    });

    const dbStats = await dbResult.json();
    const dbData = dbStats.data[0];

    return NextResponse.json({
      filesystem: {
        totalFiles: stats.totalFiles,
        totalSizeMB: Math.round(stats.totalSize / 1024 / 1024 * 100) / 100,
        oldestFile: stats.oldestFile,
        newestFile: stats.newestFile,
      },
      database: {
        totalRecords: parseInt(dbData.total_records),
        withScreenshots: parseInt(dbData.with_screenshots),
        oldScreenshots: parseInt(dbData.old_screenshots),
      },
      cleanup: {
        retentionDays: 90,
        nextCleanupRecommended: stats.oldestFile ? 
          new Date(stats.oldestFile.getTime() + (90 * 24 * 60 * 60 * 1000)) : null,
      }
    });

  } catch (error) {
    console.error('Screenshot stats error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
