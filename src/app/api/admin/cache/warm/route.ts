import { NextRequest, NextResponse } from 'next/server';
import { cache, CACHE_KEYS, CACHE_TTL, CacheHelper } from '@/lib/cache';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const warmupResults = [];

    // 1. Warm up DSP partners cache
    try {
      const dspPartners = await CacheHelper.getOrSet(
        CACHE_KEYS.PARTNER_ENDPOINTS('dsp'),
        async () => {
          const result = await clickhouse.query({
            query: `
              SELECT id, name, endpoint_url, protocol, timeout_ms, bid_price_format, api_key
              FROM partner_endpoints
              WHERE type = 'dsp' AND status = 'active'
              ORDER BY id
            `,
          });
          const data = await result.json();
          return data.data || [];
        },
        CACHE_TTL.PARTNERS
      );
      
      warmupResults.push({
        key: 'dsp_partners',
        success: true,
        count: dspPartners.length,
        cached: true
      });
    } catch (error) {
      warmupResults.push({
        key: 'dsp_partners',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 2. Warm up SSP partners cache
    try {
      const sspPartners = await CacheHelper.getOrSet(
        CACHE_KEYS.PARTNER_ENDPOINTS('ssp'),
        async () => {
          const result = await clickhouse.query({
            query: `
              SELECT id, name, endpoint_url, protocol, timeout_ms, bid_price_format, revenue_share
              FROM partner_endpoints
              WHERE type = 'ssp' AND status = 'active'
              ORDER BY id
            `,
          });
          const data = await result.json();
          return data.data || [];
        },
        CACHE_TTL.PARTNERS
      );
      
      warmupResults.push({
        key: 'ssp_partners',
        success: true,
        count: sspPartners.length,
        cached: true
      });
    } catch (error) {
      warmupResults.push({
        key: 'ssp_partners',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 3. Warm up platform settings
    try {
      const revenueShare = await CacheHelper.getOrSet(
        CACHE_KEYS.PUBLISHER_REVENUE_SHARE,
        async () => {
          const result = await clickhouse.query({
            query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
          });
          const data = await result.json();
          return data.data.length > 0 ? data.data[0].setting_value : '20';
        },
        CACHE_TTL.SETTINGS
      );
      
      warmupResults.push({
        key: 'publisher_revenue_share',
        success: true,
        value: revenueShare,
        cached: true
      });
    } catch (error) {
      warmupResults.push({
        key: 'publisher_revenue_share',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 4. Warm up active campaigns for common formats
    const adFormats = ['banner', 'native', 'video'];
    for (const format of adFormats) {
      try {
        const campaigns = await CacheHelper.getOrSet(
          CACHE_KEYS.ACTIVE_CAMPAIGNS(format),
          async () => {
            const result = await clickhouse.query({
              query: `
                SELECT c.*, u.balance as advertiser_balance, u.role as user_role
                FROM campaigns c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE c.status = 'active'
                AND c.type = {format:String}
                AND c.start_date <= now()
                AND (c.end_date IS NULL OR c.end_date >= now())
                AND c.creative_type = 1
                ORDER BY c.cpm_bid DESC
                LIMIT 10
              `,
              query_params: { format },
            });
            const data = await result.json();
            return data;
          },
          CACHE_TTL.CAMPAIGNS
        );
        
        warmupResults.push({
          key: `campaigns_${format}`,
          success: true,
          count: campaigns.data?.length || 0,
          cached: true
        });
      } catch (error) {
        warmupResults.push({
          key: `campaigns_${format}`,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      status: 'success',
      message: 'Cache warmup completed',
      results: warmupResults,
      summary: {
        total: warmupResults.length,
        successful: warmupResults.filter(r => r.success).length,
        failed: warmupResults.filter(r => !r.success).length,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Cache warmup endpoint',
    usage: 'POST to this endpoint to warm up the cache',
    endpoints: {
      warmup: 'POST /api/admin/cache/warm',
      test: 'GET /api/admin/redis/test',
    }
  });
}
