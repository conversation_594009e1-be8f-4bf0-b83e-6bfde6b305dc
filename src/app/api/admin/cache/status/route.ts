import { NextRequest, NextResponse } from 'next/server';
import { cache, CACHE_KEYS } from '@/lib/cache';

export async function GET(request: NextRequest) {
  try {
    const cacheStatus = [];

    // Check common cache keys
    const keysToCheck = [
      { key: CACHE_KEYS.PARTNER_ENDPOINTS('dsp'), name: 'DSP Partners' },
      { key: CACHE_KEYS.PARTNER_ENDPOINTS('ssp'), name: 'SSP Partners' },
      { key: CACHE_KEYS.PUBLISHER_REVENUE_SHARE, name: 'Publisher Revenue Share' },
      { key: CACHE_KEYS.ACTIVE_CAMPAIGNS('banner'), name: 'Banner Campaigns' },
      { key: CACHE_KEYS.ACTIVE_CAMPAIGNS('native'), name: 'Native Campaigns' },
      { key: CACHE_KEYS.ACTIVE_CAMPAIGNS('video'), name: 'Video Campaigns' },
    ];

    for (const item of keysToCheck) {
      try {
        const value = await cache.get(item.key);
        cacheStatus.push({
          name: item.name,
          key: item.key,
          cached: value !== null,
          dataType: value ? typeof value : null,
          dataSize: value ? (Array.isArray(value) ? value.length : 
                           typeof value === 'object' ? Object.keys(value).length : 1) : 0,
          preview: value ? (Array.isArray(value) ? `Array[${value.length}]` : 
                           typeof value === 'object' ? 'Object' : String(value).substring(0, 50)) : null
        });
      } catch (error) {
        cacheStatus.push({
          name: item.name,
          key: item.key,
          cached: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Get Redis info if available
    let redisInfo = null;
    try {
      const connectionTest = await cache.testConnection();
      redisInfo = connectionTest;
    } catch (error) {
      redisInfo = { connected: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }

    return NextResponse.json({
      status: 'success',
      redis: redisInfo,
      cache: {
        entries: cacheStatus,
        summary: {
          total: cacheStatus.length,
          cached: cacheStatus.filter(item => item.cached).length,
          empty: cacheStatus.filter(item => !item.cached).length,
        }
      },
      actions: {
        warmup: 'POST /api/admin/cache/warm - Populate cache with data',
        clear: 'DELETE /api/admin/cache/clear - Clear all cache entries',
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
