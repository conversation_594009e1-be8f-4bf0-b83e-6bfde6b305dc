import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);
    if (isNaN(userId)) {
      return NextResponse.json(
        { message: 'Invalid user ID' },
        { status: 400 }
      );
    }

    const { amount, description } = await request.json();

    if (!amount || isNaN(parseFloat(amount))) {
      return NextResponse.json(
        { message: 'Invalid amount' },
        { status: 400 }
      );
    }

    if (!description || description.trim().length === 0) {
      return NextResponse.json(
        { message: 'Description is required' },
        { status: 400 }
      );
    }

    const adjustmentAmount = parseFloat(amount);

    // Check if user exists and get current balance
    const userResult = await clickhouse.query({
      query: 'SELECT id, full_name, balance FROM users WHERE id = {userId:UInt32}',
      query_params: { userId },
    });
    const userData = await userResult.json();
    if (userData.data.length === 0) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    const user = userData.data[0];
    const currentBalance = parseFloat(user.balance);
    const newBalance = currentBalance + adjustmentAmount;

    // Prevent negative balance
    if (newBalance < 0) {
      return NextResponse.json(
        { message: 'Insufficient funds. Cannot deduct more than current balance.' },
        { status: 400 }
      );
    }

    // Update user balance
    await clickhouse.command({
      query: `
        ALTER TABLE users
        UPDATE
          balance = {newBalance:Float64},
          updated_at = {updatedAt:String}
        WHERE id = {userId:UInt32}
      `,
      query_params: {
        newBalance,
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId,
      },
    });

    // Create transaction record
    const transactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
    await clickhouse.insert({
      table: 'transactions',
      values: [{
        id: transactionId,
        user_id: userId,
        type: 'adjustment',
        amount: Math.abs(adjustmentAmount),
        status: 'completed',
        payment_method: '',
        payment_reference: '',
        description: description.trim(),
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Log the admin action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: adjustmentAmount > 0 ? 'funds_add' : 'funds_deduct',
        target_id: userId,
        target_type: 'user',
        reason: `${adjustmentAmount > 0 ? 'Added' : 'Deducted'} $${Math.abs(adjustmentAmount).toFixed(2)} - ${description.trim()}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: `Funds ${adjustmentAmount > 0 ? 'added' : 'deducted'} successfully`,
      previousBalance: currentBalance,
      newBalance,
      adjustment: adjustmentAmount,
      transactionId,
    });

  } catch (error) {
    console.error('Funds adjustment error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
