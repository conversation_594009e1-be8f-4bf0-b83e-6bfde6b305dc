import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth'
import clickhouse from '@/lib/clickhouse';
import bcrypt from 'bcryptjs';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);
    if (isNaN(userId)) {
      return NextResponse.json(
        { message: 'Invalid user ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { action, full_name, email, role, status, password } = body;

    if (action === 'edit') {
      // Handle user edit
      if (!full_name || !email || !role || !status) {
        return NextResponse.json(
          { message: 'Missing required fields' },
          { status: 400 }
        );
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { message: 'Invalid email format' },
          { status: 400 }
        );
      }

      // Check if email is already taken by another user
      const emailCheckResult = await clickhouse.query({
        query: 'SELECT id FROM users WHERE email = {email:String} AND id != {userId:UInt32}',
        query_params: { email, userId },
      });
      const emailCheckData = await emailCheckResult.json();
      if (emailCheckData.data.length > 0) {
        return NextResponse.json(
          { message: 'Email is already taken by another user' },
          { status: 400 }
        );
      }

      // Prepare update query and params
      let updateQuery = `
        ALTER TABLE users
        UPDATE
          full_name = {fullName:String},
          email = {email:String},
          role = {role:String},
          status = {status:String},
          updated_at = {updatedAt:String}
      `;
      
      const queryParams: any = {
        fullName: full_name,
        email,
        role,
        status,
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId,
      };

      // If password is provided, hash it and include in update
      if (password && password.trim() !== '') {
        const hashedPassword = await bcrypt.hash(password, 12);
        updateQuery += `, password = {password:String}`;
        queryParams.password = hashedPassword;
      }

      updateQuery += ` WHERE id = {userId:UInt32}`;

      // Update user
      await clickhouse.command({
        query: updateQuery,
        query_params: queryParams,
      });

      // Log the action
      await clickhouse.insert({
        table: 'admin_actions',
        values: [{
          id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
          admin_id: parseInt(session.user.id),
          action_type: 'user_edit',
          target_id: userId,
          target_type: 'user',
          reason: 'User details updated by admin',
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });

      return NextResponse.json({
        message: 'User updated successfully',
      });

    } else if (['enable', 'disable'].includes(action)) {
      // Handle enable/disable
      const newStatus = action === 'enable' ? 'active' : 'inactive';

      await clickhouse.command({
        query: `
          ALTER TABLE users
          UPDATE status = {status:String},
                 updated_at = {updatedAt:String}
          WHERE id = {userId:UInt32}
        `,
        query_params: {
          status: newStatus,
          updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
          userId,
        },
      });

      // Log the action
      await clickhouse.insert({
        table: 'admin_actions',
        values: [{
          id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
          admin_id: parseInt(session.user.id),
          action_type: `user_${action}`,
          target_id: userId,
          target_type: 'user',
          reason: `User ${action}d by admin`,
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });

      return NextResponse.json({
        message: `User ${action}d successfully`,
        status: newStatus,
      });

    } else {
      return NextResponse.json(
        { message: 'Invalid action' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('User update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);
    if (isNaN(userId)) {
      return NextResponse.json(
        { message: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Check if user exists and get user info
    const userResult = await clickhouse.query({
      query: 'SELECT id, full_name, role FROM users WHERE id = {userId:UInt32}',
      query_params: { userId },
    });
    const userData = await userResult.json();
    if (userData.data.length === 0) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    const user = userData.data[0] as any;

    // Prevent deleting admin users
    if (user.role === 'admin') {
      return NextResponse.json(
        { message: 'Cannot delete admin users' },
        { status: 403 }
      );
    }

    // Delete user
    await clickhouse.command({
      query: 'DELETE FROM users WHERE id = {userId:UInt32}',
      query_params: { userId },
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'user_delete',
        target_id: userId,
        target_type: 'user',
        reason: `User "${user.full_name}" deleted by admin`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'User deleted successfully',
    });

  } catch (error) {
    console.error('User delete error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
