import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth'
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT id, email, full_name, role, status, balance, created_at
        FROM users 
        ORDER BY created_at DESC
      `,
    });

    const users = await result.json();

    return NextResponse.json(users.data);

  } catch (error) {
    console.error('Admin users fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
