import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import bcrypt from 'bcryptjs';
import { EmailService } from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      email,
      password,
      full_name,
      role,
      address,
      city,
      zip,
      state,
      country,
    } = await request.json();

    // Validate required fields
    if (!email || !password || !full_name || !role || !address || !city || !zip || !state || !country) {
      return NextResponse.json(
        { message: 'All fields are required' },
        { status: 400 }
      );
    }

    // Validate role
    if (!['dsp', 'ssp'].includes(role)) {
      return NextResponse.json(
        { message: 'Invalid role. Must be dsp or ssp' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUserResult = await clickhouse.query({
      query: 'SELECT id FROM users WHERE email = {email:String}',
      query_params: { email },
    });

    const existingUsers = await existingUserResult.json();
    if (existingUsers.data.length > 0) {
      return NextResponse.json(
        { message: 'Email already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate user ID (starting from 10340)
    const lastUserResult = await clickhouse.query({
      query: 'SELECT MAX(id) as max_id FROM users',
    });

    const lastUserData = await lastUserResult.json();
    const lastId = lastUserData.data[0]?.max_id || 10339;
    const newUserId = lastId + 1;

    // Create user
    await clickhouse.insert({
      table: 'users',
      values: [{
        id: newUserId,
        email,
        password: hashedPassword,
        full_name,
        address,
        city,
        zip,
        state,
        country,
        role,
        status: 'active',
        balance: 0,
        email_verified: 1, // Admin-created accounts are pre-verified
        email_verification_token: '',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'user_create',
        target_id: newUserId,
        target_type: 'user',
        reason: `Created ${role.toUpperCase()} user: ${email}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Send account creation email with login credentials
    try {
      await EmailService.sendDspSspAccountEmail({
        id: newUserId,
        email,
        full_name,
        role
      }, password); // Send the original password (not hashed)
    } catch (emailError) {
      console.error('Failed to send account creation email:', emailError);
      // Don't fail the user creation if email fails
    }

    return NextResponse.json({
      message: 'User created successfully and login credentials sent via email',
      user_id: newUserId,
    });

  } catch (error) {
    console.error('User creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
