import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      recipient_types,
      subject,
      message,
      send_to_all,
      specific_users
    } = await request.json();

    if (!subject || !message) {
      return NextResponse.json(
        { message: 'Subject and message are required' },
        { status: 400 }
      );
    }

    if (!send_to_all && (!recipient_types || recipient_types.length === 0)) {
      return NextResponse.json(
        { message: 'Please select recipient types or specific users' },
        { status: 400 }
      );
    }

    // Get email settings
    const emailSettingsResult = await clickhouse.query({
      query: 'SELECT * FROM email_settings ORDER BY id DESC LIMIT 1',
    });

    const emailSettings = await emailSettingsResult.json();
    if (emailSettings.data.length === 0) {
      return NextResponse.json(
        { message: 'Email settings not configured' },
        { status: 400 }
      );
    }

    const settings = emailSettings.data[0];

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: settings.host,
      port: settings.port,
      secure: settings.security === 'ssl',
      auth: {
        user: settings.username,
        pass: settings.password,
      },
    });

    // Build query to get recipients
    let query = 'SELECT id, email, full_name, role FROM users WHERE status = \'active\'';
    const queryParams: any = {};

    if (!send_to_all) {
      if (specific_users && specific_users.length > 0) {
        // Send to specific users
        const userIds = specific_users.map((id: string) => parseInt(id));
        query += ` AND id IN (${userIds.join(',')})`;
      } else if (recipient_types && recipient_types.length > 0) {
        // Send to specific user types
        const types = recipient_types.map((type: string) => `'${type}'`).join(',');
        query += ` AND role IN (${types})`;
      }
    }

    // Get recipients
    const recipientsResult = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const recipients = await recipientsResult.json();
    const users = recipients.data || [];

    if (users.length === 0) {
      return NextResponse.json(
        { message: 'No recipients found' },
        { status: 400 }
      );
    }

    // Helper function to replace placeholders with actual user data
    const replacePlaceholders = async (text: string, user: any) => {
      let processedText = text;

      // Get user-specific data
      const userStats = await getUserStats(user.id, user.role);

      // Common placeholders
      processedText = processedText.replace(/\{\{user_name\}\}/g, user.full_name || 'User');
      processedText = processedText.replace(/\{\{user_email\}\}/g, user.email || '');
      processedText = processedText.replace(/\{\{user_role\}\}/g, user.role?.charAt(0).toUpperCase() + user.role?.slice(1) || 'User');
      processedText = processedText.replace(/\{\{platform_name\}\}/g, 'Global Ads Media');
      processedText = processedText.replace(/\{\{current_date\}\}/g, new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }));
      processedText = processedText.replace(/\{\{support_email\}\}/g, '<EMAIL>');

      // Role-specific placeholders
      if (userStats) {
        processedText = processedText.replace(/\{\{account_balance\}\}/g, `$${userStats.balance?.toFixed(2) || '0.00'}`);
        processedText = processedText.replace(/\{\{active_campaigns\}\}/g, userStats.activeCampaigns?.toString() || '0');
        processedText = processedText.replace(/\{\{total_spent\}\}/g, `$${userStats.totalSpent?.toFixed(2) || '0.00'}`);
        processedText = processedText.replace(/\{\{active_websites\}\}/g, userStats.activeWebsites?.toString() || '0');
        processedText = processedText.replace(/\{\{total_earned\}\}/g, `$${userStats.totalEarned?.toFixed(2) || '0.00'}`);
        processedText = processedText.replace(/\{\{pending_payout\}\}/g, `$${userStats.pendingPayout?.toFixed(2) || '0.00'}`);
        processedText = processedText.replace(/\{\{api_requests\}\}/g, userStats.apiRequests?.toLocaleString() || '0');
        processedText = processedText.replace(/\{\{win_rate\}\}/g, `${userStats.winRate?.toFixed(1) || '0.0'}%`);
        processedText = processedText.replace(/\{\{inventory_requests\}\}/g, userStats.inventoryRequests?.toLocaleString() || '0');
        processedText = processedText.replace(/\{\{fill_rate\}\}/g, `${userStats.fillRate?.toFixed(1) || '0.0'}%`);
        processedText = processedText.replace(/\{\{last_login\}\}/g, userStats.lastLogin || 'Never');
      }

      return processedText;
    };

    // Helper function to get user statistics
    const getUserStats = async (userId: number, role: string) => {
      try {
        const stats: any = { balance: 0 };

        // Get user balance
        const balanceResult = await clickhouse.query({
          query: 'SELECT balance FROM users WHERE id = {userId:UInt32}',
          query_params: { userId },
        });
        const balanceData = await balanceResult.json();
        if (balanceData.data.length > 0) {
          stats.balance = parseFloat(balanceData.data[0].balance) || 0;
        }

        // Role-specific stats
        if (role === 'advertiser') {
          // Get campaign stats
          const campaignResult = await clickhouse.query({
            query: 'SELECT COUNT(*) as count, SUM(total_spent) as spent FROM campaigns WHERE user_id = {userId:UInt32} AND status = \'active\'',
            query_params: { userId },
          });
          const campaignData = await campaignResult.json();
          if (campaignData.data.length > 0) {
            stats.activeCampaigns = parseInt(campaignData.data[0].count) || 0;
            stats.totalSpent = parseFloat(campaignData.data[0].spent) || 0;
          }
        } else if (role === 'publisher') {
          // Get website stats
          const websiteResult = await clickhouse.query({
            query: 'SELECT COUNT(*) as count FROM websites WHERE user_id = {userId:UInt32} AND status = \'active\'',
            query_params: { userId },
          });
          const websiteData = await websiteResult.json();
          if (websiteData.data.length > 0) {
            stats.activeWebsites = parseInt(websiteData.data[0].count) || 0;
          }

          // Get earnings
          const earningsResult = await clickhouse.query({
            query: 'SELECT SUM(publisher_revenue) as earned FROM impressions i JOIN websites w ON i.website_id = w.id WHERE w.user_id = {userId:UInt32}',
            query_params: { userId },
          });
          const earningsData = await earningsResult.json();
          if (earningsData.data.length > 0) {
            stats.totalEarned = parseFloat(earningsData.data[0].earned) || 0;
          }
        }

        return stats;
      } catch (error) {
        console.error('Error getting user stats:', error);
        return { balance: 0 };
      }
    };

    // Send emails
    let successCount = 0;
    let failureCount = 0;
    const failures: string[] = [];

    for (const user of users) {
      try {
        // Process placeholders in subject and message
        const processedSubject = await replacePlaceholders(subject, user);
        const processedMessage = await replacePlaceholders(message, user);

        await transporter.sendMail({
          from: `"${settings.from_name}" <${settings.from_email}>`,
          to: user.email,
          subject: processedSubject,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background: #111827; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                <img src="${process.env.PLATFORM_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/logo-white.png" alt="Global Ads Media" style="height: 40px; margin-bottom: 10px; display: block; margin-left: auto; margin-right: auto;" />
                <p style="color: #9CA3AF; margin: 10px 0 0 0; font-size: 14px;">Platform Notification</p>
              </div>

              <div style="padding: 20px; background: white; border-radius: 8px; border: 1px solid #e9ecef;">
                <h3 style="color: #333; margin-top: 0;">Hello ${user.full_name},</h3>

                <div style="line-height: 1.6; color: #555;">
                  ${processedMessage.replace(/\n/g, '<br>')}
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                  <p style="color: #666; font-size: 14px; margin: 0;">
                    Best regards,<br>
                    Global Ads Media Team
                  </p>
                </div>
              </div>

              <div style="text-align: center; margin-top: 20px;">
                <p style="color: #999; font-size: 12px;">
                  This email was sent to ${user.email} as part of our platform communications.
                </p>
              </div>
            </div>
          `,
        });
        successCount++;
      } catch (error) {
        console.error(`Failed to send email to ${user.email}:`, error);
        failureCount++;
        failures.push(user.email);
      }
    }

    // Log the bulk email action
    const actionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: actionId,
        admin_id: parseInt(session.user.id),
        action_type: 'bulk_email',
        target_id: 0,
        target_type: send_to_all ? 'all_users' : (recipient_types || []).join(','),
        reason: `Sent "${subject}" to ${successCount} users`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Bulk email completed',
      success_count: successCount,
      failure_count: failureCount,
      total_recipients: users.length,
      failures: failures,
    });

  } catch (error) {
    console.error('Bulk email error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get users for recipient selection
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');

    let query = 'SELECT id, email, full_name, role FROM users WHERE status = \'active\'';
    const queryParams: any = {};

    if (role && role !== 'all') {
      query += ' AND role = {role:String}';
      queryParams.role = role;
    }

    query += ' ORDER BY full_name ASC';

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const users = await result.json();

    return NextResponse.json({
      users: users.data || [],
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
