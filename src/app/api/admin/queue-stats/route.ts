import { NextRequest, NextResponse } from 'next/server';
import { RabbitMQManager } from '@/lib/queue-manager';

export async function GET(request: NextRequest) {
  try {
    // Get comprehensive queue statistics
    const stats = await RabbitMQManager.getQueueStats();

    // Calculate total metrics (RabbitMQ provides messageCount and consumerCount)
    const totalStats = stats.reduce((acc, stat) => ({
      waiting: acc.waiting + (stat.messageCount || 0),
      active: acc.active + (stat.consumerCount || 0),
      completed: 0, // RabbitMQ doesn't track completed jobs
      failed: 0,    // RabbitMQ doesn't track failed jobs
    }), { waiting: 0, active: 0, completed: 0, failed: 0 });

    // Calculate processing rates (RabbitMQ-based estimates)
    const processingRates = stats.map(stat => ({
      name: stat.name,
      rate: stat.consumerCount || 0, // Active consumers as processing rate indicator
      efficiency: stat.error ? 0 : 100, // Assume 100% efficiency unless there's an error
    }));

    // Queue health assessment (RabbitMQ-based)
    const queueHealth = stats.map(stat => {
      let status = 'healthy';
      let issues = [];

      if (stat.error) {
        status = 'error';
        issues.push(`Queue error: ${stat.error}`);
      }

      if (stat.messageCount > 1000) {
        status = 'warning';
        issues.push('High message backlog');
      }

      if (stat.consumerCount === 0 && stat.messageCount > 0) {
        status = 'error';
        issues.push('No active consumers');
      }

      return {
        name: stat.name,
        status,
        issues,
      };
    });

    const response = {
      timestamp: new Date().toISOString(),
      queues: stats,
      totals: totalStats,
      processing_rates: processingRates,
      health: queueHealth,
      recommendations: generateRecommendations(stats),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching queue stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch queue statistics' },
      { status: 500 }
    );
  }
}

function generateRecommendations(stats: any[]): string[] {
  const recommendations = [];

  // Check for queue errors
  const errorQueues = stats.filter(stat => stat.error);
  if (errorQueues.length > 0) {
    recommendations.push(`Queue errors detected in: ${errorQueues.map(q => q.name).join(', ')}`);
  }

  // Check for message backlogs
  const backloggedQueues = stats.filter(stat => stat.messageCount > 500);
  if (backloggedQueues.length > 0) {
    recommendations.push(`Consider adding more consumers for: ${backloggedQueues.map(q => q.name).join(', ')}`);
  }

  // Check for idle queues with pending messages
  const idleQueues = stats.filter(stat => stat.consumerCount === 0 && stat.messageCount > 0);
  if (idleQueues.length > 0) {
    recommendations.push(`Start consumers for: ${idleQueues.map(q => q.name).join(', ')}`);
  }

  // Performance recommendations
  const totalMessages = stats.reduce((sum, stat) => sum + (stat.messageCount || 0), 0);
  if (totalMessages > 10000) {
    recommendations.push('Consider implementing message batching for high-volume queues');
  }

  if (recommendations.length === 0) {
    recommendations.push('All RabbitMQ queues are operating efficiently! 🐰');
  }

  return recommendations;
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'pause':
        // RabbitMQ doesn't have pause/resume like Bull queues
        return NextResponse.json({
          message: 'RabbitMQ queues do not support pause operation. Use consumer management instead.'
        });

      case 'resume':
        // RabbitMQ doesn't have pause/resume like Bull queues
        return NextResponse.json({
          message: 'RabbitMQ queues do not support resume operation. Use consumer management instead.'
        });

      case 'purge':
        await RabbitMQManager.purgeAllQueues();
        return NextResponse.json({ message: 'All queues purged (messages cleared)' });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error managing queues:', error);
    return NextResponse.json(
      { error: 'Failed to manage queues' },
      { status: 500 }
    );
  }
}
