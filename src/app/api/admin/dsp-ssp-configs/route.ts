import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

interface User {
  id: number;
  role: string;
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'dsp' or 'ssp'
    const userId = searchParams.get('user_id');

    let query = `
      SELECT
        dsc.*,
        u.full_name as user_name,
        u.email as user_email
      FROM dsp_ssp_configs dsc
      LEFT JOIN users u ON dsc.user_id = u.id
      WHERE 1=1
    `;

    const queryParams: any = {};

    if (type) {
      query += ' AND dsc.type = {type:String}';
      queryParams.type = type;
    }

    if (userId) {
      query += ' AND dsc.user_id = {userId:UInt32}';
      queryParams.userId = parseInt(userId);
    }

    query += ' ORDER BY dsc.created_at DESC';

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const data = await result.json();
    const configs = data.data || [];

    return NextResponse.json({ configs });

  } catch (error) {
    console.error('DSP/SSP configs fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      user_id,
      name,
      type,
      bid_type,
      endpoint_url,
      api_key,
      revenue_share,
      protocol,
      openrtb_version,
      timeout_ms,
      qps_limit,
      targeting,
      seat_id,
      auth_type,
      auth_credentials,
      auction_type,
      test_mode
    } = await request.json();

    if (!user_id || !name || !type || !bid_type) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate type
    if (!['dsp', 'ssp'].includes(type)) {
      return NextResponse.json(
        { message: 'Invalid type. Must be dsp or ssp' },
        { status: 400 }
      );
    }

    // Validate bid_type
    if (!['cpm', 'cpv'].includes(bid_type)) {
      return NextResponse.json(
        { message: 'Invalid bid_type. Must be cpm or cpv' },
        { status: 400 }
      );
    }

    // Validate user exists and has correct role
    const userResult = await clickhouse.query({
      query: 'SELECT id, role FROM users WHERE id = {userId:UInt32}',
      query_params: { userId: parseInt(user_id) },
    });

    const users = await userResult.json();
    if (users.data.length === 0) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    const user: User = users.data[0] as User;
    if (user.role !== type) {
      return NextResponse.json(
        { message: `User role must be ${type}` },
        { status: 400 }
      );
    }

    // Generate config ID
    const configId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create DSP/SSP configuration
    await clickhouse.insert({
      table: 'dsp_ssp_configs',
      values: [{
        id: configId,
        user_id: parseInt(user_id),
        name,
        type,
        bid_type,
        endpoint_url: endpoint_url || '',
        api_key: api_key || '',
        revenue_share: parseFloat(revenue_share) || 0.00,
        status: 'active',
        protocol: protocol || 'openrtb',
        openrtb_version: openrtb_version || '2.5',
        timeout_ms: parseInt(timeout_ms) || 5000,
        qps_limit: parseInt(qps_limit) || 100,
        targeting: targeting ? JSON.stringify(targeting) : '{}',
        seat_id: seat_id || '',
        auth_type: auth_type || 'none',
        auth_credentials: auth_credentials ? JSON.stringify(auth_credentials) : '{}',
        auction_type: auction_type || 'first_price',
        test_mode: test_mode ? 1 : 0,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'DSP/SSP configuration created successfully',
      config_id: configId
    });

  } catch (error) {
    console.error('DSP/SSP config creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      id,
      name,
      bid_type,
      endpoint_url,
      api_key,
      revenue_share,
      status,
      protocol,
      openrtb_version,
      timeout_ms,
      qps_limit,
      targeting,
      seat_id,
      auth_type,
      auth_credentials,
      auction_type,
      test_mode
    } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    // Build update query dynamically
    const updates: string[] = [];
    const queryParams: any = { configId: parseInt(id) };

    if (name) {
      updates.push('name = {name:String}');
      queryParams.name = name;
    }

    if (bid_type && ['cpm', 'cpv'].includes(bid_type)) {
      updates.push('bid_type = {bidType:String}');
      queryParams.bidType = bid_type;
    }

    if (endpoint_url !== undefined) {
      updates.push('endpoint_url = {endpointUrl:String}');
      queryParams.endpointUrl = endpoint_url;
    }

    if (api_key !== undefined) {
      updates.push('api_key = {apiKey:String}');
      queryParams.apiKey = api_key;
    }

    if (revenue_share !== undefined) {
      updates.push('revenue_share = {revenueShare:Decimal(5,2)}');
      queryParams.revenueShare = parseFloat(revenue_share);
    }

    if (status && ['active', 'inactive'].includes(status)) {
      updates.push('status = {status:String}');
      queryParams.status = status;
    }

    if (protocol !== undefined) {
      updates.push('protocol = {protocol:String}');
      queryParams.protocol = protocol;
    }

    if (openrtb_version !== undefined) {
      updates.push('openrtb_version = {openrtbVersion:String}');
      queryParams.openrtbVersion = openrtb_version;
    }

    if (timeout_ms !== undefined) {
      updates.push('timeout_ms = {timeoutMs:UInt32}');
      queryParams.timeoutMs = parseInt(timeout_ms);
    }

    if (qps_limit !== undefined) {
      updates.push('qps_limit = {qpsLimit:UInt32}');
      queryParams.qpsLimit = parseInt(qps_limit);
    }

    if (targeting !== undefined) {
      updates.push('targeting = {targeting:String}');
      queryParams.targeting = JSON.stringify(targeting);
    }

    if (seat_id !== undefined) {
      updates.push('seat_id = {seatId:String}');
      queryParams.seatId = seat_id;
    }

    if (auth_type !== undefined) {
      updates.push('auth_type = {authType:String}');
      queryParams.authType = auth_type;
    }

    if (auth_credentials !== undefined) {
      updates.push('auth_credentials = {authCredentials:String}');
      queryParams.authCredentials = JSON.stringify(auth_credentials);
    }

    if (auction_type !== undefined) {
      updates.push('auction_type = {auctionType:String}');
      queryParams.auctionType = auction_type;
    }

    if (test_mode !== undefined) {
      updates.push('test_mode = {testMode:UInt8}');
      queryParams.testMode = test_mode ? 1 : 0;
    }

    if (updates.length === 0) {
      return NextResponse.json(
        { message: 'No valid fields to update' },
        { status: 400 }
      );
    }

    updates.push('updated_at = {updatedAt:String}');
    queryParams.updatedAt = new Date().toISOString().slice(0, 19).replace('T', ' ');

    await clickhouse.command({
      query: `
        ALTER TABLE dsp_ssp_configs
        UPDATE ${updates.join(', ')}
        WHERE id = {configId:UInt64}
      `,
      query_params: queryParams,
    });

    return NextResponse.json({
      message: 'DSP/SSP configuration updated successfully'
    });

  } catch (error) {
    console.error('DSP/SSP config update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    await clickhouse.command({
      query: 'DELETE FROM dsp_ssp_configs WHERE id = {configId:UInt64}',
      query_params: { configId: parseInt(id) },
    });

    return NextResponse.json({
      message: 'DSP/SSP configuration deleted successfully'
    });

  } catch (error) {
    console.error('DSP/SSP config deletion error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
