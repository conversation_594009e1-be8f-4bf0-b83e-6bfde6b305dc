import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Get all publishers (users with role 'publisher')
    const publishersResult = await clickhouse.query({
      query: `
        SELECT id, full_name, email
        FROM users
        WHERE role = 'publisher'
        ORDER BY full_name
      `,
    });

    const publishersData = await publishersResult.json();
    const allPublishers = publishersData.data || [];
    console.log('Found publishers:', allPublishers.length);

    // Get excluded publishers from publisher_fraud_exclusions table
    let excludedPublisherIds: number[] = [];
    try {
      const exclusionsResult = await clickhouse.query({
        query: `
          SELECT DISTINCT publisher_id
          FROM publisher_fraud_exclusions
          WHERE is_active = 1
        `,
      });

      const exclusionsData = await exclusionsResult.json();
      excludedPublisherIds = exclusionsData.data?.map((row: any) => row.publisher_id) || [];
    } catch (error) {
      // Table might not exist yet, that's okay
      console.log('publisher_fraud_exclusions table does not exist yet, will be created when needed');
      excludedPublisherIds = [];
    }

    // Separate excluded and available publishers
    const excludedPublishers = allPublishers.filter((pub: any) =>
      excludedPublisherIds.includes(pub.id)
    );

    const availablePublishers = allPublishers.filter((pub: any) =>
      !excludedPublisherIds.includes(pub.id)
    );

    return NextResponse.json({
      allPublishers,
      excludedPublishers,
      availablePublishers
    });

  } catch (error) {
    console.error('Fraud exclusions GET error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, publisherIds, publisherId } = body;

    if (action === 'add') {
      // Add multiple publishers to exclusions
      if (!publisherIds || !Array.isArray(publisherIds) || publisherIds.length === 0) {
        return NextResponse.json({ message: 'Publisher IDs are required' }, { status: 400 });
      }

      // Create publisher_fraud_exclusions table if it doesn't exist
      await clickhouse.command({
        query: `
          CREATE TABLE IF NOT EXISTS publisher_fraud_exclusions (
            id UInt64,
            publisher_id UInt32,
            excluded_by UInt32,
            excluded_at DateTime,
            is_active UInt8 DEFAULT 1,
            reason String DEFAULT 'Manual exclusion'
          ) ENGINE = MergeTree()
          ORDER BY (publisher_id, excluded_at)
        `,
      });

      // Insert exclusions for each publisher
      for (const pubId of publisherIds) {
        const exclusionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
        await clickhouse.command({
          query: `
            INSERT INTO publisher_fraud_exclusions (
              id, publisher_id, excluded_by, excluded_at, is_active, reason
            )
            VALUES (
              ${exclusionId}, ${pubId}, ${session.user.id}, now(), 1, 'Manual exclusion by admin'
            )
          `,
        });
      }

      return NextResponse.json({
        message: `Successfully excluded ${publisherIds.length} publisher(s) from fraud detection`
      });

    } else if (action === 'remove') {
      // Remove publisher from exclusions
      if (!publisherId) {
        return NextResponse.json({ message: 'Publisher ID is required' }, { status: 400 });
      }

      await clickhouse.command({
        query: `
          ALTER TABLE publisher_fraud_exclusions
          UPDATE is_active = 0
          WHERE publisher_id = ${publisherId} AND is_active = 1
        `,
      });

      return NextResponse.json({
        message: 'Publisher removed from fraud detection exclusions'
      });

    } else {
      return NextResponse.json({ message: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Fraud exclusions POST error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
