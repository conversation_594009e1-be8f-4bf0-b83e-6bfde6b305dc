import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const userRole = searchParams.get('user_role');

    const offset = (page - 1) * limit;

    // Build query conditions
    let whereConditions = ['1=1'];
    let queryParams: any = { limit, offset };

    if (type) {
      whereConditions.push('t.type = {type:String}');
      queryParams.type = type;
    }

    if (status) {
      whereConditions.push('t.status = {status:String}');
      queryParams.status = status;
    }

    if (userRole) {
      whereConditions.push('u.role = {userRole:String}');
      queryParams.userRole = userRole;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get transactions with user info
    const transactionsResult = await clickhouse.query({
      query: `
        SELECT
          t.id,
          t.user_id,
          u.full_name as user_name,
          u.role as user_role,
          t.type,
          t.amount,
          t.status,
          t.description,
          t.payment_method,
          t.payment_reference as payment_id,
          t.created_at,
          t.updated_at
        FROM transactions t
        LEFT JOIN users u ON t.user_id = u.id
        WHERE ${whereClause}
        ORDER BY t.created_at DESC
        LIMIT {limit:UInt32}
        OFFSET {offset:UInt32}
      `,
      query_params: queryParams,
    });

    // Get total count
    const countResult = await clickhouse.query({
      query: `
        SELECT COUNT(*) as total
        FROM transactions t
        LEFT JOIN users u ON t.user_id = u.id
        WHERE ${whereClause}
      `,
      query_params: queryParams,
    });

    const transactionsData = await transactionsResult.json();
    const countData = await countResult.json();

    const transactions = transactionsData.data || [];
    const total = countData.data[0]?.total || 0;

    return NextResponse.json({
      transactions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });

  } catch (error) {
    console.error('Admin transactions error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { user_id, type, amount, description } = await request.json();

    if (!user_id || !type || !amount || !description) {
      return NextResponse.json(
        { message: 'User ID, type, amount, and description are required' },
        { status: 400 }
      );
    }

    if (!['deposit', 'withdrawal', 'adjustment'].includes(type)) {
      return NextResponse.json(
        { message: 'Invalid transaction type' },
        { status: 400 }
      );
    }

    // Generate transaction ID
    const transactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create transaction
    await clickhouse.insert({
      table: 'transactions',
      values: [{
        id: transactionId,
        user_id: parseInt(user_id),
        type,
        amount: parseFloat(amount),
        status: 'completed',
        payment_method: '',
        payment_reference: '',
        description,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Update user balance
    const balanceChange = type === 'withdrawal' ? -parseFloat(amount) : parseFloat(amount);

    await clickhouse.command({
      query: `
        ALTER TABLE users
        UPDATE balance = balance + {balanceChange:Float64}
        WHERE id = {userId:UInt32}
      `,
      query_params: {
        balanceChange,
        userId: parseInt(user_id),
      },
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'transaction_create',
        target_id: transactionId,
        target_type: 'transaction',
        reason: `Created ${type} transaction: $${amount} - ${description}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Transaction created successfully',
      transaction_id: transactionId,
    });

  } catch (error) {
    console.error('Transaction creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
