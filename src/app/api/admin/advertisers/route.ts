import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

// GET - List all advertisers
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    let query = `
      SELECT 
        id,
        full_name,
        email,
        status,
        created_at
      FROM users 
      WHERE role = 'advertiser'
    `;

    let queryParams = {};

    // Add search filter if provided
    if (search.trim()) {
      query += ` AND (full_name ILIKE {search:String} OR email ILIKE {search:String})`;
      queryParams = { search: `%${search}%` };
    }

    query += ` ORDER BY full_name LIMIT 100`;

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const data = await result.json();
    return NextResponse.json(data.data);

  } catch (error) {
    console.error('Error fetching advertisers:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
