import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';
import { EmailService } from '@/lib/email-service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const campaignId = parseInt(id);
    if (isNaN(campaignId)) {
      return NextResponse.json(
        { message: 'Invalid campaign ID' },
        { status: 400 }
      );
    }

    const { action, reason } = await request.json();

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { message: 'Invalid action. Must be approve or reject' },
        { status: 400 }
      );
    }

    // Update campaign status
    const newStatus = action === 'approve' ? 'active' : 'rejected';

    await clickhouse.command({
      query: `
        ALTER TABLE campaigns
        UPDATE status = {status:String},
               updated_at = {updatedAt:String}
        WHERE id = {campaignId:UInt32}
      `,
      query_params: {
        status: newStatus,
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        campaignId,
      },
    });

    // Log the approval/rejection
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: `campaign_${action}`,
        target_id: campaignId,
        target_type: 'campaign',
        reason: reason || '',
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Send email notification to advertiser
    try {
      await EmailService.sendCampaignStatusNotification(campaignId, newStatus, reason);
    } catch (emailError) {
      console.error('Failed to send campaign status email:', emailError);
      // Don't fail the approval if email fails
    }

    return NextResponse.json({
      message: `Campaign ${action}d successfully`,
      status: newStatus,
    });

  } catch (error) {
    console.error('Campaign approval error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
