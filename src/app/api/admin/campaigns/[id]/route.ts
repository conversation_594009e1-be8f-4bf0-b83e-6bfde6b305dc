import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    const campaignResult = await clickhouse.query({
      query: `
        SELECT
          c.*,
          u.full_name as user_name,
          u.email as user_email
        FROM campaigns c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.id = {campaignId:UInt32}
      `,
      query_params: { campaignId },
    });

    const campaignData = await campaignResult.json();
    if (!campaignData.data.length) {
      return NextResponse.json(
        { success: false, message: 'Campaign not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      campaign: campaignData.data[0]
    });

  } catch (error) {
    console.error('Error fetching campaign:', error);
    return NextResponse.json(
      { message: 'Failed to fetch campaign' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const campaignId = parseInt(id);

    // Get existing campaign
    const campaignResult = await clickhouse.query({
      query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32}',
      query_params: { campaignId },
    });

    const campaignData = await campaignResult.json();
    if (!campaignData.data.length) {
      return NextResponse.json(
        { message: 'Campaign not found' },
        { status: 404 }
      );
    }

    const existingCampaign = campaignData.data[0];

    // Handle both JSON and FormData
    let formData: any = {};
    const contentType = request.headers.get('content-type');

    if (contentType?.includes('application/json')) {
      formData = await request.json();
    } else {
      // Handle FormData
      const form = await request.formData();
      for (const [key, value] of form.entries()) {
        if (key.startsWith('targeting') || key.includes('Schedule') || key.includes('list')) {
          try {
            formData[key] = JSON.parse(value as string);
          } catch {
            formData[key] = value;
          }
        } else {
          formData[key] = value;
        }
      }
    }

    // Extract fields from request body (handle both camelCase and snake_case)
    const name = formData.name;
    const cpm_bid = formData.cpmBid || formData.cpm_bid;
    const daily_budget = formData.dailyBudget || formData.daily_budget;
    const total_budget = formData.totalBudget || formData.total_budget;
    const landing_url = formData.landingUrl || formData.landing_url;
    const native_title = formData.nativeTitle || formData.native_title;
    const native_description = formData.nativeDescription || formData.native_description;
    const push_title = formData.pushTitle || formData.push_title;
    const push_description = formData.pushDescription || formData.push_description;
    const targeting_countries = formData.targetingCountries || formData.targeting_countries;
    const targeting_states = formData.targetingStates || formData.targeting_states;
    const targeting_devices = formData.targetingDevices || formData.targeting_devices;
    const targeting_os = formData.targetingOs || formData.targeting_os;
    const targeting_browsers = formData.targetingBrowsers || formData.targeting_browsers;
    const targeting_connection_types = formData.targetingConnectionTypes || formData.targeting_connection_types;
    const start_date = formData.startDate || formData.start_date;
    const end_date = formData.endDate || formData.end_date;
    const daily_schedule = formData.dailySchedule || formData.daily_schedule;
    const hourly_schedule = formData.hourlySchedule || formData.hourly_schedule;
    const frequency_cap_value = formData.frequencyCapValue || formData.frequency_cap_value;
    const frequency_cap_period = formData.frequencyCapPeriod || formData.frequency_cap_period;
    const publisher_whitelist = formData.whitelistPublishers || formData.publisher_whitelist;
    const publisher_blacklist = formData.blacklistPublishers || formData.publisher_blacklist;
    const website_whitelist = formData.whitelistWebsites || formData.website_whitelist;
    const website_blacklist = formData.blacklistWebsites || formData.website_blacklist;
    const zone_whitelist = formData.whitelistZones || formData.zone_whitelist;
    const zone_blacklist = formData.blacklistZones || formData.zone_blacklist;

    // Basic validation
    if (!name?.trim()) {
      return NextResponse.json({ message: 'Campaign name is required' }, { status: 400 });
    }

    if (!cpm_bid || cpm_bid <= 0) {
      return NextResponse.json({ message: 'Valid CPM bid is required' }, { status: 400 });
    }

    // Convert frequency cap period to enum value if provided
    let frequencyCapPeriodEnum = existingCampaign.frequency_cap_period;
    if (frequency_cap_period) {
      frequencyCapPeriodEnum = frequency_cap_period === 'hour' ? 1 :
                               frequency_cap_period === 'week' ? 3 : 2; // default to day
    }

    // Delete the old record
    await clickhouse.command({
      query: `ALTER TABLE campaigns DELETE WHERE id = ${campaignId}`,
    });

    // Wait a moment for the delete to process
    await new Promise(resolve => setTimeout(resolve, 100));

    // Insert updated record
    await clickhouse.insert({
      table: 'campaigns',
      values: [{
        id: campaignId,
        user_id: existingCampaign.user_id,
        name: name || existingCampaign.name,
        type: existingCampaign.type, // Preserve original type
        status: existingCampaign.status, // Preserve original status
        cpm_bid: cpm_bid || existingCampaign.cpm_bid,
        daily_budget: daily_budget !== undefined ? daily_budget : existingCampaign.daily_budget,
        total_budget: total_budget !== undefined ? total_budget : existingCampaign.total_budget,

        // Preserve original creative fields that cannot be changed
        banner_size: existingCampaign.banner_size,
        creative_type: existingCampaign.creative_type,
        js_tag: existingCampaign.js_tag,
        banner_image_url: existingCampaign.banner_image_url,
        native_icon_url: existingCampaign.native_icon_url,
        native_image_url: existingCampaign.native_image_url,
        push_image_url: existingCampaign.push_image_url,

        // Editable creative fields
        landing_url: landing_url || existingCampaign.landing_url,
        native_title: native_title || existingCampaign.native_title,
        native_description: native_description || existingCampaign.native_description,
        push_title: push_title || existingCampaign.push_title,
        push_description: push_description || existingCampaign.push_description,

        // Targeting fields
        targeting_countries: targeting_countries || existingCampaign.targeting_countries,
        targeting_states: targeting_states || existingCampaign.targeting_states,
        targeting_devices: targeting_devices || existingCampaign.targeting_devices,
        targeting_os: targeting_os || existingCampaign.targeting_os,
        targeting_browsers: targeting_browsers || existingCampaign.targeting_browsers,
        targeting_connection_types: targeting_connection_types || existingCampaign.targeting_connection_types,

        // Schedule fields
        start_date: start_date ? new Date(start_date).toISOString().slice(0, 19).replace('T', ' ') : existingCampaign.start_date,
        end_date: end_date ? new Date(end_date).toISOString().slice(0, 19).replace('T', ' ') : existingCampaign.end_date,
        daily_schedule: daily_schedule || existingCampaign.daily_schedule,
        hourly_schedule: hourly_schedule || existingCampaign.hourly_schedule,

        // Frequency cap fields
        frequency_cap_value: frequency_cap_value !== undefined ? frequency_cap_value : existingCampaign.frequency_cap_value,
        frequency_cap_period: frequencyCapPeriodEnum,

        // Whitelist/Blacklist fields
        publisher_whitelist: publisher_whitelist || existingCampaign.publisher_whitelist,
        publisher_blacklist: publisher_blacklist || existingCampaign.publisher_blacklist,
        website_whitelist: website_whitelist || existingCampaign.website_whitelist,
        website_blacklist: website_blacklist || existingCampaign.website_blacklist,
        zone_whitelist: zone_whitelist || existingCampaign.zone_whitelist,
        zone_blacklist: zone_blacklist || existingCampaign.zone_blacklist,

        created_at: existingCampaign.created_at, // Preserve original
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Log the admin action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'campaign_edit',
        target_id: campaignId,
        target_type: 'campaign',
        reason: `Admin edited campaign: ${name}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      success: true,
      message: 'Campaign updated successfully'
    });

  } catch (error) {
    console.error('Error updating campaign:', error);
    return NextResponse.json(
      { message: 'Failed to update campaign' },
      { status: 500 }
    );
  }
}
