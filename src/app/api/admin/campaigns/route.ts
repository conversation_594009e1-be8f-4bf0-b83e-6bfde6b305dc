import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT
          c.id,
          c.name,
          c.type,
          c.status,
          c.cpm_bid,
          c.daily_budget,
          c.landing_url,
          c.created_at,
          c.updated_at,
          u.full_name as user_name,
          u.email as user_email
        FROM campaigns c
        LEFT JOIN users u ON c.user_id = u.id
        ORDER BY c.created_at DESC
      `,
    });

    const campaigns = await result.json();

    console.log('Admin campaigns query result:', campaigns);
    console.log('Number of campaigns found:', campaigns.data?.length || 0);

    return NextResponse.json(campaigns.data);

  } catch (error) {
    console.error('Admin campaigns fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
