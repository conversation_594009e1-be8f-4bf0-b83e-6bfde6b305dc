import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get impressions in last hour
    const impressionsResult = await clickhouse.query({
      query: `
        SELECT COUNT(*) as count
        FROM impressions
        WHERE timestamp >= now() - INTERVAL 1 HOUR
      `,
    });

    // Get clicks in last hour
    const clicksResult = await clickhouse.query({
      query: `
        SELECT COUNT(*) as count
        FROM clicks
        WHERE timestamp >= now() - INTERVAL 1 HOUR
      `,
    });

    // Get conversions in last hour
    const conversionsResult = await clickhouse.query({
      query: `
        SELECT COUNT(*) as count
        FROM conversions
        WHERE timestamp >= now() - INTERVAL 1 HOUR
      `,
    });

    // Get platform revenue in last hour (advertiser spend - publisher revenue)
    const revenueResult = await clickhouse.query({
      query: `
        SELECT
          SUM(cost_deducted) - SUM(publisher_revenue) as revenue
        FROM impressions
        WHERE timestamp >= now() - INTERVAL 1 HOUR
      `,
    });

    // Get active campaigns
    const activeCampaignsResult = await clickhouse.query({
      query: `
        SELECT COUNT(DISTINCT id) as count
        FROM campaigns
        WHERE status = 'active'
        AND start_date <= now()
        AND (end_date IS NULL OR end_date >= now())
      `,
    });

    // Get active websites
    const activeWebsitesResult = await clickhouse.query({
      query: `
        SELECT COUNT(DISTINCT id) as count
        FROM websites
        WHERE status = 'approved'
      `,
    });

    // Get top performing campaigns
    const topCampaignsResult = await clickhouse.query({
      query: `
        SELECT
          c.id,
          c.name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM campaigns c
        LEFT JOIN impressions i ON c.id = i.campaign_id AND i.timestamp >= now() - INTERVAL 1 HOUR
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        WHERE c.status = 'active'
        GROUP BY c.id, c.name
        HAVING impressions > 0
        ORDER BY impressions DESC
        LIMIT 10
      `,
    });

    // Get recent activity (simplified - in production you'd have a dedicated activity log)
    const recentActivityResult = await clickhouse.query({
      query: `
        SELECT
          'impression' as type,
          CONCAT('New impression for campaign ', campaign_id) as message,
          timestamp
        FROM impressions
        WHERE timestamp >= now() - INTERVAL 10 MINUTE
        ORDER BY timestamp DESC
        LIMIT 20
      `,
    });

    // Parse results
    const impressionsData = await impressionsResult.json();
    const clicksData = await clicksResult.json();
    const conversionsData = await conversionsResult.json();
    const revenueData = await revenueResult.json();
    const activeCampaignsData = await activeCampaignsResult.json();
    const activeWebsitesData = await activeWebsitesResult.json();
    const topCampaignsData = await topCampaignsResult.json();
    const recentActivityData = await recentActivityResult.json();

    const stats = {
      impressions_last_hour: parseInt(impressionsData.data[0]?.count || 0),
      clicks_last_hour: parseInt(clicksData.data[0]?.count || 0),
      conversions_last_hour: parseInt(conversionsData.data[0]?.count || 0),
      revenue_last_hour: parseFloat(revenueData.data[0]?.revenue || 0),
      active_campaigns: parseInt(activeCampaignsData.data[0]?.count || 0),
      active_websites: parseInt(activeWebsitesData.data[0]?.count || 0),
      blocked_impressions: 0, // Would track from fraud detection logs
      blocked_clicks: 0, // Would track from fraud detection logs
      top_campaigns: topCampaignsData.data.map((campaign: any) => ({
        id: campaign.id,
        name: campaign.name,
        impressions: parseInt(campaign.impressions || 0),
        clicks: parseInt(campaign.clicks || 0),
        ctr: parseFloat(campaign.ctr || 0),
      })),
      recent_activity: recentActivityData.data.map((activity: any) => ({
        type: activity.type,
        message: activity.message,
        timestamp: activity.timestamp,
      })),
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Realtime stats error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
