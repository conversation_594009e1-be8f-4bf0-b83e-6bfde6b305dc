import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT id, name, subject, body, type, variables, event, created_at, updated_at
        FROM email_templates
        ORDER BY created_at DESC
      `,
    });

    const templates = await result.json();
    const formattedTemplates = templates.data.map((template: any) => ({
      ...template,
      variables: JSON.parse(template.variables || '[]'),
    }));

    return NextResponse.json(formattedTemplates);

  } catch (error) {
    console.error('Email templates fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { name, type, subject, body, event } = await request.json();

    if (!name || !type || !subject || !body) {
      return NextResponse.json(
        { message: 'Name, type, subject, and body are required' },
        { status: 400 }
      );
    }

    const validTypes = [
      'email_verification', 'welcome_advertiser', 'welcome_publisher',
      'account_created', 'campaign_approved', 'campaign_rejected',
      'website_approved', 'website_rejected', 'funds_deposited',
      'payout_processed', 'funds_withdrawal', 'password_reset',
      'payment_reminder', 'support_ticket_created', 'support_ticket_replied'
    ];

    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { message: 'Invalid template type' },
        { status: 400 }
      );
    }

    // Extract variables from template body
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;
    while ((match = variableRegex.exec(body)) !== null) {
      if (!variables.includes(`{{${match[1]}}}`)) {
        variables.push(`{{${match[1]}}}`);
      }
    }

    // Check if template with this type already exists
    const existingResult = await clickhouse.query({
      query: 'SELECT id FROM email_templates WHERE type = {type:String}',
      query_params: { type },
    });

    const existing = await existingResult.json();
    if (existing.data.length > 0) {
      return NextResponse.json(
        { message: 'Template with this type already exists' },
        { status: 400 }
      );
    }

    // Generate template ID
    const templateId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create email template
    await clickhouse.insert({
      table: 'email_templates',
      values: [{
        id: templateId,
        name,
        type,
        subject,
        body,
        variables: JSON.stringify(variables),
        event: event || '',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'email_template_create',
        target_id: templateId,
        target_type: 'email_template',
        reason: `Created email template: ${name}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Email template created successfully',
      template_id: templateId,
    });

  } catch (error) {
    console.error('Email template creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
