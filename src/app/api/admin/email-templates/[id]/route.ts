import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { name, type, subject, body, event } = await request.json();
    const { id } = await params;
    const templateId = parseInt(id);

    if (!name || !type || !subject || !body) {
      return NextResponse.json(
        { message: 'Name, type, subject, and body are required' },
        { status: 400 }
      );
    }

    const validTypes = [
      'email_verification', 'welcome_advertiser', 'welcome_publisher',
      'account_created', 'campaign_approved', 'campaign_rejected',
      'website_approved', 'website_rejected', 'funds_deposited',
      'payout_processed', 'funds_withdrawal', 'password_reset',
      'payment_reminder', 'support_ticket_created', 'support_ticket_replied'
    ];

    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { message: 'Invalid template type' },
        { status: 400 }
      );
    }

    // Extract variables from template body
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;
    while ((match = variableRegex.exec(body)) !== null) {
      if (!variables.includes(`{{${match[1]}}}`)) {
        variables.push(`{{${match[1]}}}`);
      }
    }

    // Check if template exists
    const existingResult = await clickhouse.query({
      query: 'SELECT id FROM email_templates WHERE id = {templateId:UInt64}',
      query_params: { templateId },
    });

    const existing = await existingResult.json();
    if (existing.data.length === 0) {
      return NextResponse.json(
        { message: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if another template with this type exists (excluding current one)
    const duplicateResult = await clickhouse.query({
      query: 'SELECT id FROM email_templates WHERE type = {type:String} AND id != {templateId:UInt64}',
      query_params: { type, templateId },
    });

    const duplicate = await duplicateResult.json();
    if (duplicate.data.length > 0) {
      return NextResponse.json(
        { message: 'Another template with this type already exists' },
        { status: 400 }
      );
    }

    // Update email template (excluding type as it cannot be updated in ClickHouse)
    await clickhouse.command({
      query: `
        ALTER TABLE email_templates
        UPDATE
          name = {name:String},
          subject = {subject:String},
          body = {body:String},
          variables = {variables:String},
          event = {event:String},
          updated_at = {updatedAt:String}
        WHERE id = {templateId:UInt64}
      `,
      query_params: {
        name,
        subject,
        body,
        variables: JSON.stringify(variables),
        event: event || '',
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        templateId,
      },
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'email_template_update',
        target_id: templateId,
        target_type: 'email_template',
        reason: `Updated email template: ${name}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Email template updated successfully',
    });

  } catch (error) {
    console.error('Email template update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const templateId = parseInt(id);

    // Check if template exists
    const existingResult = await clickhouse.query({
      query: 'SELECT name FROM email_templates WHERE id = {templateId:UInt64}',
      query_params: { templateId },
    });

    const existing = await existingResult.json();
    if (existing.data.length === 0) {
      return NextResponse.json(
        { message: 'Template not found' },
        { status: 404 }
      );
    }

    const templateName = existing.data[0].name;

    // Delete email template
    await clickhouse.command({
      query: 'ALTER TABLE email_templates DELETE WHERE id = {templateId:UInt64}',
      query_params: { templateId },
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'email_template_delete',
        target_id: templateId,
        target_type: 'email_template',
        reason: `Deleted email template: ${templateName}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Email template deleted successfully',
    });

  } catch (error) {
    console.error('Email template deletion error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
