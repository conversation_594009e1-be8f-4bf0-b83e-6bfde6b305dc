import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT id, name, type, endpoint_url, status, targeting,
               created_at, updated_at, protocol, openrtb_version,
               api_key, timeout_ms, qps_limit, revenue_share,
               seat_id, test_mode, auth_type, auth_credentials,
               user_id, auction_type, bid_price_format
        FROM partner_endpoints
        ORDER BY created_at DESC
      `,
    });

    const endpoints = await result.json();

    // Parse JSON fields for each endpoint
    const parsedEndpoints = endpoints.data.map((endpoint: any) => {
      let targeting = {};
      let authCredentials = {};

      // Parse targeting JSON
      if (endpoint.targeting) {
        try {
          targeting = JSON.parse(endpoint.targeting);
        } catch (e) {
          console.error('Failed to parse targeting for endpoint', endpoint.id, ':', e);
        }
      }

      // Parse auth_credentials JSON
      if (endpoint.auth_credentials) {
        try {
          authCredentials = JSON.parse(endpoint.auth_credentials);
        } catch (e) {
          console.error('Failed to parse auth_credentials for endpoint', endpoint.id, ':', e);
        }
      }

      return {
        ...endpoint,
        targeting: targeting,
        auth_credentials: authCredentials,
      };
    });

    return NextResponse.json(parsedEndpoints);

  } catch (error) {
    console.error('Partner endpoints fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      name,
      type,
      user_id,
      endpoint_url,
      targeting,
      protocol,
      openrtb_version,
      api_key,
      timeout_ms,
      qps_limit,
      revenue_share,
      seat_id,
      test_mode,
      auth_type,
      auth_credentials,
      auction_type,
      bid_price_format
    } = await request.json();

    if (!name || !type || !user_id || !protocol) {
      return NextResponse.json(
        { message: 'Name, type, user, and protocol are required' },
        { status: 400 }
      );
    }

    // For DSP, endpoint_url is required (their endpoint)
    // For SSP, we generate the endpoint_url (our platform endpoint)
    if (type === 'dsp' && !endpoint_url) {
      return NextResponse.json(
        { message: 'Endpoint URL is required for DSP partners' },
        { status: 400 }
      );
    }

    if (!['dsp', 'ssp'].includes(type)) {
      return NextResponse.json(
        { message: 'Type must be dsp or ssp' },
        { status: 400 }
      );
    }

    if (!['openrtb', 'xml'].includes(protocol)) {
      return NextResponse.json(
        { message: 'Protocol must be openrtb or xml' },
        { status: 400 }
      );
    }

    // Validate user exists and has correct role
    const userResult = await clickhouse.query({
      query: 'SELECT id, role FROM users WHERE id = {userId:UInt32}',
      query_params: { userId: parseInt(user_id) },
    });

    const users = await userResult.json();
    if (users.data.length === 0) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    const user = users.data[0];
    if (user.role !== type) {
      return NextResponse.json(
        { message: `Selected user must have ${type.toUpperCase()} role` },
        { status: 400 }
      );
    }

    // Generate endpoint ID
    const lastEndpointResult = await clickhouse.query({
      query: 'SELECT MAX(id) as max_id FROM partner_endpoints',
    });

    const lastEndpointData = await lastEndpointResult.json();
    const lastId = lastEndpointData.data[0]?.max_id || 0;
    const newEndpointId = lastId + 1;

    // Generate endpoint URL for SSP (our platform endpoints for inventory submission)
    let finalEndpointUrl = endpoint_url;
    if (type === 'ssp') {
      const baseUrl = process.env.NEXTAUTH_URL || process.env.PLATFORM_URL || 'https://your-domain.com';

      if (protocol === 'openrtb') {
        // OpenRTB uses standard JSON format, no URL macros needed
        finalEndpointUrl = `${baseUrl}/api/ssp/inventory?partner_id=${newEndpointId}`;
      } else if (protocol === 'xml') {
        // XML endpoints support URL macros for better targeting
        const macroParams = '&ua=[USER_AGENT]&ip=[USER_IP]&subid=[ZONE_ID]&lang=[USER_LANG]&format=[AD_FORMATS]&ref=[PAGE_URL]&country=[COUNTRY]&device=[DEVICE_TYPE]&os_ver=[OS_VERSION]&browser=[BROWSER]&region=[REGION]&city=[CITY]';
        finalEndpointUrl = `${baseUrl}/api/ssp/inventory-xml?partner_id=${newEndpointId}${macroParams}`;
      }
    }

    // Revenue share logic: only for SSP (we pay them), DSP pays us
    const finalRevenueShare = type === 'ssp' ? (revenue_share || 0) : 0;

    // Create endpoint
    await clickhouse.insert({
      table: 'partner_endpoints',
      values: [{
        id: newEndpointId,
        name,
        type,
        user_id: parseInt(user_id),
        endpoint_url: finalEndpointUrl,
        status: 'active',
        targeting: JSON.stringify(targeting || {}),
        protocol: protocol || 'openrtb',
        openrtb_version: openrtb_version || '2.5',
        api_key: api_key || '',
        timeout_ms: timeout_ms || 300,
        qps_limit: qps_limit || 100,
        revenue_share: finalRevenueShare,
        seat_id: seat_id || '',
        test_mode: test_mode || 0,
        auth_type: auth_type || 'none',
        auth_credentials: JSON.stringify(auth_credentials || {}),
        auction_type: auction_type || 'first_price',
        bid_price_format: bid_price_format || 'cpm',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'partner_endpoint_create',
        target_id: newEndpointId,
        target_type: 'partner_endpoint',
        reason: `Created ${type.toUpperCase()} endpoint: ${name}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Partner endpoint created successfully',
      endpoint_id: newEndpointId,
    });

  } catch (error) {
    console.error('Partner endpoint creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      id,
      name,
      type,
      user_id,
      endpoint_url,
      targeting,
      protocol,
      openrtb_version,
      api_key,
      timeout_ms,
      qps_limit,
      revenue_share,
      seat_id,
      test_mode,
      auth_type,
      auth_credentials,
      status,
      auction_type,
      bid_price_format
    } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Endpoint ID is required' },
        { status: 400 }
      );
    }

    // Build update query dynamically
    const updates: string[] = [];
    const queryParams: any = { endpointId: parseInt(id) };

    if (name) {
      updates.push('name = {name:String}');
      queryParams.name = name;
    }
    if (type && ['dsp', 'ssp'].includes(type)) {
      updates.push('type = {type:String}');
      queryParams.type = type;
    }
    if (user_id) {
      updates.push('user_id = {user_id:UInt32}');
      queryParams.user_id = parseInt(user_id);
    }
    if (endpoint_url) {
      updates.push('endpoint_url = {endpoint_url:String}');
      queryParams.endpoint_url = endpoint_url;
    }
    if (targeting !== undefined) {
      updates.push('targeting = {targeting:String}');
      queryParams.targeting = JSON.stringify(targeting);
    }
    if (protocol && ['openrtb', 'xml'].includes(protocol)) {
      updates.push('protocol = {protocol:String}');
      queryParams.protocol = protocol;
    }
    if (openrtb_version) {
      updates.push('openrtb_version = {openrtb_version:String}');
      queryParams.openrtb_version = openrtb_version;
    }
    if (api_key !== undefined) {
      updates.push('api_key = {api_key:String}');
      queryParams.api_key = api_key;
    }
    if (timeout_ms !== undefined) {
      updates.push('timeout_ms = {timeout_ms:UInt32}');
      queryParams.timeout_ms = timeout_ms;
    }
    if (qps_limit !== undefined) {
      updates.push('qps_limit = {qps_limit:UInt32}');
      queryParams.qps_limit = qps_limit;
    }
    if (revenue_share !== undefined) {
      updates.push('revenue_share = {revenue_share:Decimal(5,2)}');
      queryParams.revenue_share = revenue_share;
    }
    if (seat_id !== undefined) {
      updates.push('seat_id = {seat_id:String}');
      queryParams.seat_id = seat_id;
    }
    if (test_mode !== undefined) {
      updates.push('test_mode = {test_mode:UInt8}');
      queryParams.test_mode = test_mode ? 1 : 0;
    }
    if (auth_type) {
      updates.push('auth_type = {auth_type:String}');
      queryParams.auth_type = auth_type;
    }
    if (auth_credentials !== undefined) {
      updates.push('auth_credentials = {auth_credentials:String}');
      queryParams.auth_credentials = JSON.stringify(auth_credentials);
    }
    if (status && ['active', 'inactive', 'testing'].includes(status)) {
      updates.push('status = {status:String}');
      queryParams.status = status;
    }
    if (auction_type && ['first_price', 'second_price'].includes(auction_type)) {
      updates.push('auction_type = {auction_type:String}');
      queryParams.auction_type = auction_type;
    }
    if (bid_price_format && ['cpm', 'cpv'].includes(bid_price_format)) {
      updates.push('bid_price_format = {bid_price_format:String}');
      queryParams.bid_price_format = bid_price_format;
    }

    if (updates.length === 0) {
      return NextResponse.json(
        { message: 'No valid fields to update' },
        { status: 400 }
      );
    }

    updates.push('updated_at = {updated_at:DateTime}');
    queryParams.updated_at = new Date().toISOString().slice(0, 19).replace('T', ' ');

    await clickhouse.command({
      query: `
        ALTER TABLE partner_endpoints
        UPDATE ${updates.join(', ')}
        WHERE id = {endpointId:UInt32}
      `,
      query_params: queryParams,
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'partner_endpoint_update',
        target_id: parseInt(id),
        target_type: 'partner_endpoint',
        reason: `Updated partner endpoint: ${name || id}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Partner endpoint updated successfully',
    });

  } catch (error) {
    console.error('Partner endpoint update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: 'Endpoint ID is required' },
        { status: 400 }
      );
    }

    // Get endpoint details for logging
    const endpointResult = await clickhouse.query({
      query: 'SELECT name FROM partner_endpoints WHERE id = {id:UInt32}',
      query_params: { id: parseInt(id) },
    });

    const endpointData = await endpointResult.json();
    const endpointName = endpointData.data[0]?.name || id;

    // Delete endpoint
    await clickhouse.command({
      query: 'ALTER TABLE partner_endpoints DELETE WHERE id = {id:UInt32}',
      query_params: { id: parseInt(id) },
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'partner_endpoint_delete',
        target_id: parseInt(id),
        target_type: 'partner_endpoint',
        reason: `Deleted partner endpoint: ${endpointName}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Partner endpoint deleted successfully',
    });

  } catch (error) {
    console.error('Partner endpoint deletion error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
