import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { endpoint_id, request_method, content_type, request_body } = await request.json();

    if (!endpoint_id) {
      return NextResponse.json(
        { message: 'Endpoint ID is required' },
        { status: 400 }
      );
    }

    // Get endpoint details
    const endpointResult = await clickhouse.query({
      query: `
        SELECT id, name, type, endpoint_url, protocol, openrtb_version,
               api_key, timeout_ms, auth_type, auth_credentials
        FROM partner_endpoints
        WHERE id = {endpoint_id:UInt32}
      `,
      query_params: { endpoint_id: parseInt(endpoint_id) },
    });

    const endpointData = await endpointResult.json();
    const endpoint = endpointData.data[0];

    if (!endpoint) {
      return NextResponse.json(
        { message: 'Endpoint not found' },
        { status: 404 }
      );
    }

    let testResult: any = {
      success: false,
      response_time: 0,
      status_code: 0,
      response_body: '',
      error: null,
      request_sent: {
        method: request_method,
        content_type: content_type,
        body: request_body,
        url: endpoint.endpoint_url,
      },
    };

    const startTime = Date.now();

    try {
      let finalEndpointUrl = endpoint.endpoint_url;

      // Handle XML DSP endpoints with platform macro replacement
      if (endpoint.protocol === 'xml' && endpoint.type === 'dsp') {
        // Use realistic test data that simulates real impression
        const platformMacros = {
          '{user_agent}': encodeURIComponent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'),
          '{ip_address}': '*************',
          '{zone_id}': '123',
          '{user_lang}': 'en-US',
          '{ad_format}': 'popup',
          '{page_url}': encodeURIComponent('https://news.example.com/article/breaking-news'),
          '{os_version}': '10.0',
          '{timestamp}': Date.now().toString(),
          '{country}': 'USA',
          '{region}': 'CA',
          '{city}': 'San Francisco',
          '{device_type}': 'desktop',
          '{os}': 'Windows',
          '{browser}': 'Chrome',
          '{click_id}': `click_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          '{campaign_id}': '456',
          '{website_id}': '789',
        };

        // Replace platform macros in URL
        Object.entries(platformMacros).forEach(([macro, value]) => {
          finalEndpointUrl = finalEndpointUrl.replace(new RegExp(macro.replace(/[{}]/g, '\\$&'), 'g'), value);
        });

        testResult.request_sent.url = finalEndpointUrl;
        testResult.request_sent.macros_replaced = platformMacros;
      }

      // Prepare realistic headers
      const headers: any = {
        'User-Agent': 'Global-Ads-Media-RTB/1.0 (+https://global-ads-media.com/rtb)',
        'Accept': endpoint.protocol === 'openrtb' ? 'application/json' : 'application/xml, text/xml',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'X-Forwarded-For': '*************',
        'X-Real-IP': '*************',
      };

      // Add content type if POST request
      if (request_method === 'POST' && content_type) {
        headers['Content-Type'] = content_type;
      }

      // Add OpenRTB specific headers
      if (endpoint.protocol === 'openrtb') {
        headers['X-OpenRTB-Version'] = endpoint.openrtb_version || '2.5';
        headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Add authentication if configured
      if (endpoint.auth_type === 'api_key' && endpoint.api_key) {
        headers['X-API-Key'] = endpoint.api_key;
      } else if (endpoint.auth_type === 'bearer' && endpoint.api_key) {
        headers['Authorization'] = `Bearer ${endpoint.api_key}`;
      }

      // Prepare fetch options
      const fetchOptions: any = {
        method: request_method,
        headers,
        signal: AbortSignal.timeout(endpoint.timeout_ms || 10000),
      };

      // Add body for POST requests
      if (request_method === 'POST' && request_body) {
        fetchOptions.body = request_body;
      }

      // Send the request
      const response = await fetch(finalEndpointUrl, fetchOptions);

      testResult.status_code = response.status;
      testResult.response_time = Date.now() - startTime;
      testResult.response_body = await response.text();
      testResult.success = response.ok;

      // Try to format JSON response for better readability
      if (content_type === 'application/json' || response.headers.get('content-type')?.includes('json')) {
        try {
          const jsonResponse = JSON.parse(testResult.response_body);
          testResult.response_body = JSON.stringify(jsonResponse, null, 2);
        } catch (e) {
          // Keep as text if not valid JSON
        }
      }

    } catch (error: any) {
      testResult.response_time = Date.now() - startTime;
      testResult.error = error.message;

      if (error.name === 'TimeoutError') {
        testResult.error = 'Request timeout';
      } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
        testResult.error = 'Network error or invalid URL';
      }
    }

    // Log the test
    try {
      await clickhouse.insert({
        table: 'admin_actions',
        values: [{
          id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
          admin_id: parseInt(session.user.id),
          action_type: 'partner_endpoint_custom_test',
          target_id: parseInt(endpoint_id),
          target_type: 'partner_endpoint',
          reason: `Custom test of endpoint: ${endpoint.name} - ${testResult.success ? 'Success' : 'Failed'} (${request_method} ${content_type})`,
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });
    } catch (logError) {
      console.error('Failed to log test action:', logError);
      // Don't fail the test if logging fails
    }

    return NextResponse.json({
      message: 'Custom endpoint test completed',
      test_result: testResult,
      endpoint: {
        id: endpoint.id,
        name: endpoint.name,
        type: endpoint.type,
        protocol: endpoint.protocol,
        url: endpoint.endpoint_url,
      },
    });

  } catch (error) {
    console.error('Custom endpoint test error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
