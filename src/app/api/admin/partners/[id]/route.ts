import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const endpointId = parseInt(resolvedParams.id);

    console.log('API: Fetching endpoint with ID:', endpointId);

    if (isNaN(endpointId)) {
      return NextResponse.json(
        { message: 'Invalid endpoint ID' },
        { status: 400 }
      );
    }

    // Get endpoint details
    const endpointResult = await clickhouse.query({
      query: `
        SELECT
          id,
          name,
          type,
          user_id,
          endpoint_url,
          protocol,
          openrtb_version,
          api_key,
          timeout_ms,
          qps_limit,
          revenue_share,
          seat_id,
          test_mode,
          auth_type,
          auth_credentials,
          status,
          created_at,
          auction_type,
          bid_price_format
        FROM partner_endpoints
        WHERE id = {endpoint_id:UInt32}
      `,
      query_params: { endpoint_id: endpointId },
    });

    const endpointData = await endpointResult.json();
    const endpoint = endpointData.data[0];

    if (!endpoint) {
      return NextResponse.json(
        { message: 'Endpoint not found' },
        { status: 404 }
      );
    }

    // Parse auth_credentials if it exists
    let authCredentials = {};
    if (endpoint.auth_credentials) {
      try {
        authCredentials = JSON.parse(endpoint.auth_credentials);
      } catch (e) {
        console.error('Failed to parse auth_credentials:', e);
      }
    }

    // Parse targeting if it exists
    let targeting = {};
    if (endpoint.targeting) {
      try {
        targeting = JSON.parse(endpoint.targeting);
      } catch (e) {
        console.error('Failed to parse targeting:', e);
      }
    }

    const responseData = {
      ...endpoint,
      auth_credentials: authCredentials,
      targeting: targeting,
      user_name: null, // Will be populated if needed
      user_email: null, // Will be populated if needed
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Get endpoint error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
