import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'dsp' or 'ssp'

    let query = `
      SELECT id, full_name, email, role, status, created_at
      FROM users 
      WHERE role IN ('dsp', 'ssp')
      AND status = 'active'
    `;

    const queryParams: any = {};

    if (type && ['dsp', 'ssp'].includes(type)) {
      query += ` AND role = {type:String}`;
      queryParams.type = type;
    }

    query += ` ORDER BY full_name ASC`;

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const users = await result.json();

    return NextResponse.json(users.data);

  } catch (error) {
    console.error('Partner users fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
