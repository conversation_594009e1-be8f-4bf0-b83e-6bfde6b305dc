import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { endpoint_id } = await request.json();

    if (!endpoint_id) {
      return NextResponse.json(
        { message: 'Endpoint ID is required' },
        { status: 400 }
      );
    }

    // Get endpoint details
    const endpointResult = await clickhouse.query({
      query: `
        SELECT id, name, type, endpoint_url, protocol, openrtb_version, 
               api_key, timeout_ms, auth_type, auth_credentials
        FROM partner_endpoints 
        WHERE id = {endpoint_id:UInt32}
      `,
      query_params: { endpoint_id: parseInt(endpoint_id) },
    });

    const endpointData = await endpointResult.json();
    const endpoint = endpointData.data[0];

    if (!endpoint) {
      return NextResponse.json(
        { message: 'Endpoint not found' },
        { status: 404 }
      );
    }

    let testResult: any = {
      success: false,
      response_time: 0,
      status_code: 0,
      response_body: '',
      error: null,
    };

    const startTime = Date.now();

    try {
      if (endpoint.protocol === 'openrtb') {
        // Test OpenRTB endpoint
        const testBidRequest = {
          id: `test_${Date.now()}`,
          imp: [{
            id: '1',
            banner: {
              w: 300,
              h: 250,
            },
            bidfloor: 0.001,
            bidfloorcur: 'USD',
          }],
          site: {
            id: 'test_site',
            name: 'Test Site',
            domain: 'test.example.com',
            publisher: {
              id: 'test_pub',
              name: 'Test Publisher',
            },
          },
          device: {
            ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ip: '***********',
            geo: {
              country: 'US',
              region: 'CA',
              city: 'San Francisco',
            },
          },
          user: {
            id: 'test_user',
          },
          at: 1, // First price auction
          tmax: 100,
          cur: ['USD'],
        };

        const headers: any = {
          'Content-Type': 'application/json',
          'User-Agent': 'Global-Ads-Media/1.0',
        };

        // Add authentication if configured
        if (endpoint.auth_type === 'api_key' && endpoint.api_key) {
          headers['X-API-Key'] = endpoint.api_key;
        } else if (endpoint.auth_type === 'bearer' && endpoint.api_key) {
          headers['Authorization'] = `Bearer ${endpoint.api_key}`;
        }

        const response = await fetch(endpoint.endpoint_url, {
          method: 'POST',
          headers,
          body: JSON.stringify(testBidRequest),
          signal: AbortSignal.timeout(endpoint.timeout_ms || 5000),
        });

        testResult.status_code = response.status;
        testResult.response_time = Date.now() - startTime;
        testResult.response_body = await response.text();
        testResult.success = response.ok;

        // Try to parse JSON response
        try {
          const jsonResponse = JSON.parse(testResult.response_body);
          testResult.response_body = JSON.stringify(jsonResponse, null, 2);
        } catch (e) {
          // Keep as text if not valid JSON
        }

      } else if (endpoint.protocol === 'xml') {
        // Test XML endpoint
        const response = await fetch(endpoint.endpoint_url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Global-Ads-Media/1.0',
            'Accept': 'application/xml, text/xml',
          },
          signal: AbortSignal.timeout(endpoint.timeout_ms || 5000),
        });

        testResult.status_code = response.status;
        testResult.response_time = Date.now() - startTime;
        testResult.response_body = await response.text();
        testResult.success = response.ok;
      }

    } catch (error: any) {
      testResult.response_time = Date.now() - startTime;
      testResult.error = error.message;
      
      if (error.name === 'TimeoutError') {
        testResult.error = 'Request timeout';
      } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
        testResult.error = 'Network error or invalid URL';
      }
    }

    // Log the test
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'partner_endpoint_test',
        target_id: parseInt(endpoint_id),
        target_type: 'partner_endpoint',
        reason: `Tested endpoint: ${endpoint.name} - ${testResult.success ? 'Success' : 'Failed'}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Endpoint test completed',
      test_result: testResult,
      endpoint: {
        id: endpoint.id,
        name: endpoint.name,
        type: endpoint.type,
        protocol: endpoint.protocol,
      },
    });

  } catch (error) {
    console.error('Endpoint test error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
