import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const advertiserId = searchParams.get('advertiser_id');
    const publisherId = searchParams.get('publisher_id');
    const campaignId = searchParams.get('campaign_id');
    const websiteId = searchParams.get('website_id');
    const groupBy = searchParams.get('group_by') || 'day';

    let stats: any[] = [];
    let query = '';
    let queryParams: any = { dateFrom, dateTo };

    if (groupBy === 'day') {
      query = `
        SELECT
          DATE(i.timestamp) as date,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue, -- Platform commission
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY DATE(i.timestamp) ORDER BY date DESC';

    } else if (groupBy === 'advertiser') {
      query = `
        SELECT
          u.id as advertiser_id,
          u.full_name as advertiser_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue, -- Platform commission from advertiser
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM users u
        LEFT JOIN campaigns c ON u.id = c.user_id
        LEFT JOIN impressions i ON c.id = i.campaign_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        WHERE u.role = 'advertiser'
      `;

      if (publisherId) {
        query += ' AND i.website_id IN (SELECT id FROM websites WHERE user_id = {publisherId:UInt32})';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND i.website_id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY u.id, u.full_name ORDER BY impressions DESC';

    } else if (groupBy === 'publisher') {
      query = `
        SELECT
          u.id as publisher_id,
          u.full_name as publisher_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.publisher_revenue) as revenue, -- Publisher sees their actual earnings
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM users u
        LEFT JOIN websites w ON u.id = w.user_id
        LEFT JOIN impressions i ON w.id = i.website_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        WHERE u.role = 'publisher'
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY u.id, u.full_name ORDER BY impressions DESC';

    } else if (groupBy === 'campaign') {
      query = `
        SELECT
          c.id as campaign_id,
          c.name as campaign_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue, -- Platform commission from campaign
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM campaigns c
        LEFT JOIN impressions i ON c.id = i.campaign_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        WHERE 1=1
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND i.website_id IN (SELECT id FROM websites WHERE user_id = {publisherId:UInt32})';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (websiteId) {
        query += ' AND i.website_id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY c.id, c.name ORDER BY impressions DESC';

    } else if (groupBy === 'website') {
      query = `
        SELECT
          w.id as website_id,
          w.name as website_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.publisher_revenue) as revenue, -- Publisher sees their actual earnings
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM websites w
        LEFT JOIN impressions i ON w.id = i.website_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        WHERE 1=1
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }

      query += ' GROUP BY w.id, w.name ORDER BY impressions DESC';

    } else if (groupBy === 'country') {
      query = `
        SELECT
          i.country as country,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.country ORDER BY impressions DESC';

    } else if (groupBy === 'os') {
      query = `
        SELECT
          i.os as os,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.os ORDER BY impressions DESC';

    } else if (groupBy === 'browser') {
      query = `
        SELECT
          i.browser as browser,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.browser ORDER BY impressions DESC';

    } else if (groupBy === 'device') {
      query = `
        SELECT
          i.device_type as device,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.device_type ORDER BY impressions DESC';
    }

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const data = await result.json();
    stats = data.data || [];

    // Fill in missing values and ensure proper formatting
    stats = stats.map((stat: any) => ({
      ...stat,
      impressions: parseInt(stat.impressions || 0),
      clicks: parseInt(stat.clicks || 0),
      conversions: parseInt(stat.conversions || 0),
      revenue: parseFloat(stat.revenue || 0),
      ctr: parseFloat(stat.ctr || 0),
    }));

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Admin detailed statistics error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
