import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const advertiserId = searchParams.get('advertiser_id');
    const publisherId = searchParams.get('publisher_id');
    const campaignId = searchParams.get('campaign_id');
    const websiteId = searchParams.get('website_id');
    const groupBy = searchParams.get('group_by') || 'day';

    let stats: any[] = [];
    let csvHeaders: string[] = [];
    let query = '';
    let queryParams: any = { dateFrom, dateTo };

    if (groupBy === 'day') {
      csvHeaders = ['Date', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          DATE(i.timestamp) as date,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(c.cpm_bid / 1000) as revenue
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY DATE(i.timestamp) ORDER BY date DESC';

    } else if (groupBy === 'advertiser') {
      csvHeaders = ['Advertiser ID', 'Advertiser Name', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          u.id as advertiser_id,
          u.full_name as advertiser_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(c.cpm_bid / 1000) as revenue
        FROM users u
        LEFT JOIN campaigns c ON u.id = c.user_id
        LEFT JOIN impressions i ON c.id = i.campaign_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        WHERE u.role = 'advertiser'
      `;

      if (publisherId) {
        query += ' AND i.website_id IN (SELECT id FROM websites WHERE user_id = {publisherId:UInt32})';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND i.website_id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY u.id, u.full_name ORDER BY impressions DESC';

    } else if (groupBy === 'publisher') {
      csvHeaders = ['Publisher ID', 'Publisher Name', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          u.id as publisher_id,
          u.full_name as publisher_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(i.publisher_revenue) as revenue
        FROM users u
        LEFT JOIN websites w ON u.id = w.user_id
        LEFT JOIN impressions i ON w.id = i.website_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        WHERE u.role = 'publisher'
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY u.id, u.full_name ORDER BY impressions DESC';

    } else if (groupBy === 'campaign') {
      csvHeaders = ['Campaign ID', 'Campaign Name', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          c.id as campaign_id,
          c.name as campaign_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(c.cpm_bid / 1000) as revenue
        FROM campaigns c
        LEFT JOIN impressions i ON c.id = i.campaign_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        WHERE 1=1
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND i.website_id IN (SELECT id FROM websites WHERE user_id = {publisherId:UInt32})';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (websiteId) {
        query += ' AND i.website_id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY c.id, c.name ORDER BY impressions DESC';

    } else if (groupBy === 'website') {
      csvHeaders = ['Website ID', 'Website Name', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          w.id as website_id,
          w.name as website_name,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(i.publisher_revenue) as revenue
        FROM websites w
        LEFT JOIN impressions i ON w.id = i.website_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        WHERE 1=1
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }

      query += ' GROUP BY w.id, w.name ORDER BY impressions DESC';

    } else if (groupBy === 'country') {
      csvHeaders = ['Country', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          i.country,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.country ORDER BY impressions DESC';

    } else if (groupBy === 'os') {
      csvHeaders = ['Operating System', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          i.os,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.os ORDER BY impressions DESC';

    } else if (groupBy === 'browser') {
      csvHeaders = ['Browser', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          i.browser,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.browser ORDER BY impressions DESC';

    } else if (groupBy === 'device') {
      csvHeaders = ['Device Type', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'Revenue ($)'];
      query = `
        SELECT
          i.device_type as device,
          COUNT(DISTINCT i.id) as impressions,
          SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
          SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
          (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
          SUM(i.cost_deducted) - SUM(i.publisher_revenue) as revenue
        FROM impressions i
        LEFT JOIN clicks cl ON i.id = cl.impression_id
        LEFT JOIN conversions cv ON cl.id = cv.click_id
        LEFT JOIN campaigns c ON i.campaign_id = c.id
        LEFT JOIN websites w ON i.website_id = w.id
        WHERE DATE(i.timestamp) >= {dateFrom:String}
          AND DATE(i.timestamp) <= {dateTo:String}
      `;

      if (advertiserId) {
        query += ' AND c.user_id = {advertiserId:UInt32}';
        queryParams.advertiserId = parseInt(advertiserId);
      }
      if (publisherId) {
        query += ' AND w.user_id = {publisherId:UInt32}';
        queryParams.publisherId = parseInt(publisherId);
      }
      if (campaignId) {
        query += ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }
      if (websiteId) {
        query += ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      query += ' GROUP BY i.device_type ORDER BY impressions DESC';
    }

    const result = await clickhouse.query({
      query,
      query_params: queryParams,
    });

    const data = await result.json();
    stats = data.data || [];

    // Generate CSV content
    let csvContent = csvHeaders.join(',') + '\n';

    stats.forEach((stat: any) => {
      const row: string[] = [];

      if (groupBy === 'day') {
        row.push(
          stat.date || '',
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'advertiser') {
        row.push(
          (stat.advertiser_id || '').toString(),
          `"${stat.advertiser_name || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'publisher') {
        row.push(
          (stat.publisher_id || '').toString(),
          `"${stat.publisher_name || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'campaign') {
        row.push(
          (stat.campaign_id || '').toString(),
          `"${stat.campaign_name || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'website') {
        row.push(
          (stat.website_id || '').toString(),
          `"${stat.website_name || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'country') {
        row.push(
          `"${stat.country || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'os') {
        row.push(
          `"${stat.os || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'browser') {
        row.push(
          `"${stat.browser || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      } else if (groupBy === 'device') {
        row.push(
          `"${stat.device || ''}"`,
          (stat.impressions || 0).toString(),
          (stat.clicks || 0).toString(),
          (stat.conversions || 0).toString(),
          parseFloat(stat.ctr || 0).toFixed(2),
          parseFloat(stat.revenue || 0).toFixed(2)
        );
      }

      csvContent += row.join(',') + '\n';
    });

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="admin-statistics-${dateFrom}-${dateTo}.csv"`,
      },
    });

  } catch (error) {
    console.error('Admin statistics export error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
