import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const priority = searchParams.get('priority');

    let whereConditions = ['1=1'];
    let queryParams: any = {};

    // Only filter by status if it's provided and not 'all'
    if (status && status !== 'all') {
      whereConditions.push('st.status = {status:String}');
      queryParams.status = status;
    }

    // Only filter by category if it's provided and not 'all'
    if (category && category !== 'all') {
      whereConditions.push('st.category = {category:String}');
      queryParams.category = category;
    }

    // Only filter by priority if it's provided and not 'all'
    if (priority && priority !== 'all') {
      whereConditions.push('st.priority = {priority:String}');
      queryParams.priority = priority;
    }

    const whereClause = whereConditions.join(' AND ');



    const result = await clickhouse.query({
      query: `
        SELECT
          st.id as id,
          st.user_id as user_id,
          u.full_name as user_name,
          u.role as user_role,
          st.subject as subject,
          st.message as message,
          st.priority as priority,
          st.status as status,
          st.category as category,
          toString(st.created_at) as created_at,
          toString(st.updated_at) as updated_at,
          COUNT(str.id) as reply_count
        FROM support_tickets st
        LEFT JOIN users u ON st.user_id = u.id
        LEFT JOIN support_ticket_replies str ON st.id = str.ticket_id
        WHERE ${whereClause}
        GROUP BY st.id, st.user_id, u.full_name, u.role, st.subject, st.message, st.priority, st.status, st.category, st.created_at, st.updated_at
        ORDER BY
          CASE st.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
          END,
          st.created_at DESC
      `,
      query_params: queryParams,
    });

    const tickets = await result.json();
    return NextResponse.json(tickets.data || []);

  } catch (error) {
    console.error('Admin support tickets fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
