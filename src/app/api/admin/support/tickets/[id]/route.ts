import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const ticketId = parseInt(id);

    if (isNaN(ticketId)) {
      return NextResponse.json(
        { message: 'Invalid ticket ID' },
        { status: 400 }
      );
    }

    // Get ticket details
    const ticketResult = await clickhouse.query({
      query: `
        SELECT
          st.id as id,
          st.user_id as user_id,
          u.full_name as user_name,
          u.email as user_email,
          u.role as user_role,
          st.subject as subject,
          st.message as message,
          st.priority as priority,
          st.status as status,
          st.category as category,
          toString(st.created_at) as created_at,
          toString(st.updated_at) as updated_at
        FROM support_tickets st
        LEFT JOIN users u ON st.user_id = u.id
        WHERE st.id = {ticketId:UInt64}
      `,
      query_params: { ticketId },
    });

    const ticketData = await ticketResult.json();

    if (!ticketData.data || ticketData.data.length === 0) {
      return NextResponse.json(
        { message: 'Ticket not found' },
        { status: 404 }
      );
    }

    const ticket = ticketData.data[0];

    // Get ticket replies
    const repliesResult = await clickhouse.query({
      query: `
        SELECT
          str.id as id,
          str.message as message,
          str.is_admin as is_admin,
          toString(str.created_at) as created_at,
          CASE
            WHEN str.is_admin = 1 THEN 'Admin'
            ELSE u.full_name
          END as author_name,
          CASE
            WHEN str.is_admin = 1 THEN 'admin'
            ELSE u.role
          END as author_role
        FROM support_ticket_replies str
        LEFT JOIN users u ON str.user_id = u.id
        WHERE str.ticket_id = {ticketId:UInt64}
        ORDER BY str.created_at ASC
      `,
      query_params: { ticketId },
    });

    const repliesData = await repliesResult.json();
    const replies = repliesData.data || [];

    return NextResponse.json({
      ticket,
      replies,
    });

  } catch (error) {
    console.error('Ticket fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const { status, reply } = await request.json();
    const ticketId = parseInt(id);

    // Handle status update
    if (status && ['open', 'pending', 'resolved', 'closed'].includes(status)) {
      await clickhouse.command({
        query: `
          ALTER TABLE support_tickets
          UPDATE
            status = {status:String},
            updated_at = {updatedAt:String}
          WHERE id = {ticketId:UInt64}
        `,
        query_params: {
          status,
          updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
          ticketId,
        },
      });
    }

    // Handle reply submission
    if (reply && reply.trim()) {
      const replyId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
      await clickhouse.insert({
        table: 'support_ticket_replies',
        values: [{
          id: replyId,
          ticket_id: ticketId,
          user_id: parseInt(session.user.id),
          message: reply.trim(),
          is_admin: 1,
          created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });

      // Update ticket status to pending if it was closed/resolved and no specific status was provided
      if (!status || (status !== 'closed' && status !== 'resolved')) {
        await clickhouse.command({
          query: `
            ALTER TABLE support_tickets
            UPDATE
              status = 'pending',
              updated_at = {updatedAt:String}
            WHERE id = {ticketId:UInt64}
          `,
          query_params: {
            updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
            ticketId,
          },
        });
      }
    }

    // Log the admin action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'ticket_status_update',
        target_id: ticketId,
        target_type: 'support_ticket',
        reason: `Updated ticket status to ${status}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Ticket status updated successfully',
    });

  } catch (error) {
    console.error('Ticket status update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
