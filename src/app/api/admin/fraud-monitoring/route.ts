import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { FraudMonitoring } from '@/lib/fraud-monitoring';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'logs';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sourceType = searchParams.get('source_type') || undefined;
    const publisherId = searchParams.get('publisher_id') ? parseInt(searchParams.get('publisher_id')!) : undefined;
    const days = parseInt(searchParams.get('days') || '7');

    switch (action) {
      case 'logs':
        const logs = await FraudMonitoring.getRecentFraudLogs(limit, offset, sourceType, publisherId);
        return NextResponse.json({ logs });

      case 'statistics':
        const stats = await FraudMonitoring.getFraudStatistics(days);
        return NextResponse.json({ statistics: stats });

      case 'top-reasons':
        const topReasons = await FraudMonitoring.getTopFraudReasons(days);
        return NextResponse.json({ topReasons });

      default:
        return NextResponse.json({ message: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Fraud monitoring API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, fraudId, reason } = body;

    if (action === 'exclude') {
      const success = await FraudMonitoring.excludeFraudDetection(
        fraudId,
        parseInt(session.user.id),
        reason || 'Manual exclusion by admin'
      );

      if (success) {
        return NextResponse.json({ message: 'Fraud detection excluded successfully' });
      } else {
        return NextResponse.json({ message: 'Failed to exclude fraud detection' }, { status: 500 });
      }
    }

    return NextResponse.json({ message: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Fraud monitoring POST error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
