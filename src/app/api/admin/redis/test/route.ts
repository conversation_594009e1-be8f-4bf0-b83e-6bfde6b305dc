import { NextRequest, NextResponse } from 'next/server';
import { cache } from '@/lib/cache';

export async function GET(request: NextRequest) {
  try {
    // Test Redis connection
    const connectionTest = await cache.testConnection();
    
    // Test cache operations if connected
    let cacheTest = null;
    if (connectionTest.connected) {
      try {
        // Test set and get
        const testKey = `test_${Date.now()}`;
        const testValue = { message: 'Hello Redis!', timestamp: new Date().toISOString() };
        
        await cache.set(testKey, testValue, 60); // 1 minute TTL
        const retrievedValue = await cache.get(testKey);
        
        cacheTest = {
          setSuccess: true,
          getSuccess: retrievedValue !== null,
          dataMatch: JSON.stringify(retrievedValue) === JSON.stringify(testValue),
          retrievedValue,
        };
        
        // Clean up test key
        await cache.del(testKey);
      } catch (error) {
        cacheTest = {
          setSuccess: false,
          getSuccess: false,
          dataMatch: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }

    return NextResponse.json({
      status: 'success',
      redis: {
        connection: connectionTest,
        cacheOperations: cacheTest,
      },
      environment: {
        REDIS_HOST: process.env.REDIS_HOST || 'localhost',
        REDIS_PORT: process.env.REDIS_PORT || '6379',
        REDIS_PASSWORD: process.env.REDIS_PASSWORD ? '***SET***' : 'NOT_SET',
        REDIS_URL: process.env.REDIS_URL ? '***SET***' : 'NOT_SET',
      },
      recommendations: getRecommendations(connectionTest),
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      environment: {
        REDIS_HOST: process.env.REDIS_HOST || 'localhost',
        REDIS_PORT: process.env.REDIS_PORT || '6379',
        REDIS_PASSWORD: process.env.REDIS_PASSWORD ? '***SET***' : 'NOT_SET',
        REDIS_URL: process.env.REDIS_URL ? '***SET***' : 'NOT_SET',
      },
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

function getRecommendations(connectionTest: any): string[] {
  const recommendations = [];

  if (!connectionTest.connected) {
    recommendations.push('Redis connection failed. Check if Redis server is running.');
    
    if (connectionTest.error?.includes('ECONNREFUSED')) {
      recommendations.push('Connection refused. Verify REDIS_HOST and REDIS_PORT are correct.');
    }
    
    if (connectionTest.error?.includes('AUTH')) {
      recommendations.push('Authentication failed. Check REDIS_PASSWORD or remove it if Redis has no password.');
    }
    
    if (connectionTest.error?.includes('timeout')) {
      recommendations.push('Connection timeout. Check network connectivity to Redis server.');
    }
  } else {
    recommendations.push('✅ Redis is working perfectly!');
  }

  // Configuration recommendations
  if (!process.env.REDIS_URL && !process.env.REDIS_HOST) {
    recommendations.push('Consider setting REDIS_HOST environment variable for clarity.');
  }

  return recommendations;
}
