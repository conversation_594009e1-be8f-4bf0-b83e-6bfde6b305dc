import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');

    let whereClause = '1=1';
    let queryParams: any = {};

    if (status) {
      whereClause = 'pr.status = {status:String}';
      queryParams.status = status;
    }

    const result = await clickhouse.query({
      query: `
        SELECT 
          pr.id,
          pr.user_id,
          u.full_name as user_name,
          pr.amount,
          pm.type as method_type,
          pm.details as method_details,
          pr.status,
          pr.requested_at,
          pr.processed_at
        FROM payout_requests pr
        LEFT JOIN users u ON pr.user_id = u.id
        LEFT JOIN payout_methods pm ON pr.method_id = pm.id
        WHERE ${whereClause}
        ORDER BY pr.requested_at DESC
      `,
      query_params: queryParams,
    });

    const requests = await result.json();
    const formattedRequests = requests.data.map((request: any) => ({
      ...request,
      method_details: JSON.parse(request.method_details || '{}'),
    }));

    return NextResponse.json(formattedRequests);

  } catch (error) {
    console.error('Admin payouts fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
