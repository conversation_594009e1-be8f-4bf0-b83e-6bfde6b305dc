import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, reason } = await request.json();
    const requestId = parseInt(params.id);

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { message: 'Invalid action' },
        { status: 400 }
      );
    }

    if (action === 'reject' && !reason) {
      return NextResponse.json(
        { message: 'Rejection reason is required' },
        { status: 400 }
      );
    }

    // Get the payout request details
    const requestResult = await clickhouse.query({
      query: 'SELECT user_id, amount, status FROM payout_requests WHERE id = {requestId:UInt64}',
      query_params: { requestId },
    });

    const requestData = await requestResult.json();
    if (requestData.data.length === 0) {
      return NextResponse.json(
        { message: 'Payout request not found' },
        { status: 404 }
      );
    }

    const payoutRequest = requestData.data[0];

    if (payoutRequest.status !== 'pending') {
      return NextResponse.json(
        { message: 'Payout request is not pending' },
        { status: 400 }
      );
    }

    const newStatus = action === 'approve' ? 'approved' : 'rejected';

    // Update payout request status
    await clickhouse.command({
      query: `
        ALTER TABLE payout_requests
        UPDATE
          status = {status:String},
          processed_at = {processedAt:String}
        WHERE id = {requestId:UInt64}
      `,
      query_params: {
        status: newStatus,
        processedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        requestId,
      },
    });

    // If rejected, return the amount to user's balance
    if (action === 'reject') {
      await clickhouse.command({
        query: `
          ALTER TABLE users
          UPDATE balance = balance + {amount:Float64}
          WHERE id = {userId:UInt32}
        `,
        query_params: {
          amount: parseFloat(payoutRequest.amount),
          userId: payoutRequest.user_id,
        },
      });
    }

    // If approved, create a transaction record
    if (action === 'approve') {
      const transactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

      await clickhouse.insert({
        table: 'transactions',
        values: [{
          id: transactionId,
          user_id: payoutRequest.user_id,
          type: 'withdrawal',
          amount: parseFloat(payoutRequest.amount),
          status: 'completed',
          payment_method: '',
          payment_reference: '',
          description: `Payout approved - Request #${requestId}${reason ? ` - ${reason}` : ''}`,
          created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });
    }

    // Log the admin action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: `payout_${action}`,
        target_id: requestId,
        target_type: 'payout_request',
        reason: `${action === 'approve' ? 'Approved' : 'Rejected'} payout request #${requestId}${reason ? ` - ${reason}` : ''}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: `Payout request ${action}d successfully`,
    });

  } catch (error) {
    console.error('Payout approval error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
