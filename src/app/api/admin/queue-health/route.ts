import { NextRequest, NextResponse } from 'next/server';
import QueueHealthMonitor from '@/lib/queue-health-monitor';
import FallbackManager from '@/lib/fallback-manager';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'detailed';

    if (format === 'simple') {
      // Simple health check for load balancers
      const healthCheck = await QueueHealthMonitor.performHealthCheck();
      
      return NextResponse.json({
        status: healthCheck.healthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
      }, {
        status: healthCheck.healthy ? 200 : 503,
      });
    }

    // Detailed health report
    const report = await QueueHealthMonitor.generateHealthReport();
    
    return NextResponse.json({
      status: report.alerts.length === 0 ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      ...report,
    });

  } catch (error) {
    console.error('Error generating health report:', error);
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Failed to generate health report',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'force-health-check':
        const healthCheck = await QueueHealthMonitor.performHealthCheck();
        return NextResponse.json({
          message: 'Health check completed',
          ...healthCheck,
        });

      case 'process-fallback':
        await FallbackManager.processFallbackJobs();
        return NextResponse.json({
          message: 'Fallback processing initiated',
        });

      case 'check-queue-health':
        const isHealthy = await FallbackManager.checkQueueHealth();
        return NextResponse.json({
          message: 'Queue health checked',
          healthy: isHealthy,
        });

      case 'get-fallback-status':
        const status = await FallbackManager.getStatus();
        return NextResponse.json({
          message: 'Fallback status retrieved',
          ...status,
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error managing queue health:', error);
    return NextResponse.json(
      { error: 'Failed to manage queue health' },
      { status: 500 }
    );
  }
}
