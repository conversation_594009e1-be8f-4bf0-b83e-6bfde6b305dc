import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      platformName,
      publisherRevenueShare,
      minimumPayout,
      minimumDepositStripe,
      minimumDepositPaypal,
      minimumCpmBanner,
      minimumCpmNative,
      minimumCpmInPagePush,
      minimumCpmPopup,
    } = body;

    const settings = [
      { key: 'platform_name', value: platformName },
      { key: 'publisher_revenue_share', value: publisherRevenueShare },
      { key: 'minimum_payout', value: minimumPayout },
      { key: 'minimum_deposit_stripe', value: minimumDepositStripe },
      { key: 'minimum_deposit_paypal', value: minimumDepositPaypal },
      { key: 'minimum_cpm_banner', value: minimumCpmBanner },
      { key: 'minimum_cpm_native', value: minimumCpmNative },
      { key: 'minimum_cpm_in_page_push', value: minimumCpmInPagePush },
      { key: 'minimum_cpm_popup', value: minimumCpmPopup },
    ];

    // Delete existing settings first, then insert new ones
    await clickhouse.command({
      query: 'ALTER TABLE platform_settings DELETE WHERE 1=1',
    });

    // Insert new settings with proper field names and IDs
    const settingsToInsert = settings.map((setting, index) => ({
      id: Date.now() + index, // Generate unique IDs
      setting_key: setting.key,
      setting_value: setting.value,
      description: '',
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
    }));

    await clickhouse.insert({
      table: 'platform_settings',
      values: settingsToInsert,
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Settings updated successfully',
    });

  } catch (error) {
    console.error('Settings update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: 'SELECT setting_key, setting_value FROM platform_settings ORDER BY setting_key',
    });

    const settingsData = await result.json();

    // Convert to object format
    const settings: Record<string, string> = {};
    settingsData.data.forEach((row: any) => {
      settings[row.setting_key] = row.setting_value;
    });

    return NextResponse.json(settings);

  } catch (error) {
    console.error('Settings fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
