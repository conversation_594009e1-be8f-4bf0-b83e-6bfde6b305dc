import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

// DELETE - Remove monitoring exclusion
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const advertiserId = parseInt(id);

    if (!advertiserId) {
      return NextResponse.json(
        { message: 'Invalid advertiser ID' },
        { status: 400 }
      );
    }

    // Get advertiser details
    const advertiserResult = await clickhouse.query({
      query: 'SELECT id, full_name FROM users WHERE id = {advertiserId:UInt32} AND role = \'advertiser\'',
      query_params: { advertiserId },
    });

    const advertiserData = await advertiserResult.json();
    if (advertiserData.data.length === 0) {
      return NextResponse.json(
        { message: 'Advertiser not found' },
        { status: 404 }
      );
    }

    // Get current excluded advertisers
    const settingsResult = await clickhouse.query({
      query: 'SELECT setting_value FROM platform_settings WHERE setting_key = \'monitoring_excluded_advertisers\'',
    });

    let excludedAdvertisers: number[] = [];
    const settingsData = await settingsResult.json();
    if (settingsData.data.length > 0) {
      try {
        excludedAdvertisers = JSON.parse(settingsData.data[0].setting_value || '[]');
      } catch (error) {
        console.error('Error parsing excluded advertisers:', error);
        excludedAdvertisers = [];
      }
    }

    // Check if advertiser is actually excluded
    if (!excludedAdvertisers.includes(advertiserId)) {
      return NextResponse.json(
        { message: 'Advertiser is not excluded from monitoring' },
        { status: 400 }
      );
    }

    // Remove from exclusion list
    excludedAdvertisers = excludedAdvertisers.filter(id => id !== advertiserId);

    // Update settings
    await clickhouse.command({
      query: `
        ALTER TABLE platform_settings
        UPDATE
          setting_value = {newValue:String},
          updated_at = {updatedAt:String}
        WHERE setting_key = 'monitoring_excluded_advertisers'
      `,
      query_params: {
        newValue: JSON.stringify(excludedAdvertisers),
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
      },
    });

    // Log admin action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'monitoring_exclusion_remove',
        target_id: advertiserId,
        target_type: 'user',
        reason: `Removed monitoring exclusion for ${advertiserData.data[0].full_name}`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Monitoring exclusion removed successfully',
      advertiser_name: advertiserData.data[0].full_name,
    });

  } catch (error) {
    console.error('Error removing monitoring exclusion:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
