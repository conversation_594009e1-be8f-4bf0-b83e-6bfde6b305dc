import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

// GET - List all monitoring exclusions
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get excluded advertisers from settings
    const settingsResult = await clickhouse.query({
      query: 'SELECT setting_value FROM platform_settings WHERE setting_key = \'monitoring_excluded_advertisers\'',
    });

    let excludedAdvertisers: number[] = [];
    const settingsData = await settingsResult.json();
    if (settingsData.data.length > 0) {
      try {
        excludedAdvertisers = JSON.parse(settingsData.data[0].setting_value || '[]');
      } catch (error) {
        console.error('Error parsing excluded advertisers:', error);
        excludedAdvertisers = [];
      }
    }

    // Get advertiser details for excluded IDs
    if (excludedAdvertisers.length === 0) {
      return NextResponse.json([]);
    }

    const placeholders = excludedAdvertisers.map((_, index) => `{id${index}:UInt32}`).join(', ');
    let queryParams = {};
    excludedAdvertisers.forEach((advertiserId, index) => {
      queryParams[`id${index}`] = advertiserId;
    });

    const advertisersResult = await clickhouse.query({
      query: `
        SELECT
          id,
          full_name,
          email,
          created_at
        FROM users
        WHERE id IN (${placeholders})
        AND role = 'advertiser'
        ORDER BY full_name
      `,
      query_params: queryParams,
    });

    const data = await advertisersResult.json();
    return NextResponse.json(data.data);

  } catch (error) {
    console.error('Error fetching monitoring exclusions:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Add new monitoring exclusion
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { advertiser_id } = await request.json();

    if (!advertiser_id) {
      return NextResponse.json(
        { message: 'Advertiser ID is required' },
        { status: 400 }
      );
    }

    // Check if advertiser exists and is actually an advertiser
    const advertiserResult = await clickhouse.query({
      query: 'SELECT id, full_name, email FROM users WHERE id = {advertiserId:UInt32} AND role = \'advertiser\'',
      query_params: { advertiserId: advertiser_id },
    });

    const advertiserData = await advertiserResult.json();
    if (advertiserData.data.length === 0) {
      return NextResponse.json(
        { message: 'Advertiser not found' },
        { status: 404 }
      );
    }

    // Get current excluded advertisers
    const settingsResult = await clickhouse.query({
      query: 'SELECT setting_value FROM platform_settings WHERE setting_key = \'monitoring_excluded_advertisers\'',
    });

    let excludedAdvertisers: number[] = [];
    const settingsData = await settingsResult.json();
    if (settingsData.data.length > 0) {
      try {
        excludedAdvertisers = JSON.parse(settingsData.data[0].setting_value || '[]');
      } catch (error) {
        console.error('Error parsing excluded advertisers:', error);
        excludedAdvertisers = [];
      }
    }

    // Check if already excluded
    if (excludedAdvertisers.includes(advertiser_id)) {
      return NextResponse.json(
        { message: 'Advertiser is already excluded from monitoring' },
        { status: 400 }
      );
    }

    // Add to exclusion list
    excludedAdvertisers.push(advertiser_id);

    // Update settings
    if (settingsData.data.length > 0) {
      // Update existing setting
      await clickhouse.command({
        query: `
          ALTER TABLE platform_settings
          UPDATE
            setting_value = {newValue:String},
            updated_at = {updatedAt:String}
          WHERE setting_key = 'monitoring_excluded_advertisers'
        `,
        query_params: {
          newValue: JSON.stringify(excludedAdvertisers),
          updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        },
      });
    } else {
      // Create new setting
      await clickhouse.insert({
        table: 'platform_settings',
        values: [{
          id: Date.now(),
          setting_key: 'monitoring_excluded_advertisers',
          setting_value: JSON.stringify(excludedAdvertisers),
          description: 'List of advertiser IDs excluded from monitoring',
          created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });
    }

    // Log admin action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'monitoring_exclusion_add',
        target_id: advertiser_id,
        target_type: 'user',
        reason: `Excluded advertiser ${advertiserData.data[0].full_name} from ad monitoring`,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Advertiser excluded from monitoring successfully',
      advertiser_name: advertiserData.data[0].full_name,
    });

  } catch (error) {
    console.error('Error adding monitoring exclusion:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
