import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { QpsLimiter } from '@/lib/qps-limiter';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get QPS statistics from the limiter
    const qpsStats = QpsLimiter.getAllQpsStats();

    // Get partner endpoint details
    const partnersResult = await clickhouse.query({
      query: `
        SELECT id, name, type, qps_limit, status
        FROM partner_endpoints
        WHERE type = 'dsp' AND status = 'active'
        ORDER BY id
      `,
    });

    const partners = await partnersResult.json();
    const partnerMap = new Map();
    partners.data.forEach((partner: any) => {
      partnerMap.set(partner.id, partner);
    });

    // Combine QPS stats with partner details
    const enrichedStats = qpsStats.map(stat => {
      const partner = partnerMap.get(stat.partnerId);
      return {
        ...stat,
        partnerName: partner?.name || `Partner ${stat.partnerId}`,
        status: partner?.status || 'unknown',
        configuredLimit: partner?.qps_limit || stat.limit
      };
    });

    // Add partners that haven't made any requests yet
    for (const partner of partners.data) {
      if (!qpsStats.find(stat => stat.partnerId === partner.id)) {
        enrichedStats.push({
          partnerId: partner.id,
          partnerName: partner.name,
          current: 0,
          limit: partner.qps_limit || 100,
          utilization: 0,
          status: partner.status,
          configuredLimit: partner.qps_limit || 100
        });
      }
    }

    // Sort by utilization (highest first)
    enrichedStats.sort((a, b) => b.utilization - a.utilization);

    return NextResponse.json({
      success: true,
      data: {
        stats: enrichedStats,
        summary: {
          totalPartners: partners.data.length,
          activePartners: qpsStats.length,
          highUtilization: enrichedStats.filter(s => s.utilization > 80).length,
          rateLimited: enrichedStats.filter(s => s.current >= s.limit).length
        }
      }
    });

  } catch (error) {
    console.error('QPS monitor error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, partnerId } = await request.json();

    if (action === 'reset_partner' && partnerId) {
      QpsLimiter.resetPartner(parseInt(partnerId));
      return NextResponse.json({
        success: true,
        message: `Reset QPS counters for partner ${partnerId}`
      });
    }

    if (action === 'reset_all') {
      QpsLimiter.resetAll();
      return NextResponse.json({
        success: true,
        message: 'Reset all QPS counters'
      });
    }

    return NextResponse.json(
      { message: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('QPS monitor action error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
