import { NextRequest, NextResponse } from 'next/server';
import ComprehensiveHealthMonitor from '@/lib/comprehensive-health-monitor';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'current';
    const timeframe = searchParams.get('timeframe') || '1h';

    switch (type) {
      case 'current':
        return await getCurrentHealth();
      
      case 'historical':
        return await getHistoricalHealth(timeframe);
      
      case 'alerts':
        return await getActiveAlerts();
      
      case 'services':
        return await getServiceStatus();
      
      case 'summary':
        return await getHealthSummary();
      
      default:
        return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
    }

  } catch (error) {
    console.error('Health monitor API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch health data' },
      { status: 500 }
    );
  }
}

async function getCurrentHealth() {
  const health = await ComprehensiveHealthMonitor.getCurrentHealth();
  
  return NextResponse.json({
    status: 'success',
    timestamp: new Date().toISOString(),
    data: health,
  });
}

async function getHistoricalHealth(timeframe: string) {
  const timeMap: { [key: string]: number } = {
    '1h': 1,
    '6h': 6,
    '24h': 24,
    '7d': 168,
    '30d': 720,
  };

  const hours = timeMap[timeframe] || 1;
  const startTime = new Date(Date.now() - hours * 60 * 60 * 1000);

  const [systemMetrics, appMetrics] = await Promise.all([
    clickhouse.query({
      query: `
        SELECT 
          timestamp,
          cpu_usage_percent,
          memory_usage_percent,
          disk_usage_percent,
          network_rx_mb,
          network_tx_mb,
          pm2_instances,
          overall_health
        FROM system_health_metrics 
        WHERE timestamp >= {start:DateTime}
        ORDER BY timestamp DESC
        LIMIT 1000
      `,
      query_params: {
        start: startTime.toISOString().slice(0, 19).replace('T', ' '),
      },
    }),
    clickhouse.query({
      query: `
        SELECT 
          timestamp,
          qps_current,
          response_time_avg_ms,
          queue_impressions_waiting,
          queue_cost_waiting,
          impressions_per_minute,
          revenue_per_minute,
          error_rate_percent,
          fallback_mode
        FROM app_health_metrics 
        WHERE timestamp >= {start:DateTime}
        ORDER BY timestamp DESC
        LIMIT 1000
      `,
      query_params: {
        start: startTime.toISOString().slice(0, 19).replace('T', ' '),
      },
    }),
  ]);

  const systemData = await systemMetrics.json();
  const appData = await appMetrics.json();

  return NextResponse.json({
    status: 'success',
    timestamp: new Date().toISOString(),
    timeframe,
    data: {
      system: systemData.data,
      application: appData.data,
    },
  });
}

async function getActiveAlerts() {
  const result = await clickhouse.query({
    query: `
      SELECT 
        timestamp,
        alert_type,
        alert_category,
        alert_title,
        alert_message,
        alert_source,
        severity,
        metric_name,
        metric_value,
        threshold_value,
        status
      FROM health_alerts 
      WHERE status = 'active'
        AND timestamp >= now() - INTERVAL 24 HOUR
      ORDER BY severity DESC, timestamp DESC
      LIMIT 100
    `,
  });

  const data = await result.json();

  return NextResponse.json({
    status: 'success',
    timestamp: new Date().toISOString(),
    data: data.data,
  });
}

async function getServiceStatus() {
  const services = [
    {
      name: 'RTB API',
      status: 'up',
      responseTime: 5,
      uptime: 99.9,
      version: '1.0.0',
      port: 3102,
      endpoint: '/api/health',
    },
    {
      name: 'ClickHouse',
      status: 'up',
      responseTime: 2,
      uptime: 99.95,
      version: '23.8',
      port: 8123,
      endpoint: '/ping',
    },
    {
      name: 'Redis',
      status: 'up',
      responseTime: 1,
      uptime: 99.99,
      version: '7.0',
      port: 6379,
      endpoint: 'PING',
    },
    {
      name: 'Queue System',
      status: 'up',
      responseTime: 3,
      uptime: 99.8,
      version: '4.12.9',
      port: 0,
      endpoint: '/api/admin/queue-stats',
    },
  ];

  return NextResponse.json({
    status: 'success',
    timestamp: new Date().toISOString(),
    data: services,
  });
}

async function getHealthSummary() {
  const [currentHealth, recentAlerts] = await Promise.all([
    ComprehensiveHealthMonitor.getCurrentHealth(),
    clickhouse.query({
      query: `
        SELECT 
          alert_type,
          count() as count
        FROM health_alerts 
        WHERE timestamp >= now() - INTERVAL 24 HOUR
        GROUP BY alert_type
        ORDER BY count DESC
      `,
    }),
  ]);

  const alertsData = await recentAlerts.json();

  // Calculate uptime percentage
  const uptimeResult = await clickhouse.query({
    query: `
      SELECT 
        avg(CASE WHEN overall_health = 1 THEN 100 ELSE 0 END) as uptime_percent
      FROM system_health_metrics 
      WHERE timestamp >= now() - INTERVAL 24 HOUR
    `,
  });

  const uptimeData = await uptimeResult.json();
  const uptime = uptimeData.data[0]?.uptime_percent || 0;

  return NextResponse.json({
    status: 'success',
    timestamp: new Date().toISOString(),
    data: {
      overall: currentHealth.overall,
      uptime: uptime,
      alerts: alertsData.data,
      metrics: {
        cpu: currentHealth.system.cpu.usage,
        memory: currentHealth.system.memory.usage,
        disk: currentHealth.system.disk.usage,
        qps: currentHealth.app.performance.qps.current,
        responseTime: currentHealth.app.performance.responseTime.avg,
        errorRate: currentHealth.app.errors.errorRate,
      },
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const { action, alertId } = await request.json();

    switch (action) {
      case 'acknowledge-alert':
        if (!alertId) {
          return NextResponse.json({ error: 'Alert ID required' }, { status: 400 });
        }
        
        await clickhouse.command({
          query: `
            ALTER TABLE health_alerts 
            UPDATE status = 'acknowledged'
            WHERE id = {alertId:UInt64}
          `,
          query_params: { alertId },
        });

        return NextResponse.json({ message: 'Alert acknowledged' });

      case 'resolve-alert':
        if (!alertId) {
          return NextResponse.json({ error: 'Alert ID required' }, { status: 400 });
        }
        
        await clickhouse.command({
          query: `
            ALTER TABLE health_alerts 
            UPDATE status = 'resolved', resolved_at = now()
            WHERE id = {alertId:UInt64}
          `,
          query_params: { alertId },
        });

        return NextResponse.json({ message: 'Alert resolved' });

      case 'force-collection':
        const [systemMetrics, appMetrics] = await Promise.all([
          ComprehensiveHealthMonitor.collectSystemMetrics(),
          ComprehensiveHealthMonitor.collectApplicationMetrics(),
        ]);

        await ComprehensiveHealthMonitor.storeMetrics(systemMetrics, appMetrics);

        return NextResponse.json({ 
          message: 'Metrics collected',
          data: { system: systemMetrics, app: appMetrics },
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Health monitor action error:', error);
    return NextResponse.json(
      { error: 'Failed to perform action' },
      { status: 500 }
    );
  }
}
