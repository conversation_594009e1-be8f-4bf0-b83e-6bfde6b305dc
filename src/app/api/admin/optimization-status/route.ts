import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { StartupOptimizer } from '@/lib/startup-optimizer';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get health check
    const health = await StartupOptimizer.healthCheck();
    
    // Get detailed metrics
    const metrics = StartupOptimizer.getMetrics();
    
    // Calculate performance score
    const performanceScore = calculatePerformanceScore(health, metrics);
    
    return NextResponse.json({
      success: true,
      data: {
        health,
        metrics,
        performanceScore,
        recommendations: getRecommendations(health, metrics),
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error) {
    console.error('Error getting optimization status:', error);
    return NextResponse.json(
      { error: 'Failed to get optimization status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action } = await request.json();
    
    switch (action) {
      case 'initialize':
        await StartupOptimizer.initialize();
        return NextResponse.json({
          success: true,
          message: 'Optimization initialized successfully'
        });
        
      case 'shutdown':
        await StartupOptimizer.shutdown();
        return NextResponse.json({
          success: true,
          message: 'Optimization shutdown completed'
        });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error executing optimization action:', error);
    return NextResponse.json(
      { error: 'Failed to execute optimization action' },
      { status: 500 }
    );
  }
}

function calculatePerformanceScore(health: any, metrics: any): number {
  let score = 0;
  
  // Health checks (40 points)
  if (health.cache) score += 20;
  if (health.connections) score += 20;
  
  // Cache efficiency (30 points)
  const cacheUsage = metrics.cache.memoryUsagePercent || 0;
  if (cacheUsage > 0) score += 10;
  if (cacheUsage > 25) score += 10;
  if (cacheUsage > 50) score += 10;
  
  // Connection pool efficiency (30 points)
  const httpsConnections = metrics.connections.https?.sockets || 0;
  const httpConnections = metrics.connections.http?.sockets || 0;
  const totalConnections = httpsConnections + httpConnections;
  
  if (totalConnections > 0) score += 10;
  if (totalConnections > 5) score += 10;
  if (totalConnections > 10) score += 10;
  
  return Math.min(score, 100);
}

function getRecommendations(health: any, metrics: any): string[] {
  const recommendations: string[] = [];
  
  if (!health.cache) {
    recommendations.push('Cache system is not healthy - consider restarting optimization');
  }
  
  if (!health.connections) {
    recommendations.push('Connection pool is not healthy - check network connectivity');
  }
  
  const cacheUsage = metrics.cache.memoryUsagePercent || 0;
  if (cacheUsage < 10) {
    recommendations.push('Cache usage is low - consider running cache warm-up');
  }
  
  if (cacheUsage > 90) {
    recommendations.push('Cache usage is high - consider increasing memory cache size');
  }
  
  const totalConnections = 
    (metrics.connections.https?.sockets || 0) + 
    (metrics.connections.http?.sockets || 0);
    
  if (totalConnections === 0) {
    recommendations.push('No active connections - consider warming up connections to partners');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('All systems optimized - RTB platform ready for high QPS!');
  }
  
  return recommendations;
}
