import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import net from 'net';
import tls from 'tls';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { host, port, username, password, security, from_email, from_name } = await request.json();

    // Validate required fields
    if (!host || typeof host !== 'string' || host.trim() === '') {
      return NextResponse.json(
        { message: 'SMTP host is required' },
        { status: 400 }
      );
    }

    if (!port) {
      return NextResponse.json(
        { message: 'SMTP port is required' },
        { status: 400 }
      );
    }

    if (!username || typeof username !== 'string' || username.trim() === '') {
      return NextResponse.json(
        { message: 'SMTP username is required' },
        { status: 400 }
      );
    }

    if (!from_email || typeof from_email !== 'string' || from_email.trim() === '') {
      return NextResponse.json(
        { message: 'From email is required' },
        { status: 400 }
      );
    }

    if (!password || typeof password !== 'string' || password.trim() === '') {
      return NextResponse.json(
        { message: 'SMTP password is required for testing' },
        { status: 400 }
      );
    }

    // Test connection using raw socket connection
    const testResult = await testSMTPConnection({
      host,
      port: typeof port === 'number' ? port : parseInt(port),
      username,
      password,
      security,
      from_email,
      from_name
    });

    if (testResult.success) {
      return NextResponse.json({
        success: true,
        message: testResult.message,
        details: testResult.details
      });
    } else {
      return NextResponse.json({
        success: false,
        message: testResult.message,
        error: testResult.error,
        troubleshooting: testResult.troubleshooting,
        testedConfig: {
          host,
          port: typeof port === 'number' ? port : parseInt(port),
          security,
          username,
          from_email
        }
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('SMTP test error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error during SMTP test',
      error: error?.message || 'Unknown error'
    }, { status: 500 });
  }
}

// Simple SMTP connection test using raw sockets
async function testSMTPConnection(config: {
  host: string;
  port: number;
  username: string;
  password: string;
  security: string;
  from_email: string;
  from_name: string;
}): Promise<{
  success: boolean;
  message: string;
  error?: string;
  details?: any;
  troubleshooting?: string[];
}> {
  return new Promise((resolve) => {
    const timeout = 10000; // 10 seconds timeout
    let socket: net.Socket | tls.TLSSocket;
    let isResolved = false;

    const resolveOnce = (result: any) => {
      if (!isResolved) {
        isResolved = true;
        if (socket) {
          socket.destroy();
        }
        resolve(result);
      }
    };

    try {
      // Create appropriate socket based on security type
      if (config.security === 'ssl' || config.port === 465) {
        // SSL connection
        socket = tls.connect({
          host: config.host,
          port: config.port,
          timeout: timeout,
          rejectUnauthorized: false // Allow self-signed certificates for testing
        });
      } else {
        // Plain or STARTTLS connection
        socket = new net.Socket();
        socket.setTimeout(timeout);
        socket.connect(config.port, config.host);
      }

      let response = '';
      let step = 'connecting';

      socket.on('connect', () => {
        step = 'connected';
        console.log(`Connected to ${config.host}:${config.port}`);
      });

      socket.on('data', (data) => {
        response += data.toString();
        console.log('Received:', data.toString().trim());

        // Check for SMTP greeting (220 response)
        if (step === 'connected' && response.includes('220')) {
          step = 'greeting_received';
          // Send EHLO command
          socket.write(`EHLO ${config.host}\r\n`);
        }
        // Check for EHLO response (250 response)
        else if (step === 'greeting_received' && response.includes('250')) {
          step = 'ehlo_success';
          resolveOnce({
            success: true,
            message: `✅ SMTP connection test successful! Connected to ${config.host}:${config.port}`,
            details: {
              host: config.host,
              port: config.port,
              security: config.security,
              connectionType: config.security === 'ssl' || config.port === 465 ? 'SSL/TLS' : 'Plain/STARTTLS',
              serverResponse: response.trim()
            }
          });
        }
        // Check for error responses
        else if (response.includes('421') || response.includes('554') || response.includes('550')) {
          resolveOnce({
            success: false,
            message: 'SMTP server rejected the connection',
            error: response.trim(),
            troubleshooting: [
              'The SMTP server is not accepting connections',
              'Check if your IP address is allowed',
              'Verify the hostname and port are correct',
              'Try a different port (587 for STARTTLS, 465 for SSL)'
            ]
          });
        }
      });

      socket.on('error', (error: any) => {
        console.error('Socket error:', error);
        let troubleshooting: string[] = [];
        let message = 'Connection failed';

        if (error.code === 'ENOTFOUND') {
          message = 'SMTP server not found - Invalid hostname';
          troubleshooting = [
            'Double-check the SMTP server hostname spelling',
            'Ensure you have internet connectivity',
            'Verify DNS resolution is working'
          ];
        } else if (error.code === 'ECONNREFUSED') {
          message = 'Connection refused - SMTP server rejected the connection';
          troubleshooting = [
            'Check if the port number is correct',
            'Try different ports: 587 (STARTTLS), 465 (SSL), or 25 (plain)',
            'Verify the SMTP server is running',
            'Check if your hosting provider blocks the port'
          ];
        } else if (error.code === 'ETIMEDOUT') {
          message = 'Connection timeout - Unable to reach SMTP server';
          troubleshooting = [
            'Check if the SMTP server hostname is correct',
            'Verify your internet connection',
            'Try a different port',
            'Check firewall settings'
          ];
        } else {
          message = `Connection error: ${error.message}`;
          troubleshooting = [
            'Check all SMTP settings are correct',
            'Verify your hosting provider allows outgoing connections',
            'Try testing with a different SMTP provider'
          ];
        }

        resolveOnce({
          success: false,
          message,
          error: error.message,
          troubleshooting
        });
      });

      socket.on('timeout', () => {
        resolveOnce({
          success: false,
          message: 'Connection timeout - Unable to reach SMTP server within 10 seconds',
          error: 'Connection timeout',
          troubleshooting: [
            'Check if the SMTP server hostname is correct',
            'Verify the port number (587 for STARTTLS, 465 for SSL)',
            'Ensure your server can reach the SMTP server',
            'Check firewall/network settings'
          ]
        });
      });

      socket.on('close', () => {
        if (!isResolved) {
          resolveOnce({
            success: false,
            message: 'Connection closed unexpectedly',
            error: 'Connection closed by server',
            troubleshooting: [
              'The SMTP server closed the connection',
              'Check if the server is running',
              'Verify your connection settings'
            ]
          });
        }
      });

    } catch (error: any) {
      resolveOnce({
        success: false,
        message: 'Failed to create connection',
        error: error.message,
        troubleshooting: [
          'Check all SMTP settings are correct',
          'Verify your hosting environment supports outgoing connections',
          'Try testing with a different SMTP provider'
        ]
      });
    }
  });
}
