import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: 'SELECT * FROM email_settings ORDER BY id DESC LIMIT 1',
    });

    const settings = await result.json();

    if (settings.data.length === 0) {
      // Return default settings
      return NextResponse.json({
        host: '',
        port: 587,
        username: '',
        password: '',
        security: 'starttls',
        from_email: '',
        from_name: 'Global Ads Media',
      });
    }

    const emailSettings = settings.data[0];

    // Return the actual password for easier management
    return NextResponse.json({
      ...emailSettings,
    });

  } catch (error) {
    console.error('Email settings fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { host, port, username, password, security, from_email, from_name } = await request.json();

    if (!host || !port || !username || !from_email || !from_name) {
      return NextResponse.json(
        { message: 'All fields except password are required' },
        { status: 400 }
      );
    }

    if (!['starttls', 'tls', 'ssl', 'none'].includes(security)) {
      return NextResponse.json(
        { message: 'Invalid security type' },
        { status: 400 }
      );
    }

    // Use the provided password directly
    const finalPassword = password;

    // Delete existing settings
    await clickhouse.command({
      query: 'ALTER TABLE email_settings DELETE WHERE 1=1',
    });

    // Insert new settings
    await clickhouse.insert({
      table: 'email_settings',
      values: [{
        id: Date.now(),
        host,
        port: parseInt(port),
        username,
        password: finalPassword,
        security,
        from_email,
        from_name,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Log the action
    await clickhouse.insert({
      table: 'admin_actions',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        admin_id: parseInt(session.user.id),
        action_type: 'email_settings_update',
        target_id: 0,
        target_type: 'email_settings',
        reason: 'Updated email SMTP settings',
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Email settings updated successfully',
    });

  } catch (error) {
    console.error('Email settings update error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
