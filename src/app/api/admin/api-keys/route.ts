import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import Api<PERSON>eyManager from '@/lib/api-key-manager';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const keyType = searchParams.get('key_type') as 'dsp' | 'ssp' | 'admin' | null;

    const apiKeys = await ApiKeyManager.getUserApiKeys(parseInt(session.user.id), keyType || undefined);

    // Don't return the secret in the list
    const safeApiKeys = apiKeys.map(key => ({
      ...key,
      apiSecret: '***hidden***',
    }));

    return NextResponse.json({
      success: true,
      data: safeApiKeys,
    });

  } catch (error) {
    console.error('Error fetching API keys:', error);
    return NextResponse.json(
      { error: 'Failed to fetch API keys' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      keyName,
      keyType,
      permissions,
      partnerId,
      rateLimitPerHour,
      rateLimitPerDay,
      allowedIps,
      allowedDomains,
      expiresAt,
      description,
    } = body;

    // Validate required fields
    if (!keyName || !keyType || !permissions) {
      return NextResponse.json({
        error: 'Missing required fields: keyName, keyType, permissions',
      }, { status: 400 });
    }

    // Validate key type
    if (!['dsp', 'ssp', 'admin'].includes(keyType)) {
      return NextResponse.json({
        error: 'Invalid keyType. Must be dsp, ssp, or admin',
      }, { status: 400 });
    }

    // Validate permissions
    const validPermissions = [
      'stats:read',
      'campaigns:read',
      'campaigns:write',
      'reports:read',
      'billing:read',
      '*'
    ];
    
    const invalidPermissions = permissions.filter((p: string) => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      return NextResponse.json({
        error: `Invalid permissions: ${invalidPermissions.join(', ')}`,
      }, { status: 400 });
    }

    // For DSP/SSP keys, require partnerId
    if ((keyType === 'dsp' || keyType === 'ssp') && !partnerId) {
      return NextResponse.json({
        error: 'partnerId is required for DSP and SSP keys',
      }, { status: 400 });
    }

    // Create API key
    const apiKey = await ApiKeyManager.createApiKey({
      userId: parseInt(session.user.id),
      partnerId: partnerId || 0,
      keyName,
      keyType,
      permissions,
      rateLimitPerHour: rateLimitPerHour || 1000,
      rateLimitPerDay: rateLimitPerDay || 10000,
      allowedIps: allowedIps || [],
      allowedDomains: allowedDomains || [],
      expiresAt,
      description,
    });

    return NextResponse.json({
      success: true,
      message: 'API key created successfully',
      data: apiKey,
      warning: 'Save the API secret now. It will not be shown again.',
    });

  } catch (error) {
    console.error('Error creating API key:', error);
    return NextResponse.json(
      { error: 'Failed to create API key' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const apiKey = searchParams.get('api_key');

    if (!apiKey) {
      return NextResponse.json({
        error: 'API key is required',
      }, { status: 400 });
    }

    const success = await ApiKeyManager.revokeApiKey(apiKey, parseInt(session.user.id));

    if (!success) {
      return NextResponse.json({
        error: 'Failed to revoke API key or key not found',
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'API key revoked successfully',
    });

  } catch (error) {
    console.error('Error revoking API key:', error);
    return NextResponse.json(
      { error: 'Failed to revoke API key' },
      { status: 500 }
    );
  }
}
