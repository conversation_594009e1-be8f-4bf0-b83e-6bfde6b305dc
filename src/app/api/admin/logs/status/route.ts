import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import logger from '@/lib/logger';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get log statistics
    const logStats = logger.getLogStats();
    
    // Get log directory info
    const logDir = logger.logDir;
    const logFiles = fs.readdirSync(logDir).filter(file => file.endsWith('.log'));
    
    // Calculate total log size
    let totalSize = 0;
    logFiles.forEach(file => {
      const filePath = path.join(logDir, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
    });

    // Get rotation info
    const rotationInterval = logger.rotationInterval;
    const nextRotationTimes: { [key: string]: Date } = {};
    
    Object.keys(logStats).forEach(category => {
      const age = logStats[category].age;
      const timeUntilRotation = rotationInterval - (age % rotationInterval);
      nextRotationTimes[category] = new Date(Date.now() + timeUntilRotation);
    });

    return NextResponse.json({
      success: true,
      data: {
        logDirectory: logDir,
        rotationInterval: rotationInterval / (60 * 60 * 1000), // Convert to hours
        totalLogFiles: logFiles.length,
        totalSize: totalSize,
        totalSizeFormatted: formatBytes(totalSize),
        logStats,
        nextRotationTimes,
        availableLoggers: Object.keys(logger).filter(key => 
          typeof (logger as any)[key].info === 'function'
        ),
        systemInfo: {
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
        }
      }
    });

  } catch (error) {
    console.error('Error getting log status:', error);
    return NextResponse.json(
      { error: 'Failed to get log status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action, category } = await request.json();
    
    switch (action) {
      case 'rotate_all':
        logger.rotateAllLogs();
        return NextResponse.json({
          success: true,
          message: 'All log files rotated successfully'
        });
        
      case 'rotate_single':
        if (!category) {
          return NextResponse.json(
            { error: 'Category is required for single rotation' },
            { status: 400 }
          );
        }
        
        const fileName = path.join(logger.logDir, `${category}.log`);
        if (fs.existsSync(fileName)) {
          fs.writeFileSync(fileName, '');
          return NextResponse.json({
            success: true,
            message: `Log file ${category}.log rotated successfully`
          });
        } else {
          return NextResponse.json(
            { error: `Log file ${category}.log not found` },
            { status: 404 }
          );
        }
        
      case 'cleanup_old':
        logger.cleanupOldFiles();
        return NextResponse.json({
          success: true,
          message: 'Old log files cleaned up successfully'
        });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error executing log action:', error);
    return NextResponse.json(
      { error: 'Failed to execute log action' },
      { status: 500 }
    );
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
