import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const zoneId = searchParams.get('zone_id');
  const format = searchParams.get('format') || 'banner';
  const size = searchParams.get('size') || '300x250';

  if (!zoneId) {
    return new NextResponse('Zone ID required', { status: 400 });
  }

  const baseUrl = process.env.NEXTAUTH_URL || process.env.PLATFORM_URL || 'http://localhost:3000';

  const adScript = `
(async function() {
  // Get current script for precise placement
  var currentScript = document.currentScript;
  if (!currentScript) {
    console.error('GAM Ad Loader: document.currentScript is not available');
    return;
  }

  // Zone configuration
  var zoneId = '${zoneId}';
  var format = '${format}';
  var size = '${size}';
  var baseUrl = '${baseUrl}';

  console.log('GAM Ad Loader: Loading zone', zoneId, 'format', format, 'size', size);

  // Create ad container
  var adContainer;
  try {
    // Try to find an existing container by ID, otherwise create a new one
    adContainer = document.getElementById('gam-zone-' + zoneId);
    if (!adContainer) {
      adContainer = document.createElement('div');
      // Create unique container ID if not using an existing one
      adContainer.id = 'gam-container-' + zoneId + '-' + Math.random().toString(36).substring(2, 9);

      // Insert container before the current script if new
      if (currentScript.parentNode) {
        currentScript.parentNode.insertBefore(adContainer, currentScript);
      } else {
        console.warn('GAM Ad Loader: Could not determine placement, appending to body');
        document.body.appendChild(adContainer);
      }
    }

  } catch (e) {
    console.error('GAM Ad Loader: Error creating container for zone', zoneId, e);
    return;
  }

  // Fetch ad content
  // For popup ads, add no_tracking=1 to skip billing during preparation
  var apiUrl = baseUrl + '/api/serve?zone_id=' + zoneId + '&format=' + format + '&size=' + size;
  if (format === 'popup') {
    apiUrl += '&no_tracking=1';
  }
  console.log('GAM Ad Loader: Fetching from', apiUrl);

  try {
    var response = await fetch(apiUrl);
    if (!response.ok) {
      console.error('GAM Ad Loader: Failed to fetch ad for zone', zoneId, response.status, response.statusText);
      adContainer.innerHTML = '<!-- Ad Load Error -->';
      return;
    }

    var adData = await response.json();
    console.log('GAM Ad Loader: Received ad data', adData);

    if (!adData.ad || !adData.ad.html) {
      console.log('GAM Ad Loader: No ad content available');
      adContainer.innerHTML = '<!-- No Ad Available -->';
      return;
    }

    // Handle popup format
    if (format === 'popup') {
      // For popup, we need user interaction and should open the landing URL directly
      var popupTriggered = false;

      function triggerPopup(event) {
        if (popupTriggered) return;
        popupTriggered = true;

        // First, track the popup impression (billing happens here)
        if (adData.ad && adData.ad.impression_id) {
          var impressionData = {
            impression_id: adData.ad.impression_id,
            campaign_id: adData.ad.campaign_id || 0,
            dsp_partner_id: adData.ad.dsp_partner_id || 0,
            zone_id: parseInt(zoneId),
            website_id: adData.ad.website_id || 0,
            bid_price: adData.ad.original_bid || adData.ad.cpm || 0,
            publisher_cpm: adData.ad.cpm || 0,
            source_type: adData.ad.source || 'local'
          };

          console.log('GAM Ad Loader: Tracking popup impression:', impressionData);
          fetch(baseUrl + '/api/popup-impression', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(impressionData),
          }).catch(function(error) {
            console.error('GAM Ad Loader: Failed to track popup impression:', error);
          });
        }

        // Then open popup if we have a valid landing URL
        if (adData.ad && adData.ad.landing_url && adData.ad.landing_url.trim() !== '') {
          console.log('GAM Ad Loader: Opening popup - landing URL:', adData.ad.landing_url);
          // Open the landing URL directly in a new tab (no click tracking redirect)
          var newTab = window.open(adData.ad.landing_url, '_blank', 'noopener,noreferrer');

          // Track the click separately without redirecting
          if (adData.ad.impression_id && adData.ad.campaign_id) {
            var trackingUrl = baseUrl + '/api/click?impression_id=' + adData.ad.impression_id + '&campaign_id=' + adData.ad.campaign_id + '&track_only=1';
            console.log('GAM Ad Loader: Sending click tracking request:', trackingUrl);
            // Send tracking request without redirecting
            fetch(trackingUrl, { method: 'GET', mode: 'no-cors' }).catch(function() {
              // Ignore tracking errors
            });
          }
        }

        adContainer.style.display = 'none';

        // Allow the original click to proceed normally (don't prevent default)
        // This way the user's intended action (clicking a link) still works
      }

      // Wait for user interaction (click only, not keyboard)
      // Use capture phase to trigger popup before the target element handles the click
      document.addEventListener('click', triggerPopup, { once: true, capture: true });

      // Hide the container since popup doesn't show content on page
      adContainer.style.display = 'none';
      return;
    }

    // Handle banner and other formats
    if (adData.ad.creative_type === 'js') {
      // For JS tag campaigns, execute the JavaScript directly on the page
      console.log('GAM Ad Loader: Loading JS tag campaign');

      // Handle JS creative
      var adHtmlContent = adData.ad.html.trim();
      var tempDiv = document.createElement('div');
      tempDiv.innerHTML = adHtmlContent;
      var scriptTags = tempDiv.getElementsByTagName('script');

      if (scriptTags.length > 0) {
        // If <script> tags are found in adHtmlContent, process them
        for (var i = 0; i < scriptTags.length; i++) {
          var existingScript = scriptTags[i];
          var newScriptElement = document.createElement('script');

          // Copy attributes (like src, type, async, defer)
          for (var j = 0; j < existingScript.attributes.length; j++) {
            var attr = existingScript.attributes[j];
            if (attr.name && attr.value) { // Ensure attribute name and value are not null/undefined
              newScriptElement.setAttribute(attr.name, attr.value);
            }
          }

          // For inline scripts, use .text or .textContent for reliability
          // existingScript.text is preferred as it gets the raw text content of script tag
          if (!existingScript.src && existingScript.text) {
            newScriptElement.text = existingScript.text;
          } else if (!existingScript.src && existingScript.textContent) { // Fallback to textContent
             newScriptElement.text = existingScript.textContent;
          } else if (!existingScript.src && existingScript.innerHTML) { // Fallback to innerHTML if others are empty
             newScriptElement.text = existingScript.innerHTML;
          }

          adContainer.appendChild(newScriptElement);
        }
      } else {
        // If no <script> tags are found, assume adHtmlContent is raw JS code
        // Or it could be an HTML snippet without scripts, which might be an issue
        // For now, we assume it's raw JS if no script tags are explicitly found
        var scriptElement = document.createElement('script');
        scriptElement.type = 'text/javascript';
        scriptElement.text = adHtmlContent; // Assign raw JS to .text
        adContainer.appendChild(scriptElement);
      }

      console.log('GAM Ad Loader: JS tag executed for zone', zoneId);
    } else {
      // For image campaigns and other HTML content, use iframe
      var iframe = document.createElement('iframe');
      iframe.setAttribute('frameborder', '0');
      iframe.setAttribute('scrolling', 'no');
      iframe.setAttribute('marginheight', '0');
      iframe.setAttribute('marginwidth', '0');
      iframe.style.border = 'none';
      iframe.style.overflow = 'hidden';

      // Set dimensions
      if (size) {
        var dimensions = size.split('x');
        var width = parseInt(dimensions[0], 10);
        var height = parseInt(dimensions[1], 10);

        if (width && height) {
          iframe.width = width.toString();
          iframe.height = height.toString();
          iframe.style.width = width + 'px';
          iframe.style.height = height + 'px';
        }
      }

      // Create iframe content using srcdoc (like your working platform)
      var iframeContent = '<!DOCTYPE html><html><head><title>Ad</title><style>body{margin:0;padding:0;}</style></head><body>' + adData.ad.html + '</body></html>';
      iframe.srcdoc = iframeContent;

      adContainer.appendChild(iframe);
      console.log('GAM Ad Loader: Image/HTML ad loaded successfully for zone', zoneId);
    }

  } catch (error) {
    console.error('GAM Ad Loader: Error processing ad for zone', zoneId, error);
    adContainer.innerHTML = '<!-- Ad Load Error -->';
  }

})();`;

  return new NextResponse(adScript, {
    headers: {
      'Content-Type': 'application/javascript',
      'Cache-Control': 'no-cache, no-store, must-revalidate', // No cache for testing
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}