import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT
          z.id,
          z.name,
          z.ad_format,
          z.size,
          z.config,
          z.status,
          z.created_at,
          z.updated_at,
          w.name as website_name,
          w.url as website_url
        FROM ad_zones z
        LEFT JOIN websites w ON z.website_id = w.id
        WHERE w.user_id = {userId:UInt32}
        ORDER BY z.created_at DESC
      `,
      query_params: { userId: parseInt(session.user.id) },
    });

    const zones = await result.json();
    console.log('Fetched ad zones for user:', session.user.id, 'Count:', zones.data?.length || 0);

    return NextResponse.json(zones.data);

  } catch (error) {
    console.error('Ad zones fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { websiteId, name, adFormat, size, nativeConfig, pushConfig } = body;

    console.log('Ad zone creation request:', { websiteId, name, adFormat, size, nativeConfig, pushConfig, userId: session.user.id });

    // Validate required fields
    if (!websiteId || !name || !adFormat) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify website ownership
    const websiteResult = await clickhouse.query({
      query: 'SELECT id FROM websites WHERE id = {websiteId:UInt32} AND user_id = {userId:UInt32}',
      query_params: {
        websiteId: parseInt(websiteId),
        userId: parseInt(session.user.id)
      },
    });

    const websiteData = await websiteResult.json();
    console.log('Website verification result:', websiteData);

    if (websiteData.data.length === 0) {
      return NextResponse.json(
        { message: 'Website not found or access denied' },
        { status: 404 }
      );
    }

    // Get the next zone ID
    const zoneIdStart = parseInt(process.env.ZONE_ID_START || '230763');

    const maxIdResult = await clickhouse.query({
      query: 'SELECT MAX(id) as max_id FROM ad_zones',
    });

    const maxIdData = await maxIdResult.json();
    let nextId = zoneIdStart;
    if (maxIdData.data.length > 0 && maxIdData.data[0].max_id) {
      nextId = Math.max(zoneIdStart, maxIdData.data[0].max_id + 1);
    }

    // Prepare configuration data
    let config = {};
    if (adFormat === 'native' && nativeConfig) {
      config = nativeConfig;
    } else if (adFormat === 'in_page_push' && pushConfig) {
      config = pushConfig;
    }

    // Insert new ad zone
    const zoneData = {
      id: nextId,
      website_id: parseInt(websiteId),
      name,
      ad_format: adFormat,
      size: size || '',
      config: JSON.stringify(config),
      status: 'active',
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
    };

    console.log('Inserting ad zone:', zoneData);

    await clickhouse.insert({
      table: 'ad_zones',
      values: [zoneData],
      format: 'JSONEachRow',
    });

    console.log('Ad zone created successfully with ID:', nextId);

    return NextResponse.json(
      {
        message: 'Ad zone created successfully',
        zoneId: nextId
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Ad zone creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
