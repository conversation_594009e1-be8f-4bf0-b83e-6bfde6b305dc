import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const zoneId = parseInt(id);
    if (isNaN(zoneId)) {
      return NextResponse.json(
        { message: 'Invalid zone ID' },
        { status: 400 }
      );
    }

    let query = `
      SELECT z.*, w.name as website_name, w.url as website_url, w.user_id as website_user_id
      FROM ad_zones z
      LEFT JOIN websites w ON z.website_id = w.id
      WHERE z.id = {zoneId:UInt32}
    `;

    const result = await clickhouse.query({
      query,
      query_params: { zoneId },
    });

    const zones = await result.json();

    if (zones.data.length === 0) {
      return NextResponse.json(
        { message: 'Zone not found' },
        { status: 404 }
      );
    }

    const zone = zones.data[0];

    // Check permissions
    if (session.user?.role === 'publisher' && zone.website_user_id !== parseInt(session.user.id)) {
      return NextResponse.json(
        { message: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json(zone);
  } catch (error) {
    console.error('Zone fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const zoneId = parseInt(id);

    if (isNaN(zoneId)) {
      return NextResponse.json(
        { message: 'Invalid zone ID' },
        { status: 400 }
      );
    }

    const { name } = await request.json();

    if (!name || !name.trim()) {
      return NextResponse.json(
        { message: 'Zone name is required' },
        { status: 400 }
      );
    }

    // Verify zone ownership
    const zoneResult = await clickhouse.query({
      query: `
        SELECT z.id
        FROM ad_zones z
        LEFT JOIN websites w ON z.website_id = w.id
        WHERE z.id = {zoneId:UInt32} AND w.user_id = {userId:UInt32}
      `,
      query_params: {
        zoneId,
        userId: parseInt(session.user.id)
      },
    });

    const zoneData = await zoneResult.json();
    if (zoneData.data.length === 0) {
      return NextResponse.json(
        { message: 'Ad zone not found or unauthorized' },
        { status: 404 }
      );
    }

    // Update the ad zone
    await clickhouse.command({
      query: `
        ALTER TABLE ad_zones
        UPDATE
          name = {name:String},
          updated_at = {updatedAt:String}
        WHERE id = {zoneId:UInt32}
      `,
      query_params: {
        zoneId,
        name: name.trim(),
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' ')
      },
    });

    return NextResponse.json({
      message: 'Ad zone updated successfully',
    });

  } catch (error) {
    console.error('Update ad zone error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const zoneId = parseInt(id);

    if (isNaN(zoneId)) {
      return NextResponse.json(
        { message: 'Invalid zone ID' },
        { status: 400 }
      );
    }

    // Verify zone ownership
    const zoneResult = await clickhouse.query({
      query: `
        SELECT z.id
        FROM ad_zones z
        LEFT JOIN websites w ON z.website_id = w.id
        WHERE z.id = {zoneId:UInt32} AND w.user_id = {userId:UInt32}
      `,
      query_params: {
        zoneId,
        userId: parseInt(session.user.id)
      },
    });

    const zoneData = await zoneResult.json();
    if (zoneData.data.length === 0) {
      return NextResponse.json(
        { message: 'Ad zone not found or unauthorized' },
        { status: 404 }
      );
    }

    // Delete the ad zone
    await clickhouse.command({
      query: 'DELETE FROM ad_zones WHERE id = {zoneId:UInt32}',
      query_params: { zoneId },
    });

    return NextResponse.json({
      message: 'Ad zone deleted successfully',
    });

  } catch (error) {
    console.error('Delete ad zone error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
