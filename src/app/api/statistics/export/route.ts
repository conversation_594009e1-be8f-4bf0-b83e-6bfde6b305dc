import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const campaignId = searchParams.get('campaign_id');
    const groupBy = searchParams.get('group_by') || 'day';
    const format = searchParams.get('format') || 'csv';

    const role = session.user?.role;
    const userId = parseInt(session.user.id);

    let stats: any[] = [];
    let csvHeaders: string[] = [];

    if (role === 'advertiser') {
      let query = '';
      let queryParams: any = { userId, dateFrom, dateTo };

      if (groupBy === 'day') {
        csvHeaders = ['Date', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'CPC ($)', 'Cost ($)'];
        query = `
          SELECT
            DATE(i.timestamp) as date,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc,
            SUM(c.cpm_bid / 1000) as cost
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY DATE(i.timestamp) ORDER BY date DESC';

      } else if (groupBy === 'campaign') {
        csvHeaders = ['Campaign ID', 'Campaign Name', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'CPC ($)', 'Cost ($)'];
        query = `
          SELECT
            c.id as campaign_id,
            c.name as campaign_name,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc,
            SUM(c.cpm_bid / 1000) as cost
          FROM campaigns c
          LEFT JOIN impressions i ON c.id = i.campaign_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          WHERE c.user_id = {userId:UInt32}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY c.id, c.name ORDER BY impressions DESC';
      }

      const result = await clickhouse.query({
        query,
        query_params: queryParams,
      });

      const data = await result.json();
      stats = data.data || [];

    } else if (role === 'publisher') {
      let query = '';
      let queryParams: any = { userId, dateFrom, dateTo };

      if (groupBy === 'day') {
        csvHeaders = ['Date', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'RPM ($)', 'Earnings ($)'];
        query = `
          SELECT
            DATE(i.timestamp) as date,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm,
            SUM(i.publisher_revenue) as earnings
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY DATE(i.timestamp)
          ORDER BY date DESC
        `;
      } else if (groupBy === 'website') {
        csvHeaders = ['Website ID', 'Website Name', 'Impressions', 'Clicks', 'Conversions', 'CTR (%)', 'RPM ($)', 'Earnings ($)'];
        query = `
          SELECT
            w.id as website_id,
            w.name as website_name,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm,
            SUM(i.publisher_revenue) as earnings
          FROM websites w
          LEFT JOIN impressions i ON w.id = i.website_id AND DATE(i.timestamp) >= {dateFrom:String} AND DATE(i.timestamp) <= {dateTo:String}
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE w.user_id = {userId:UInt32}
          GROUP BY w.id, w.name
          ORDER BY impressions DESC
        `;
      }

      const result = await clickhouse.query({
        query,
        query_params: queryParams,
      });

      const data = await result.json();
      stats = data.data || [];
    }

    // Generate CSV content
    let csvContent = csvHeaders.join(',') + '\n';

    stats.forEach((stat: any) => {
      const row: string[] = [];

      if (role === 'advertiser') {
        if (groupBy === 'day') {
          row.push(
            stat.date || '',
            (stat.impressions || 0).toString(),
            (stat.clicks || 0).toString(),
            (stat.conversions || 0).toString(),
            parseFloat(stat.ctr || 0).toFixed(2),
            parseFloat(stat.cpc || 0).toFixed(2),
            parseFloat(stat.cost || 0).toFixed(2)
          );
        } else if (groupBy === 'campaign') {
          row.push(
            (stat.campaign_id || '').toString(),
            `"${stat.campaign_name || ''}"`,
            (stat.impressions || 0).toString(),
            (stat.clicks || 0).toString(),
            (stat.conversions || 0).toString(),
            parseFloat(stat.ctr || 0).toFixed(2),
            parseFloat(stat.cpc || 0).toFixed(2),
            parseFloat(stat.cost || 0).toFixed(2)
          );
        }
      } else if (role === 'publisher') {
        if (groupBy === 'day') {
          row.push(
            stat.date || '',
            (stat.impressions || 0).toString(),
            (stat.clicks || 0).toString(),
            (stat.conversions || 0).toString(),
            parseFloat(stat.ctr || 0).toFixed(2),
            parseFloat(stat.rpm || 0).toFixed(2),
            parseFloat(stat.earnings || 0).toFixed(2)
          );
        } else if (groupBy === 'website') {
          row.push(
            (stat.website_id || '').toString(),
            `"${stat.website_name || ''}"`,
            (stat.impressions || 0).toString(),
            (stat.clicks || 0).toString(),
            (stat.conversions || 0).toString(),
            parseFloat(stat.ctr || 0).toFixed(2),
            parseFloat(stat.rpm || 0).toFixed(2),
            parseFloat(stat.earnings || 0).toFixed(2)
          );
        }
      }

      csvContent += row.join(',') + '\n';
    });

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${role}-statistics-${dateFrom}-${dateTo}.csv"`,
      },
    });

  } catch (error) {
    console.error('Statistics export error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
