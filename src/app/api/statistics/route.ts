import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const campaignId = searchParams.get('campaign_id');
    const websiteId = searchParams.get('website_id');

    const role = session.user?.role;
    const userId = parseInt(session.user.id);

    let stats: any = {};

    if (role === 'advertiser') {
      // Advertiser statistics
      let campaignFilter = '';
      let queryParams: any = { userId, dateFrom, dateTo };

      if (campaignId) {
        campaignFilter = ' AND c.id = {campaignId:UInt32}';
        queryParams.campaignId = parseInt(campaignId);
      }

      // Get impression and click stats
      const statsResult = await clickhouse.query({
        query: `
          SELECT
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(c.cpm_bid / 1000) as total_cost,
            COUNT(DISTINCT i.campaign_id) as active_campaigns
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
            ${campaignFilter}
        `,
        query_params: queryParams,
      });

      const statsData = await statsResult.json();
      const data = statsData.data[0] || {};

      stats = {
        impressions: parseInt(data.impressions || 0),
        clicks: parseInt(data.clicks || 0),
        ctr: data.impressions > 0 ? ((data.clicks / data.impressions) * 100).toFixed(2) : '0.00',
        total_cost: parseFloat(data.total_cost || 0).toFixed(2),
        active_campaigns: parseInt(data.active_campaigns || 0),
        avg_cpm: data.impressions > 0 ? ((data.total_cost / data.impressions) * 1000).toFixed(2) : '0.00',
      };

      // Get daily breakdown
      const dailyResult = await clickhouse.query({
        query: `
          SELECT
            DATE(i.timestamp) as date,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(c.cpm_bid / 1000) as cost
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
            ${campaignFilter}
          GROUP BY DATE(i.timestamp)
          ORDER BY date
        `,
        query_params: queryParams,
      });

      const dailyData = await dailyResult.json();
      stats.daily_breakdown = dailyData.data || [];

    } else if (role === 'publisher') {
      // Publisher statistics
      let websiteFilter = '';
      let queryParams: any = { userId, dateFrom, dateTo };

      if (websiteId) {
        websiteFilter = ' AND w.id = {websiteId:UInt32}';
        queryParams.websiteId = parseInt(websiteId);
      }

      // Get impression and earnings stats
      const statsResult = await clickhouse.query({
        query: `
          SELECT
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(i.publisher_revenue) as total_earnings,
            COUNT(DISTINCT w.id) as active_websites
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
            ${websiteFilter}
        `,
        query_params: queryParams,
      });

      const statsData = await statsResult.json();
      const data = statsData.data[0] || {};

      stats = {
        impressions: parseInt(data.impressions || 0),
        clicks: parseInt(data.clicks || 0),
        ctr: data.impressions > 0 ? ((data.clicks / data.impressions) * 100).toFixed(2) : '0.00',
        total_earnings: parseFloat(data.total_earnings || 0).toFixed(2),
        active_websites: parseInt(data.active_websites || 0),
        avg_rpm: data.impressions > 0 ? ((data.total_earnings / data.impressions) * 1000).toFixed(2) : '0.00',
      };

    } else if (role === 'admin') {
      // Admin platform statistics
      const statsResult = await clickhouse.query({
        query: `
          SELECT
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            COUNT(DISTINCT c.id) as total_campaigns,
            COUNT(DISTINCT w.id) as total_websites,
            COUNT(DISTINCT u.id) as total_users,
            SUM(i.cost_deducted) - SUM(i.publisher_revenue) as total_revenue
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          LEFT JOIN users u ON c.user_id = u.id OR w.user_id = u.id
          WHERE DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `,
        query_params: { dateFrom, dateTo },
      });

      const statsData = await statsResult.json();
      const data = statsData.data[0] || {};

      stats = {
        impressions: parseInt(data.impressions || 0),
        clicks: parseInt(data.clicks || 0),
        ctr: data.impressions > 0 ? ((data.clicks / data.impressions) * 100).toFixed(2) : '0.00',
        total_campaigns: parseInt(data.total_campaigns || 0),
        total_websites: parseInt(data.total_websites || 0),
        total_users: parseInt(data.total_users || 0),
        total_revenue: parseFloat(data.total_revenue || 0).toFixed(2),
        platform_fee: parseFloat(data.total_revenue || 0).toFixed(2), // Already calculated as platform commission
      };
    }

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Statistics error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
