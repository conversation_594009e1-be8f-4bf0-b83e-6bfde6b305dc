import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('date_from') || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateTo = searchParams.get('date_to') || new Date().toISOString().split('T')[0];
    const campaignId = searchParams.get('campaign_id');
    const groupBy = searchParams.get('group_by') || 'day';

    const role = session.user?.role;
    const userId = parseInt(session.user.id);

    let stats: any[] = [];

    if (role === 'advertiser') {
      let query = '';
      let queryParams: any = { userId, dateFrom, dateTo };

      if (groupBy === 'day') {
        query = `
          SELECT
            DATE(i.timestamp) as date,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY DATE(i.timestamp) ORDER BY date DESC';

      } else if (groupBy === 'campaign') {
        query = `
          SELECT
            c.id as campaign_id,
            c.name as campaign_name,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY c.id, c.name ORDER BY impressions DESC';
      } else if (groupBy === 'publisher_id') {
        query = `
          SELECT
            w.user_id as publisher_id,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY w.user_id ORDER BY impressions DESC';
      } else if (groupBy === 'website_id') {
        query = `
          SELECT
            i.website_id as website_id,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
            AND i.website_id > 0
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY i.website_id ORDER BY impressions DESC';
      } else if (groupBy === 'zone_id') {
        query = `
          SELECT
            i.zone_id as zone_id,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
            AND i.zone_id > 0
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY i.zone_id ORDER BY impressions DESC';
      } else if (groupBy === 'country') {
        query = `
          SELECT
            i.country as country,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY i.country ORDER BY impressions DESC';
      } else if (groupBy === 'device') {
        query = `
          SELECT
            i.device_type as device,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY i.device_type ORDER BY impressions DESC';
      } else if (groupBy === 'os') {
        query = `
          SELECT
            i.os as os,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY i.os ORDER BY impressions DESC';
      } else if (groupBy === 'browser') {
        query = `
          SELECT
            i.browser as browser,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(c.cpm_bid / 1000) as cost,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) > 0 THEN SUM(c.cpm_bid / 1000) / SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END)
              ELSE 0
            END as cpc
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          WHERE c.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
        `;

        if (campaignId) {
          query += ' AND c.id = {campaignId:UInt32}';
          queryParams.campaignId = parseInt(campaignId);
        }

        query += ' GROUP BY i.browser ORDER BY impressions DESC';
      }

      const result = await clickhouse.query({
        query,
        query_params: queryParams,
      });

      const data = await result.json();
      stats = data.data || [];

    } else if (role === 'publisher') {
      let query = '';
      let queryParams: any = { userId, dateFrom, dateTo };

      if (groupBy === 'day') {
        query = `
          SELECT
            DATE(i.timestamp) as date,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY DATE(i.timestamp)
          ORDER BY date DESC
        `;
      } else if (groupBy === 'website') {
        query = `
          SELECT
            w.id as website_id,
            w.name as website_name,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY w.id, w.name
          ORDER BY impressions DESC
        `;
      } else if (groupBy === 'adzones') {
        query = `
          SELECT
            z.id as zone_id,
            z.name as zone_name,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN ad_zones z ON i.zone_id = z.id
          LEFT JOIN websites w ON z.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY z.id, z.name
          ORDER BY impressions DESC
        `;
      } else if (groupBy === 'country') {
        query = `
          SELECT
            i.country as country,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.country
          ORDER BY impressions DESC
        `;
      } else if (groupBy === 'os') {
        query = `
          SELECT
            i.os as os,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.os
          ORDER BY impressions DESC
        `;
      } else if (groupBy === 'device') {
        query = `
          SELECT
            i.device_type as device,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.device_type
          ORDER BY impressions DESC
        `;
      } else if (groupBy === 'browser') {
        query = `
          SELECT
            i.browser as browser,
            COUNT(DISTINCT i.id) as impressions,
            SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) as clicks,
            SUM(CASE WHEN cv.id > 0 THEN 1 ELSE 0 END) as conversions,
            SUM(i.publisher_revenue) as earnings,
            (SUM(CASE WHEN cl.id > 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT i.id)) * 100 as ctr,
            CASE
              WHEN COUNT(DISTINCT i.id) > 0 THEN SUM(i.publisher_revenue) / COUNT(DISTINCT i.id) * 1000
              ELSE 0
            END as rpm
          FROM impressions i
          LEFT JOIN clicks cl ON i.id = cl.impression_id
          LEFT JOIN conversions cv ON cl.id = cv.click_id
          LEFT JOIN campaigns c ON i.campaign_id = c.id
          LEFT JOIN websites w ON i.website_id = w.id
          WHERE w.user_id = {userId:UInt32}
            AND DATE(i.timestamp) >= {dateFrom:String}
            AND DATE(i.timestamp) <= {dateTo:String}
          GROUP BY i.browser
          ORDER BY impressions DESC
        `;
      }

      const result = await clickhouse.query({
        query,
        query_params: queryParams,
      });

      const data = await result.json();
      stats = data.data || [];
    }

    // Fill in missing values and ensure proper formatting
    stats = stats.map((stat: any) => ({
      ...stat,
      impressions: parseInt(stat.impressions || 0),
      clicks: parseInt(stat.clicks || 0),
      conversions: parseInt(stat.conversions || 0),
      cost: parseFloat(stat.cost || 0),
      earnings: parseFloat(stat.earnings || 0),
      ctr: parseFloat(stat.ctr || 0),
      cpc: parseFloat(stat.cpc || 0),
      rpm: parseFloat(stat.rpm || 0),
    }));

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Detailed statistics error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
