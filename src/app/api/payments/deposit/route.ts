import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { amount, paymentMethod, depositAmount, processingFee } = body;

    // Use the total amount for payment processing, but depositAmount for account credit
    const totalAmount = parseFloat(amount); // Total amount to charge (deposit + fee)
    const actualDepositAmount = parseFloat(depositAmount || amount); // Amount to credit to account
    const feeAmount = parseFloat(processingFee || '0'); // Processing fee amount

    // Fetch minimum deposit settings
    let minimumDeposit = 100; // Default fallback
    try {
      const settingsResult = await clickhouse.query({
        query: `
          SELECT setting_value
          FROM platform_settings
          WHERE setting_key = {key:String}
        `,
        query_params: {
          key: `minimum_deposit_${paymentMethod}`
        }
      });

      const settingsData = await settingsResult.json();
      if (settingsData.data.length > 0) {
        minimumDeposit = parseFloat(settingsData.data[0].setting_value);
      }
    } catch (error) {
      console.error('Failed to fetch minimum deposit setting:', error);
      // Use default value if fetch fails
    }

    // Validate deposit amount (not total amount)
    if (isNaN(actualDepositAmount) || actualDepositAmount < minimumDeposit) {
      return NextResponse.json(
        { message: `Minimum deposit amount is $${minimumDeposit}` },
        { status: 400 }
      );
    }

    // Validate total amount
    if (isNaN(totalAmount) || totalAmount <= 0) {
      return NextResponse.json(
        { message: 'Invalid total amount' },
        { status: 400 }
      );
    }

    // Generate transaction ID
    const transactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create pending transaction record
    await clickhouse.insert({
      table: 'transactions',
      values: [{
        id: transactionId,
        user_id: parseInt(session.user.id),
        type: 'deposit',
        amount: actualDepositAmount, // Amount to be credited to account
        status: 'pending',
        payment_method: paymentMethod,
        payment_reference: '',
        description: `Deposit via ${paymentMethod}${feeAmount > 0 ? ` (Fee: $${feeAmount.toFixed(2)}, Total: $${totalAmount.toFixed(2)})` : ''}`,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Real payment processing with Stripe/PayPal
    if (paymentMethod === 'stripe') {
      try {
        const stripe = (await import('@/lib/stripe')).default;

        // Create Stripe checkout session
        const stripeSession = await stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          line_items: [
            {
              price_data: {
                currency: 'usd',
                product_data: {
                  name: 'Account Deposit',
                  description: `Deposit $${actualDepositAmount.toFixed(2)} + $${feeAmount.toFixed(2)} processing fee`,
                },
                unit_amount: Math.round(totalAmount * 100), // Convert total amount to cents
              },
              quantity: 1,
            },
          ],
          mode: 'payment',
          success_url: `${process.env.NEXTAUTH_URL}/advertiser/billing/payment-success?transaction_id=${transactionId}&method=stripe&amount=${actualDepositAmount}&session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${process.env.NEXTAUTH_URL}/advertiser/billing/deposit?cancelled=true`,
          metadata: {
            transaction_id: transactionId.toString(),
            user_id: session.user.id,
            deposit_amount: actualDepositAmount.toString(),
            processing_fee: feeAmount.toString(),
            total_amount: totalAmount.toString(),
          },
        });

        return NextResponse.json({
          message: 'Payment initiated',
          transactionId,
          redirectUrl: stripeSession.url,
        });
      } catch (error) {
        console.error('Stripe error:', error);
        return NextResponse.json(
          { message: 'Failed to create payment session' },
          { status: 500 }
        );
      }
    } else if (paymentMethod === 'paypal') {
      try {
        // Use PayPal live credentials from environment
        const paypalClientId = process.env.PAYPAL_CLIENT_ID;
        const paypalClientSecret = process.env.PAYPAL_CLIENT_SECRET;

        if (!paypalClientId || !paypalClientSecret) {
          throw new Error('PayPal credentials not configured');
        }

        // PayPal production URLs (for live credentials)
        const authUrl = 'https://api-m.paypal.com/v1/oauth2/token';
        const paymentUrl = 'https://api-m.paypal.com/v1/payments/payment';

        console.log('Creating PayPal payment...');

        // Get PayPal access token
        const authResponse = await fetch(authUrl, {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Accept-Language': 'en_US',
            'Authorization': `Basic ${Buffer.from(`${paypalClientId}:${paypalClientSecret}`).toString('base64')}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: 'grant_type=client_credentials',
        });

        if (!authResponse.ok) {
          const errorText = await authResponse.text();
          console.error('PayPal auth error status:', authResponse.status);
          console.error('PayPal auth error response:', errorText);
          console.error('PayPal auth request headers:', {
            'Accept': 'application/json',
            'Accept-Language': 'en_US',
            'Authorization': `Basic ${Buffer.from(`${paypalClientId}:${paypalClientSecret}`).toString('base64')}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          });
          throw new Error(`Failed to get PayPal access token: ${authResponse.status} - ${errorText}`);
        }

        const authData = await authResponse.json();
        const accessToken = authData.access_token;
        console.log('PayPal access token obtained');

        // Create payment
        const paymentData = {
          intent: 'sale',
          payer: {
            payment_method: 'paypal'
          },
          transactions: [{
            amount: {
              total: totalAmount.toFixed(2), // Charge total amount including fee
              currency: 'USD'
            },
            description: `Account Deposit $${actualDepositAmount.toFixed(2)} + $${feeAmount.toFixed(2)} processing fee - Global Ads Media`,
            custom: transactionId.toString()
          }],
          redirect_urls: {
            return_url: `${process.env.NEXTAUTH_URL}/advertiser/billing/payment-success?transaction_id=${transactionId}&method=paypal&amount=${actualDepositAmount}`,
            cancel_url: `${process.env.NEXTAUTH_URL}/advertiser/billing/deposit?cancelled=true`
          }
        };

        const paymentResponse = await fetch(paymentUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
          },
          body: JSON.stringify(paymentData),
        });

        if (!paymentResponse.ok) {
          const errorText = await paymentResponse.text();
          console.error('PayPal payment creation error:', errorText);
          throw new Error(`Failed to create PayPal payment: ${paymentResponse.status}`);
        }

        const payment = await paymentResponse.json();
        console.log('PayPal payment created:', payment.id);

        const approvalUrl = payment.links.find((link: any) => link.rel === 'approval_url')?.href;

        if (!approvalUrl) {
          console.error('PayPal payment response:', payment);
          throw new Error('No approval URL found in PayPal response');
        }

        console.log('PayPal approval URL:', approvalUrl);

        return NextResponse.json({
          message: 'Payment initiated',
          transactionId,
          redirectUrl: approvalUrl,
        });
      } catch (error) {
        console.error('PayPal integration error:', error);

        // Fallback to demo mode for development
        console.log('PayPal demo mode: Simulating payment for development');
        return NextResponse.json({
          message: 'Payment initiated (Demo Mode)',
          transactionId,
          redirectUrl: `/advertiser/billing/payment-success?transaction_id=${transactionId}&method=paypal&amount=${actualDepositAmount}`,
        });
      }
    }

    return NextResponse.json(
      { message: 'Invalid payment method' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Deposit error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
