import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'advertiser') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { transactionId, method, amount, status } = body;

    if (!transactionId || !status) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Update transaction status
    await clickhouse.command({
      query: `
        ALTER TABLE transactions
        UPDATE status = {status:String},
               payment_reference = {paymentId:String},
               updated_at = {updatedAt:String}
        WHERE id = {transactionId:UInt64} AND user_id = {userId:UInt32}
      `,
      query_params: {
        status,
        paymentId: `${method}_${transactionId}`,
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
        transactionId: parseInt(transactionId),
        userId: parseInt(session.user.id),
      },
    });

    // If payment was successful, update user balance
    if (status === 'completed') {
      const depositAmount = parseFloat(amount);

      await clickhouse.command({
        query: `
          ALTER TABLE users
          UPDATE balance = balance + {amount:Decimal(10,2)},
                 updated_at = {updatedAt:String}
          WHERE id = {userId:UInt32}
        `,
        query_params: {
          amount: depositAmount,
          updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
          userId: parseInt(session.user.id),
        },
      });
    }

    return NextResponse.json({
      message: 'Payment processed successfully',
      status,
    });

  } catch (error) {
    console.error('Payment completion error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
