import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT id, type, details, is_default, status, created_at
        FROM payout_methods 
        WHERE user_id = {userId:UInt32}
        ORDER BY is_default DESC, created_at DESC
      `,
      query_params: { userId: parseInt(session.user.id) },
    });

    const methods = await result.json();
    const formattedMethods = methods.data.map((method: any) => ({
      ...method,
      details: JSON.parse(method.details),
    }));

    return NextResponse.json(formattedMethods);

  } catch (error) {
    console.error('Payout methods fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { type, details } = await request.json();

    if (!type || !details) {
      return NextResponse.json(
        { message: 'Type and details are required' },
        { status: 400 }
      );
    }

    if (!['paypal', 'crypto', 'bank'].includes(type)) {
      return NextResponse.json(
        { message: 'Invalid payout method type' },
        { status: 400 }
      );
    }

    // Validate details based on type
    if (type === 'paypal' && !details.email) {
      return NextResponse.json(
        { message: 'PayPal email is required' },
        { status: 400 }
      );
    }

    if (type === 'crypto' && (!details.currency || !details.address)) {
      return NextResponse.json(
        { message: 'Cryptocurrency currency and address are required' },
        { status: 400 }
      );
    }

    if (type === 'bank' && (!details.bank_name || !details.account_number || !details.routing_number)) {
      return NextResponse.json(
        { message: 'Bank name, account number, and routing number are required' },
        { status: 400 }
      );
    }

    // Check if this is the first method (make it default)
    const existingMethodsResult = await clickhouse.query({
      query: 'SELECT COUNT(*) as count FROM payout_methods WHERE user_id = {userId:UInt32}',
      query_params: { userId: parseInt(session.user.id) },
    });

    const existingMethods = await existingMethodsResult.json();
    const isFirstMethod = existingMethods.data[0]?.count === 0;

    // Generate method ID
    const methodId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create payout method
    await clickhouse.insert({
      table: 'payout_methods',
      values: [{
        id: methodId,
        user_id: parseInt(session.user.id),
        type,
        details: JSON.stringify(details),
        is_default: isFirstMethod,
        status: 'active',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      message: 'Payout method added successfully',
      method_id: methodId,
    });

  } catch (error) {
    console.error('Payout method creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
