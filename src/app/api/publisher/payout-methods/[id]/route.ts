import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const methodId = parseInt(id);

    if (isNaN(methodId)) {
      return NextResponse.json(
        { message: 'Invalid method ID' },
        { status: 400 }
      );
    }

    // Check if the method belongs to the current user
    const checkResult = await clickhouse.query({
      query: 'SELECT user_id FROM payout_methods WHERE id = {methodId:UInt64} AND user_id = {userId:UInt32}',
      query_params: {
        methodId,
        userId: parseInt(session.user.id)
      },
    });

    const checkData = await checkResult.json();
    if (checkData.data.length === 0) {
      return NextResponse.json(
        { message: 'Payment method not found' },
        { status: 404 }
      );
    }

    // Delete the payment method
    await clickhouse.command({
      query: 'DELETE FROM payout_methods WHERE id = {methodId:UInt64} AND user_id = {userId:UInt32}',
      query_params: {
        methodId,
        userId: parseInt(session.user.id)
      },
    });

    return NextResponse.json({
      message: 'Payment method deleted successfully',
    });

  } catch (error) {
    console.error('Delete payment method error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const methodId = parseInt(id);

    if (isNaN(methodId)) {
      return NextResponse.json(
        { message: 'Invalid method ID' },
        { status: 400 }
      );
    }

    const { type, details } = await request.json();

    if (!type || !details) {
      return NextResponse.json(
        { message: 'Type and details are required' },
        { status: 400 }
      );
    }

    if (!['paypal', 'crypto', 'bank'].includes(type)) {
      return NextResponse.json(
        { message: 'Invalid payout method type' },
        { status: 400 }
      );
    }

    // Validate details based on type
    if (type === 'paypal' && !details.email) {
      return NextResponse.json(
        { message: 'PayPal email is required' },
        { status: 400 }
      );
    }

    if (type === 'crypto' && (!details.currency || !details.address)) {
      return NextResponse.json(
        { message: 'Cryptocurrency currency and address are required' },
        { status: 400 }
      );
    }

    // Check if the method belongs to the current user
    const checkResult = await clickhouse.query({
      query: 'SELECT user_id FROM payout_methods WHERE id = {methodId:UInt64} AND user_id = {userId:UInt32}',
      query_params: {
        methodId,
        userId: parseInt(session.user.id)
      },
    });

    const checkData = await checkResult.json();
    if (checkData.data.length === 0) {
      return NextResponse.json(
        { message: 'Payment method not found' },
        { status: 404 }
      );
    }

    // Update the payment method
    await clickhouse.command({
      query: `
        ALTER TABLE payout_methods
        UPDATE
          type = {type:String},
          details = {details:String},
          updated_at = {updatedAt:String}
        WHERE id = {methodId:UInt64} AND user_id = {userId:UInt32}
      `,
      query_params: {
        methodId,
        userId: parseInt(session.user.id),
        type,
        details: JSON.stringify(details),
        updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' ')
      },
    });

    return NextResponse.json({
      message: 'Payment method updated successfully',
    });

  } catch (error) {
    console.error('Update payment method error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
