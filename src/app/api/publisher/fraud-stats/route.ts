import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { FraudMonitoring } from '@/lib/fraud-monitoring';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user || session.user.role !== 'publisher') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const publisherId = parseInt(session.user.id);

    const fraudStats = await FraudMonitoring.getPublisherFraudStats(publisherId, days);

    // Group by fraud reason for easier display
    const groupedStats = fraudStats.reduce((acc: any, stat: any) => {
      if (!acc[stat.fraud_reason]) {
        acc[stat.fraud_reason] = {
          reason: stat.fraud_reason,
          total_blocked: 0,
          avg_risk_score: 0,
          unique_ips: 0,
          daily_data: []
        };
      }

      acc[stat.fraud_reason].total_blocked += stat.total_blocked;
      acc[stat.fraud_reason].avg_risk_score = Math.max(acc[stat.fraud_reason].avg_risk_score, stat.avg_risk_score);
      acc[stat.fraud_reason].unique_ips += stat.unique_ips;
      acc[stat.fraud_reason].daily_data.push({
        date: stat.date,
        blocked: stat.total_blocked,
        risk_score: stat.avg_risk_score,
        unique_ips: stat.unique_ips
      });

      return acc;
    }, {});

    const fraudSummary = Object.values(groupedStats);

    return NextResponse.json({
      fraudStats: fraudSummary,
      totalBlocked: fraudSummary.reduce((sum: number, stat: any) => sum + stat.total_blocked, 0),
      period: `${days} days`
    });

  } catch (error) {
    console.error('Publisher fraud stats error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
