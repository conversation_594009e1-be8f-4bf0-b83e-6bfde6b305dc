import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);

    // Get active websites count
    const websitesResult = await clickhouse.query({
      query: 'SELECT COUNT(*) as count FROM websites WHERE user_id = {userId:UInt32} AND status = \'approved\'',
      query_params: { userId },
    });
    const websitesData = await websitesResult.json();
    const activeWebsites = parseInt(websitesData.data[0]?.count || 0);

    // Get total impressions for this publisher's websites
    const impressionsResult = await clickhouse.query({
      query: `
        SELECT COUNT(*) as count
        FROM impressions i
        INNER JOIN websites w ON i.website_id = w.id
        WHERE w.user_id = {userId:UInt32}
      `,
      query_params: { userId },
    });
    const impressionsData = await impressionsResult.json();
    const totalImpressions = parseInt(impressionsData.data[0]?.count || 0);

    // Get total earnings from actual publisher revenue
    const earningsResult = await clickhouse.query({
      query: `
        SELECT SUM(i.publisher_revenue) as total_earnings
        FROM impressions i
        INNER JOIN websites w ON i.website_id = w.id
        WHERE w.user_id = {userId:UInt32}
      `,
      query_params: { userId },
    });
    const earningsData = await earningsResult.json();
    const totalEarnings = parseFloat(earningsData.data[0]?.total_earnings || 0);

    // Get pending payout (from user balance or payout requests)
    const userResult = await clickhouse.query({
      query: 'SELECT balance FROM users WHERE id = {userId:UInt32}',
      query_params: { userId },
    });
    const userData = await userResult.json();
    const pendingPayout = userData.data[0]?.balance || 0;

    const stats = {
      activeWebsites,
      totalImpressions,
      totalEarnings,
      pendingPayout,
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
