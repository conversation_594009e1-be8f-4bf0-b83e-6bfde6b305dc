import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await clickhouse.query({
      query: `
        SELECT 
          pr.id,
          pr.amount,
          pm.type as method_type,
          pr.status,
          pr.requested_at,
          pr.processed_at
        FROM payout_requests pr
        LEFT JOIN payout_methods pm ON pr.method_id = pm.id
        WHERE pr.user_id = {userId:UInt32}
        ORDER BY pr.requested_at DESC
      `,
      query_params: { userId: parseInt(session.user.id) },
    });

    const requests = await result.json();
    return NextResponse.json(requests.data);

  } catch (error) {
    console.error('Payout requests fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { amount, method_id } = await request.json();

    if (!amount || !method_id) {
      return NextResponse.json(
        { message: 'Amount and method ID are required' },
        { status: 400 }
      );
    }

    if (amount < 50) {
      return NextResponse.json(
        { message: 'Minimum payout amount is $50' },
        { status: 400 }
      );
    }

    // Get user's current balance
    const userResult = await clickhouse.query({
      query: 'SELECT balance FROM users WHERE id = {userId:UInt32}',
      query_params: { userId: parseInt(session.user.id) },
    });

    const userData = await userResult.json();
    const currentBalance = userData.data[0]?.balance || 0;

    if (amount > currentBalance) {
      return NextResponse.json(
        { message: 'Insufficient balance' },
        { status: 400 }
      );
    }

    // Verify payout method belongs to user
    const methodResult = await clickhouse.query({
      query: 'SELECT id FROM payout_methods WHERE id = {methodId:UInt64} AND user_id = {userId:UInt32} AND status = \'active\'',
      query_params: { 
        methodId: parseInt(method_id),
        userId: parseInt(session.user.id) 
      },
    });

    const methodData = await methodResult.json();
    if (methodData.data.length === 0) {
      return NextResponse.json(
        { message: 'Invalid payout method' },
        { status: 400 }
      );
    }

    // Check for pending requests
    const pendingResult = await clickhouse.query({
      query: 'SELECT COUNT(*) as count FROM payout_requests WHERE user_id = {userId:UInt32} AND status = \'pending\'',
      query_params: { userId: parseInt(session.user.id) },
    });

    const pendingData = await pendingResult.json();
    if (pendingData.data[0]?.count > 0) {
      return NextResponse.json(
        { message: 'You already have a pending payout request' },
        { status: 400 }
      );
    }

    // Generate request ID
    const requestId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

    // Create payout request
    await clickhouse.insert({
      table: 'payout_requests',
      values: [{
        id: requestId,
        user_id: parseInt(session.user.id),
        method_id: parseInt(method_id),
        amount: parseFloat(amount),
        status: 'pending',
        requested_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    // Deduct amount from user balance (hold it)
    await clickhouse.command({
      query: `
        ALTER TABLE users 
        UPDATE balance = balance - {amount:Float64}
        WHERE id = {userId:UInt32}
      `,
      query_params: {
        amount: parseFloat(amount),
        userId: parseInt(session.user.id),
      },
    });

    return NextResponse.json({
      message: 'Payout request submitted successfully',
      request_id: requestId,
    });

  } catch (error) {
    console.error('Payout request creation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
