import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type');

    const offset = (page - 1) * limit;

    // Build query conditions - only show withdrawal/payout transactions for publishers
    let whereConditions = ['t.user_id = {userId:UInt32}'];
    let queryParams: any = {
      userId: parseInt(session.user.id),
      limit,
      offset
    };

    // For publishers, we only want to show outgoing money (withdrawals/payouts)
    if (type && ['withdrawal', 'payout', 'adjustment'].includes(type)) {
      if (type === 'payout') {
        // Map 'payout' to 'withdrawal' since that's what we use in the database
        whereConditions.push("t.type = 'withdrawal'");
      } else if (type === 'adjustment') {
        // For adjustment filter, show only adjustment transactions (admin use)
        whereConditions.push("t.type = 'adjustment'");
      } else {
        whereConditions.push('t.type = {type:String}');
        queryParams.type = type;
      }
    } else {
      // Default: show only withdrawal transactions (exclude ad_revenue and adjustment transactions)
      whereConditions.push("t.type = 'withdrawal'");
    }

    const whereClause = whereConditions.join(' AND ');

    // Get transactions
    const transactionsResult = await clickhouse.query({
      query: `
        SELECT
          t.id,
          t.type,
          t.amount,
          t.status,
          t.description,
          t.payment_method,
          t.payment_reference as payment_id,
          t.created_at,
          t.updated_at
        FROM transactions t
        WHERE ${whereClause}
        ORDER BY t.created_at DESC
        LIMIT {limit:UInt32}
        OFFSET {offset:UInt32}
      `,
      query_params: queryParams,
    });

    // Get total count
    const countResult = await clickhouse.query({
      query: `
        SELECT COUNT(*) as total
        FROM transactions t
        WHERE ${whereClause}
      `,
      query_params: queryParams,
    });

    const transactionsData = await transactionsResult.json();
    const countData = await countResult.json();

    const transactions = transactionsData.data || [];
    const total = countData.data[0]?.total || 0;

    return NextResponse.json({
      transactions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });

  } catch (error) {
    console.error('Publisher transactions error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
