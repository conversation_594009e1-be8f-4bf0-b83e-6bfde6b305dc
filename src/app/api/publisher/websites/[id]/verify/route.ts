import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { method } = await request.json();
    const { id } = await params;
    const websiteId = parseInt(id);

    if (!['dns', 'html', 'meta'].includes(method)) {
      return NextResponse.json(
        { message: 'Invalid verification method' },
        { status: 400 }
      );
    }

    // Map method names to database enum values
    const methodMapping: { [key: string]: string } = {
      'dns': 'dns',
      'html': 'html_file',
      'meta': 'meta_tag'
    };
    const dbMethod = methodMapping[method];

    // Get website details
    const websiteResult = await clickhouse.query({
      query: 'SELECT url, user_id, verification_code FROM websites WHERE id = {websiteId:UInt32} AND user_id = {userId:UInt32}',
      query_params: {
        websiteId,
        userId: parseInt(session.user.id)
      },
    });

    const websiteData = await websiteResult.json();
    if (websiteData.data.length === 0) {
      return NextResponse.json(
        { message: 'Website not found' },
        { status: 404 }
      );
    }

    const website = websiteData.data[0];
    const websiteUrl = new URL(website.url);
    const domain = websiteUrl.hostname;
    const verificationToken = website.verification_code;

    console.log('Website verification details:', {
      websiteUrl: website.url,
      domain,
      verificationToken,
      method
    });

    let verificationResult = false;
    let verificationDetails = '';

    try {
      switch (method) {
        case 'dns':
          verificationResult = await verifyDNS(domain, verificationToken);
          verificationDetails = verificationResult
            ? 'DNS TXT record found and verified'
            : 'DNS TXT record not found or incorrect';
          break;

        case 'html':
          verificationResult = await verifyHTML(website.url, verificationToken);
          verificationDetails = verificationResult
            ? 'HTML verification file found and verified'
            : 'HTML verification file not found or incorrect';
          break;

        case 'meta':
          verificationResult = await verifyMeta(website.url, verificationToken);
          verificationDetails = verificationResult
            ? 'Meta tag found and verified'
            : 'Meta tag not found or incorrect';
          break;
      }
    } catch (error) {
      verificationDetails = `Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }

    // Update website verification status
    if (verificationResult) {
      await clickhouse.command({
        query: `
          ALTER TABLE websites
          UPDATE
            verification_status = 'verified',
            verification_method = {method:String},
            verified_at = {verifiedAt:String}
          WHERE id = {websiteId:UInt32}
        `,
        query_params: {
          method: dbMethod,
          verifiedAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
          websiteId,
        },
      });
    }

    // Log verification attempt
    await clickhouse.insert({
      table: 'website_verification_logs',
      values: [{
        id: Date.now() * 1000 + Math.floor(Math.random() * 1000),
        website_id: websiteId,
        user_id: parseInt(session.user.id),
        method: dbMethod,
        success: verificationResult ? 1 : 0,
        details: verificationDetails,
        attempted_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    return NextResponse.json({
      success: verificationResult,
      method,
      details: verificationDetails,
      verified: verificationResult,
    });

  } catch (error) {
    console.error('Website verification error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function verifyDNS(domain: string, token: string): Promise<boolean> {
  try {
    console.log(`DNS Verification via Cloudflare - Domain: ${domain}, Token: ${token}`);

    // Use Cloudflare DNS over HTTPS API
    const dnsUrl = `https://cloudflare-dns.com/dns-query?name=${encodeURIComponent(domain)}&type=TXT`;

    const response = await fetch(dnsUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/dns-json',
        'User-Agent': 'GlobalAdsMedia-DNS-Verification/1.0',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      console.error('Cloudflare DNS API error:', response.status, response.statusText);
      return false;
    }

    const dnsData = await response.json();
    console.log('Cloudflare DNS response:', dnsData);

    if (!dnsData.Answer || dnsData.Answer.length === 0) {
      console.log('No TXT records found for domain');
      return false;
    }

    const expectedRecord = `globaladsmedia-verification=${token}`;
    console.log('Expected record:', expectedRecord);

    // Check each TXT record
    for (const record of dnsData.Answer) {
      if (record.type === 16) { // TXT record type
        // Remove quotes from the TXT record data
        const recordData = record.data.replace(/^"|"$/g, '');
        console.log('Checking TXT record:', recordData);

        // Check for exact match
        if (recordData === expectedRecord) {
          console.log('✅ Exact match found!');
          return true;
        }

        // Check if it contains the expected record
        if (recordData.includes(expectedRecord)) {
          console.log('✅ Contains match found!');
          return true;
        }

        // Check for case-insensitive match
        if (recordData.toLowerCase().includes(expectedRecord.toLowerCase())) {
          console.log('✅ Case-insensitive match found!');
          return true;
        }
      }
    }

    console.log('❌ No matching DNS record found');
    return false;
  } catch (error) {
    console.error('DNS verification error:', error);
    return false;
  }
}

async function verifyHTML(websiteUrl: string, token: string): Promise<boolean> {
  try {
    const verificationUrl = `${websiteUrl.replace(/\/$/, '')}/globaladsmedia-${token}.html`;
    const response = await fetch(verificationUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'GlobalAdsMedia-Verification/1.0',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      return false;
    }

    const content = await response.text();
    return content.includes(token) && content.includes('globaladsmedia-verification');
  } catch (error) {
    console.error('HTML verification error:', error);
    return false;
  }
}

async function verifyMeta(websiteUrl: string, token: string): Promise<boolean> {
  try {
    const response = await fetch(websiteUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'GlobalAdsMedia-Verification/1.0',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      return false;
    }

    const html = await response.text();
    const metaTagRegex = /<meta\s+name=["']globaladsmedia-verification["']\s+content=["']([^"']+)["']/i;
    const match = html.match(metaTagRegex);

    return match && match[1] === token;
  } catch (error) {
    console.error('Meta verification error:', error);
    return false;
  }
}
