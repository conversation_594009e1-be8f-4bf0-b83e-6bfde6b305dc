import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import clickhouse from '@/lib/clickhouse';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'publisher') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const websiteId = parseInt(id);
    if (isNaN(websiteId)) {
      return NextResponse.json(
        { message: 'Invalid website ID' },
        { status: 400 }
      );
    }

    // Get website details for the current publisher
    const websiteResult = await clickhouse.query({
      query: 'SELECT * FROM websites WHERE id = {websiteId:UInt32} AND user_id = {userId:UInt32}',
      query_params: {
        websiteId,
        userId: parseInt(session.user.id)
      },
    });

    const websiteData = await websiteResult.json();
    if (websiteData.data.length === 0) {
      return NextResponse.json(
        { message: 'Website not found' },
        { status: 404 }
      );
    }

    const website = websiteData.data[0];
    return NextResponse.json(website);

  } catch (error) {
    console.error('Website fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
