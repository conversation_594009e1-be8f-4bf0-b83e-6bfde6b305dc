import clickhouse from './clickhouse';

interface CostProcessingParams {
  campaignId: number;
  websiteId: number;
  zoneId: number;
  impressionId: number;
  bidType: 'cpm' | 'cpv';
  bidAmount: number;
  publisherCpm?: number; // Optional: pre-calculated publisher-facing CPM
  partnerId?: number; // Optional: SSP partner ID for revenue sharing (legacy)
  dspPartnerId?: number; // Optional: DSP partner ID for DSP wins
  sspPartnerId?: number; // Optional: SSP partner ID for crediting SSP
}

interface Campaign {
  id: number;
  user_id: number;
  cpm_bid: number;
  daily_budget: number;
  total_budget: number;
  daily_spent: number;
  total_spent: number;
}

interface User {
  id: number;
  balance: number;
  role: string;
}

interface Website {
  id: number;
  user_id: number;
}

export class CostProcessor {
  /**
   * Check if advertiser has sufficient balance and budget for ad delivery
   * DSPs work on credit terms and skip balance checks
   */
  static async checkAdvertiserEligibility(campaignId: number): Promise<{
    eligible: boolean;
    reason?: string;
    campaign?: Campaign;
    advertiser?: User;
  }> {
    try {
      // Get campaign details
      const campaignResult = await clickhouse.query({
        query: 'SELECT * FROM campaigns WHERE id = {campaignId:UInt32}',
        query_params: { campaignId },
      });

      const campaigns = await campaignResult.json();
      if (campaigns.data.length === 0) {
        return { eligible: false, reason: 'Campaign not found' };
      }

      const campaign: Campaign = campaigns.data[0] as Campaign;

      // Get advertiser balance
      const advertiserResult = await clickhouse.query({
        query: 'SELECT * FROM users WHERE id = {userId:UInt32}',
        query_params: { userId: campaign.user_id },
      });

      const advertisers = await advertiserResult.json();
      if (advertisers.data.length === 0) {
        return { eligible: false, reason: 'Advertiser not found' };
      }

      const advertiser: User = advertisers.data[0] as User;

      // Calculate cost per impression
      const costPerImpression = campaign.cpm_bid / 1000;

      // DSPs and SSPs work on credit terms - skip balance checks (negative balance allowed)
      if (advertiser.role === 'dsp' || advertiser.role === 'ssp') {
        return {
          eligible: true,
          campaign,
          advertiser
        };
      }

      // For advertisers - check balance
      if (advertiser.balance < costPerImpression) {
        return {
          eligible: false,
          reason: 'Insufficient advertiser balance',
          campaign,
          advertiser
        };
      }

      // For advertisers - check daily budget (0 means unlimited)
      if (campaign.daily_budget > 0 && (campaign.daily_spent + costPerImpression) > campaign.daily_budget) {
        return {
          eligible: false,
          reason: 'Daily budget exceeded',
          campaign,
          advertiser
        };
      }

      // For advertisers - check total budget (0 means unlimited)
      if (campaign.total_budget > 0 && (campaign.total_spent + costPerImpression) > campaign.total_budget) {
        return {
          eligible: false,
          reason: 'Total budget exceeded',
          campaign,
          advertiser
        };
      }

      return {
        eligible: true,
        campaign,
        advertiser
      };

    } catch (error) {
      console.error('Error checking advertiser eligibility:', error);
      return { eligible: false, reason: 'System error' };
    }
  }

  /**
   * Process cost deduction from advertiser and credit to publisher
   * ONLY when win confirmation is received or impression is tracked
   * For DSP wins: Only deduct cost from DSP and credit SSP/Publisher upon win confirmation
   * For Local campaigns: Process immediately upon impression tracking
   */
  static async processWinConfirmationCost(params: CostProcessingParams & {
    isWinConfirmation?: boolean;
    sspImpressionId?: string;
  }): Promise<{
    success: boolean;
    costDeducted: number;
    publisherRevenue: number;
    error?: string;
  }> {
    try {
      // Check if this is a DSP win (no local campaign)
      const isDspWin = params.campaignId === 0;

      // For DSP wins, only process cost if this is a win confirmation or impression tracking
      if (isDspWin && !params.isWinConfirmation) {
        console.log(`DSP win detected but no win confirmation yet. Skipping cost processing for impression ${params.impressionId}`);
        return {
          success: true,
          costDeducted: 0,
          publisherRevenue: 0,
        };
      }

      // Check if cost has already been processed for this impression to prevent double processing
      const existingImpressionResult = await clickhouse.query({
        query: `
          SELECT cost_deducted, publisher_revenue
          FROM impressions
          WHERE id = {impressionId:UInt64}
        `,
        query_params: { impressionId: params.impressionId },
      });

      const existingImpressionData = await existingImpressionResult.json();
      if (existingImpressionData.data.length > 0) {
        const existingImpression = existingImpressionData.data[0] as any;
        if (existingImpression.cost_deducted > 0 || existingImpression.publisher_revenue > 0) {
          console.log(`Cost already processed for impression ${params.impressionId}. Cost: $${existingImpression.cost_deducted}, Revenue: $${existingImpression.publisher_revenue}`);
          return {
            success: true,
            costDeducted: existingImpression.cost_deducted,
            publisherRevenue: existingImpression.publisher_revenue,
          };
        }
      }

      // Calculate costs - advertiser pays full amount, publisher gets their share
      let costDeducted: number;
      let publisherRevenue: number;

      if (params.publisherCpm) {
        // Publisher-facing CPM already calculated (new system)
        if (params.bidType === 'cpm') {
          costDeducted = params.bidAmount / 1000; // Advertiser pays full CPM
          publisherRevenue = params.publisherCpm / 1000; // Publisher gets their rate
        } else {
          costDeducted = params.bidAmount; // CPV
          publisherRevenue = params.publisherCpm; // Publisher CPV rate
        }
      } else {
        // Calculate using revenue share system
        let revenueShare: number;

        // Check if this is from an SSP partner with specific revenue share
        if (params.partnerId) {
          const partnerResult = await clickhouse.query({
            query: `
              SELECT revenue_share, type
              FROM partner_endpoints
              WHERE id = {partnerId:UInt32} AND status = 'active'
            `,
            query_params: { partnerId: params.partnerId },
          });

          const partners = await partnerResult.json();
          if (partners.data.length > 0) {
            const partner = partners.data[0] as any;
            if (partner.type === 'ssp' && partner.revenue_share > 0) {
              revenueShare = partner.revenue_share;
            } else {
              // Fallback to global settings
              const revenueShareResult = await clickhouse.query({
                query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
              });

              const revenueShareData = await revenueShareResult.json();
              if (revenueShareData.data.length === 0) {
                throw new Error('Publisher revenue share not configured in admin settings');
              }
              revenueShare = parseFloat((revenueShareData.data[0] as any).setting_value);
            }
          } else {
            throw new Error('Partner endpoint not found');
          }
        } else {
          // Regular publisher - use global settings
          const revenueShareResult = await clickhouse.query({
            query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
          });

          const revenueShareData = await revenueShareResult.json();
          if (revenueShareData.data.length === 0) {
            throw new Error('Publisher revenue share not configured in admin settings');
          }
          revenueShare = parseFloat((revenueShareData.data[0] as any).setting_value);
        }

        if (params.bidType === 'cpm') {
          costDeducted = params.bidAmount / 1000;
        } else {
          costDeducted = params.bidAmount;
        }
        publisherRevenue = costDeducted * (revenueShare / 100);
      }

      // Get website owner (publisher/SSP)
      const websiteResult = await clickhouse.query({
        query: 'SELECT user_id FROM websites WHERE id = {websiteId:UInt32}',
        query_params: { websiteId: params.websiteId },
      });

      const websites = await websiteResult.json();
      if (websites.data.length === 0) {
        return {
          success: false,
          costDeducted: 0,
          publisherRevenue: 0,
          error: 'Website not found'
        };
      }

      const website: Website = websites.data[0] as Website;

      // Start transaction-like operations
      if (!isDspWin) {
        // 1. Deduct cost from advertiser balance (local campaigns)
        await clickhouse.command({
          query: `
            ALTER TABLE users
            UPDATE balance = balance - {cost:Decimal(18,8)}
            WHERE id = (SELECT user_id FROM campaigns WHERE id = {campaignId:UInt32})
          `,
          query_params: {
            cost: costDeducted,
            campaignId: params.campaignId,
          },
        });

        // 2. Update campaign spent amounts (local campaigns)
        await clickhouse.command({
          query: `
            ALTER TABLE campaigns
            UPDATE daily_spent = daily_spent + {cost:Decimal(18,8)},
                   total_spent = total_spent + {cost:Decimal(18,8)}
            WHERE id = {campaignId:UInt32}
            AND user_id IN (SELECT id FROM users WHERE role = 'advertiser')
          `,
          query_params: {
            cost: costDeducted,
            campaignId: params.campaignId,
          },
        });
      } else {
        // DSP win: Deduct cost from DSP partner's account (allow negative balance - credit terms)
        if (!params.dspPartnerId) {
          throw new Error('DSP win requires dspPartnerId for billing');
        }

        console.log(`💰 DSP win confirmed: Deducting $${costDeducted} from DSP partner ${params.dspPartnerId} (credit terms - negative balance allowed)`);
        await clickhouse.command({
          query: `
            ALTER TABLE users
            UPDATE balance = balance - {cost:Decimal(18,8)}
            WHERE id = (SELECT user_id FROM partner_endpoints WHERE id = {dspPartnerId:UInt32})
            AND id IN (SELECT id FROM users WHERE role IN ('dsp', 'ssp'))
          `,
          query_params: {
            cost: costDeducted,
            dspPartnerId: params.dspPartnerId,
          },
        });
      }

      // 3. Credit publisher
      await clickhouse.command({
        query: `
          ALTER TABLE users
          UPDATE balance = balance + {revenue:Decimal(18,8)}
          WHERE id = {publisherId:UInt32}
        `,
        query_params: {
          revenue: publisherRevenue,
          publisherId: website.user_id,
        },
      });

      // 4. Credit SSP if this impression came from an SSP request
      // Check if there's an SSP partner associated with this impression
      if (params.sspPartnerId) {
        // Get SSP partner details
        const sspPartnerResult = await clickhouse.query({
          query: `
            SELECT user_id, revenue_share, name
            FROM partner_endpoints
            WHERE id = {sspPartnerId:UInt32} AND type = 'ssp' AND status = 'active'
          `,
          query_params: { sspPartnerId: params.sspPartnerId },
        });

        const sspPartners = await sspPartnerResult.json();
        if (sspPartners.data.length > 0) {
          const sspPartner = sspPartners.data[0] as any;

          // Calculate SSP revenue based on their revenue share
          const sspRevenueShare = sspPartner.revenue_share || revenueShare; // Use SSP-specific or fallback to publisher share
          const sspRevenue = costDeducted * (sspRevenueShare / 100);

          // Credit SSP
          await clickhouse.command({
            query: `
              ALTER TABLE users
              UPDATE balance = balance + {revenue:Decimal(18,8)}
              WHERE id = {sspUserId:UInt32}
            `,
            query_params: {
              revenue: sspRevenue,
              sspUserId: sspPartner.user_id,
            },
          });

          console.log(`💰 SSP credited: ${sspPartner.name} (ID: ${params.sspPartnerId}) received $${sspRevenue.toFixed(6)} (${sspRevenueShare}% of $${costDeducted.toFixed(6)})`);

          // Log SSP transaction
          const sspTransactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000) + 2;
          await clickhouse.insert({
            table: 'transactions',
            values: [{
              id: sspTransactionId,
              user_id: sspPartner.user_id,
              type: 'ad_revenue',
              amount: sspRevenue,
              status: 'completed',
              payment_method: '',
              payment_reference: '',
              description: `SSP revenue from impression ${params.impressionId} - ${isDspWin ? 'DSP' : 'Local'} win`,
              created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
              updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            }],
            format: 'JSONEachRow',
          });
        }
      }

      // 4. Update impression record with cost details
      await clickhouse.command({
        query: `
          ALTER TABLE impressions
          UPDATE cost_deducted = {costDeducted:Decimal(18,8)},
                 publisher_revenue = {publisherRevenue:Decimal(18,8)},
                 bid_type = {bidType:UInt8}
          WHERE id = {impressionId:UInt64}
        `,
        query_params: {
          costDeducted,
          publisherRevenue,
          bidType: params.bidType === 'cpm' ? 1 : 2, // 1 = cpm, 2 = cpv (matching Enum8 values)
          impressionId: params.impressionId,
        },
      });

      // 5. Log transaction for payer (advertiser or DSP partner)
      const payerTransactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

      if (!isDspWin) {
        // Local campaign: Log advertiser transaction
        await clickhouse.insert({
          table: 'transactions',
          values: [{
            id: payerTransactionId,
            user_id: 0, // Will be updated with actual advertiser ID
            type: 'ad_spend',
            amount: costDeducted,
            status: 'completed',
            payment_method: '',
            payment_reference: '',
            description: `Ad impression cost - Campaign ${params.campaignId}`,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });

        // Update advertiser transaction with correct user_id
        await clickhouse.command({
          query: `
            ALTER TABLE transactions
            UPDATE user_id = (SELECT user_id FROM campaigns WHERE id = {campaignId:UInt32})
            WHERE id = {transactionId:UInt64}
          `,
          query_params: {
            campaignId: params.campaignId,
            transactionId: payerTransactionId,
          },
        });
      } else {
        // DSP win: Log DSP partner transaction (use 'ad_spend' type)
        await clickhouse.insert({
          table: 'transactions',
          values: [{
            id: payerTransactionId,
            user_id: 0, // Will be updated with actual DSP partner user ID
            type: 'ad_spend',
            amount: costDeducted,
            status: 'completed',
            payment_method: '',
            payment_reference: '',
            description: `DSP win confirmed - Partner ${params.dspPartnerId}${params.sspImpressionId ? ` (SSP Imp: ${params.sspImpressionId})` : ''}`,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });

        // Update DSP partner transaction with correct user_id
        await clickhouse.command({
          query: `
            ALTER TABLE transactions
            UPDATE user_id = (SELECT user_id FROM partner_endpoints WHERE id = {dspPartnerId:UInt32})
            WHERE id = {transactionId:UInt64}
          `,
          query_params: {
            dspPartnerId: params.dspPartnerId,
            transactionId: payerTransactionId,
          },
        });
      }

      // 6. Log transaction for publisher (credit)
      const publisherTransactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000) + 1;
      const revenueSource = isDspWin ? 'DSP partner' : `Campaign ${params.campaignId}`;
      await clickhouse.insert({
        table: 'transactions',
        values: [{
          id: publisherTransactionId,
          user_id: website.user_id,
          type: 'ad_revenue',
          amount: publisherRevenue,
          status: 'completed',
          payment_method: '',
          payment_reference: '',
          description: `Ad revenue from ${revenueSource} - Website ${params.websiteId}, Zone ${params.zoneId}${params.sspImpressionId ? ` (SSP Imp: ${params.sspImpressionId})` : ''}`,
          created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });

      return {
        success: true,
        costDeducted,
        publisherRevenue,
      };

    } catch (error) {
      console.error('Error processing win confirmation cost:', error);
      return {
        success: false,
        costDeducted: 0,
        publisherRevenue: 0,
        error: 'Failed to process cost'
      };
    }
  }

  /**
   * Legacy method - kept for backward compatibility
   * Process cost deduction from advertiser and credit to publisher
   * For DSP wins (campaignId = 0), only credit publisher - no advertiser deduction
   */
  static async processImpressionCost(params: CostProcessingParams): Promise<{
    success: boolean;
    costDeducted: number;
    publisherRevenue: number;
    error?: string;
  }> {
    try {
      // Check if this is a DSP win (no local campaign)
      const isDspWin = params.campaignId === 0;

      // Check if cost has already been processed for this impression to prevent double processing
      const existingImpressionResult = await clickhouse.query({
        query: `
          SELECT cost_deducted, publisher_revenue
          FROM impressions
          WHERE id = {impressionId:UInt64}
        `,
        query_params: { impressionId: params.impressionId },
      });

      const existingImpressionData = await existingImpressionResult.json();
      if (existingImpressionData.data.length > 0) {
        const existingImpression = existingImpressionData.data[0] as any;
        if (existingImpression.cost_deducted > 0 || existingImpression.publisher_revenue > 0) {
          console.log(`Cost already processed for impression ${params.impressionId}. Cost: $${existingImpression.cost_deducted}, Revenue: $${existingImpression.publisher_revenue}`);
          return {
            success: true,
            costDeducted: existingImpression.cost_deducted,
            publisherRevenue: existingImpression.publisher_revenue,
          };
        }
      }

      // Calculate costs - advertiser pays full amount, publisher gets their share
      let costDeducted: number;
      let publisherRevenue: number;

      if (params.publisherCpm) {
        // Publisher-facing CPM already calculated (new system)
        if (params.bidType === 'cpm') {
          costDeducted = params.bidAmount / 1000; // Advertiser pays full CPM
          publisherRevenue = params.publisherCpm / 1000; // Publisher gets their rate
        } else {
          costDeducted = params.bidAmount; // CPV
          publisherRevenue = params.publisherCpm; // Publisher CPV rate
        }
      } else {
        // Calculate using revenue share system
        let revenueShare: number;

        // Check if this is from an SSP partner with specific revenue share
        if (params.partnerId) {
          const partnerResult = await clickhouse.query({
            query: `
              SELECT revenue_share, type
              FROM partner_endpoints
              WHERE id = {partnerId:UInt32} AND status = 'active'
            `,
            query_params: { partnerId: params.partnerId },
          });

          const partners = await partnerResult.json();
          if (partners.data.length > 0) {
            const partner = partners.data[0] as any;
            if (partner.type === 'ssp' && partner.revenue_share > 0) {
              revenueShare = partner.revenue_share;
            } else {
              // Fallback to global settings
              const revenueShareResult = await clickhouse.query({
                query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
              });

              const revenueShareData = await revenueShareResult.json();
              if (revenueShareData.data.length === 0) {
                throw new Error('Publisher revenue share not configured in admin settings');
              }
              revenueShare = parseFloat((revenueShareData.data[0] as any).setting_value);
            }
          } else {
            throw new Error('Partner endpoint not found');
          }
        } else {
          // Regular publisher - use global settings
          const revenueShareResult = await clickhouse.query({
            query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
          });

          const revenueShareData = await revenueShareResult.json();
          if (revenueShareData.data.length === 0) {
            throw new Error('Publisher revenue share not configured in admin settings');
          }
          revenueShare = parseFloat((revenueShareData.data[0] as any).setting_value);
        }

        if (params.bidType === 'cpm') {
          costDeducted = params.bidAmount / 1000;
        } else {
          costDeducted = params.bidAmount;
        }
        publisherRevenue = costDeducted * (revenueShare / 100);
      }

      // Get website owner (publisher/SSP)
      const websiteResult = await clickhouse.query({
        query: 'SELECT user_id FROM websites WHERE id = {websiteId:UInt32}',
        query_params: { websiteId: params.websiteId },
      });

      const websites = await websiteResult.json();
      if (websites.data.length === 0) {
        return {
          success: false,
          costDeducted: 0,
          publisherRevenue: 0,
          error: 'Website not found'
        };
      }

      const website: Website = websites.data[0] as Website;

      // Start transaction-like operations
      if (!isDspWin) {
        // 1. Deduct cost from advertiser balance (local campaigns)
        await clickhouse.command({
          query: `
            ALTER TABLE users
            UPDATE balance = balance - {cost:Decimal(18,8)}
            WHERE id = (SELECT user_id FROM campaigns WHERE id = {campaignId:UInt32})
          `,
          query_params: {
            cost: costDeducted,
            campaignId: params.campaignId,
          },
        });

        // 2. Update campaign spent amounts (local campaigns)
        await clickhouse.command({
          query: `
            ALTER TABLE campaigns
            UPDATE daily_spent = daily_spent + {cost:Decimal(18,8)},
                   total_spent = total_spent + {cost:Decimal(18,8)}
            WHERE id = {campaignId:UInt32}
            AND user_id IN (SELECT id FROM users WHERE role = 'advertiser')
          `,
          query_params: {
            cost: costDeducted,
            campaignId: params.campaignId,
          },
        });
      } else {
        // DSP win: Deduct cost from DSP partner's account (allow negative balance - credit terms)
        if (!params.partnerEndpointId) {
          throw new Error('DSP win requires partnerEndpointId for billing');
        }

        console.log(`💰 DSP win: Deducting $${costDeducted} from DSP partner endpoint ${params.partnerEndpointId} (credit terms - negative balance allowed)`);
        await clickhouse.command({
          query: `
            ALTER TABLE users
            UPDATE balance = balance - {cost:Decimal(18,8)}
            WHERE id = (SELECT user_id FROM partner_endpoints WHERE id = {partnerEndpointId:UInt32})
            AND id IN (SELECT id FROM users WHERE role IN ('dsp', 'ssp'))
          `,
          query_params: {
            cost: costDeducted,
            partnerEndpointId: params.partnerEndpointId,
          },
        });
      }

      // 3. Credit publisher/SSP
      await clickhouse.command({
        query: `
          ALTER TABLE users
          UPDATE balance = balance + {revenue:Decimal(18,8)}
          WHERE id = {publisherId:UInt32}
        `,
        query_params: {
          revenue: publisherRevenue,
          publisherId: website.user_id,
        },
      });

      // 4. Update impression record with cost details
      await clickhouse.command({
        query: `
          ALTER TABLE impressions
          UPDATE cost_deducted = {costDeducted:Decimal(18,8)},
                 publisher_revenue = {publisherRevenue:Decimal(18,8)},
                 bid_type = {bidType:UInt8}
          WHERE id = {impressionId:UInt64}
        `,
        query_params: {
          costDeducted,
          publisherRevenue,
          bidType: params.bidType === 'cpm' ? 1 : 2, // 1 = cpm, 2 = cpv (matching Enum8 values)
          impressionId: params.impressionId,
        },
      });

      // 5. Log transaction for payer (advertiser or DSP partner)
      const payerTransactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

      if (!isDspWin) {
        // Local campaign: Log advertiser transaction
        await clickhouse.insert({
          table: 'transactions',
          values: [{
            id: payerTransactionId,
            user_id: 0, // Will be updated with actual advertiser ID
            type: 'ad_spend',
            amount: costDeducted,
            status: 'completed',
            payment_method: '',
            payment_reference: '',
            description: `Ad impression cost - Campaign ${params.campaignId}`,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });

        // Update advertiser transaction with correct user_id
        await clickhouse.command({
          query: `
            ALTER TABLE transactions
            UPDATE user_id = (SELECT user_id FROM campaigns WHERE id = {campaignId:UInt32})
            WHERE id = {transactionId:UInt64}
          `,
          query_params: {
            campaignId: params.campaignId,
            transactionId: payerTransactionId,
          },
        });
      } else {
        // DSP win: Log DSP partner transaction (use 'ad_spend' type)
        await clickhouse.insert({
          table: 'transactions',
          values: [{
            id: payerTransactionId,
            user_id: 0, // Will be updated with actual DSP partner user ID
            type: 'ad_spend',
            amount: costDeducted,
            status: 'completed',
            payment_method: '',
            payment_reference: '',
            description: `DSP impression cost - Partner endpoint ${params.partnerEndpointId}`,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });

        // Update DSP partner transaction with correct user_id
        await clickhouse.command({
          query: `
            ALTER TABLE transactions
            UPDATE user_id = (SELECT user_id FROM partner_endpoints WHERE id = {partnerEndpointId:UInt32})
            WHERE id = {transactionId:UInt64}
          `,
          query_params: {
            partnerEndpointId: params.partnerEndpointId,
            transactionId: payerTransactionId,
          },
        });
      }

      // 6. Log transaction for publisher (credit)
      const publisherTransactionId = Date.now() * 1000 + Math.floor(Math.random() * 1000) + 1;
      const revenueSource = isDspWin ? 'DSP partner' : `Campaign ${params.campaignId}`;
      await clickhouse.insert({
        table: 'transactions',
        values: [{
          id: publisherTransactionId,
          user_id: website.user_id,
          type: 'ad_revenue',
          amount: publisherRevenue,
          status: 'completed',
          payment_method: '',
          payment_reference: '',
          description: `Ad revenue from ${revenueSource} - Website ${params.websiteId}, Zone ${params.zoneId}`,
          created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        }],
        format: 'JSONEachRow',
      });

      return {
        success: true,
        costDeducted,
        publisherRevenue,
      };

    } catch (error) {
      console.error('Error processing impression cost:', error);
      return {
        success: false,
        costDeducted: 0,
        publisherRevenue: 0,
        error: 'Failed to process cost'
      };
    }
  }

  /**
   * Get DSP/SSP configuration for bid type conversion
   */
  static async getDspSspConfig(userId: number, type: 'dsp' | 'ssp'): Promise<{
    bidType: 'cpm' | 'cpv';
    revenueShare: number;
  } | null> {
    try {
      const configResult = await clickhouse.query({
        query: `
          SELECT bid_type, revenue_share
          FROM dsp_ssp_configs
          WHERE user_id = {userId:UInt32} AND type = {type:String} AND status = 'active'
          LIMIT 1
        `,
        query_params: { userId, type },
      });

      const configs = await configResult.json();
      if (configs.data.length === 0) {
        return null;
      }

      const config = configs.data[0] as any;
      return {
        bidType: config.bid_type === 1 ? 'cpm' : 'cpv',
        revenueShare: parseFloat(config.revenue_share),
      };

    } catch (error) {
      console.error('Error getting DSP/SSP config:', error);
      return null;
    }
  }

  /**
   * Convert CPM to CPV or vice versa based on DSP/SSP requirements
   */
  static convertBidAmount(amount: number, fromType: 'cpm' | 'cpv', toType: 'cpm' | 'cpv'): number {
    if (fromType === toType) {
      return amount;
    }

    if (fromType === 'cpm' && toType === 'cpv') {
      return amount / 1000; // CPM to CPV
    }

    if (fromType === 'cpv' && toType === 'cpm') {
      return amount * 1000; // CPV to CPM
    }

    return amount;
  }
}
