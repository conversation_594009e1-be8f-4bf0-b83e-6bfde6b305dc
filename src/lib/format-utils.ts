/**
 * Utility functions for formatting numbers, currency, and dates in US format
 */

/**
 * Format number with US locale (commas for thousands)
 * Examples: 1,234 | 1,234,567 | 0
 */
export const formatNumber = (num: number | string): string => {
  const number = typeof num === 'string' ? parseFloat(num) : num;
  if (isNaN(number)) return '0';
  return new Intl.NumberFormat('en-US').format(number);
};

/**
 * Format currency in USD with US locale
 * Examples: $1,234.56 | $0.00 | $1,234,567.89
 */
export const formatCurrency = (amount: number | string): string => {
  const number = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(number)) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(number);
};

/**
 * Format percentage with 2 decimal places
 * Examples: 12.34% | 0.00% | 100.00%
 */
export const formatPercentage = (value: number | string): string => {
  const number = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(number)) return '0.00%';
  return `${number.toFixed(2)}%`;
};

/**
 * Format date in US format
 * Examples: Jan 15, 2024 | Dec 31, 2023
 */
export const formatDate = (dateString: string | Date): string => {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid Date';
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Format date and time in US format
 * Examples: Jan 15, 2024 at 3:45 PM
 */
export const formatDateTime = (dateString: string | Date): string => {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid Date';
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

/**
 * Format large numbers with K, M, B suffixes
 * Examples: 1.2K | 1.5M | 2.3B
 */
export const formatCompactNumber = (num: number | string): string => {
  const number = typeof num === 'string' ? parseFloat(num) : num;
  if (isNaN(number)) return '0';
  
  if (number >= 1000000000) {
    return (number / 1000000000).toFixed(1) + 'B';
  }
  if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M';
  }
  if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K';
  }
  return formatNumber(number);
};

/**
 * Format decimal numbers with specified precision
 * Examples: 12.34 | 0.00 | 1,234.567
 */
export const formatDecimal = (num: number | string, decimals: number = 2): string => {
  const number = typeof num === 'string' ? parseFloat(num) : num;
  if (isNaN(number)) return '0.' + '0'.repeat(decimals);
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(number);
};

/**
 * Safe number conversion that handles null/undefined/empty values
 */
export const safeNumber = (value: any): number => {
  if (value === null || value === undefined || value === '') return 0;
  const num = typeof value === 'string' ? parseFloat(value) : Number(value);
  return isNaN(num) ? 0 : num;
};
