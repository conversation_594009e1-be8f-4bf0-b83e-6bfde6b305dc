/**
 * QPS (Queries Per Second) Rate Limiter for DSP Partner Endpoints
 * Ensures we don't exceed the configured QPS limits for each partner
 */
import logger from '@/lib/logger';

interface QpsRecord {
  partnerId: number;
  qpsLimit: number;
  requests: number[];
  lastCleanup: number;
}

export class QpsLimiter {
  private static records = new Map<number, QpsRecord>();
  private static readonly WINDOW_SIZE_MS = 1000; // 1 second window
  private static readonly CLEANUP_INTERVAL_MS = 5000; // Clean up every 5 seconds

  /**
   * Check if a request to a partner is allowed based on QPS limit
   */
  static isRequestAllowed(partnerId: number, qpsLimit: number): boolean {
    const now = Date.now();

    // Get or create record for this partner
    let record = this.records.get(partnerId);
    if (!record) {
      record = {
        partnerId,
        qpsLimit,
        requests: [],
        lastCleanup: now
      };
      this.records.set(partnerId, record);
    }

    // Update QPS limit if it changed
    record.qpsLimit = qpsLimit;

    // Clean up old requests (older than 1 second)
    this.cleanupOldRequests(record, now);

    // Check if we're within the QPS limit
    if (record.requests.length >= qpsLimit) {
      logger.dsp.warn(`QPS Limiter: Partner ${partnerId} rate limited (${record.requests.length}/${qpsLimit} QPS)`);
      return false;
    }

    // Allow the request and record it
    record.requests.push(now);
    logger.dsp.info(`QPS Limiter: Partner ${partnerId} request allowed (${record.requests.length}/${qpsLimit} QPS)`);
    return true;
  }

  /**
   * Remove requests older than the window size
   */
  private static cleanupOldRequests(record: QpsRecord, now: number): void {
    const cutoff = now - this.WINDOW_SIZE_MS;
    record.requests = record.requests.filter(timestamp => timestamp > cutoff);
    record.lastCleanup = now;
  }

  /**
   * Get current QPS usage for a partner
   */
  static getCurrentQps(partnerId: number): { current: number; limit: number } {
    const record = this.records.get(partnerId);
    if (!record) {
      return { current: 0, limit: 0 };
    }

    const now = Date.now();
    this.cleanupOldRequests(record, now);

    return {
      current: record.requests.length,
      limit: record.qpsLimit
    };
  }

  /**
   * Get QPS statistics for all partners
   */
  static getAllQpsStats(): Array<{ partnerId: number; current: number; limit: number; utilization: number }> {
    const stats: Array<{ partnerId: number; current: number; limit: number; utilization: number }> = [];

    for (const [partnerId, record] of this.records.entries()) {
      const now = Date.now();
      this.cleanupOldRequests(record, now);

      const utilization = record.qpsLimit > 0 ? (record.requests.length / record.qpsLimit) * 100 : 0;

      stats.push({
        partnerId,
        current: record.requests.length,
        limit: record.qpsLimit,
        utilization: Math.round(utilization * 100) / 100
      });
    }

    return stats.sort((a, b) => b.utilization - a.utilization);
  }

  /**
   * Reset QPS counters for a specific partner (useful for testing)
   */
  static resetPartner(partnerId: number): void {
    const record = this.records.get(partnerId);
    if (record) {
      record.requests = [];
      logger.dsp.info(`QPS Limiter: Reset counters for partner ${partnerId}`);
    }
  }

  /**
   * Reset all QPS counters (useful for testing)
   */
  static resetAll(): void {
    for (const record of this.records.values()) {
      record.requests = [];
    }
    logger.dsp.info('QPS Limiter: Reset all counters');
  }

  /**
   * Periodic cleanup of old records and requests
   */
  static performMaintenance(): void {
    const now = Date.now();
    const cutoff = now - (this.CLEANUP_INTERVAL_MS * 2); // Remove records inactive for 10+ seconds

    for (const [partnerId, record] of this.records.entries()) {
      // Clean up old requests
      this.cleanupOldRequests(record, now);

      // Remove inactive records
      if (record.lastCleanup < cutoff && record.requests.length === 0) {
        this.records.delete(partnerId);
        logger.dsp.info(`QPS Limiter: Removed inactive record for partner ${partnerId}`);
      }
    }
  }
}

// Start periodic maintenance
setInterval(() => {
  QpsLimiter.performMaintenance();
}, QpsLimiter['CLEANUP_INTERVAL_MS']);
