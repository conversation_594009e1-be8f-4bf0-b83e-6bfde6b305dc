import fs from 'fs';
import path from 'path';

const LOG_DIR = path.join(process.cwd(), 'logs');

// Track file creation times for hourly rotation
const fileCreationTimes = new Map<string, number>();

const createLogger = (category: string) => {
  const getLogFileName = (cat: string) => {
    return path.join(LOG_DIR, `${cat}.log`);
  };

  const shouldRotateFile = (fileName: string): boolean => {
    const now = Date.now();
    const creationTime = fileCreationTimes.get(fileName);

    if (!creationTime) {
      // File doesn't exist or we don't have creation time, check if file exists
      if (fs.existsSync(fileName)) {
        const stats = fs.statSync(fileName);
        const fileAge = now - stats.mtimeMs;
        fileCreationTimes.set(fileName, stats.mtimeMs);
        return fileAge > 60 * 60 * 1000; // 1 hour
      }
      return false;
    }

    return (now - creationTime) > 60 * 60 * 1000; // 1 hour
  };

  const rotateLogFile = (fileName: string) => {
    try {
      // Truncate the file to 0 bytes (clear it)
      fs.writeFileSync(fileName, '');
      fileCreationTimes.set(fileName, Date.now());
      console.log(`Rotated log file: ${path.basename(fileName)} (hourly rotation)`);
    } catch (error) {
      console.error(`Failed to rotate log file ${fileName}:`, error);
    }
  };

  const writeLog = (entry: any, cat: string) => {
    const logMessage = `[${entry.timestamp}] [${entry.level}] ${cat.toUpperCase()}: ${entry.message}${entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : ''}\n`;
    const fileName = getLogFileName(cat);

    // Check if file needs rotation before writing
    if (shouldRotateFile(fileName)) {
      rotateLogFile(fileName);
    }

    fs.appendFile(fileName, logMessage, (err) => {
      if (err) {
        console.error(`Failed to write to log file ${fileName}:`, err);
      }
    });
  };

  return {
    info: (...args: any[]) => {
      writeLog({ timestamp: new Date().toISOString(), level: 'INFO', message: args[0], data: args[1] }, category);
    },
    warn: (...args: any[]) => {
      writeLog({ timestamp: new Date().toISOString(), level: 'WARN', message: args[0], data: args[1] }, category);
    },
    error: (...args: any[]) => {
      writeLog({ timestamp: new Date().toISOString(), level: 'ERROR', message: args[0], data: args[1] }, category);
    },
    debug: (...args: any[]) => {
      writeLog({ timestamp: new Date().toISOString(), level: 'DEBUG', message: args[0], data: args[1] }, category);
    },
  };
};

const logger = {
  // Core RTB loggers
  auction: createLogger('auction'),
  dsp: createLogger('dsp'),
  ssp: createLogger('ssp'),

  // System loggers
  admin: createLogger('admin'),
  app: createLogger('app'),
  api: createLogger('api'),
  serve: createLogger('serve'),
  db: createLogger('db'),

  // Feature loggers
  fraud: createLogger('fraud'),
  campaign: createLogger('campaign'),
  user: createLogger('user'),
  tracking: createLogger('tracking'),

  // Infrastructure loggers
  cache: createLogger('cache'),
  queue: createLogger('queue'),
  dsp: createLogger('dsp'),
  health: createLogger('health'),
  circuitBreaker: createLogger('circuitBreaker'),

  // Development loggers
  test: createLogger('test'),

  // Utility functions for the logger itself, not specific categories
  logDir: LOG_DIR,
  rotationInterval: 60 * 60 * 1000, // 1 hour in milliseconds
  cleanupInterval: null as NodeJS.Timeout | null,

  ensureLogDirectory: () => {
    if (!fs.existsSync(LOG_DIR)) {
      fs.mkdirSync(LOG_DIR, { recursive: true });
    }
  },

  startRotationInterval: () => {
    if (logger.cleanupInterval) {
      clearInterval(logger.cleanupInterval);
    }

    // Run rotation check every 10 minutes
    logger.cleanupInterval = setInterval(logger.rotateAllLogs, 10 * 60 * 1000);

    // Initialize file creation times for existing files
    logger.initializeFileCreationTimes();

    console.log('🔄 Logger hourly rotation system started');
  },

  initializeFileCreationTimes: () => {
    const loggerCategories = Object.keys(logger).filter(key =>
      typeof (logger as any)[key].info === 'function'
    );

    loggerCategories.forEach(category => {
      const fileName = path.join(LOG_DIR, `${category}.log`);
      if (fs.existsSync(fileName)) {
        const stats = fs.statSync(fileName);
        fileCreationTimes.set(fileName, stats.mtimeMs);
      }
    });
  },

  rotateAllLogs: () => {
    const loggerCategories = Object.keys(logger).filter(key =>
      typeof (logger as any)[key].info === 'function'
    );

    loggerCategories.forEach(category => {
      const fileName = path.join(LOG_DIR, `${category}.log`);
      if (fs.existsSync(fileName)) {
        const now = Date.now();
        const creationTime = fileCreationTimes.get(fileName);

        if (creationTime && (now - creationTime) > logger.rotationInterval) {
          try {
            fs.writeFileSync(fileName, '');
            fileCreationTimes.set(fileName, now);
            console.log(`🔄 Rotated log file: ${category}.log (hourly rotation)`);
          } catch (error) {
            console.error(`❌ Failed to rotate log file ${category}.log:`, error);
          }
        }
      }
    });
  },

  cleanupOldFiles: () => {
    // Clean up any non-category log files that might be left over
    fs.readdir(LOG_DIR, (err, files) => {
      if (err) {
        console.error(`Error reading log directory:`, err);
        return;
      }

      const categoryFiles = Object.keys(logger)
        .filter(key => typeof (logger as any)[key].info === 'function')
        .map(cat => `${cat}.log`);

      files.forEach(file => {
        if (!categoryFiles.includes(file) && file.endsWith('.log')) {
          const filePath = path.join(LOG_DIR, file);
          fs.unlink(filePath, unlinkErr => {
            if (unlinkErr) {
              console.error(`Error deleting old log file ${filePath}:`, unlinkErr);
            } else {
              console.log(`🗑️ Deleted old log file: ${file}`);
            }
          });
        }
      });
    });
  },

  // Get log file statistics
  getLogStats: () => {
    const stats: { [key: string]: { size: number; lastModified: Date; age: number } } = {};
    const loggerCategories = Object.keys(logger).filter(key =>
      typeof (logger as any)[key].info === 'function'
    );

    loggerCategories.forEach(category => {
      const fileName = path.join(LOG_DIR, `${category}.log`);
      if (fs.existsSync(fileName)) {
        const fileStats = fs.statSync(fileName);
        const creationTime = fileCreationTimes.get(fileName) || fileStats.mtimeMs;

        stats[category] = {
          size: fileStats.size,
          lastModified: new Date(fileStats.mtimeMs),
          age: Date.now() - creationTime,
        };
      }
    });

    return stats;
  }
};

// Initialize logger system
logger.ensureLogDirectory();
logger.startRotationInterval();
logger.cleanupOldFiles();

export default logger;
