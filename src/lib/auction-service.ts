import clickhouse from '@/lib/clickhouse';
import { RequestTracker } from '@/lib/request-tracker';
import { QpsLimiter } from '@/lib/qps-limiter';
import { CircuitBreaker } from '@/lib/circuit-breaker';
import logger from '@/lib/logger';
import { DspHealthMonitor, PartnerEndpoint } from '@/lib/dsp-health-monitor';
import { CACHE_TTL, <PERSON>ache<PERSON>elper, CACHE_KEYS } from '@/lib/cache';
import HighPerformanceCache from '@/lib/high-performance-cache';
import HighPerformanceHttpClient from '@/lib/http-client';
import { createHash } from 'crypto'; // Import createHash for hashing

export interface Website {
  url: string;
  user_id: number;
}

const dspHealthMonitor = DspHealthMonitor.getInstance();
const dspCircuitBreakers: Map<number, CircuitBreaker> = new Map();

// Circuit breaker cleanup interval (every 30 minutes)
const CIRCUIT_BREAKER_CLEANUP_INTERVAL = 30 * 60 * 1000;
let circuitBreakerCleanupTimer: NodeJS.Timeout | null = null;

// Track if DSP Health Monitor has been initialized for this instance
let dspHealthMonitorInitialized = false;

// Initialize circuit breaker cleanup
function initializeCircuitBreakerCleanup() {
  if (!circuitBreakerCleanupTimer) {
    circuitBreakerCleanupTimer = setInterval(() => {
      cleanupInactiveCircuitBreakers();
    }, CIRCUIT_BREAKER_CLEANUP_INTERVAL);
  }
}

// Cleanup inactive circuit breakers to prevent memory leaks
function cleanupInactiveCircuitBreakers() {
  const now = Date.now();
  const inactiveThreshold = 60 * 60 * 1000; // 1 hour of inactivity

  for (const [partnerId, circuitBreaker] of dspCircuitBreakers.entries()) {
    // Check if circuit breaker has been inactive for too long
    const lastUsed = (circuitBreaker as any).lastUsed || now;
    if (now - lastUsed > inactiveThreshold) {
      dspCircuitBreakers.delete(partnerId);
      logger.dsp.info(`Cleaned up inactive circuit breaker for DSP partner ${partnerId}`);
    }
  }

  logger.dsp.info(`Circuit breaker cleanup completed. Active breakers: ${dspCircuitBreakers.size}`);
}

// Initialize cleanup on module load
initializeCircuitBreakerCleanup();

// Type definitions for database objects
export interface Campaign {
  id: number;
  cpm_bid: number;
  landing_url: string;
  creative_type: string | number;
  user_id?: number;
  [key: string]: any;
}

interface Targeting {
  ad_formats?: string[];
  geo?: {
    countries?: string[];
    states?: string[];
  };
  device?: {
    devices?: string[];
    os?: string[];
    browsers?: string[];
  };
}

interface Partner {
  id: number;
  name: string;
  endpoint_url: string;
  protocol: string;
  openrtb_version?: string;
  api_key?: string;
  timeout_ms?: number;
  auth_type?: string;
  auth_credentials?: string;
  seat_id?: string;
  targeting?: string;
  parsedTargeting?: Targeting;
  bid_price_format?: string;
  qps_limit?: number;
  [key: string]: any;
}

export interface AuctionRequest {
  zoneId: number;
  websiteId: number;
  format: string;
  size: string;
  userAgent: string;
  ipAddress: string;
  country: string;
  state: string;
  city: string;
  deviceType: string;
  os: string;
  browser: string;
  referrerUrl?: string;
  connectionType?: string;
  publisherId?: number;
}

export interface AuctionBid {
  source: 'local' | 'dsp';
  campaignId?: number;
  partnerId?: number;
  partnerName?: string;
  bidPrice: number; // CPM
  adMarkup?: string;
  landingUrl?: string;
  creativeType?: string;
  winUrl?: string;
  data?: any;
}

export interface AuctionResult {
  winner: AuctionBid | null;
  allBids: AuctionBid[];
  auctionId: string;
}

// HTTP connection pooling is now handled by HighPerformanceHttpClient

export class AuctionService {
  /**
   * Run a unified auction with both local campaigns and DSP partners
   */
  static async runAuction(request: AuctionRequest): Promise<AuctionResult> {
    const auctionId = `auction_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const startTime = process.hrtime.bigint(); // Start high-resolution timer
    logger.auction.info(`🎯 Starting auction ${auctionId} for zone ${request.zoneId}, format: ${request.format}`);

    // Generate a cache key for the auction request
    const requestCacheKey = `auction_result:${request.zoneId}:${request.format}:${request.size}:${request.country}:${request.deviceType}:${createHash('md5').update(request.ipAddress).digest('hex')}`;

    try {
      const cachedResult = await CacheHelper.getOrSet(
        requestCacheKey,
        async () => {
          logger.auction.info(`Auction ${auctionId}: Cache miss for request, proceeding with auction logic.`);
          const allBids: AuctionBid[] = [];

          let websiteDomain = 'unknown.com'; // Default fallback
          let websiteUserId = 0;
          if (request.websiteId) {
            // Define cache key for website data
            const websiteCacheKey = `website:${request.websiteId}`;

            // Get website data from cache or DB
            const websiteData = await CacheHelper.getOrSet(
              websiteCacheKey,
              async () => {
                logger.auction.info(`Auction ${auctionId}: Website cache miss for ID ${request.websiteId}, querying DB...`);
                const dbStartTime = process.hrtime.bigint();
                const websiteResult = await clickhouse.query({
                  query: `SELECT url, user_id FROM websites WHERE id = {websiteId:UInt32}`,
                  query_params: { websiteId: request.websiteId },
                });
                const data = await websiteResult.json();
                const dbEndTime = process.hrtime.bigint();
                const dbDurationMs = Number(dbEndTime - dbStartTime) / 1e6;
                logger.auction.info(`Auction ${auctionId}: Website DB query took ${dbDurationMs.toFixed(2)} ms, returned ${data.data.length > 0 ? 1 : 0} result.`);
                return data.data.length > 0 ? data.data[0] : null;
              },
              CACHE_TTL.SETTINGS // Use a reasonable TTL for website settings
            );

            if (websiteData) {
              const website = websiteData as Website;
              try {
                websiteDomain = new URL(website.url).hostname;
                logger.auction.info(`Auction ${auctionId}: Using website domain ${websiteDomain} for ID ${request.websiteId}`);
              } catch (e) {
                logger.auction.warn(`Auction ${auctionId}: Invalid website URL for ID ${request.websiteId}: ${website.url}, using default domain.`);
              }
              // Store website user_id for tracking later
              websiteUserId = website.user_id;
            } else {
              logger.auction.warn(`Auction ${auctionId}: Website with ID ${request.websiteId} not found in cache or DB, using default domain.`);
            }
          }

          const websiteLookupEndTime = process.hrtime.bigint();
          const websiteLookupDurationMs = Number(websiteLookupEndTime - startTime) / 1e6;
          logger.auction.info(`Auction ${auctionId}: Website lookup took ${websiteLookupDurationMs.toFixed(2)} ms`);

          // Get publisher revenue share based on request type
          let publisherRevenueShare = 20; // Default fallback - 20% as specified
          try {
            // For SSP requests, check partner_endpoints.revenue_share first
            if (request.publisherId && request.publisherId > 0) {
              // Check if this publisher is an SSP partner
              const sspPartnerResult = await clickhouse.query({
                query: `
                  SELECT revenue_share
                  FROM partner_endpoints
                  WHERE user_id = {publisherId:UInt32} AND type = 'ssp' AND status = 'active'
                  LIMIT 1
                `,
                query_params: { publisherId: request.publisherId },
              });

              const sspPartnerData = await sspPartnerResult.json();
              if (sspPartnerData.data.length > 0) {
                const sspRevenueShare = (sspPartnerData.data[0] as any).revenue_share;
                if (sspRevenueShare && sspRevenueShare > 0) {
                  publisherRevenueShare = sspRevenueShare;
                  logger.auction.info(`Auction ${auctionId}: Using SSP-specific revenue share: ${publisherRevenueShare}%`);
                } else {
                  // SSP partner exists but no specific revenue share, use platform default
                  const revenueShareSetting = await CacheHelper.getOrSet<string | null>(
                    CACHE_KEYS.PUBLISHER_REVENUE_SHARE,
                    async () => {
                      const revenueShareResult = await clickhouse.query({
                        query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
                      });
                      const revenueShareData = await revenueShareResult.json();
                      return revenueShareData.data.length > 0 ? String((revenueShareData.data[0] as any).setting_value) : null;
                    },
                    CACHE_TTL.SETTINGS
                  );
                  publisherRevenueShare = revenueShareSetting !== null ? parseFloat(revenueShareSetting) || 20 : 20;
                  logger.auction.info(`Auction ${auctionId}: SSP partner with no specific revenue share, using platform default: ${publisherRevenueShare}%`);
                }
              } else {
                // Regular publisher - use platform settings
                const revenueShareSetting = await CacheHelper.getOrSet<string | null>(
                  CACHE_KEYS.PUBLISHER_REVENUE_SHARE,
                  async () => {
                    const revenueShareResult = await clickhouse.query({
                      query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
                    });
                    const revenueShareData = await revenueShareResult.json();
                    return revenueShareData.data.length > 0 ? String((revenueShareData.data[0] as any).setting_value) : null;
                  },
                  CACHE_TTL.SETTINGS
                );
                publisherRevenueShare = revenueShareSetting !== null ? parseFloat(revenueShareSetting) || 20 : 20;
                logger.auction.info(`Auction ${auctionId}: Regular publisher, using platform revenue share: ${publisherRevenueShare}%`);
              }
            } else {
              // No publisher ID, use platform default
              const revenueShareSetting = await CacheHelper.getOrSet<string | null>(
                CACHE_KEYS.PUBLISHER_REVENUE_SHARE,
                async () => {
                  const revenueShareResult = await clickhouse.query({
                    query: `SELECT setting_value FROM platform_settings WHERE setting_key = 'publisher_revenue_share'`,
                  });
                  const revenueShareData = await revenueShareResult.json();
                  return revenueShareData.data.length > 0 ? String((revenueShareData.data[0] as any).setting_value) : null;
                },
                CACHE_TTL.SETTINGS
              );
              publisherRevenueShare = revenueShareSetting !== null ? parseFloat(revenueShareSetting) || 20 : 20;
              logger.auction.info(`Auction ${auctionId}: No publisher ID, using platform default revenue share: ${publisherRevenueShare}%`);
            }
          } catch (error) {
            logger.auction.error(`Auction ${auctionId}: Error fetching revenue share, using default 20%`, error);
          }

          // Run local campaigns and DSP requests in parallel for maximum efficiency
          const [localBids, dspBids] = await Promise.all([
            this.getLocalCampaignBids(request),
            this.getDSPBids(request, websiteDomain)
          ]);

          const bidFetchingEndTime = process.hrtime.bigint();
          const bidFetchingDurationMs = Number(bidFetchingEndTime - websiteLookupEndTime) / 1e6;
          logger.auction.info(`Auction ${auctionId}: Bid fetching (local + DSP) took ${bidFetchingDurationMs.toFixed(2)} ms`);

          allBids.push(...localBids, ...dspBids);

          logger.auction.info(`Auction ${auctionId}: Received ${localBids.length} local bids, ${dspBids.length} DSP bids`);

          // Sort by bid price (highest first) for first-price auction
          allBids.sort((a, b) => b.bidPrice - a.bidPrice);

          const sortingEndTime = process.hrtime.bigint();
          const sortingDurationMs = Number(sortingEndTime - bidFetchingEndTime) / 1e6;
          logger.auction.info(`Auction ${auctionId}: Bid sorting took ${sortingDurationMs.toFixed(2)} ms`);

          const winner = allBids.length > 0 ? allBids[0] : null;

          if (winner) {
            logger.auction.info(`Auction ${auctionId} winner: ${winner.source} with bid $${winner.bidPrice}`);

            // NOTE: Win notifications are NOT recorded here during auction
            // They are only recorded when:
            // 1. Impression is actually tracked via /api/serve
            // 2. SSP sends win notification via /api/ssp/win
            // 3. RTB win notification via /api/rtb/win

            // Track the auction request (for statistics only, not win notification)
            if (winner.source === 'local') {
              // Local campaign won - track as advertiser incoming request
              await RequestTracker.trackRequest({
                sourceType: 'advertiser_incoming',
                campaignId: winner.campaignId,
                websiteId: request.websiteId,
                advertiserId: winner.data?.user_id || 0,
                publisherId: websiteUserId || 0,
                requestCount: 1,
                winCount: 0, // Don't count as win until impression is actually tracked
              });
            } else if (winner.source === 'dsp') {
              // DSP won - track as DSP incoming request
              await RequestTracker.trackRequest({
                sourceType: 'dsp_incoming',
                partnerEndpointId: winner.partnerId,
                websiteId: request.websiteId,
                dspId: winner.data?.user_id || 0,
                publisherId: websiteUserId || 0,
                requestCount: 1,
                winCount: 0, // Don't count as win until impression is actually tracked
              });
            }

            // --- NEW CODE TO NOTIFY DSP --- //
            if (winner.source === 'dsp' && winner.winUrl) {
              // Replace OpenRTB macros in the winUrl
              let dspWinUrl = winner.winUrl.replace('\${AUCTION_PRICE}', winner.bidPrice.toFixed(4));
              // Add other macros if needed, e.g., \${AUCTION_ID}, \${IMPRESSION_ID}

              logger.auction.info(`Auction ${auctionId}: Notifying winning DSP ${winner.partnerName || winner.partnerId} at ${dspWinUrl}`);
              HighPerformanceHttpClient.request({
                url: dspWinUrl,
                method: 'GET',
                timeout: 2000,
                retries: 1,
              })
                .then(response => {
                  if (response.status < 200 || response.status >= 300) {
                    logger.auction.error(`Auction ${auctionId}: Failed to notify DSP ${winner.partnerName || winner.partnerId}. HTTP Status: ${response.status} ${response.statusText}`);
                  } else {
                    logger.auction.info(`Auction ${auctionId}: Successfully notified DSP ${winner.partnerName || winner.partnerId}.`);
                  }
                })
                .catch(error => {
                  logger.auction.error(`Auction ${auctionId}: Error notifying DSP ${winner.partnerName || winner.partnerId}:`, error);
                });
            }
            // --- END NEW CODE --- //

          } else {
            logger.auction.warn(`Auction ${auctionId}: No bids received`);
          }

          const trackingEndTime = process.hrtime.bigint();
          const trackingDurationMs = Number(trackingEndTime - sortingEndTime) / 1e6;
          logger.auction.info(`Auction ${auctionId}: Tracking took ${trackingDurationMs.toFixed(2)} ms`);

          const endTime = process.hrtime.bigint();
          const totalDurationMs = Number(endTime - startTime) / 1e6;
          logger.auction.info(`🎉 Auction ${auctionId} finished in ${totalDurationMs.toFixed(2)} ms`);

          const auctionResult: AuctionResult = {
            winner,
            allBids,
            auctionId
          };

          return auctionResult;
        },
        CACHE_TTL.AUCTION_RESULT // Use the defined TTL for auction results
      );

      // If we reached here, it means either it was cached or the auction ran and stored the result
      return cachedResult;

    } catch (error) {
      logger.auction.error(`Auction ${auctionId}: Top-level error in runAuction (e.g., from cache retrieval):`, error);
      return {
        winner: null,
        allBids: [],
        auctionId
      };
    }
  }

  /**
   * Get bids from local campaigns
   */
  private static async getLocalCampaignBids(request: AuctionRequest): Promise<AuctionBid[]> {
    const startTime = process.hrtime.bigint(); // Start high-resolution timer
    const formatSize = `${request.format}:${request.size}`;
    try {
      // Define cache key for local campaigns based on format and size
      const cacheKey = `active_local_campaigns:${formatSize}`;

      // Get local campaigns from cache or DB
      const campaigns = await CacheHelper.getOrSet(
        cacheKey,
        async () => {
          logger.auction.info(`Local Campaigns (${formatSize}): Cache miss, querying DB...`);
          const dbStartTime = process.hrtime.bigint();
          // Build query with banner size filtering for banner campaigns
          let query = `
            SELECT c.*, u.balance as advertiser_balance, u.role as user_role
            FROM campaigns c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.status = 'active'
            AND c.type = {format:String}
            AND c.start_date <= now()
            AND (c.end_date IS NULL OR c.end_date >= now())`;

          // Add banner size filtering for banner campaigns
          if (request.format === 'banner') {
            query += ` AND c.banner_size = {bannerSize:String}`;
          }

          query += `
            AND (
              u.role = 'dsp' OR
              (u.role = 'advertiser' AND u.balance >= (c.cpm_bid / 1000))
            )
            ORDER BY c.cpm_bid DESC
            LIMIT 20
          `;

          const queryParams: any = { format: request.format };
          if (request.format === 'banner') {
            queryParams.bannerSize = request.size;
          }

          const campaignsResult = await clickhouse.query({
            query,
            query_params: queryParams,
          });

          const data = await campaignsResult.json();
          const dbEndTime = process.hrtime.bigint();
          const dbDurationMs = Number(dbEndTime - dbStartTime) / 1e6;
          logger.auction.info(`Local Campaigns (${formatSize}): DB query took ${dbDurationMs.toFixed(2)} ms, returned ${data.data.length} campaigns.`);
          return data.data;
        },
        CACHE_TTL.CAMPAIGNS // Use defined TTL for campaign data
      );

      const processingStartTime = process.hrtime.bigint();
      const bids: AuctionBid[] = [];

      if (!campaigns || campaigns.length === 0) {
        logger.auction.info(`Local Campaigns (${formatSize}): No active local campaigns found (cache or DB).`);
        const endTime = process.hrtime.bigint();
        const durationMs = Number(endTime - startTime) / 1e6;
        logger.auction.info(`Local Campaigns (${formatSize}): Completed in ${durationMs.toFixed(2)} ms.`);
        return [];
      }

      for (const campaign of campaigns) {
        // Apply targeting filters here if needed
        // For now, include all active campaigns

        // Generate tracking URLs for local campaigns
        const trackingData = this.generateTrackingUrls({
          source: 'local',
          campaignId: (campaign as any).id,
          creativeId: (campaign as any).id.toString(),
          originalLandingUrl: (campaign as any).landing_url,
          originalAdMarkup: this.generateLocalAdMarkup(campaign as any, request.format),
          bidPrice: this.convertToCpm((campaign as any).cpm_bid, request.format),
          format: request.format,
          zoneId: request.zoneId,
          websiteId: request.websiteId
        });

        bids.push({
          source: 'local',
          campaignId: (campaign as any).id,
          bidPrice: this.convertToCpm((campaign as any).cpm_bid, request.format),
          landingUrl: trackingData.clickTrackingUrl,
          creativeType: (campaign as any).creative_type,
          adMarkup: trackingData.trackedAdMarkup,
          data: {
            ...(campaign as Record<string, any>),
            originalLandingUrl: (campaign as any).landing_url,
            impressionTrackingUrl: trackingData.impressionTrackingUrl,
            clickTrackingUrl: trackingData.clickTrackingUrl
          }
        });
      }

      const processingEndTime = process.hrtime.bigint();
      const processingDurationMs = Number(processingEndTime - processingStartTime) / 1e6;
      logger.auction.info(`Local Campaigns (${formatSize}): Processed ${bids.length} bids in ${processingDurationMs.toFixed(2)} ms`);

      const endTime = process.hrtime.bigint();
      const totalDurationMs = Number(endTime - startTime) / 1e6;
      logger.auction.info(`Local Campaigns (${formatSize}): Completed in ${totalDurationMs.toFixed(2)} ms`);

      return bids;

    } catch (error) {
      const endTime = process.hrtime.bigint();
      const totalDurationMs = Number(endTime - startTime) / 1e6;
      logger.auction.error(`Local Campaigns (${formatSize}): Error after ${totalDurationMs.toFixed(2)} ms:`, error);
      return [];
    }
  }

  /**
   * Ensure DSP Health Monitor is initialized only once per instance
   */
  private static async ensureDspHealthMonitorInitialized(partners: PartnerEndpoint[]): Promise<void> {
    if (!dspHealthMonitorInitialized) {
      await dspHealthMonitor.initialize(partners);
      dspHealthMonitorInitialized = true;
    }
  }

  /**
   * Get bids from DSP partners
   */
  private static async getDSPBids(request: AuctionRequest, websiteDomain: string): Promise<AuctionBid[]> {
    const startTime = process.hrtime.bigint(); // Start high-resolution timer
    try {
      // Get active DSP partner endpoints using high-performance cache
      const partners = await HighPerformanceCache.getPartnerEndpoints('dsp');

      // Initialize DSP Health Monitor only once per instance (not on every request)
      // This prevents the infinite loop issue
      await this.ensureDspHealthMonitorInitialized(partners as PartnerEndpoint[]);

      const dspBids: AuctionBid[] = [];

      if (!partners || partners.length === 0) {
        logger.dsp.info('Active DSP Partners: No active DSP partners found (cache or DB)');
        const endTime = process.hrtime.bigint();
        const durationMs = Number(endTime - startTime) / 1e6;
        logger.dsp.info(`Active DSP Partners: Completed in ${durationMs.toFixed(2)} ms.`);
        return [];
      }

      const filteringStartTime = process.hrtime.bigint();
      // Parse targeting data for each partner
      const partnersWithParsedTargeting = partners.map((partner: any) => {
        try {
          const targeting = partner.targeting ? JSON.parse(partner.targeting) : {};
          return { ...partner, parsedTargeting: targeting };
        } catch (error) {
          logger.dsp.warn(`DSP ${partner.name}: Failed to parse targeting data:`, error);
          return { ...partner, parsedTargeting: {} };
        }
      });

      // Filter partners by ad format targeting, geo, device, os, and health status
      const eligibleAndHealthyPartners = partnersWithParsedTargeting.filter((partner: Partner) => {
        // Existing targeting and QPS filtering logic will go here
        try {
          const targeting = partner.parsedTargeting || {}; // Provide a default empty object

          logger.dsp.debug(`DSP ${partner.name}: Partner data:`, {
            id: partner.id,
            name: partner.name,
            protocol: partner.protocol,
            qps_limit: partner.qps_limit,
            targeting: targeting
          });

          // 1. Ad Format Targeting
          const supportedFormats = targeting.ad_formats || [];
          const supportsFormat = supportedFormats.length === 0 || supportedFormats.includes(request.format);
          if (!supportsFormat) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - Does not support ad format '${request.format}'`);
            return false;
          }

          // 2. Geo Targeting (Countries)
          const targetedCountries = targeting.geo?.countries || [];
          const passesCountryTargeting = targetedCountries.length === 0 || targetedCountries.includes(request.country);
          if (!passesCountryTargeting) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - Country '${request.country}' not targeted`);
            return false;
          }

          // 3. Geo Targeting (States/Regions) - only apply if countries are targeted and request has a state
          const targetedStates = targeting.geo?.states || [];
          const passesStateTargeting = targetedStates.length === 0 || !request.state || targetedStates.includes(request.state);
          if (!passesStateTargeting) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - State/Region '${request.state}' not targeted`);
            return false;
          }

          // 4. Device Type Targeting
          const targetedDevices = targeting.device?.devices || [];
          const passesDeviceTargeting = targetedDevices.length === 0 || targetedDevices.includes(request.deviceType);
          if (!passesDeviceTargeting) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - Device type '${request.deviceType}' not targeted`);
            return false;
          }

          // 5. Operating System Targeting
          const targetedOs = targeting.device?.os || [];
          const passesOsTargeting = targetedOs.length === 0 || targetedOs.includes(request.os);
          if (!passesOsTargeting) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - OS '${request.os}' not targeted`);
            return false;
          }

          // 6. Browser Targeting
          const targetedBrowsers = targeting.device?.browsers || [];
          const passesBrowserTargeting = targetedBrowsers.length === 0 || targetedBrowsers.includes(request.browser);
          if (!passesBrowserTargeting) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - Browser '${request.browser}' not targeted`);
            return false;
          }

          // 7. Health Status Check
          if (!dspHealthMonitor.isDspHealthy(partner.id)) {
            logger.dsp.info(`DSP ${partner.name}: FILTERED OUT - Marked UNHEALTHY by Health Monitor.`);
            return false;
          }

          // If all checks pass
          logger.dsp.info(`DSP ${partner.name}: ELIGIBLE for auction`);
          return true;
        } catch (error) {
          logger.dsp.warn(`DSP ${partner.name}: Error parsing targeting, assuming eligible for now:`, error);
          return true; // If targeting parsing fails, include the partner (fail-safe)
        }
      });

      const filteringEndTime = process.hrtime.bigint();
      const filteringDurationMs = Number(filteringEndTime - filteringStartTime) / 1e6;
      logger.dsp.info(`Active DSP Partners: Filtering took ${filteringDurationMs.toFixed(2)} ms. ${eligibleAndHealthyPartners.length}/${partners.length} eligible and healthy.`);

      const qpsFilteringStartTime = process.hrtime.bigint();
      // Apply QPS rate limiting before sending requests
      const qpsFilteredPartners = eligibleAndHealthyPartners.filter((partner: any) => {
        const qpsLimit = partner.qps_limit || 100;
        const isAllowed = QpsLimiter.isRequestAllowed(partner.id, qpsLimit);

        if (!isAllowed) {
          logger.dsp.warn(`DSP ${partner.name}: Rate limited (QPS: ${qpsLimit})`);
        }

        return isAllowed;
      });

      const qpsFilteringEndTime = process.hrtime.bigint();
      const qpsFilteringDurationMs = Number(qpsFilteringEndTime - qpsFilteringStartTime) / 1e6;
      logger.dsp.info(`Active DSP Partners: QPS filtering took ${qpsFilteringDurationMs.toFixed(2)} ms. ${qpsFilteredPartners.length}/${eligibleAndHealthyPartners.length} QPS allowed.`);

      const bidRequestStartTime = process.hrtime.bigint();
      // Send bid requests to QPS-approved partners in parallel
      const bidPromises = qpsFilteredPartners.map(partner =>
        this.sendDSPBidRequest(partner, request, websiteDomain)
      );

      const bidResults = await Promise.allSettled(bidPromises);

      const bidRequestEndTime = process.hrtime.bigint();
      const bidRequestDurationMs = Number(bidRequestEndTime - bidRequestStartTime) / 1e6;
      logger.dsp.info(`Active DSP Partners: Sending ${qpsFilteredPartners.length} bid requests took ${bidRequestDurationMs.toFixed(2)} ms.`);

      // Track DSP requests in request_stats
      const { RequestTracker } = await import('@/lib/request-tracker');

      const trackingStartTime = process.hrtime.bigint();
      // Process results and track requests (use for loop to properly await)
      for (let index = 0; index < bidResults.length; index++) {
        const result = bidResults[index];
        const partner = qpsFilteredPartners[index] as any;

        try {
          if (result.status === 'fulfilled' && result.value) {
            dspBids.push(...result.value);

            // Track successful DSP request with bids
            await RequestTracker.trackRequest({
              sourceType: 'dsp_incoming',
              partnerEndpointId: partner.id,
              requestCount: 1,
              winCount: 0, // Will be updated when DSP actually wins
            });

            logger.dsp.info(`DSP ${partner.name}: Tracked request with ${result.value.length} bids`);
          } else {
            logger.dsp.warn(`DSP ${partner.name} bid failed:`,
              result.status === 'rejected' ? result.reason : 'No bids');

            // Track failed DSP request
            await RequestTracker.trackRequest({
              sourceType: 'dsp_incoming',
              partnerEndpointId: partner.id,
              requestCount: 1,
              winCount: 0,
            });

            logger.dsp.info(`DSP ${partner.name}: Tracked failed request`);
          }
        } catch (trackingError) {
          logger.dsp.error(`DSP ${partner.name}: Failed to track request:`, trackingError);
        }
      }

      const trackingEndTime = process.hrtime.bigint();
      const trackingDurationMs = Number(trackingEndTime - trackingStartTime) / 1e6;
      logger.dsp.info(`Active DSP Partners: Processing bid results and tracking took ${trackingDurationMs.toFixed(2)} ms.`);

      logger.dsp.info(`DSP partners: ${dspBids.length} total bids`);

      const endTime = process.hrtime.bigint();
      const totalDurationMs = Number(endTime - startTime) / 1e6;
      logger.dsp.info(`Active DSP Partners: Completed in ${totalDurationMs.toFixed(2)} ms`);

      return dspBids;

    } catch (error) {
      const endTime = process.hrtime.bigint();
      const totalDurationMs = Number(endTime - startTime) / 1e6;
      logger.dsp.error(`Error getting DSP bids after ${totalDurationMs.toFixed(2)} ms:`, error);
      return [];
    }
  }

  /**
   * Send bid request to a single DSP partner
   */
  private static async sendDSPBidRequest(partner: any, request: AuctionRequest, websiteDomain: string): Promise<AuctionBid[]> {
    const timeout = Math.min(partner.timeout_ms || 100, 100); // Max 100ms for RTB speed
    const maxRetries = 1; // Reduced retries for faster response
    const retryDelayMs = 10; // Reduced retry delay to 10ms

    // Get or create a circuit breaker for this DSP
    let circuitBreaker = dspCircuitBreakers.get(partner.id);
    if (!circuitBreaker) {
      circuitBreaker = new CircuitBreaker({
        failureThreshold: 5,   // Increased to 5 failures for less aggressive circuit breaking
        resetTimeout: 2000, // Reduced to 2 seconds for faster recovery
      });
      dspCircuitBreakers.set(partner.id, circuitBreaker);
    }

    // Update last used time for cleanup tracking
    (circuitBreaker as any).lastUsed = Date.now();

    try {
      return await circuitBreaker.call(async () => {
        for (let i = 0; i <= maxRetries; i++) {
          try {
            logger.dsp.info(`DSP ${partner.name}: Using protocol '${partner.protocol}' for bid request`);

            if (partner.protocol === 'openrtb') {
              logger.dsp.info(`DSP ${partner.name}: Sending OpenRTB request`);
              return await this.sendOpenRTBRequest(partner, request, timeout, websiteDomain);
            } else if (partner.protocol === 'xml') {
              logger.dsp.info(`DSP ${partner.name}: Sending XML request`);
              return await this.sendXMLRequest(partner, request, timeout, websiteDomain);
            } else {
              logger.dsp.warn(`DSP ${partner.name}: Unknown protocol '${partner.protocol}', skipping request`);
            }
            return [];
          } catch (error: any) {
            if (error.name === 'AbortError' && i < maxRetries) {
              logger.dsp.warn(`DSP ${partner.name} request aborted. Retrying... (${i + 1}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, retryDelayMs));
              continue; // Retry the request
            } else {
              throw error; // Propagate other errors or if max retries reached
            }
          }
        }
        return []; // Should not be reached
      }, async () => {
        // Fallback function when circuit is open
        logger.dsp.warn(`DSP ${partner.name}: Circuit is OPEN. Skipping bid request.`);
        return [];
      });
    } catch (error: any) {
      // This catch block will handle errors thrown by the circuit breaker (e.g., when it's OPEN)
      logger.dsp.error(`DSP ${partner.name} ${partner.protocol} ${request.format} request failed due to circuit breaker:`, error);
      return []; // Return empty bids if circuit breaker prevents call or call fails after retries
    }
  }

  /**
   * Send OpenRTB bid request
   */
  private static async sendOpenRTBRequest(partner: any, request: AuctionRequest, timeout: number, websiteDomain: string): Promise<AuctionBid[]> {
    // Build OpenRTB 2.5 compliant bid request
    const bidRequest: any = {
      id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      at: 1, // First-price auction (required for your platform)
      tmax: timeout, // Maximum time in milliseconds to submit a bid
      cur: ['USD'], // Array of allowed currencies
      test: partner.test_mode || 0, // Test mode indicator
      imp: [],
      site: {
        id: request.websiteId.toString(),
        domain: websiteDomain,
        page: request.referrerUrl || `https://${websiteDomain}`,
        cat: [], // Site content categories (can be populated later)
        publisher: {
          id: request.publisherId?.toString() || request.websiteId.toString()
        }
      },
      device: {
        ua: request.userAgent,
        ip: request.ipAddress,
        geo: {
          country: request.country,
          region: request.state,
          city: request.city
        },
        devicetype: request.deviceType === 'mobile' ? 1 : request.deviceType === 'tablet' ? 5 : 2,
        os: request.os,
        language: 'en', // Default language
        connectiontype: request.connectionType === 'wifi' ? 2 : request.connectionType === 'cellular' ? 4 : 0
      },
      user: {
        id: 'anonymous',
        geo: {
          country: request.country,
          region: request.state,
          city: request.city
        }
      },
      source: {
        tid: `txn_${Date.now()}`, // Transaction ID
        pchain: 'global-ads-media' // Payment chain
      },
      regs: {
        coppa: 0 // COPPA compliance (assume not child-directed)
      }
    };

    // Build impression object based on ad format
    const impression: any = {
      id: '1',
      bidfloor: 0.001,
      bidfloorcur: 'USD',
      secure: 1, // Require HTTPS creatives
      tagid: request.zoneId.toString()
    };

    // Handle different ad formats for OpenRTB
    if (request.format === 'banner') {
      const [width, height] = request.size.split('x').map(s => parseInt(s) || 0);
      impression.banner = {
        w: width || 300,
        h: height || 250,
        format: [{
          w: width || 300,
          h: height || 250
        }],
        btype: [], // Blocked creative types
        battr: [], // Blocked creative attributes
        pos: 1, // Ad position (above the fold)
        api: [3, 5] // Supported API frameworks (MRAID 1.0, MRAID 2.0)
      };
    } else if (request.format === 'native') {
      // Native ad request according to OpenRTB Native 1.2
      const nativeRequest = {
        ver: '1.2',
        layout: 1, // Content wall layout
        adunit: 1, // Paid search unit
        plcmtcnt: 1, // Number of identical placements
        assets: [
          {
            id: 1,
            required: 1,
            title: {
              len: 90 // Maximum title length
            }
          },
          {
            id: 2,
            required: 1,
            img: {
              type: 3, // Main image
              wmin: 300,
              hmin: 250
            }
          },
          {
            id: 3,
            required: 0,
            data: {
              type: 2, // Description/body text
              len: 140
            }
          },
          {
            id: 4,
            required: 0,
            img: {
              type: 1, // Icon image
              wmin: 50,
              hmin: 50
            }
          }
        ]
      };

      impression.native = {
        request: JSON.stringify(nativeRequest),
        ver: '1.2',
        api: [3, 5], // Supported API frameworks
        battr: [] // Blocked creative attributes
      };
    }

    bidRequest.imp.push(impression);

    logger.dsp.debug(`DSP ${partner.name} OpenRTB Request Body: ${JSON.stringify(bidRequest, null, 2)}`);

    // Build authentication headers based on partner configuration
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'GlobalAdsMedia/1.0',
      'Accept': 'application/json',
      'X-Openrtb-Version': partner.openrtb_version || '2.5'
    };

    // Handle different authentication types
    if (partner.auth_type && partner.auth_type !== 'none') {
      try {
        const authCredentials = partner.auth_credentials ? JSON.parse(partner.auth_credentials) : {};

        switch (partner.auth_type) {
          case 'api_key':
          case 'bearer':
            if (partner.api_key) {
              headers['Authorization'] = `Bearer ${partner.api_key}`;
            }
            break;

          case 'header':
            if (authCredentials.header_name && authCredentials.header_value) {
              headers[authCredentials.header_name] = authCredentials.header_value;
            }
            break;

          case 'basic':
            if (authCredentials.username && authCredentials.password) {
              const credentials = Buffer.from(`${authCredentials.username}:${authCredentials.password}`).toString('base64');
              headers['Authorization'] = `Basic ${credentials}`;
            }
            break;

          case 'custom':
            if (authCredentials.headers && typeof authCredentials.headers === 'object') {
              Object.assign(headers, authCredentials.headers);
            }
            break;

          default:
            logger.dsp.warn(`DSP ${partner.name}: Unknown auth type '${partner.auth_type}', proceeding without authentication`);
        }
      } catch (error) {
        logger.dsp.error(`DSP ${partner.name}: Error parsing auth credentials:`, error);
      }
    }

    try {
      // Log the actual request being sent
      logger.dsp.info(`DSP ${partner.name} OpenRTB: Sending request to ${partner.endpoint_url}`);
      logger.dsp.debug(`DSP ${partner.name} OpenRTB request body:`, JSON.stringify(bidRequest, null, 2));

      const response = await HighPerformanceHttpClient.sendDspBidRequest(
        partner.endpoint_url,
        bidRequest,
        timeout
      );

      // Log response details before trying to read the body
      logger.dsp.info(`DSP ${partner.name} OpenRTB response status: ${response.status} ${response.statusText}`);
      logger.dsp.info(`DSP ${partner.name} OpenRTB response content-type: ${response.headers['content-type']}`);
      logger.dsp.debug(`DSP ${partner.name} OpenRTB response body:`, response.body.substring(0, 500));

      if (response.status < 200 || response.status >= 300) {
        logger.dsp.error(`DSP ${partner.name} OpenRTB error ${response.status}:`, response.body.substring(0, 200));
        throw new Error(`HTTP ${response.status}`);
      }

      // Handle different response types with proper validation
      const contentType = response.headers['content-type'] || '';
      let bidResponse: any = {};

      try {
        if (contentType.includes('application/json')) {
          bidResponse = JSON.parse(response.body);
        } else {
          // Non-JSON response - might be XML or plain text
          const responseText = response.body;
          logger.dsp.info(`DSP ${partner.name} returned non-JSON response (${contentType}):`, responseText.substring(0, 200));

          // Try to parse as JSON anyway (some DSPs don't set correct content-type)
          try {
            bidResponse = JSON.parse(responseText);
          } catch (parseError) {
            logger.dsp.warn(`DSP ${partner.name} response is not valid JSON:`, parseError);
            return [];
          }
        }
      } catch (error) {
        logger.dsp.error(`DSP ${partner.name} response parsing failed:`, error);
        return [];
      }

      // Validate OpenRTB response structure
      if (!this.validateOpenRTBResponse(bidResponse, partner.name)) {
        return [];
      }

      const bids: AuctionBid[] = [];

      if (bidResponse.seatbid && bidResponse.seatbid.length > 0) {
        for (const seatbid of bidResponse.seatbid) {
          for (const bid of seatbid.bid || []) {
            // Normalize bid price to CPM format for fair comparison
            let normalizedBidPrice = bid.price;
            const bidFormat = partner.bid_price_format || 'cpm';

            if (bidFormat === 'cpv') {
              // Convert CPV to CPM by multiplying by 1000
              normalizedBidPrice = bid.price * 1000;
              logger.dsp.info(`DSP ${partner.name}: Converting CPV bid $${bid.price} to CPM $${normalizedBidPrice}`);
            } else if (bidFormat === 'cpc') {
              // Convert CPC to CPM by multiplying by 1000
              normalizedBidPrice = bid.price * 1000;
              logger.dsp.info(`DSP ${partner.name}: Converting CPC bid $${bid.price} to CPM $${normalizedBidPrice}`);
            } else {
              logger.dsp.info(`DSP ${partner.name}: Using ${bidFormat.toUpperCase()} bid $${bid.price} as-is`);
            }

            let landingUrl = bid.url || bid.click_url || bid.adomain?.[0] || '';
            let adMarkup = bid.adm || bid.ad || bid.creative || '';

            // If adMarkup is not found but landingUrl is present, construct adMarkup based on format
            if (!adMarkup && landingUrl) {
              if (request.format === 'popup' || request.format === 'in_page_push') {
                // For popup/in_page_push, pass the URL directly to the SSP
                adMarkup = landingUrl;
                logger.dsp.info(`DSP ${partner.name}: Using landingUrl directly as adMarkup for ${request.format} format.`);
              } else {
                // For other formats, construct a simple iframe
                adMarkup = `<iframe src="${landingUrl}" style="width:100%;height:100%;border:none;"></iframe>`;
                logger.dsp.info(`DSP ${partner.name}: Constructed iframe adMarkup from landingUrl for JSON response.`);
              }
            }

            // If adMarkup is XML, try to extract landingUrl from it (re-check if it's not already extracted)
            if (adMarkup.startsWith('<?xml') || adMarkup.startsWith('<ad>')) {
              const clickUrlMatch = adMarkup.match(/<clickUrl><!\[CDATA\[([^\]]+)\]\]><\/clickUrl>/i);
              if (clickUrlMatch) {
                landingUrl = clickUrlMatch[1];
              } else {
                // Try to find a direct URL within the XML if clickUrl is not present
                const urlMatch = adMarkup.match(/<url><!\[CDATA\[([^\]]+)\]\]><\/url>/i);
                if (urlMatch) {
                  landingUrl = urlMatch[1];
                }
              }
            }

            // Generate tracking URLs for impression and click tracking
            const trackingData = this.generateTrackingUrls({
              source: 'dsp',
              partnerId: partner.id,
              partnerName: partner.name,
              campaignId: bid.cid || bid.campaign_id,
              creativeId: bid.crid || bid.creative_id || bid.id,
              originalLandingUrl: landingUrl,
              originalAdMarkup: adMarkup,
              bidPrice: normalizedBidPrice,
              format: request.format,
              zoneId: request.zoneId,
              websiteId: request.websiteId,
              winUrl: bid.nurl || bid.win_url || ''
            });

            bids.push({
              source: 'dsp',
              partnerId: partner.id,
              partnerName: partner.name,
              bidPrice: normalizedBidPrice,
              adMarkup: trackingData.trackedAdMarkup,
              landingUrl: trackingData.clickTrackingUrl,
              winUrl: bid.nurl || bid.win_url || '',
              data: {
                ...bid,
                originalAdMarkup: adMarkup,
                originalLandingUrl: landingUrl,
                impressionTrackingUrl: trackingData.impressionTrackingUrl,
                clickTrackingUrl: trackingData.clickTrackingUrl
              }
            });
          }
        }
      }

      return bids;
    } catch (error) {
      // timeoutId is no longer used with HighPerformanceHttpClient
      throw error;
    }
  }

  /**
   * Send XML bid request (simplified for now)
   */
  private static async sendXMLRequest(partner: any, request: AuctionRequest, timeout: number, websiteDomain: string): Promise<AuctionBid[]> {
    logger.dsp.info(`DSP ${partner.name}: Starting XML request preparation`);
    let url: string = '';
    try {
      url = partner.endpoint_url;

      // Enhanced macro replacement for XML requests
      const macros = {
        '{user_agent}': encodeURIComponent(request.userAgent),
        '{ip_address}': request.ipAddress,
        '{zone_id}': request.zoneId.toString(),
        '{ad_format}': request.format,
        '{country}': request.country,
        '{state}': request.state,
        '{city}': request.city,
        '{device_type}': request.deviceType,
        '{os}': request.os,
        '{browser}': request.browser,
        '{page_url}': encodeURIComponent(websiteDomain),
        '{referrer_url}': encodeURIComponent(request.referrerUrl || ''),
        '{connection_type}': request.connectionType || 'unknown',
        '{user_lang}': 'en', // Default language
        '{os_version}': '10.0', // Default OS version
        '{timestamp}': Date.now().toString(),
        '{random}': Math.random().toString(36).substring(2, 11),
        // Size macros for banner formats
        '{width}': request.size.split('x')[0] || '300',
        '{height}': request.size.split('x')[1] || '250',
        '{size}': request.size,
        // Publisher information
        '{website_id}': request.websiteId.toString(),
        '{publisher_id}': request.publisherId?.toString() || request.websiteId.toString()
      };

      Object.entries(macros).forEach(([macro, value]) => {
        url = url.replace(new RegExp(macro.replace(/[{}]/g, '\\$&'), 'g'), value || '');
      });

      logger.dsp.info(`DSP ${partner.name} XML request URL:`, url);
      logger.dsp.debug(`DSP ${partner.name} XML final request URL with macros: ${url}`);

      // Build headers for XML request with authentication
      const headers: Record<string, string> = {
        'User-Agent': 'AdX-Bidder/1.0',
        'Accept': 'application/xml, application/json, text/plain',
      };

      // Add authentication headers for XML requests
      if (partner.auth_type && partner.auth_type !== 'none') {
        try {
          const authCredentials = partner.auth_credentials ? JSON.parse(partner.auth_credentials) : {};

          switch (partner.auth_type) {
            case 'api_key':
            case 'bearer':
              if (partner.api_key) {
                headers['Authorization'] = `Bearer ${partner.api_key}`;
              }
              break;

            case 'header':
              if (authCredentials.header_name && authCredentials.header_value) {
                headers[authCredentials.header_name] = authCredentials.header_value;
              }
              break;

            case 'basic':
              if (authCredentials.username && authCredentials.password) {
                const credentials = Buffer.from(`${authCredentials.username}:${authCredentials.password}`).toString('base64');
                headers['Authorization'] = `Basic ${credentials}`;
              }
              break;
          }
        } catch (error) {
          logger.dsp.error(`DSP ${partner.name}: Error parsing XML auth credentials:`, error);
        }
      }

      // Log the actual request being sent
      logger.dsp.info(`DSP ${partner.name} XML: Sending request to ${url}`);

      const response = await HighPerformanceHttpClient.request({
        url,
        method: 'GET',
        headers,
        timeout,
        retries: 1,
      });

      // Log response details before trying to read the body
      logger.dsp.info(`DSP ${partner.name} XML response status: ${response.status} ${response.statusText}`);
      logger.dsp.info(`DSP ${partner.name} XML response content-type: ${response.headers['content-type']}`);
      logger.dsp.debug(`DSP ${partner.name} XML response body:`, response.body.substring(0, 500));

      if (response.status < 200 || response.status >= 300) {
        logger.dsp.error(`DSP ${partner.name} XML error ${response.status}:`, response.body.substring(0, 200));
        logger.dsp.info(`DSP ${partner.name} XML request URL was:`, url);

        // Don't throw error for XML requests - just return empty bids
        logger.dsp.warn(`DSP ${partner.name}: XML request failed, returning no bids`);
        return [];
      }

      // Auto-detect response format and parse accordingly
      const responseText = response.body;
      logger.dsp.info(`DSP ${partner.name} XML response:`, responseText);

      const bids: AuctionBid[] = [];

      // Try to parse as JSON first
      let jsonResponse = null;
      try {
        jsonResponse = JSON.parse(responseText);
        logger.dsp.info(`DSP ${partner.name}: Detected JSON response format`);
      } catch (e) {
        logger.dsp.info(`DSP ${partner.name}: Detected XML/text response format`);
      }

      if (jsonResponse) {
        // Handle JSON response format
        bids.push(...this.parseJsonBidResponse(partner, jsonResponse, request, websiteDomain));
      } else {
        // Handle XML/text response format
        bids.push(...this.parseXmlBidResponse(partner, responseText, request, websiteDomain));
      }

      logger.dsp.info(`DSP ${partner.name}: Tracked request with ${bids.length} bids`);
      return bids;

    } catch (error: any) {
      if (error.name === 'AbortError') {
        logger.dsp.error(`DSP ${partner.name} XML request error: Aborted - request to ${url} timed out or was cancelled.`);
      } else {
        logger.dsp.error(`DSP ${partner.name} XML request error:`, error);
        logger.dsp.error(`DSP ${partner.name} XML request URL was:`, url);
      }

      // Don't throw error for XML requests - just return empty bids to prevent breaking the auction
      logger.dsp.warn(`DSP ${partner.name}: XML request failed, returning no bids`);
      return [];
    }
  }

  /**
   * Parse JSON bid response and extract bid information
   */
  private static parseJsonBidResponse(partner: any, jsonResponse: any, request: AuctionRequest, websiteDomain: string): AuctionBid[] {
    const bids: AuctionBid[] = [];

    try {
      // Handle different JSON response formats
      let bidData = [];

      // Format 1: {"results": [{"cpc": 0.001, "url": "...", "ad_id": "..."}]}
      if (jsonResponse.results && Array.isArray(jsonResponse.results)) {
        bidData = jsonResponse.results;
      }
      // Format 2: {"bids": [{"price": 0.001, "adm": "...", "id": "..."}]}
      else if (jsonResponse.bids && Array.isArray(jsonResponse.bids)) {
        bidData = jsonResponse.bids;
      }
      // Format 3: {"seatbid": [{"bid": [{"price": 0.001, "adm": "..."}]}]} (OpenRTB-like)
      else if (jsonResponse.seatbid && Array.isArray(jsonResponse.seatbid)) {
        for (const seatbid of jsonResponse.seatbid) {
          if (seatbid.bid && Array.isArray(seatbid.bid)) {
            bidData.push(...seatbid.bid);
          }
        }
      }
      // Format 4: Direct bid object {"price": 0.001, "adm": "..."}
      else if (jsonResponse.price || jsonResponse.cpc || jsonResponse.cpm || jsonResponse.cpv) {
        bidData = [jsonResponse];
      }

      for (const bid of bidData) {
        // Extract bid price from various possible fields
        let bidPrice = 0;
        let detectedFormat = 'cpm';

        logger.dsp.debug(`DSP ${partner.name}: Parsing individual bid object: ${JSON.stringify(bid)}`);

        if (bid.cpc !== undefined) {
          bidPrice = parseFloat(bid.cpc);
          detectedFormat = 'cpc';
          logger.dsp.debug(`DSP ${partner.name}: Found CPC bid: ${bidPrice}`);
        } else if (bid.cpm !== undefined) {
          bidPrice = parseFloat(bid.cpm);
          detectedFormat = 'cpm';
          logger.dsp.debug(`DSP ${partner.name}: Found CPM bid: ${bidPrice}`);
        } else if (bid.cpv !== undefined) {
          bidPrice = parseFloat(bid.cpv);
          detectedFormat = 'cpv';
          logger.dsp.debug(`DSP ${partner.name}: Found CPV bid: ${bidPrice}`);
        } else if (bid.ecpm !== undefined) {
          bidPrice = parseFloat(bid.ecpm);
          detectedFormat = 'ecpm';
          logger.dsp.debug(`DSP ${partner.name}: Found eCPM bid: ${bidPrice}`);
        } else if (bid.price !== undefined) {
          bidPrice = parseFloat(bid.price);
          detectedFormat = partner.bid_price_format || 'cpm';
          logger.dsp.debug(`DSP ${partner.name}: Found generic price bid: ${bidPrice}`);
        }

        if (bidPrice > 0) {
          // Convert to CPM for fair comparison
          const normalizedBidPrice = AuctionService.convertToCpm(bidPrice, detectedFormat);

          logger.dsp.info(`DSP ${partner.name}: Found ${detectedFormat.toUpperCase()} bid $${bidPrice} -> CPM $${normalizedBidPrice}`);

          let landingUrl = bid.url || bid.click_url || bid.adomain?.[0] || '';
          let adMarkup = bid.adm || bid.ad || bid.creative || '';

          // If adMarkup is not found but landingUrl is present, construct adMarkup based on format
          if (!adMarkup && landingUrl) {
            if (request.format === 'popup' || request.format === 'in_page_push') {
              // For popup/in_page_push, pass the URL directly to the SSP
              adMarkup = landingUrl;
              logger.dsp.info(`DSP ${partner.name}: Using landingUrl directly as adMarkup for ${request.format} format.`);
            } else {
              // For other formats, construct a simple iframe
              adMarkup = `<iframe src="${landingUrl}" style="width:100%;height:100%;border:none;"></iframe>`;
              logger.dsp.info(`DSP ${partner.name}: Constructed iframe adMarkup from landingUrl for JSON response.`);
            }
          }

          // If adMarkup is XML, try to extract landingUrl from it (re-check if it's not already extracted)
          if (adMarkup.startsWith('<?xml') || adMarkup.startsWith('<ad>')) {
            const clickUrlMatch = adMarkup.match(/<clickUrl><!\[CDATA\[([^\]]+)\]\]><\/clickUrl>/i);
            if (clickUrlMatch) {
              landingUrl = clickUrlMatch[1];
            } else {
              // Try to find a direct URL within the XML if clickUrl is not present
              const urlMatch = adMarkup.match(/<url><!\[CDATA\[([^\]]+)\]\]><\/url>/i);
              if (urlMatch) {
                landingUrl = urlMatch[1];
              }
            }
          }

          // Generate tracking URLs for JSON bid responses
          const trackingData = this.generateTrackingUrls({
            source: 'dsp',
            partnerId: partner.id,
            partnerName: partner.name,
            campaignId: bid.cid || bid.campaign_id || bid.ad_id,
            creativeId: bid.crid || bid.creative_id || bid.id,
            originalLandingUrl: landingUrl,
            originalAdMarkup: adMarkup,
            bidPrice: normalizedBidPrice,
            format: request.format,
            zoneId: request.zoneId,
            websiteId: request.websiteId,
            winUrl: bid.nurl || bid.win_url || ''
          });

          bids.push({
            source: 'dsp',
            partnerId: partner.id,
            partnerName: partner.name,
            bidPrice: normalizedBidPrice,
            adMarkup: trackingData.trackedAdMarkup,
            landingUrl: trackingData.clickTrackingUrl,
            winUrl: bid.nurl || bid.win_url || '',
            data: {
              ...bid,
              originalAdMarkup: adMarkup,
              originalLandingUrl: landingUrl,
              impressionTrackingUrl: trackingData.impressionTrackingUrl,
              clickTrackingUrl: trackingData.clickTrackingUrl
            }
          });
        }
      }

    } catch (error) {
      logger.dsp.error(`DSP ${partner.name}: Error parsing JSON response:`, error);
    }

    return bids;
  }

  /**
   * Parse XML bid response and extract bid information
   */
  private static parseXmlBidResponse(partner: any, xmlResponse: string, request: AuctionRequest, websiteDomain: string): AuctionBid[] {
    const bids: AuctionBid[] = [];

    logger.dsp.debug(`DSP ${partner.name}: Raw XML response for parsing: ${xmlResponse}`);

    try {
      // Simple XML parsing - look for common patterns
      if (xmlResponse.includes('<ad>') || xmlResponse.includes('bid') || xmlResponse.includes('price')) {

        // Extract price from XML using regex patterns
        let bidPrice = 0;
        let detectedFormat = 'cpm';
        let winUrlMatch = xmlResponse.match(/<nUrl><!\[CDATA\[([^\]]+)\]\]><\/nUrl>/i);

        // Extract URL from XML - this is the landing URL where user should go
        let landingUrlMatch = xmlResponse.match(/<url><!\[CDATA\[([^\]]+)\]\]><\/url>/i);

        logger.dsp.debug(`DSP ${partner.name}: Attempting to extract bid price from XML: ${xmlResponse.substring(0, 500)}...`);
        logger.dsp.debug(`DSP ${partner.name}: Landing URL match: ${landingUrlMatch ? landingUrlMatch[1] : 'none'}`);

        // Try to extract CPC
        const cpcMatch = xmlResponse.match(/<cpc[^>]*>([^<]+)<\/cpc>|cpc["\s]*[:=]["\s]*([0-9.]+)/i);
        if (cpcMatch) {
          bidPrice = parseFloat(cpcMatch[1] || cpcMatch[2]);
          detectedFormat = 'cpc';
          logger.dsp.debug(`DSP ${partner.name}: XML CPC match: ${cpcMatch[0]}, value: ${bidPrice}`);
        }

        // Try to extract CPM
        if (!bidPrice) {
          const cpmMatch = xmlResponse.match(/<cpm[^>]*>([^<]+)<\/cpm>|cpm["\s]*[:=]["\s]*([0-9.]+)/i);
          if (cpmMatch) {
            bidPrice = parseFloat(cpmMatch[1] || cpmMatch[2]);
            detectedFormat = 'cpm';
            logger.dsp.debug(`DSP ${partner.name}: XML CPM match: ${cpmMatch[0]}, value: ${bidPrice}`);
          }
        }

        // Try to extract generic price
        if (!bidPrice) {
          const valueMatch = xmlResponse.match(/<value[^>]*><!\[CDATA\[([0-9.]+)\]\]><\/value>/i);
          if (valueMatch) {
            bidPrice = parseFloat(valueMatch[1]);
            detectedFormat = partner.bid_price_format || 'cpm';
            logger.dsp.debug(`DSP ${partner.name}: XML Value match: ${valueMatch[0]}, value: ${bidPrice}`);
          }
        }

        if (!bidPrice) {
          const priceMatch = xmlResponse.match(/<price[^>]*>([^<]+)<\/price>|price["\s]*[:=]["\s]*([0-9.]+)/i);
          if (priceMatch) {
            bidPrice = parseFloat(priceMatch[1] || priceMatch[2]);
            detectedFormat = partner.bid_price_format || 'cpm';
            logger.dsp.debug(`DSP ${partner.name}: XML Price match: ${priceMatch[0]}, value: ${bidPrice}`);
          }
        }

        // Fallback: use configured mock price if no price found but response indicates bid
        if (!bidPrice && (xmlResponse.includes('<ad>') || xmlResponse.includes('bid'))) {
          bidPrice = 0.012; // Mock price
          detectedFormat = partner.bid_price_format || 'cpm';
          logger.dsp.info(`DSP ${partner.name}: Using fallback mock price for XML response`);
        }

        if (bidPrice > 0) {
          // Convert to CPM for fair comparison
          const normalizedBidPrice = AuctionService.convertToCpm(bidPrice, detectedFormat);

          logger.dsp.info(`DSP ${partner.name}: Found XML ${detectedFormat.toUpperCase()} bid $${bidPrice} -> CPM $${normalizedBidPrice}`);

          let adMarkup = '';
          let originalLandingUrl = '';

          // For XML responses, the <url> element contains the landing URL
          if (landingUrlMatch && landingUrlMatch[1]) {
              originalLandingUrl = landingUrlMatch[1];
              logger.dsp.info(`DSP ${partner.name}: Found landing URL: ${originalLandingUrl}`);
          } else {
              logger.dsp.warn(`DSP ${partner.name}: No <url> element found in XML response.`);
          }

          // For XML ad markup, we have several options:
          // 1. Look for specific creative content in <ad> or <creative> tags
          // 2. For popup/push formats, use the landing URL directly
          // 3. For banner/native, look for HTML content or use the entire XML

          if (request.format === 'popup' || request.format === 'in_page_push') {
              // For popup/push, the "ad markup" is just the landing URL
              adMarkup = originalLandingUrl;
              logger.dsp.info(`DSP ${partner.name}: Using landing URL as adMarkup for ${request.format} format.`);
          } else {
              // For banner/native, look for creative content
              const creativeMatch = xmlResponse.match(/<creative>([^]*?)<\/creative>/i) ||
                                   xmlResponse.match(/<ad>([^]*?)<\/ad>/i) ||
                                   xmlResponse.match(/<html>([^]*?)<\/html>/i);

              if (creativeMatch && creativeMatch[1]) {
                  adMarkup = creativeMatch[1];
                  logger.dsp.info(`DSP ${partner.name}: Found creative content in XML for adMarkup.`);
              } else {
                  // Fallback: use the entire XML response as markup (some DSPs send complete HTML)
                  adMarkup = xmlResponse;
                  logger.dsp.info(`DSP ${partner.name}: Using entire XML response as adMarkup.`);
              }
          }

          // Generate tracking URLs for XML bid responses
          const trackingData = this.generateTrackingUrls({
            source: 'dsp',
            partnerId: partner.id,
            partnerName: partner.name,
            campaignId: undefined, // XML responses may not have campaign ID
            creativeId: undefined, // XML responses may not have creative ID
            originalLandingUrl: originalLandingUrl,
            originalAdMarkup: adMarkup,
            bidPrice: normalizedBidPrice,
            format: request.format,
            zoneId: request.zoneId,
            websiteId: request.websiteId,
            winUrl: winUrlMatch ? winUrlMatch[1] : ''
          });

          bids.push({
            source: 'dsp',
            partnerId: partner.id,
            partnerName: partner.name,
            bidPrice: normalizedBidPrice,
            adMarkup: trackingData.trackedAdMarkup,
            landingUrl: trackingData.clickTrackingUrl,
            winUrl: winUrlMatch ? winUrlMatch[1] : '',
            data: {
              xmlResponse,
              originalAdMarkup: adMarkup,
              originalLandingUrl: originalLandingUrl,
              impressionTrackingUrl: trackingData.impressionTrackingUrl,
              clickTrackingUrl: trackingData.clickTrackingUrl
            }
          });
        }
      }

    } catch (error) {
      logger.dsp.error(`DSP ${partner.name}: Error parsing XML response:`, error);
    }

    return bids;
  }

  /**
   * Generate ad markup for local campaigns based on format and campaign data
   */
  private static generateLocalAdMarkup(campaign: any, format: string): string {
    switch (format) {
      case 'banner':
        if (campaign.creative_type === 'image' && campaign.banner_image_url) {
          return `<img src="${campaign.banner_image_url}" alt="Ad" style="max-width:100%;height:auto;" />`;
        } else if (campaign.creative_type === 'js' && campaign.js_tag) {
          return campaign.js_tag;
        } else {
          return `<div style="background:#f0f0f0;padding:20px;text-align:center;">Ad Content</div>`;
        }

      case 'native':
        return JSON.stringify({
          title: campaign.native_title || 'Native Ad',
          description: campaign.native_description || 'Native ad description',
          icon: campaign.native_icon_url || '',
          image: campaign.native_image_url || '',
          clickUrl: campaign.landing_url
        });

      case 'popup':
        return campaign.landing_url; // For popup, the markup is just the URL

      case 'in_page_push':
        return JSON.stringify({
          title: campaign.push_title || 'Push Notification',
          description: campaign.push_description || 'Push notification description',
          image: campaign.push_image_url || '',
          clickUrl: campaign.landing_url
        });

      default:
        return campaign.landing_url;
    }
  }

  /**
   * Generate tracking URLs for ad serving with impression and click tracking
   * Uses existing /api/click endpoint for click tracking
   */
  private static generateTrackingUrls(params: {
    source: 'local' | 'dsp';
    partnerId?: number;
    partnerName?: string;
    campaignId?: number;
    creativeId?: string;
    originalLandingUrl: string;
    originalAdMarkup: string;
    bidPrice: number;
    format: string;
    zoneId: number;
    websiteId: number;
    winUrl?: string;
  }): {
    impressionTrackingUrl: string;
    clickTrackingUrl: string;
    trackedAdMarkup: string;
  } {
    // The impression ID will be generated in /api/serve, so we use a placeholder
    // The actual tracking URL will be constructed in /api/serve with the real impression ID
    const clickTrackingUrl = 'PLACEHOLDER_SERVE_URL'; // Will be replaced in serve.ts with /api/serve URL

    // For impression tracking, we'll use a simple pixel approach
    // The main impression tracking is already handled in /api/serve
    const impressionTrackingUrl = `data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7`;

    // Generate tracked ad markup based on format and source
    let trackedAdMarkup = params.originalAdMarkup;

    if (params.source === 'dsp') {
      // For DSP ads, we need to modify the markup to redirect clicks through our tracking
      if (params.format === 'banner') {
        // Replace any existing click URLs in DSP markup with placeholder
        if (params.originalAdMarkup.includes('href=')) {
          // Replace href attributes with placeholder that will be replaced in serve.ts
          trackedAdMarkup = params.originalAdMarkup.replace(
            /href=["']([^"']+)["']/g,
            'href="PLACEHOLDER_SERVE_URL"'
          );
        } else if (params.originalAdMarkup.includes('<iframe')) {
          // For iframe ads, wrap with click tracking placeholder
          trackedAdMarkup = `
            <div style="position:relative;display:inline-block;">
              <a href="PLACEHOLDER_SERVE_URL" target="_blank" style="position:absolute;top:0;left:0;width:100%;height:100%;z-index:10;opacity:0;"></a>
              ${params.originalAdMarkup}
            </div>
          `;
        } else if (params.originalAdMarkup.includes('onclick=')) {
          // Replace existing onclick handlers
          trackedAdMarkup = params.originalAdMarkup.replace(
            /onclick=["']([^"']+)["']/g,
            'onclick="window.open(\'PLACEHOLDER_SERVE_URL\', \'_blank\')"'
          );
        } else {
          // For other DSP markup, wrap in clickable div
          trackedAdMarkup = `
            <div onclick="window.open('PLACEHOLDER_SERVE_URL', '_blank')" style="cursor:pointer;">
              ${params.originalAdMarkup}
            </div>
          `;
        }
      } else if (params.format === 'popup' || params.format === 'in_page_push') {
        // For popup/push, the adMarkup is usually a URL
        // If it's a URL (creative URL), we need to redirect it through our tracking
        if (params.originalAdMarkup.startsWith('http')) {
          // This is a URL - for popup/push, we redirect to our serve tracking URL
          trackedAdMarkup = 'PLACEHOLDER_SERVE_URL';
        } else {
          // This might be HTML markup for in-page push
          trackedAdMarkup = `
            <div onclick="window.open('PLACEHOLDER_SERVE_URL', '_blank')" style="cursor:pointer;">
              ${params.originalAdMarkup}
            </div>
          `;
        }
      } else if (params.format === 'native') {
        // For native DSP ads, try to parse and modify the native response
        try {
          const nativeData = JSON.parse(params.originalAdMarkup);
          if (nativeData.native && nativeData.native.link) {
            nativeData.native.link.url = 'PLACEHOLDER_SERVE_URL';
          }
          trackedAdMarkup = JSON.stringify(nativeData);
        } catch {
          // If not valid JSON, treat as regular markup and wrap with click
          trackedAdMarkup = `
            <div onclick="window.open('PLACEHOLDER_SERVE_URL', '_blank')" style="cursor:pointer;">
              ${params.originalAdMarkup}
            </div>
          `;
        }
      }
    } else {
      // For local campaigns, the markup will be generated in serve.ts with proper click URLs
      // We don't need to modify it here
      trackedAdMarkup = params.originalAdMarkup;
    }

    return {
      impressionTrackingUrl,
      clickTrackingUrl,
      trackedAdMarkup
    };
  }

  /**
   * Validate OpenRTB response structure
   */
  private static validateOpenRTBResponse(bidResponse: any, partnerName: string): boolean {
    try {
      // Check if response has required id field
      if (!bidResponse.id) {
        logger.dsp.warn(`DSP ${partnerName}: Missing required 'id' field in bid response`);
        return false;
      }

      // Check for no-bid response (nbr field present)
      if (bidResponse.nbr !== undefined) {
        logger.dsp.info(`DSP ${partnerName}: No-bid response with reason code ${bidResponse.nbr}`);
        return false;
      }

      // If seatbid is present, validate its structure
      if (bidResponse.seatbid) {
        if (!Array.isArray(bidResponse.seatbid)) {
          logger.dsp.warn(`DSP ${partnerName}: 'seatbid' field must be an array`);
          return false;
        }

        // Validate each seatbid
        for (const seatbid of bidResponse.seatbid) {
          if (!seatbid.bid || !Array.isArray(seatbid.bid)) {
            logger.dsp.warn(`DSP ${partnerName}: Each seatbid must have a 'bid' array`);
            return false;
          }

          // Validate each bid
          for (const bid of seatbid.bid) {
            if (!bid.id || !bid.impid || bid.price === undefined) {
              logger.dsp.warn(`DSP ${partnerName}: Bid missing required fields (id, impid, price)`);
              return false;
            }

            if (typeof bid.price !== 'number' || bid.price < 0) {
              logger.dsp.warn(`DSP ${partnerName}: Invalid bid price: ${bid.price}`);
              return false;
            }
          }
        }
      }

      return true;
    } catch (error) {
      logger.dsp.error(`DSP ${partnerName}: Error validating OpenRTB response:`, error);
      return false;
    }
  }

  /**
   * Convert different bid formats to CPM for fair comparison
   */
  private static convertToCpm(bidPrice: number, format: string): number {
    switch (format.toLowerCase()) {
      case 'cpc':
        // CPC to CPM: multiply by 1000
        return bidPrice * 1000;
      case 'cpv':
        // CPV to CPM: multiply by 1000
        return bidPrice * 1000;
      case 'ecpm':
        // eCPM is already CPM
        return bidPrice;
      case 'cpm':
      default:
        // Already CPM
        return bidPrice;
    }
  }
}
