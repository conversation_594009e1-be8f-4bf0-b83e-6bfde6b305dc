import amqp, { Connection, Channel, Message } from 'amqplib';
import logger from './logger';

// RabbitMQ connection configuration from environment variables
const rabbitmqConfig = {
  url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
  host: process.env.RABBITMQ_HOST || 'localhost',
  port: parseInt(process.env.RABBITMQ_PORT || '5672'),
  username: process.env.RABBITMQ_USERNAME || process.env.RABBITMQ_USER || 'guest',
  password: process.env.RABBITMQ_PASSWORD || 'guest',
  vhost: process.env.RABBITMQ_VHOST || '/',
  heartbeat: parseInt(process.env.RABBITMQ_HEARTBEAT || '60'),
  connectionTimeout: parseInt(process.env.RABBITMQ_CONNECTION_TIMEOUT || '10000'),
};

// Build connection URL if not provided
const getConnectionUrl = (): string => {
  if (rabbitmqConfig.url && rabbitmqConfig.url !== 'amqp://localhost:5672') {
    return rabbitmqConfig.url;
  }

  const auth = rabbitmqConfig.username && rabbitmqConfig.password
    ? `${rabbitmqConfig.username}:${rabbitmqConfig.password}@`
    : '';

  return `amqp://${auth}${rabbitmqConfig.host}:${rabbitmqConfig.port}${rabbitmqConfig.vhost}`;
};

// RabbitMQ connection and channel management
class RabbitMQConnection {
  private static connection: Connection | null = null;
  private static channel: Channel | null = null;
  private static isConnecting = false;

  static async getConnection(): Promise<Connection> {
    if (this.connection) {
      return this.connection;
    }

    if (this.isConnecting) {
      // Wait for existing connection attempt
      while (this.isConnecting) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      if (this.connection) return this.connection;
    }

    this.isConnecting = true;
    try {
      const connectionUrl = getConnectionUrl();
      logger.queue.info(`🐰 Connecting to RabbitMQ: ${connectionUrl.replace(/\/\/.*@/, '//***@')}`);

      this.connection = await amqp.connect(connectionUrl, {
        heartbeat: rabbitmqConfig.heartbeat,
        timeout: rabbitmqConfig.connectionTimeout,
      });

      this.connection.on('error', (err: Error) => {
        logger.queue.error('🐰 RabbitMQ connection error:', err);
        this.connection = null;
        this.channel = null;
      });

      this.connection.on('close', () => {
        logger.queue.warn('🐰 RabbitMQ connection closed');
        this.connection = null;
        this.channel = null;
      });

      logger.queue.info('🐰 RabbitMQ connected successfully');
      return this.connection;
    } catch (error) {
      logger.queue.error('🐰 Failed to connect to RabbitMQ:', error);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  static async getChannel(): Promise<Channel> {
    if (this.channel) {
      return this.channel;
    }

    const connection = await this.getConnection();
    this.channel = await connection.createChannel();

    logger.queue.info('🐰 RabbitMQ channel created');
    return this.channel as Channel;
  }

  static async closeConnection(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close();
        this.channel = null;
      }
      if (this.connection) {
        await (this.connection as any).close();
        this.connection = null;
      }
      logger.queue.info('🐰 RabbitMQ connection closed');
    } catch (error) {
      logger.queue.error('🐰 Error closing RabbitMQ connection:', error);
    }
  }
}

// RabbitMQ Queue definitions with persistence and durability
export const QUEUE_NAMES = {
  IMPRESSION_TRACKING: 'impression-tracking',
  COST_PROCESSING: 'cost-processing',
  REQUEST_STATS: 'request-stats',
  WIN_NOTIFICATIONS: 'win-notifications',
  TRANSACTIONS: 'transactions',
  BATCH_PROCESSING: 'batch-processing',
} as const;

// Queue options for different priority levels
const QUEUE_OPTIONS = {
  HIGH_PRIORITY: {
    durable: true,        // Survive broker restarts
    persistent: true,     // Messages survive broker restarts
    maxPriority: 10,      // Enable priority queues
    messageTtl: 3600000,  // 1 hour message TTL
    maxLength: 100000,    // Max queue length
  },
  MEDIUM_PRIORITY: {
    durable: true,
    persistent: true,
    maxPriority: 5,
    messageTtl: 1800000,  // 30 minutes TTL
    maxLength: 50000,
  },
  LOW_PRIORITY: {
    durable: true,
    persistent: true,
    maxPriority: 1,
    messageTtl: 900000,   // 15 minutes TTL
    maxLength: 25000,
  },
};

// Initialize all queues with proper configuration
export class RabbitMQManager {
  private static initialized = false;

  static async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      const channel = await RabbitMQConnection.getChannel();

      // Declare all queues with persistence and durability
      await Promise.all([
        // High-priority queues for critical RTB operations
        channel.assertQueue(QUEUE_NAMES.IMPRESSION_TRACKING, {
          ...QUEUE_OPTIONS.HIGH_PRIORITY,
          arguments: {
            'x-max-priority': 10,
            'x-message-ttl': 3600000,
            'x-max-length': 100000,
          }
        }),

        channel.assertQueue(QUEUE_NAMES.COST_PROCESSING, {
          ...QUEUE_OPTIONS.HIGH_PRIORITY,
          arguments: {
            'x-max-priority': 9,
            'x-message-ttl': 3600000,
            'x-max-length': 100000,
          }
        }),

        // Medium-priority queues
        channel.assertQueue(QUEUE_NAMES.WIN_NOTIFICATIONS, {
          ...QUEUE_OPTIONS.MEDIUM_PRIORITY,
          arguments: {
            'x-max-priority': 7,
            'x-message-ttl': 1800000,
            'x-max-length': 50000,
          }
        }),

        channel.assertQueue(QUEUE_NAMES.TRANSACTIONS, {
          ...QUEUE_OPTIONS.MEDIUM_PRIORITY,
          arguments: {
            'x-max-priority': 6,
            'x-message-ttl': 1800000,
            'x-max-length': 50000,
          }
        }),

        channel.assertQueue(QUEUE_NAMES.REQUEST_STATS, {
          ...QUEUE_OPTIONS.MEDIUM_PRIORITY,
          arguments: {
            'x-max-priority': 5,
            'x-message-ttl': 1800000,
            'x-max-length': 50000,
          }
        }),

        // Low-priority batch processing
        channel.assertQueue(QUEUE_NAMES.BATCH_PROCESSING, {
          ...QUEUE_OPTIONS.LOW_PRIORITY,
          arguments: {
            'x-max-priority': 1,
            'x-message-ttl': 900000,
            'x-max-length': 25000,
          }
        }),
      ]);

      this.initialized = true;
      logger.queue.info('🐰 All RabbitMQ queues initialized with persistence and durability');

    } catch (error) {
      logger.queue.error('🐰 Failed to initialize RabbitMQ queues:', error);
      throw error;
    }
  }

  // High-performance message publishing with persistence
  static async publishMessage(
    queueName: string,
    message: any,
    options: { priority?: number; persistent?: boolean; delay?: number } = {}
  ): Promise<boolean> {
    try {
      const channel = await RabbitMQConnection.getChannel();

      const messageBuffer = Buffer.from(JSON.stringify({
        ...message,
        timestamp: new Date().toISOString(),
        id: `${Date.now()}-${Math.random().toString(36).substring(2)}`,
      }));

      const publishOptions = {
        persistent: options.persistent !== false, // Default to persistent
        priority: options.priority || 5,
        timestamp: Date.now(),
        messageId: `${Date.now()}-${Math.random().toString(36).substring(2)}`,
        ...(options.delay && {
          headers: { 'x-delay': options.delay }
        }),
      };

      const result = channel.sendToQueue(queueName, messageBuffer, publishOptions);

      if (result) {
        logger.queue.debug(`🐰 Message published to ${queueName}`, {
          messageId: publishOptions.messageId,
          priority: publishOptions.priority
        });
      }

      return result;
    } catch (error) {
      logger.queue.error(`🐰 Failed to publish message to ${queueName}:`, error);
      return false;
    }
  }

  // Consumer setup with automatic retries and error handling
  static async setupConsumer(
    queueName: string,
    processor: (message: any) => Promise<void>,
    options: { concurrency?: number; retries?: number } = {}
  ): Promise<void> {
    try {
      const channel = await RabbitMQConnection.getChannel();
      const { concurrency = 10, retries = 3 } = options;

      // Set prefetch count for load balancing
      await channel.prefetch(concurrency);

      await channel.consume(queueName, async (msg: Message | null) => {
        if (!msg) return;

        try {
          const messageData = JSON.parse(msg.content.toString());
          await processor(messageData);

          // Acknowledge successful processing
          channel.ack(msg);

          logger.queue.debug(`🐰 Message processed from ${queueName}`, {
            messageId: messageData.id
          });

        } catch (error) {
          logger.queue.error(`🐰 Error processing message from ${queueName}:`, error);

          // Check retry count
          const retryCount = (msg.properties.headers?.['x-retry-count'] || 0) + 1;

          if (retryCount <= retries) {
            // Requeue with retry count
            const retryMessage = {
              ...JSON.parse(msg.content.toString()),
              retryCount,
            };

            await this.publishMessage(queueName, retryMessage, {
              priority: msg.properties.priority || 5,
              persistent: true,
              delay: Math.pow(2, retryCount) * 1000, // Exponential backoff
            });

            logger.queue.warn(`🐰 Message requeued for retry ${retryCount}/${retries}`, {
              queueName,
              retryCount,
            });
          } else {
            logger.queue.error(`🐰 Message failed after ${retries} retries, sending to DLQ`, {
              queueName,
              messageId: JSON.parse(msg.content.toString()).id,
            });

            // Send to dead letter queue (implement if needed)
            // await this.publishMessage(`${queueName}-dlq`, JSON.parse(msg.content.toString()));
          }

          // Acknowledge to remove from queue
          channel.ack(msg);
        }
      });

      logger.queue.info(`🐰 Consumer setup for ${queueName} with concurrency ${concurrency}`);

    } catch (error) {
      logger.queue.error(`🐰 Failed to setup consumer for ${queueName}:`, error);
      throw error;
    }
  }

  // Get queue statistics
  static async getQueueStats(): Promise<any[]> {
    try {
      const channel = await RabbitMQConnection.getChannel();
      const queueNames = Object.values(QUEUE_NAMES);

      const stats = await Promise.all(
        queueNames.map(async (queueName) => {
          try {
            const queueInfo = await channel.checkQueue(queueName);
            return {
              name: queueName,
              messageCount: queueInfo.messageCount,
              consumerCount: queueInfo.consumerCount,
            };
          } catch (error) {
            logger.queue.warn(`🐰 Could not get stats for queue ${queueName}:`, error);
            return {
              name: queueName,
              messageCount: 0,
              consumerCount: 0,
              error: error instanceof Error ? error.message : String(error),
            };
          }
        })
      );

      return stats;
    } catch (error) {
      logger.queue.error('🐰 Failed to get queue stats:', error);
      return [];
    }
  }

  // Purge all queues (for testing/maintenance)
  static async purgeAllQueues(): Promise<void> {
    try {
      const channel = await RabbitMQConnection.getChannel();
      const queueNames = Object.values(QUEUE_NAMES);

      await Promise.all(
        queueNames.map(async (queueName) => {
          try {
            await channel.purgeQueue(queueName);
            logger.queue.info(`🐰 Purged queue: ${queueName}`);
          } catch (error) {
            logger.queue.warn(`🐰 Could not purge queue ${queueName}:`, error);
          }
        })
      );

      logger.queue.info('🐰 All queues purged');
    } catch (error) {
      logger.queue.error('🐰 Failed to purge queues:', error);
    }
  }
}

// Convenience functions for each queue type
export const publishToImpressionQueue = (message: any, priority = 10) =>
  RabbitMQManager.publishMessage(QUEUE_NAMES.IMPRESSION_TRACKING, message, { priority });

export const publishToCostProcessingQueue = (message: any, priority = 9) =>
  RabbitMQManager.publishMessage(QUEUE_NAMES.COST_PROCESSING, message, { priority });

export const publishToWinNotificationQueue = (message: any, priority = 7) =>
  RabbitMQManager.publishMessage(QUEUE_NAMES.WIN_NOTIFICATIONS, message, { priority });

export const publishToTransactionQueue = (message: any, priority = 6) =>
  RabbitMQManager.publishMessage(QUEUE_NAMES.TRANSACTIONS, message, { priority });

export const publishToRequestStatsQueue = (message: any, priority = 5) =>
  RabbitMQManager.publishMessage(QUEUE_NAMES.REQUEST_STATS, message, { priority });

export const publishToBatchProcessingQueue = (message: any, priority = 1) =>
  RabbitMQManager.publishMessage(QUEUE_NAMES.BATCH_PROCESSING, message, { priority });

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.queue.info('🐰 Gracefully closing RabbitMQ connection...');
  await RabbitMQConnection.closeConnection();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.queue.info('🐰 Gracefully closing RabbitMQ connection...');
  await RabbitMQConnection.closeConnection();
  process.exit(0);
});

// Export everything
export default {
  RabbitMQManager,
  RabbitMQConnection,
  QUEUE_NAMES,
  publishToImpressionQueue,
  publishToCostProcessingQueue,
  publishToWinNotificationQueue,
  publishToTransactionQueue,
  publishToRequestStatsQueue,
  publishToBatchProcessingQueue,
};
