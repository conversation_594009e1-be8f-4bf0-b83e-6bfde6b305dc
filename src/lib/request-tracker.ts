import clickhouse from '@/lib/clickhouse';
import { clickHouseBatchWriter } from '@/lib/clickhouse-batch-writer';

export type SourceType =
  | 'publisher_incoming' | 'publisher_outgoing'
  | 'advertiser_incoming' | 'advertiser_outgoing'
  | 'ssp_incoming' | 'ssp_outgoing'
  | 'dsp_incoming' | 'dsp_outgoing';

export interface RequestTrackingParams {
  sourceType: SourceType;
  partnerEndpointId?: number;
  campaignId?: number;
  websiteId?: number;
  dspId?: number;
  sspId?: number;
  advertiserId?: number;
  publisherId?: number;
  requestCount: number;
  winCount?: number;
}

export interface WinTrackingParams {
  winType: 'publisher_direct' | 'dsp_rtb' | 'ssp_inventory';
  campaignId?: number;
  inventoryId?: string;
  winnerId: number;  // DSP/Advertiser who won
  supplierId: number; // Publisher/SSP who supplied inventory
  websiteId?: number;
  zoneId?: number;
  partnerEndpointId?: number;
  winPrice: number;
  platformRevenue: number;
  supplierRevenue: number;
  sspImpressionId?: string;
}

export class RequestTracker {
  /**
   * Track incoming/outgoing requests and wins for unified statistics (SPACE-OPTIMIZED)
   * Uses atomic increments instead of individual inserts
   */
  static async trackRequest(params: RequestTrackingParams): Promise<void> {
    try {
      const now = new Date();
      const date = now.toISOString().slice(0, 10);
      const hour = now.getHours();

      // Use batch writer for request_stats to reduce part count
      await clickHouseBatchWriter.write('request_stats', {
        date,
        hour,
        source_type: params.sourceType,
        partner_endpoint_id: params.partnerEndpointId || 0,
        campaign_id: params.campaignId || 0,
        website_id: params.websiteId || 0,
        dsp_id: params.dspId || 0,
        ssp_id: params.sspId || 0,
        advertiser_id: params.advertiserId || 0,
        publisher_id: params.publisherId || 0,
        total_requests: params.requestCount,
        total_wins: params.winCount || 0,
      }, 'JSONEachRow');
    } catch (error) {
      console.error('Error tracking request:', error);
      // Don't throw - tracking failures shouldn't break main functionality
    }
  }

  /**
   * Track win notifications for all traffic sources (SPACE-OPTIMIZED)
   * Aggregates wins by hour instead of storing individual records
   */
  static async trackWin(params: WinTrackingParams): Promise<void> {
    try {
      const now = new Date();
      const date = now.toISOString().slice(0, 10);
      const hour = now.getHours();

      // Insert into win_notifications table (not win_stats)
      const winId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
      await clickHouseBatchWriter.write('win_notifications', {
          id: winId,
          win_type: params.winType,
          campaign_id: params.campaignId || 0,
          inventory_id: params.inventoryId || '',
          winner_id: params.winnerId,
          supplier_id: params.supplierId,
          website_id: params.websiteId || 0,
          zone_id: params.zoneId || 0,
          partner_endpoint_id: params.partnerEndpointId || 0,
          win_price: params.winPrice,
          platform_revenue: params.platformRevenue,
          supplier_revenue: params.supplierRevenue,
          timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
      });
    } catch (error) {
      console.error('Error tracking win:', error);
      // Don't throw - tracking failures shouldn't break main functionality
    }
  }

  /**
   * Track both request and win in a single optimized call
   * Use this for scenarios where request always results in a win (like publisher direct)
   */
  static async trackRequestAndWin(
    requestParams: RequestTrackingParams,
    winParams: WinTrackingParams
  ): Promise<void> {
    try {
      // Track request and win simultaneously for maximum efficiency
      await Promise.all([
        this.trackRequest(requestParams),
        this.trackWin(winParams),
      ]);
    } catch (error) {
      console.error('Combined tracking error:', error);
    }
  }

  /**
   * Get aggregated stats for a specific period (SPACE-EFFICIENT QUERIES)
   */
  static async getStats(
    period: 'today' | 'week' | 'month' = 'today',
    sourceType?: SourceType
  ): Promise<any> {
    try {
      let dateFilter = '';
      switch (period) {
        case 'week':
          dateFilter = 'date >= today() - 7';
          break;
        case 'month':
          dateFilter = 'date >= today() - 30';
          break;
        default:
          dateFilter = 'date = today()';
      }

      let sourceFilter = '';
      const queryParams: any = {};

      if (sourceType) {
        sourceFilter = 'AND source_type = {sourceType:String}';
        queryParams.sourceType = sourceType;
      }

      const result = await clickhouse.query({
        query: `
          SELECT
            source_type,
            partner_endpoint_id,
            campaign_id,
            website_id,
            dsp_id,
            ssp_id,
            advertiser_id,
            publisher_id,
            sum(total_requests) as total_requests,
            sum(total_wins) as total_wins,
            round(sum(total_wins) / sum(total_requests) * 100, 2) as win_rate
          FROM request_stats
          WHERE ${dateFilter} ${sourceFilter}
          GROUP BY source_type, partner_endpoint_id, campaign_id, website_id, dsp_id, ssp_id, advertiser_id, publisher_id
          ORDER BY total_requests DESC
        `,
        query_params: queryParams,
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting stats:', error);
      return [];
    }
  }

  /**
   * Get hourly breakdown for today
   */
  static async getHourlyStats(): Promise<any> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT
            hour,
            source_type,
            sum(total_requests) as requests,
            sum(total_wins) as wins
          FROM request_stats
          WHERE date = today()
          GROUP BY hour, source_type
          ORDER BY hour, source_type
        `,
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting hourly stats:', error);
      return [];
    }
  }
}
