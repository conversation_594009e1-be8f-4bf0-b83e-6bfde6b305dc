import clickhouse from './clickhouse';
import { FraudMonitoring } from './fraud-monitoring';

interface FraudCheckResult {
  isValid: boolean;
  reason?: string;
  riskScore: number;
}

export class FraudDetection {

  // Check for click fraud
  static async checkClick(
    ip: string,
    userAgent: string,
    campaignId: number,
    zoneId: number
  ): Promise<FraudCheckResult> {
    let riskScore = 0;
    let reasons: string[] = [];

    try {
      // Check for too many clicks from same IP in last hour
      const ipClicksResult = await clickhouse.query({
        query: `
          SELECT COUNT(*) as click_count
          FROM clicks
          WHERE ip_address = {ip:String}
          AND timestamp >= now() - INTERVAL 1 HOUR
        `,
        query_params: { ip },
      });

      const ipClicks = await ipClicksResult.json();
      const clickCount = ipClicks.data[0]?.click_count || 0;

      if (clickCount > 10) {
        riskScore += 50;
        reasons.push('Too many clicks from same IP');
      } else if (clickCount > 5) {
        riskScore += 25;
        reasons.push('High click frequency from IP');
      }

      // Check for clicks from same IP on same campaign in last 24 hours
      const campaignClicksResult = await clickhouse.query({
        query: `
          SELECT COUNT(*) as click_count
          FROM clicks
          WHERE ip_address = {ip:String}
          AND campaign_id = {campaignId:UInt32}
          AND timestamp >= now() - INTERVAL 24 HOUR
        `,
        query_params: { ip, campaignId },
      });

      const campaignClicks = await campaignClicksResult.json();
      const campaignClickCount = campaignClicks.data[0]?.click_count || 0;

      if (campaignClickCount > 3) {
        riskScore += 40;
        reasons.push('Multiple clicks on same campaign from IP');
      }

      // Check for suspicious user agent patterns
      if (this.isSuspiciousUserAgent(userAgent)) {
        riskScore += 30;
        reasons.push('Suspicious user agent');
      }

      // Check for bot-like behavior (very fast clicks)
      const recentClicksResult = await clickhouse.query({
        query: `
          SELECT timestamp
          FROM clicks
          WHERE ip_address = {ip:String}
          AND timestamp >= now() - INTERVAL 5 MINUTE
          ORDER BY timestamp DESC
          LIMIT 5
        `,
        query_params: { ip },
      });

      const recentClicks = await recentClicksResult.json();
      if (recentClicks.data.length >= 3) {
        const timestamps = recentClicks.data.map((c: any) => new Date(c.timestamp).getTime());
        const intervals = [];
        for (let i = 1; i < timestamps.length; i++) {
          intervals.push(timestamps[i-1] - timestamps[i]);
        }

        // If all intervals are less than 2 seconds, likely bot
        if (intervals.every(interval => interval < 2000)) {
          riskScore += 60;
          reasons.push('Bot-like clicking pattern');
        }
      }

      // Check for known bad IPs (simplified - in production you'd use a real blacklist)
      if (this.isKnownBadIP(ip)) {
        riskScore += 80;
        reasons.push('Known fraudulent IP');
      }

      const isValid = riskScore < 70; // Threshold for blocking

      return {
        isValid,
        reason: reasons.join(', '),
        riskScore,
      };

    } catch (error) {
      console.error('Fraud detection error:', error);
      // In case of error, allow the click but log it
      return {
        isValid: true,
        reason: 'Fraud check failed',
        riskScore: 0,
      };
    }
  }

  // Check for impression fraud with logging and exclusion support
  static async checkImpression(
    ip: string,
    userAgent: string,
    campaignId: number,
    sourceType: 'publisher_direct' | 'dsp_inbound' | 'ssp_inbound' = 'publisher_direct',
    publisherId?: number,
    websiteId?: number,
    zoneId?: number,
    partnerId?: number,
    country?: string,
    deviceType?: string
  ): Promise<FraudCheckResult> {
    let riskScore = 0;
    let reasons: string[] = [];

    try {
      // Skip fraud detection for SSP traffic
      if (sourceType === 'ssp_inbound') {
        return {
          isValid: true,
          reason: 'SSP traffic - fraud detection disabled',
          riskScore: 0,
        };
      }

      // Check if this IP/criteria is excluded from fraud detection
      const isExcluded = await FraudMonitoring.isExcluded(ip, publisherId, websiteId, zoneId);
      if (isExcluded) {
        return {
          isValid: true,
          reason: 'Excluded from fraud detection',
          riskScore: 0,
        };
      }

      // Check for too many impressions from same IP
      const ipImpressionsResult = await clickhouse.query({
        query: `
          SELECT COUNT(*) as impression_count
          FROM impressions
          WHERE ip_address = {ip:String}
          AND timestamp >= now() - INTERVAL 1 HOUR
        `,
        query_params: { ip },
      });

      const ipImpressions = await ipImpressionsResult.json();
      const impressionCount = ipImpressions.data[0]?.impression_count || 0;

      if (impressionCount > 100) {
        riskScore += 40;
        reasons.push('Too many impressions from same IP');
      }

      // Check for suspicious user agent
      if (this.isSuspiciousUserAgent(userAgent)) {
        riskScore += 20;
        reasons.push('Suspicious user agent');
      }

      // Check for known bad IPs
      if (this.isKnownBadIP(ip)) {
        riskScore += 60;
        reasons.push('Known fraudulent IP');
      }

      const isValid = riskScore < 50; // Lower threshold for impressions
      const fraudReason = reasons.join(', ');

      // Log fraud detection event if blocked
      if (!isValid) {
        await FraudMonitoring.logFraudDetection({
          ipAddress: ip,
          userAgent,
          sourceType,
          publisherId,
          websiteId,
          zoneId,
          partnerId,
          fraudReason,
          riskScore,
          country,
          deviceType,
        });
      }

      return {
        isValid,
        reason: fraudReason,
        riskScore,
      };

    } catch (error) {
      console.error('Fraud detection error:', error);
      return {
        isValid: true,
        reason: 'Fraud check failed',
        riskScore: 0,
      };
    }
  }

  private static isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /^$/,  // Empty user agent
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  private static isKnownBadIP(ip: string): boolean {
    // Simplified bad IP check - in production you'd use a real database/service
    const knownBadIPs = [
      '127.0.0.1',
      '0.0.0.0',
      '::1',
    ];

    // Check for private/local IPs that shouldn't be clicking ads
    const privateIPPatterns = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
    ];

    return knownBadIPs.includes(ip) ||
           privateIPPatterns.some(pattern => pattern.test(ip));
  }
}

export default FraudDetection;
