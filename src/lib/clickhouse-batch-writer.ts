import clickhouse from '@/lib/clickhouse';
import { DataFormat } from '@clickhouse/client';
import logger from '@/lib/logger';

interface BatchItem {
  values: any[];
  format: DataFormat;
}

class ClickHouseBatchWriter {
  private batches: Map<string, BatchItem>;
  private timers: Map<string, NodeJS.Timeout>;
  private flushIntervalMs: number;
  private batchSizeLimit: number;

  constructor(flushIntervalMs: number = 1000, batchSizeLimit: number = 1000) {
    this.batches = new Map();
    this.timers = new Map();
    this.flushIntervalMs = flushIntervalMs;
    this.batchSizeLimit = batchSizeLimit;
  }

  /**
   * Write a single record to the batch for a specific table.
   * @param table Table name (e.g., 'request_stats')
   * @param value Object to insert
   * @param format ClickHouse format (default: 'JSONEachRow')
   */
  public async write(table: string, value: any, format: DataFormat = 'JSONEachRow'): Promise<void> {
    if (!this.batches.has(table)) {
      this.batches.set(table, { values: [], format });
    }

    const batch = this.batches.get(table)!;
    batch.values.push(value);

    if (batch.values.length >= this.batchSizeLimit) {
      await this.flush(table);
    } else {
      if (this.timers.has(table)) clearTimeout(this.timers.get(table));
      this.timers.set(
        table,
        setTimeout(() => this.flush(table), this.flushIntervalMs)
      );
    }
  }

  /**
   * Flush the current batch to ClickHouse.
   * @param table (Optional) Table to flush, or all tables if not specified
   */
  public async flush(table?: string): Promise<void> {
    const tablesToFlush = table ? [table] : Array.from(this.batches.keys());

    for (const tableName of tablesToFlush) {
      const batch = this.batches.get(tableName);
      if (batch && batch.values.length > 0) {
        this.batches.set(tableName, { values: [], format: batch.format });
        if (this.timers.has(tableName)) {
          clearTimeout(this.timers.get(tableName));
          this.timers.delete(tableName);
        }

        try {
          await clickhouse.insert({
            table: tableName,
            values: batch.values,
            format: batch.format,
          });
          logger.db.info(
            `✅ Flushed ${batch.values.length} records to ClickHouse table '${tableName}'.`
          );
        } catch (error) {
          logger.db.error(
            `❌ Failed to flush ${batch.values.length} records to '${tableName}' — retrying once.`
          );

          // Retry once
          await new Promise((r) => setTimeout(r, 500));
          try {
            await clickhouse.insert({
              table: tableName,
              values: batch.values,
              format: batch.format,
            });
            logger.db.info(
              `♻️ Retry successful: ${batch.values.length} records flushed to '${tableName}'.`
            );
          } catch (finalError) {
            logger.db.error(
              `🚨 Retry failed: Dropping ${batch.values.length} records from '${tableName}'.`,
              finalError
            );
            // Optionally, save to file or dead-letter queue here
          }
        }
      }
    }
  }

  /**
   * Flush all batches immediately.
   */
  public async flushAll(): Promise<void> {
    logger.db.info('⚙️ Flushing all pending ClickHouse batches...');
    await this.flush();
    logger.db.info('✅ All ClickHouse batches flushed.');
  }
}

// Optimized for RTB: Faster flush intervals for better QPS
export const clickHouseBatchWriter = new ClickHouseBatchWriter(1000, 2000); // 1s interval, 2000 batch size

// Graceful shutdown handlers
process.on('beforeExit', async () => {
  await clickHouseBatchWriter.flushAll();
});
process.on('SIGINT', async () => {
  await clickHouseBatchWriter.flushAll();
  process.exit(0);
});
process.on('SIGTERM', async () => {
  await clickHouseBatchWriter.flushAll();
  process.exit(0);
});
