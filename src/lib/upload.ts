import { writeFile, mkdir, unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export class FileUploadService {
  private uploadDir = path.join(process.cwd(), 'public', 'uploads');

  constructor() {
    this.ensureUploadDir();
  }

  private async ensureUploadDir() {
    if (!existsSync(this.uploadDir)) {
      await mkdir(this.uploadDir, { recursive: true });
    }
  }

  async uploadFile(file: File, subfolder: string = ''): Promise<string> {
    await this.ensureUploadDir();

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = path.extname(file.name);
    const filename = `${timestamp}_${randomString}${extension}`;

    // Create subfolder if specified
    const targetDir = subfolder ? path.join(this.uploadDir, subfolder) : this.uploadDir;
    if (!existsSync(targetDir)) {
      await mkdir(targetDir, { recursive: true });
    }

    const filepath = path.join(targetDir, filename);
    await writeFile(filepath, buffer);

    // Return the public URL
    const publicPath = subfolder ? `/uploads/${subfolder}/${filename}` : `/uploads/${filename}`;
    return publicPath;
  }

  async deleteFile(fileUrl: string): Promise<boolean> {
    try {
      if (!fileUrl || !fileUrl.startsWith('/uploads/')) {
        return false;
      }

      const filepath = path.join(process.cwd(), 'public', fileUrl);
      if (existsSync(filepath)) {
        await unlink(filepath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  async uploadCampaignImages(files: {
    bannerImage?: File;
    nativeIcon?: File;
    nativeImage?: File;
    pushImage?: File;
  }, campaignId: number): Promise<{
    bannerImageUrl?: string;
    nativeIconUrl?: string;
    nativeImageUrl?: string;
    pushImageUrl?: string;
  }> {
    const results: any = {};

    if (files.bannerImage) {
      results.bannerImageUrl = await this.uploadFile(files.bannerImage, `campaigns/${campaignId}/banner`);
    }

    if (files.nativeIcon) {
      results.nativeIconUrl = await this.uploadFile(files.nativeIcon, `campaigns/${campaignId}/native`);
    }

    if (files.nativeImage) {
      results.nativeImageUrl = await this.uploadFile(files.nativeImage, `campaigns/${campaignId}/native`);
    }

    if (files.pushImage) {
      results.pushImageUrl = await this.uploadFile(files.pushImage, `campaigns/${campaignId}/push`);
    }

    return results;
  }

  async deleteCampaignImages(imageUrls: string[]): Promise<void> {
    for (const url of imageUrls) {
      if (url) {
        await this.deleteFile(url);
      }
    }
  }
}

export const uploadService = new FileUploadService();
