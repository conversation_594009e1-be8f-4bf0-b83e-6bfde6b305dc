import { SmartImpressionTracker, ImpressionD<PERSON> } from './smart-impression-tracker';
import { CostProcessor } from './cost-processor';
import { RequestTracker } from './request-tracker';
import { clickHouseBatchWriter } from './clickhouse-batch-writer';
import clickhouse from './clickhouse';
import logger from './logger';
import { RabbitMQManager, QUEUE_NAMES } from './queue-manager';

// RabbitMQ processor concurrency settings
const HIGH_CONCURRENCY = 50;  // 50 concurrent consumers
const MEDIUM_CONCURRENCY = 20; // 20 concurrent consumers
const LOW_CONCURRENCY = 5;     // 5 concurrent consumers

// Initialize RabbitMQ processors
export class QueueProcessors {
  private static initialized = false;

  static async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Initialize RabbitMQ queues first
      await RabbitMQManager.initialize();

      // Setup all processors
      await Promise.all([
        this.setupImpressionProcessor(),
        this.setupCostProcessor(),
        this.setupRequestStatsProcessor(),
        this.setupWinNotificationProcessor(),
        this.setupTransactionProcessor(),
        this.setupBatchProcessor(),
      ]);

      this.initialized = true;
      logger.queue.info('🚀 All RabbitMQ processors initialized');

    } catch (error) {
      logger.queue.error('❌ Failed to initialize queue processors:', error);
      throw error;
    }
  }

  /**
   * IMPRESSION TRACKING PROCESSOR
   * Highest priority - processes impression tracking asynchronously
   */
  private static async setupImpressionProcessor(): Promise<void> {
    await RabbitMQManager.setupConsumer(
      QUEUE_NAMES.IMPRESSION_TRACKING,
      async (messageData: ImpressionData) => {
        try {
          await SmartImpressionTracker.trackImpression(messageData);
          logger.queue.info(`✅ Impression tracked: ${messageData.impressionId}`);
        } catch (error) {
          logger.queue.error('❌ Failed to track impression:', error);
          throw error; // Will trigger retry
        }
      },
      { concurrency: HIGH_CONCURRENCY, retries: 5 }
    );
  }

  /**
   * COST PROCESSING PROCESSOR
   * High priority - processes billing and revenue distribution
   */
  private static async setupCostProcessor(): Promise<void> {
    await RabbitMQManager.setupConsumer(
      QUEUE_NAMES.COST_PROCESSING,
      async (costParams: any) => {
        try {
          const result = await CostProcessor.processImpressionCost(costParams);
          logger.queue.info(`💰 Cost processed: $${result.costDeducted} deducted, $${result.publisherRevenue} credited`);
        } catch (error) {
          logger.queue.error('❌ Failed to process cost:', error);
          throw error;
        }
      },
      { concurrency: HIGH_CONCURRENCY, retries: 5 }
    );
  }

  /**
   * REQUEST STATS PROCESSOR
   * Medium priority - processes request tracking and statistics
   */
  private static async setupRequestStatsProcessor(): Promise<void> {
    await RabbitMQManager.setupConsumer(
      QUEUE_NAMES.REQUEST_STATS,
      async (requestParams: any) => {
        try {
          await RequestTracker.trackRequest(requestParams);
          logger.queue.info(`📊 Request tracked: ${requestParams.sourceType}`);
        } catch (error) {
          logger.queue.error('❌ Failed to track request:', error);
          throw error;
        }
      },
      { concurrency: MEDIUM_CONCURRENCY, retries: 3 }
    );
  }

  /**
   * WIN NOTIFICATION PROCESSOR
   * Medium priority - processes SSP win notifications
   */
  private static async setupWinNotificationProcessor(): Promise<void> {
    await RabbitMQManager.setupConsumer(
      QUEUE_NAMES.WIN_NOTIFICATIONS,
      async (winData: any) => {
        try {
          // Store win notification record
          const winId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
          await clickHouseBatchWriter.write('win_notifications', {
            id: winId,
            ssp_partner_id: winData.sspPartnerId,
            ssp_impression_id: winData.sspImpressionId || '',
            bid_price: winData.bidPrice,
            gross_bid_cpm: winData.grossBidCpm,
            winner_source: winData.winnerSource,
            winner_id: winData.winnerId,
            timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
          });

          logger.queue.info(`🏆 Win notification processed: SSP ${winData.sspPartnerId}`);
        } catch (error) {
          logger.queue.error('❌ Failed to process win notification:', error);
          throw error;
        }
      },
      { concurrency: MEDIUM_CONCURRENCY, retries: 3 }
    );
  }

  /**
   * TRANSACTION LOGGING PROCESSOR
   * Medium priority - logs financial transactions
   */
  private static async setupTransactionProcessor(): Promise<void> {
    await RabbitMQManager.setupConsumer(
      QUEUE_NAMES.TRANSACTIONS,
      async (transactionData: any) => {
        try {
          // Insert transaction record
          await clickHouseBatchWriter.write('transactions', {
            id: transactionData.id || Date.now() * 1000 + Math.floor(Math.random() * 1000),
            user_id: transactionData.userId,
            type: transactionData.type,
            amount: transactionData.amount,
            status: transactionData.status || 'completed',
            payment_method: transactionData.paymentMethod || '',
            payment_reference: transactionData.paymentReference || '',
            description: transactionData.description,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          });

          logger.queue.info(`💳 Transaction logged: ${transactionData.type} $${transactionData.amount}`);
        } catch (error) {
          logger.queue.error('❌ Failed to log transaction:', error);
          throw error;
        }
      },
      { concurrency: MEDIUM_CONCURRENCY, retries: 3 }
    );
  }

  /**
   * BATCH PROCESSING PROCESSOR
   * Low priority - handles batch operations and cleanup
   */
  private static async setupBatchProcessor(): Promise<void> {
    // Batch impressions processor
    await RabbitMQManager.setupConsumer(
      QUEUE_NAMES.BATCH_PROCESSING,
      async (messageData: any) => {
        try {
          if (messageData.type === 'batch-impressions') {
            const impressionBatch = messageData.impressions;

            // Process batch of impressions
            for (const impression of impressionBatch) {
              await SmartImpressionTracker.trackImpression(impression);
            }

            logger.queue.info(`📦 Batch processed: ${impressionBatch.length} impressions`);

          } else if (messageData.type === 'cleanup-old-data') {
            const { table, daysOld } = messageData;

            // Clean up old data
            await clickhouse.command({
              query: `
                ALTER TABLE ${table}
                DELETE WHERE timestamp < now() - INTERVAL ${daysOld} DAY
              `,
            });

            logger.queue.info(`🧹 Cleanup completed: ${table} older than ${daysOld} days`);
          }
        } catch (error) {
          logger.queue.error('❌ Failed to process batch operation:', error);
          throw error;
        }
      },
      { concurrency: LOW_CONCURRENCY, retries: 2 }
    );
  }
}

// Auto-initialize processors when module is imported
QueueProcessors.initialize().catch((error) => {
  logger.queue.error('❌ Failed to initialize queue processors:', error);
});
