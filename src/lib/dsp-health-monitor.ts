import logger from '@/lib/logger';
import { URL } from 'url';

export interface PartnerEndpoint {
  id: number;
  name: string;
  endpoint_url: string;
  protocol: string;
}

interface DSPHealthStatus {
  isHealthy: boolean;
  lastCheckTime: number;
  lastFailureTime: number; // To track consecutive failures for re-evaluation
  consecutiveFailures: number;
}

export class DspHealthMonitor {
  private static instance: DspHealthMonitor;
  private healthStatuses: Map<number, DSPHealthStatus> = new Map();
  private partners: PartnerEndpoint[] = [];
  private checkInterval: NodeJS.Timeout | null = null;
  private healthCheckIntervalMs: number = 60000; // Check every 60 seconds
  private failureThreshold: number = 3; // Mark unhealthy after 3 consecutive failures
  private recheckIntervalMs: number = 300000; // Try rechecking unhealthy DSPs every 5 minutes
  private isInitialized: boolean = false;

  private constructor() {
    // Private constructor to enforce singleton
  }

  public static getInstance(): DspHealthMonitor {
    if (!DspHealthMonitor.instance) {
      DspHealthMonitor.instance = new DspHealthMonitor();
    }
    return DspHealthMonitor.instance;
  }

  public async initialize(partners: PartnerEndpoint[]): Promise<void> {
    // Only initialize once to prevent multiple monitoring intervals
    if (this.isInitialized) {
      // Update partners list but don't restart monitoring
      this.updatePartners(partners);
      return;
    }

    this.partners = partners;
    this.partners.forEach(partner => {
      this.healthStatuses.set(partner.id, {
        isHealthy: true,
        lastCheckTime: 0,
        lastFailureTime: 0,
        consecutiveFailures: 0,
      });
    });

    this.isInitialized = true;
    this.startMonitoring();
  }

  private updatePartners(partners: PartnerEndpoint[]): void {
    // Update the partners list and add any new partners to health status
    this.partners = partners;
    this.partners.forEach(partner => {
      if (!this.healthStatuses.has(partner.id)) {
        this.healthStatuses.set(partner.id, {
          isHealthy: true,
          lastCheckTime: 0,
          lastFailureTime: 0,
          consecutiveFailures: 0,
        });
      }
    });
  }

  private startMonitoring(): void {
    if (this.checkInterval) {
      logger.app.warn('DSP Health Monitor already running, skipping start');
      return;
    }

    this.checkInterval = setInterval(() => this.runHealthChecks(), this.healthCheckIntervalMs);
    logger.app.info(`DSP Health Monitor started. Checking every ${this.healthCheckIntervalMs / 1000} seconds.`);
  }

  private async runHealthChecks(): Promise<void> {
    logger.app.info('Running DSP health checks...');
    for (const partner of this.partners) {
      await this.checkDspHealth(partner);
    }
  }

  private async checkDspHealth(partner: PartnerEndpoint): Promise<void> {
    const status = this.healthStatuses.get(partner.id);
    if (!status) return; // Should not happen if initialized correctly

    const now = Date.now();

    // Skip check if recently checked and healthy, or if unhealthy and not yet time to recheck
    if (status.isHealthy && (now - status.lastCheckTime < this.healthCheckIntervalMs)) {
      return;
    }

    if (!status.isHealthy && (now - status.lastFailureTime < this.recheckIntervalMs)) {
      return;
    }

    status.lastCheckTime = now;
    let currentIsHealthy = false;

    try {
      const url = new URL(partner.endpoint_url);
      // Use a very short timeout for health checks
      const healthCheckTimeout = 1000; // 1 second timeout for health check

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), healthCheckTimeout);

      // For health checks, we can send a HEAD request or a very minimal GET request
      // Assuming a GET request to the endpoint is sufficient for a basic check
      const response = await Promise.race([
        fetch(url.toString(), {
          method: 'GET',
          signal: controller.signal,
          headers: {
            'User-Agent': 'GlobalAdsMedia-HealthChecker/1.0',
          },
        }),
        new Promise<Response>((_, reject) =>
          setTimeout(() => reject(new Error('Health check timed out')), healthCheckTimeout)
        )
      ]);

      clearTimeout(timeoutId);

      if (response.ok) {
        currentIsHealthy = true;
        logger.app.info(`DSP Health Check: ${partner.name} (${partner.id}) is HEALTHY.`);
      } else {
        logger.app.warn(`DSP Health Check: ${partner.name} (${partner.id}) returned status ${response.status}.`);
      }
    } catch (error: any) {
      logger.app.warn(`DSP Health Check: ${partner.name} (${partner.id}) failed: ${error.message}`);
    }

    if (currentIsHealthy) {
      if (!status.isHealthy) {
        logger.app.info(`DSP Health Check: ${partner.name} (${partner.id}) recovered and is now HEALTHY.`);
      }
      status.isHealthy = true;
      status.consecutiveFailures = 0;
    } else {
      status.consecutiveFailures++;
      status.lastFailureTime = now;
      if (status.consecutiveFailures >= this.failureThreshold) {
        if (status.isHealthy) {
          logger.app.error(`DSP Health Check: ${partner.name} (${partner.id}) marked UNHEALTHY after ${status.consecutiveFailures} failures.`);
        }
        status.isHealthy = false;
      } else {
        logger.app.warn(`DSP Health Check: ${partner.name} (${partner.id}) failed. Consecutive failures: ${status.consecutiveFailures}.`);
      }
    }
    this.healthStatuses.set(partner.id, status);
  }

  public isDspHealthy(dspId: number): boolean {
    const status = this.healthStatuses.get(dspId);
    return status ? status.isHealthy : true; // Default to healthy if no status found
  }

  public stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      this.isInitialized = false;
      logger.app.info('DSP Health Monitor stopped.');
    }
  }
} 