import NextAuth from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import clickhouse from './clickhouse';

export const { handlers, signIn, signOut, auth } = NextAuth({
  trustHost: true, // Fix for UntrustedHost error in development
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const result = await clickhouse.query({
            query: 'SELECT * FROM users WHERE email = {email:String}',
            query_params: { email: credentials.email },
          });

          const users = await result.json();
          const user = users.data[0];

          if (!user) {
            return null;
          }

          const userData = user as any;

          const isPasswordValid = await bcrypt.compare(
            credentials.password as string,
            userData.password
          );

          if (!isPasswordValid) {
            return null;
          }

          // Check if email is verified (only for advertiser and publisher)
          if (userData.email_verified === 0 && ['advertiser', 'publisher'].includes(userData.role)) {
            // Return null with a specific error indicator
            return null;
          }

          // Check if account is active
          if (userData.status !== 'active') {
            throw new Error('Your account is not active. Please contact support if you believe this is an error.');
          }

          return {
            id: userData.id.toString(),
            email: userData.email,
            name: userData.full_name,
            role: userData.role,
            status: userData.status,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (account) {
        token.accessToken = account.access_token;
      }
      if (user) {
        token.role = user.role;
        token.status = user.status;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub as string;
        session.user.role = token.role as string;
        session.user.status = token.status as string;
        session.accessToken = token.accessToken as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
  },
  session: {
    strategy: 'jwt',
  },
});

declare module 'next-auth' {
  interface User {
    role: string;
    status: string;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
      status: string;
    };
    accessToken?: string;
  }
}


