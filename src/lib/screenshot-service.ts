import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';
import { existsSync } from 'fs';

export class ScreenshotService {
  private static screenshotDir = path.join(process.cwd(), 'public', 'screenshots');
  private static baseUrl = process.env.NEXTAUTH_URL || process.env.PLATFORM_URL || 'http://localhost:3000';

  // Ensure screenshot directory exists
  private static async ensureDirectoryExists() {
    if (!existsSync(this.screenshotDir)) {
      await fs.mkdir(this.screenshotDir, { recursive: true });
    }
  }

  // Validate URL format
  private static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  // Take screenshot of a URL
  static async takeScreenshot(url: string, campaignId: number, region: string): Promise<string | null> {
    let browser = null;

    try {
      // Validate URL first
      if (!this.isValidUrl(url)) {
        console.error(`Invalid URL for screenshot: ${url}`);
        return null;
      }

      await this.ensureDirectoryExists();

      // Launch browser with optimized settings for server environment
      browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-field-trial-config',
          '--disable-back-forward-cache',
          '--disable-ipc-flooding-protection',
          '--single-process'
        ],
        timeout: 30000
      });

      const page = await browser.newPage();

      // Set viewport and user agent
      await page.setViewport({
        width: 1200,
        height: 800,
        deviceScaleFactor: 1
      });

      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

      // Set timeout and navigate with better error handling
      page.setDefaultTimeout(15000);

      console.log(`Navigating to: ${url}`);
      await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });

      // Wait a bit for any dynamic content to load
      console.log('Waiting for page to stabilize...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `campaign-${campaignId}-${region}-${timestamp}.png`;
      const filepath = path.join(this.screenshotDir, filename);

      // Take screenshot
      console.log(`Taking screenshot and saving to: ${filepath}`);
      await page.screenshot({
        path: filepath,
        fullPage: false, // Changed to false for faster processing
        type: 'png',
        clip: { x: 0, y: 0, width: 1200, height: 800 } // Fixed size for consistency
      });

      // Verify file was created
      const fileExists = existsSync(filepath);
      if (!fileExists) {
        throw new Error('Screenshot file was not created');
      }

      // Return public URL
      const publicUrl = `${this.baseUrl}/screenshots/${filename}`;

      console.log(`✅ Screenshot taken successfully: ${publicUrl}`);
      return publicUrl;

    } catch (error) {
      console.error('Screenshot failed:', error);
      return null;
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  // Clean up old screenshots (older than 90 days)
  static async cleanupOldScreenshots(): Promise<void> {
    try {
      await this.ensureDirectoryExists();

      const files = await fs.readdir(this.screenshotDir);
      const now = Date.now();
      const ninetyDaysAgo = now - (90 * 24 * 60 * 60 * 1000); // 90 days in milliseconds

      let deletedCount = 0;

      for (const file of files) {
        if (!file.endsWith('.png')) continue;

        const filepath = path.join(this.screenshotDir, file);

        try {
          const stats = await fs.stat(filepath);

          // Delete if older than 90 days
          if (stats.mtime.getTime() < ninetyDaysAgo) {
            await fs.unlink(filepath);
            deletedCount++;
            console.log(`Deleted old screenshot: ${file}`);
          }
        } catch (error) {
          console.error(`Error processing file ${file}:`, error);
        }
      }

      console.log(`Screenshot cleanup completed. Deleted ${deletedCount} old files.`);

    } catch (error) {
      console.error('Screenshot cleanup failed:', error);
    }
  }

  // Get screenshot info from URL
  static getScreenshotInfo(screenshotUrl: string): { filename: string; filepath: string } | null {
    try {
      const url = new URL(screenshotUrl);
      const filename = path.basename(url.pathname);
      const filepath = path.join(this.screenshotDir, filename);

      return { filename, filepath };
    } catch (error) {
      return null;
    }
  }

  // Delete specific screenshot
  static async deleteScreenshot(screenshotUrl: string): Promise<boolean> {
    try {
      const info = this.getScreenshotInfo(screenshotUrl);
      if (!info) return false;

      if (existsSync(info.filepath)) {
        await fs.unlink(info.filepath);
        console.log(`Deleted screenshot: ${info.filename}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to delete screenshot:', error);
      return false;
    }
  }

  // Get screenshot file size and age
  static async getScreenshotStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile: Date | null;
    newestFile: Date | null;
  }> {
    try {
      await this.ensureDirectoryExists();

      const files = await fs.readdir(this.screenshotDir);
      let totalFiles = 0;
      let totalSize = 0;
      let oldestFile: Date | null = null;
      let newestFile: Date | null = null;

      for (const file of files) {
        if (!file.endsWith('.png')) continue;

        const filepath = path.join(this.screenshotDir, file);

        try {
          const stats = await fs.stat(filepath);
          totalFiles++;
          totalSize += stats.size;

          if (!oldestFile || stats.mtime < oldestFile) {
            oldestFile = stats.mtime;
          }

          if (!newestFile || stats.mtime > newestFile) {
            newestFile = stats.mtime;
          }
        } catch (error) {
          console.error(`Error reading stats for ${file}:`, error);
        }
      }

      return {
        totalFiles,
        totalSize,
        oldestFile,
        newestFile
      };
    } catch (error) {
      console.error('Failed to get screenshot stats:', error);
      return {
        totalFiles: 0,
        totalSize: 0,
        oldestFile: null,
        newestFile: null
      };
    }
  }
}
