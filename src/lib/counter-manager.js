/**
 * Counter Management System
 * Manages auto-incrementing IDs for all entities using .env starting points
 */

const { createClient } = require('@clickhouse/client');

const clickhouse = createClient({
  url: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
  username: process.env.CLICKHOUSE_USERNAME || 'default',
  password: process.env.CLICKHOUSE_PASSWORD || 'gamuschdb171288',
  database: process.env.CLICKHOUSE_DATABASE || 'global_ads_media',
  // Reduced connection pool to prevent TOO_MANY_SIMULTANEOUS_QUERIES
  max_open_connections: 5,
  keep_alive: {
    enabled: true,
    idle_socket_ttl: 30000,
  },
  request_timeout: 3000,
});

// CounterType: 'user' | 'admin' | 'campaign' | 'creative' | 'website' | 'zone' | 'impression' | 'click' | 'transaction' | 'partner_endpoint'

// CounterConfig interface:
// {
//   table: string;
//   envVar: string;
//   defaultStart: number;
//   roleFilter?: string; // For users table role filtering
// }

const COUNTER_CONFIGS = {
  user: {
    table: 'users',
    envVar: 'USER_ID_START',
    defaultStart: 10340,
    roleFilter: "role != 'admin'"
  },
  admin: {
    table: 'users',
    envVar: 'ADMIN_ID_START',
    defaultStart: 1,
    roleFilter: "role = 'admin'"
  },
  campaign: {
    table: 'campaigns',
    envVar: 'CAMPAIGN_ID_START',
    defaultStart: 171288
  },
  creative: {
    table: 'campaigns', // Creatives are part of campaigns
    envVar: 'CREATIVE_ID_START',
    defaultStart: 311216
  },
  website: {
    table: 'websites',
    envVar: 'WEBSITE_ID_START',
    defaultStart: 70589
  },
  zone: {
    table: 'ad_zones',
    envVar: 'ZONE_ID_START',
    defaultStart: 230763
  },
  impression: {
    table: 'impressions',
    envVar: 'IMPRESSION_ID_START',
    defaultStart: 1
  },
  click: {
    table: 'clicks',
    envVar: 'CLICK_ID_START',
    defaultStart: 1
  },
  transaction: {
    table: 'transactions',
    envVar: 'TRANSACTION_ID_START',
    defaultStart: 1
  },
  partner_endpoint: {
    table: 'partner_endpoints',
    envVar: 'PARTNER_ENDPOINT_ID_START',
    defaultStart: 1
  }
};

class CounterManager {
  /**
   * Get the next available ID for a specific counter type (SIMPLE & RELIABLE)
   */
  static async getNextId(counterType) {
    try {
      const config = COUNTER_CONFIGS[counterType];
      if (!config) {
        throw new Error(`Unknown counter type: ${counterType}`);
      }

      // Get starting point from environment
      const startingPoint = parseInt(process.env[config.envVar] || config.defaultStart.toString());

      // Query database to get the current maximum ID
      let query = `SELECT MAX(id) as max_id FROM ${config.table}`;
      if (config.roleFilter) {
        query += ` WHERE ${config.roleFilter}`;
      }

      const result = await clickhouse.query({ query });
      const data = await result.json();
      const maxId = data.data[0]?.max_id || 0;

      // Generate next ID: max(startingPoint, maxId + 1) + process offset
      const baseNextId = Math.max(startingPoint, maxId + 1);

      // Add small process-based offset to avoid collisions (0-23 for PM2 processes)
      const processOffset = process.pid % 24;
      const nextId = baseNextId + processOffset;

      console.log(`🆔 Counter ${counterType}: Process ${process.pid} - Max: ${maxId}, Base: ${baseNextId}, Offset: ${processOffset}, Final: ${nextId}`);

      return nextId;
    } catch (error) {
      console.error(`Error getting next ID for ${counterType}:`, error);
      // Fallback to simple sequential ID
      const fallbackId = Date.now() % 1000000 + Math.floor(Math.random() * 100);
      console.warn(`🆔 Counter ${counterType}: Using fallback ID ${fallbackId}`);
      return fallbackId;
    }
  }

  /**
   * Get multiple consecutive IDs for batch operations
   */
  static async getNextIds(counterType, count) {
    const startId = await this.getNextId(counterType);
    const ids = [];

    for (let i = 0; i < count; i++) {
      ids.push(startId + i);
    }

    return ids;
  }

  /**
   * Get the current counter status for all types
   */
  static async getCounterStatus() {
    const status = {};

    for (const [counterType, config] of Object.entries(COUNTER_CONFIGS)) {
      try {
        const startingPoint = parseInt(process.env[config.envVar] || config.defaultStart.toString());

        let query = `SELECT MAX(id) as max_id FROM ${config.table}`;
        if (config.roleFilter) {
          query += ` WHERE ${config.roleFilter}`;
        }

        const result = await clickhouse.query({ query });
        const data = await result.json();

        const current = data.data[0]?.max_id || 0;
        const nextId = Math.max(startingPoint, current + 1);

        status[counterType] = {
          current,
          startingPoint,
          nextId
        };
      } catch (error) {
        console.error(`Error getting status for ${counterType}:`, error);
        status[counterType] = {
          current: 0,
          startingPoint: parseInt(process.env[config.envVar] || config.defaultStart.toString()),
          nextId: parseInt(process.env[config.envVar] || config.defaultStart.toString())
        };
      }
    }

    return status;
  }

  /**
   * Validate that a counter type exists
   */
  static isValidCounterType(counterType) {
    return counterType in COUNTER_CONFIGS;
  }

  /**
   * Get counter configuration for a specific type
   */
  static getCounterConfig(counterType) {
    return COUNTER_CONFIGS[counterType];
  }

  /**
   * Reset counter to starting point (DANGEROUS - use only for testing)
   */
  static async resetCounter(counterType, confirm = false) {
    if (!confirm) {
      throw new Error('Counter reset requires explicit confirmation');
    }

    const config = COUNTER_CONFIGS[counterType];
    const startingPoint = parseInt(process.env[config.envVar] || config.defaultStart.toString());

    console.warn(`⚠️  RESETTING ${counterType} counter to ${startingPoint} - THIS WILL DELETE ALL DATA!`);

    // This is a dangerous operation - only implement if absolutely necessary
    throw new Error('Counter reset not implemented for safety');
  }

  /**
   * Get next user ID with proper role handling
   */
  static async getNextUserId(role = 'user') {
    const counterType = role === 'admin' ? 'admin' : 'user';
    return this.getNextId(counterType);
  }

  /**
   * Batch ID generation for migration purposes
   */
  static async generateUserIdBatch(count, role = 'user') {
    const counterType = role === 'admin' ? 'admin' : 'user';
    return this.getNextIds(counterType, count);
  }
}

// Export for CommonJS
module.exports = {
  CounterManager,
  getNextId: CounterManager.getNextId.bind(CounterManager),
  getNextUserId: CounterManager.getNextUserId.bind(CounterManager),
  getCounterStatus: CounterManager.getCounterStatus.bind(CounterManager)
};
