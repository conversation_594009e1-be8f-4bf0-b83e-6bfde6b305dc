import { cache, CACHE_KEYS, CACH<PERSON>_TTL, CacheHelper } from './cache';
import clickhouse from './clickhouse';
import logger from './logger';

/**
 * High-Performance Caching Layer for RTB Operations
 * Implements aggressive caching strategies for maximum QPS
 */

export class HighPerformanceCache {
  // In-memory cache for ultra-fast access (LRU with 1000 entries)
  private static memoryCache = new Map<string, { value: any; expires: number }>();
  private static maxMemoryEntries = 1000;

  /**
   * Get active campaigns with aggressive caching
   */
  static async getActiveCampaigns(format: string): Promise<any[]> {
    const cacheKey = CACHE_KEYS.ACTIVE_CAMPAIGNS(format);
    
    // Try memory cache first (fastest)
    const memCached = this.getFromMemory(cacheKey);
    if (memCached !== null) {
      return memCached;
    }

    // Try Redis cache
    return await CacheHelper.getOrSet(
      cacheKey,
      async () => {
        const result = await clickhouse.query({
          query: `
            SELECT 
              id, name, type, cpm_bid, daily_budget, total_budget,
              daily_spent, total_spent, status, targeting_countries,
              targeting_devices, targeting_os, targeting_browsers,
              banner_image_url, landing_url, js_tag,
              native_title, native_description, native_icon_url, native_image_url,
              push_title, push_description, push_image_url,
              start_date, end_date, user_id
            FROM campaigns 
            WHERE status = 'active' 
              AND start_date <= now() 
              AND (end_date IS NULL OR end_date >= now())
              AND daily_spent < daily_budget
              AND total_spent < total_budget
            ORDER BY cmp_bid DESC
          `,
        });
        const data = await result.json();
        
        // Store in memory cache for ultra-fast access
        this.setInMemory(cacheKey, data.data, CACHE_TTL.CAMPAIGNS);
        
        return data.data;
      },
      CACHE_TTL.CAMPAIGNS
    );
  }

  /**
   * Get partner endpoints with caching
   */
  static async getPartnerEndpoints(type: 'dsp' | 'ssp'): Promise<any[]> {
    const cacheKey = CACHE_KEYS.PARTNER_ENDPOINTS(type);
    
    // Try memory cache first
    const memCached = this.getFromMemory(cacheKey);
    if (memCached !== null) {
      return memCached;
    }

    return await CacheHelper.getOrSet(
      cacheKey,
      async () => {
        const result = await clickhouse.query({
          query: `
            SELECT id, name, type, endpoint_url, status, timeout_ms, revenue_share, qps_limit, protocol, targeting
            FROM partner_endpoints
            WHERE type = {type:String} AND status = 'active'
            ORDER BY id ASC
          `,
          query_params: { type },
        });
        const data = await result.json();
        
        // Store in memory cache
        this.setInMemory(cacheKey, data.data, CACHE_TTL.PARTNERS);
        
        return data.data;
      },
      CACHE_TTL.PARTNERS
    );
  }

  /**
   * Get platform settings with caching
   */
  static async getPlatformSettings(): Promise<{ [key: string]: string }> {
    const cacheKey = CACHE_KEYS.PLATFORM_SETTINGS;
    
    // Try memory cache first
    const memCached = this.getFromMemory(cacheKey);
    if (memCached !== null) {
      return memCached;
    }

    return await CacheHelper.getOrSet(
      cacheKey,
      async () => {
        const result = await clickhouse.query({
          query: 'SELECT setting_key, setting_value FROM platform_settings',
        });
        const data = await result.json();
        
        const settings: { [key: string]: string } = {};
        data.data.forEach((row: any) => {
          settings[row.setting_key] = row.setting_value;
        });
        
        // Store in memory cache
        this.setInMemory(cacheKey, settings, CACHE_TTL.SETTINGS);
        
        return settings;
      },
      CACHE_TTL.SETTINGS
    );
  }

  /**
   * Get user balance with caching
   */
  static async getUserBalance(userId: number): Promise<number> {
    const cacheKey = CACHE_KEYS.USER_BALANCE(userId);
    
    return await CacheHelper.getOrSet(
      cacheKey,
      async () => {
        const result = await clickhouse.query({
          query: 'SELECT balance FROM users WHERE id = {userId:UInt32}',
          query_params: { userId },
        });
        const data = await result.json();
        return data.data[0]?.balance || 0;
      },
      CACHE_TTL.USER_DATA
    );
  }

  /**
   * Get campaign budget info with caching
   */
  static async getCampaignBudget(campaignId: number): Promise<{
    dailyBudget: number;
    totalBudget: number;
    dailySpent: number;
    totalSpent: number;
  }> {
    const cacheKey = CACHE_KEYS.CAMPAIGN_BUDGET(campaignId);
    
    return await CacheHelper.getOrSet(
      cacheKey,
      async () => {
        const result = await clickhouse.query({
          query: `
            SELECT daily_budget, total_budget, daily_spent, total_spent
            FROM campaigns 
            WHERE id = {campaignId:UInt32}
          `,
          query_params: { campaignId },
        });
        const data = await result.json();
        const row = data.data[0];
        
        return {
          dailyBudget: row?.daily_budget || 0,
          totalBudget: row?.total_budget || 0,
          dailySpent: row?.daily_spent || 0,
          totalSpent: row?.total_spent || 0,
        };
      },
      CACHE_TTL.BUDGET
    );
  }

  /**
   * Batch get multiple campaigns
   */
  static async getCampaignsBatch(campaignIds: number[]): Promise<{ [id: number]: any }> {
    const cacheKeys = campaignIds.map(id => `campaign:${id}`);
    const cached = await cache.mget(cacheKeys);
    
    const result: { [id: number]: any } = {};
    const missingIds: number[] = [];
    
    // Check which campaigns are cached
    campaignIds.forEach((id, index) => {
      if (cached[index]) {
        result[id] = cached[index];
      } else {
        missingIds.push(id);
      }
    });

    // Fetch missing campaigns from database
    if (missingIds.length > 0) {
      const dbResult = await clickhouse.query({
        query: `
          SELECT * FROM campaigns 
          WHERE id IN (${missingIds.join(',')})
        `,
      });
      const data = await dbResult.json();
      
      // Cache the fetched campaigns
      const cacheEntries = data.data.map((campaign: any) => ({
        key: `campaign:${campaign.id}`,
        value: campaign,
        ttl: CACHE_TTL.CAMPAIGNS,
      }));
      
      await cache.mset(cacheEntries);
      
      // Add to result
      data.data.forEach((campaign: any) => {
        result[campaign.id] = campaign;
      });
    }

    return result;
  }

  /**
   * Memory cache operations (ultra-fast)
   */
  private static getFromMemory(key: string): any | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expires) {
      this.memoryCache.delete(key);
      return null;
    }
    
    return entry.value;
  }

  private static setInMemory(key: string, value: any, ttlSeconds: number): void {
    // Implement LRU eviction
    if (this.memoryCache.size >= this.maxMemoryEntries) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }
    
    this.memoryCache.set(key, {
      value,
      expires: Date.now() + (ttlSeconds * 1000),
    });
  }

  /**
   * Invalidate cache when data changes
   */
  static async invalidateCampaign(campaignId: number): Promise<void> {
    const patterns = [
      `campaigns:active:*`,
      `campaign:${campaignId}*`,
    ];
    
    await CacheHelper.invalidateRelated(patterns);
    
    // Also clear memory cache
    for (const [key] of this.memoryCache) {
      if (key.includes(`campaign`) || key.includes(`${campaignId}`)) {
        this.memoryCache.delete(key);
      }
    }
  }

  static async invalidateUser(userId: number): Promise<void> {
    const patterns = [
      `user:${userId}:*`,
    ];
    
    await CacheHelper.invalidateRelated(patterns);
  }

  static async invalidatePartners(): Promise<void> {
    const patterns = [
      `partners:*`,
      `partner:*`,
    ];
    
    await CacheHelper.invalidateRelated(patterns);
    
    // Clear memory cache
    for (const [key] of this.memoryCache) {
      if (key.includes('partners') || key.includes('partner')) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * Warm up cache with frequently accessed data
   */
  static async warmUpCache(): Promise<void> {
    try {
      logger.cache.info('🔥 Warming up high-performance cache...');
      
      // Pre-load active campaigns
      await this.getActiveCampaigns('banner');
      await this.getActiveCampaigns('native');
      await this.getActiveCampaigns('popup');
      await this.getActiveCampaigns('in_page_push');
      
      // Pre-load partner endpoints
      await this.getPartnerEndpoints('dsp');
      await this.getPartnerEndpoints('ssp');
      
      // Pre-load platform settings
      await this.getPlatformSettings();
      
      logger.cache.info('✅ Cache warm-up completed');
    } catch (error) {
      logger.cache.error('❌ Cache warm-up failed:', error);
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): {
    memoryEntries: number;
    memoryMaxEntries: number;
    memoryUsagePercent: number;
  } {
    return {
      memoryEntries: this.memoryCache.size,
      memoryMaxEntries: this.maxMemoryEntries,
      memoryUsagePercent: (this.memoryCache.size / this.maxMemoryEntries) * 100,
    };
  }

  /**
   * Clear all caches
   */
  static async clearAllCaches(): Promise<void> {
    this.memoryCache.clear();
    await cache.invalidatePattern('*');
    logger.cache.info('🧹 All caches cleared');
  }
}

export default HighPerformanceCache;
