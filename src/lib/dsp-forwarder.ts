import clickhouse from './clickhouse';

export interface DSPBidRequest {
  id: string;
  imp: Array<{
    id: string;
    banner?: {
      w: number;
      h: number;
    };
    native?: any;
    bidfloor?: number;
  }>;
  site: {
    id: string;
    name: string;
    domain: string;
    page: string;
  };
  device: {
    ua: string;
    ip: string;
    geo: {
      country: string;
      region?: string;
      city?: string;
    };
    devicetype: number;
    os: string;
    browser: string;
  };
  user: {
    id: string;
  };
  at: number; // Auction type: 1 = first price, 2 = second price
  tmax: number; // Max timeout in ms
  cur: string[];
}

export interface DSPBidResponse {
  id: string;
  seatbid?: Array<{
    bid: Array<{
      id: string;
      impid: string;
      price: number;
      adm: string;
      adomain: string[];
      cid: string;
      crid: string;
      w?: number;
      h?: number;
      nurl?: string;
    }>;
    seat: string;
  }>;
  nbr?: number; // No bid reason
  cur?: string;
}

export interface DSPPartner {
  id: number;
  name: string;
  endpoint_url: string;
  protocol: string;
  api_key: string;
  timeout_ms: number;
  auth_type: string;
  auth_credentials: any;
}

export class DSPForwarder {
  /**
   * Get active DSP partners
   */
  static async getActiveDSPPartners(): Promise<DSPPartner[]> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT id, name, endpoint_url, protocol, api_key, timeout_ms, auth_type, auth_credentials
          FROM partner_endpoints
          WHERE type = 'dsp' AND status = 'active'
          ORDER BY id
        `,
      });

      const data = await result.json();
      return data.data.map((partner: any) => ({
        ...partner,
        auth_credentials: JSON.parse(partner.auth_credentials || '{}'),
      }));
    } catch (error) {
      console.error('Error fetching DSP partners:', error);
      return [];
    }
  }

  /**
   * Create OpenRTB bid request
   */
  static createBidRequest(params: {
    zoneId: string;
    format: string;
    size: string;
    website: any;
    zone: any;
    userAgent: string;
    ip: string;
    deviceGeoInfo: any;
  }): DSPBidRequest {
    const { zoneId, format, size, website, zone, userAgent, ip, deviceGeoInfo } = params;
    const [width, height] = size.split('x').map(Number);

    const bidRequest: DSPBidRequest = {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      imp: [{
        id: `imp_${zoneId}`,
        bidfloor: 0.001, // Minimum bid floor
      }],
      site: {
        id: website.id.toString(),
        name: website.name || 'Unknown Site',
        domain: website.domain || 'unknown.com',
        page: website.url || `https://${website.domain}`,
      },
      device: {
        ua: userAgent,
        ip: ip,
        geo: {
          country: deviceGeoInfo.country || 'US',
          region: deviceGeoInfo.region,
          city: deviceGeoInfo.city,
        },
        devicetype: deviceGeoInfo.device_type === 'mobile' ? 1 : deviceGeoInfo.device_type === 'tablet' ? 5 : 2,
        os: deviceGeoInfo.os || 'Unknown',
        browser: deviceGeoInfo.browser || 'Unknown',
      },
      user: {
        id: `user_${ip.replace(/\./g, '_')}`,
      },
      at: 1, // First price auction
      tmax: 300, // 300ms timeout
      cur: ['USD'],
    };

    // Add format-specific impression details
    if (format === 'banner') {
      bidRequest.imp[0].banner = {
        w: width || 300,
        h: height || 250,
      };
    } else if (format === 'native') {
      bidRequest.imp[0].native = {
        request: JSON.stringify({
          ver: '1.2',
          layout: 1,
          assets: [
            { id: 1, required: 1, title: { len: 90 } },
            { id: 2, required: 1, data: { type: 2, len: 140 } },
            { id: 3, required: 0, img: { type: 3, wmin: 300, hmin: 250 } },
          ],
        }),
      };
    }

    return bidRequest;
  }

  /**
   * Send bid request to DSP partner
   */
  static async sendBidRequest(partner: DSPPartner, bidRequest: DSPBidRequest): Promise<DSPBidResponse | null> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'GlobalAdsMedia-DSP/1.0',
      };

      // Add authentication headers
      if (partner.auth_type === 'api_key' && partner.api_key) {
        headers['Authorization'] = `Bearer ${partner.api_key}`;
      } else if (partner.auth_type === 'header' && partner.auth_credentials.header_name) {
        headers[partner.auth_credentials.header_name] = partner.auth_credentials.header_value;
      }

      console.log(`DSP Forwarder: Sending bid request to ${partner.name} (${partner.endpoint_url})`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), partner.timeout_ms || 300);

      const response = await fetch(partner.endpoint_url, {
        method: 'POST',
        headers,
        body: JSON.stringify(bidRequest),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`DSP ${partner.name} returned ${response.status}: ${response.statusText}`);
        return null;
      }

      const bidResponse: DSPBidResponse = await response.json();
      console.log(`DSP Forwarder: Received response from ${partner.name}:`, bidResponse);

      return bidResponse;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error(`DSP ${partner.name} request timed out`);
      } else {
        console.error(`DSP ${partner.name} request failed:`, error);
      }
      return null;
    }
  }

  /**
   * Send bid requests to all active DSP partners
   */
  static async requestBidsFromAllDSPs(bidRequest: DSPBidRequest): Promise<Array<{
    partner: DSPPartner;
    response: DSPBidResponse;
    bid: any;
  }>> {
    try {
      const partners = await this.getActiveDSPPartners();
      
      if (partners.length === 0) {
        console.log('DSP Forwarder: No active DSP partners found');
        return [];
      }

      console.log(`DSP Forwarder: Sending bid requests to ${partners.length} DSP partners`);

      // Send requests to all DSPs in parallel with Promise.allSettled for better error handling
      const bidPromises = partners.map(async (partner) => {
        try {
          const response = await this.sendBidRequest(partner, bidRequest);
          return { partner, response, success: true };
        } catch (error) {
          console.error(`DSP ${partner.name} request failed:`, error);
          return { partner, response: null, success: false };
        }
      });

      const results = await Promise.all(bidPromises);

      // Extract valid bids
      const validBids: Array<{ partner: DSPPartner; response: DSPBidResponse; bid: any }> = [];

      for (const { partner, response } of results) {
        if (response && response.seatbid && response.seatbid.length > 0) {
          for (const seatbid of response.seatbid) {
            for (const bid of seatbid.bid) {
              if (bid.price > 0) {
                validBids.push({ partner, response, bid });
              }
            }
          }
        }
      }

      console.log(`DSP Forwarder: Received ${validBids.length} valid bids from DSPs`);
      return validBids;
    } catch (error) {
      console.error('DSP Forwarder: Error requesting bids from DSPs:', error);
      return [];
    }
  }
}
