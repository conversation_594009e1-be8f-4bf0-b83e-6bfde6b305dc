import HighPerformanceCache from './high-performance-cache';
import HighPerformanceHttpClient from './http-client';
import logger from './logger';

/**
 * Startup Optimizer for RTB Platform
 * Warms up caches and connections for maximum QPS performance
 */

export class StartupOptimizer {
  /**
   * Initialize all performance optimizations
   */
  static async initialize(): Promise<void> {
    logger.cache.info('🚀 Starting RTB Platform Performance Optimization...');
    
    const startTime = Date.now();
    
    try {
      // Run optimizations in parallel for faster startup
      await Promise.all([
        this.warmUpCache(),
        this.warmUpConnections(),
        this.preloadCriticalData(),
      ]);
      
      const duration = Date.now() - startTime;
      logger.cache.info(`✅ RTB Platform optimization completed in ${duration}ms`);
      
      // Log performance statistics
      this.logPerformanceStats();
      
    } catch (error) {
      logger.cache.error('❌ RTB Platform optimization failed:', error);
      // Don't throw - let the app start even if optimization fails
    }
  }

  /**
   * Warm up high-performance cache
   */
  private static async warmUpCache(): Promise<void> {
    try {
      logger.cache.info('🔥 Warming up high-performance cache...');
      await HighPerformanceCache.warmUpCache();
      logger.cache.info('✅ Cache warm-up completed');
    } catch (error) {
      logger.cache.error('❌ Cache warm-up failed:', error);
    }
  }

  /**
   * Warm up HTTP connections to frequently used endpoints
   */
  private static async warmUpConnections(): Promise<void> {
    try {
      logger.cache.info('🌐 Warming up HTTP connections...');
      
      // Get common DSP/SSP endpoints for connection warm-up
      const dspEndpoints = await this.getDspEndpoints();
      const sspEndpoints = await this.getSspEndpoints();
      
      const allEndpoints = [...dspEndpoints, ...sspEndpoints];
      
      if (allEndpoints.length > 0) {
        await HighPerformanceHttpClient.warmUpConnections(allEndpoints);
        logger.cache.info(`✅ Warmed up connections to ${allEndpoints.length} endpoints`);
      } else {
        logger.cache.info('ℹ️ No endpoints found for connection warm-up');
      }
    } catch (error) {
      logger.cache.error('❌ Connection warm-up failed:', error);
    }
  }

  /**
   * Preload critical data for faster auction processing
   */
  private static async preloadCriticalData(): Promise<void> {
    try {
      logger.cache.info('📊 Preloading critical data...');
      
      // Preload platform settings
      await HighPerformanceCache.getPlatformSettings();
      
      // Preload partner endpoints
      await Promise.all([
        HighPerformanceCache.getPartnerEndpoints('dsp'),
        HighPerformanceCache.getPartnerEndpoints('ssp'),
      ]);
      
      logger.cache.info('✅ Critical data preloaded');
    } catch (error) {
      logger.cache.error('❌ Critical data preload failed:', error);
    }
  }

  /**
   * Get DSP endpoints for connection warm-up
   */
  private static async getDspEndpoints(): Promise<string[]> {
    try {
      const partners = await HighPerformanceCache.getPartnerEndpoints('dsp');
      return partners
        .filter((partner: any) => partner.endpoint_url)
        .map((partner: any) => partner.endpoint_url)
        .slice(0, 10); // Limit to top 10 for warm-up
    } catch (error) {
      logger.cache.warn('Failed to get DSP endpoints for warm-up:', error);
      return [];
    }
  }

  /**
   * Get SSP endpoints for connection warm-up
   */
  private static async getSspEndpoints(): Promise<string[]> {
    try {
      const partners = await HighPerformanceCache.getPartnerEndpoints('ssp');
      return partners
        .filter((partner: any) => partner.endpoint_url)
        .map((partner: any) => partner.endpoint_url)
        .slice(0, 10); // Limit to top 10 for warm-up
    } catch (error) {
      logger.cache.warn('Failed to get SSP endpoints for warm-up:', error);
      return [];
    }
  }

  /**
   * Log performance statistics
   */
  private static logPerformanceStats(): void {
    try {
      // Cache statistics
      const cacheStats = HighPerformanceCache.getCacheStats();
      logger.cache.info('📈 Cache Statistics:', {
        memoryEntries: cacheStats.memoryEntries,
        memoryUsage: `${cacheStats.memoryUsagePercent.toFixed(1)}%`,
        maxEntries: cacheStats.memoryMaxEntries,
      });

      // Connection pool statistics
      const connectionStats = HighPerformanceHttpClient.getConnectionStats();
      logger.cache.info('🔗 Connection Pool Statistics:', {
        https: {
          active: connectionStats.https.sockets,
          free: connectionStats.https.freeSockets,
          pending: connectionStats.https.requests,
        },
        http: {
          active: connectionStats.http.sockets,
          free: connectionStats.http.freeSockets,
          pending: connectionStats.http.requests,
        },
      });

      // System readiness
      logger.cache.info('🎯 RTB Platform Ready for High-Performance Auctions!');
      
    } catch (error) {
      logger.cache.warn('Failed to log performance stats:', error);
    }
  }

  /**
   * Graceful shutdown - clean up resources
   */
  static async shutdown(): Promise<void> {
    try {
      logger.cache.info('🛑 Shutting down RTB Platform optimizations...');
      
      // Clear caches
      await HighPerformanceCache.clearAllCaches();
      
      // Close HTTP connections
      HighPerformanceHttpClient.closeAllConnections();
      
      logger.cache.info('✅ RTB Platform shutdown completed');
    } catch (error) {
      logger.cache.error('❌ Shutdown failed:', error);
    }
  }

  /**
   * Health check for optimization systems
   */
  static async healthCheck(): Promise<{
    cache: boolean;
    connections: boolean;
    overall: boolean;
  }> {
    try {
      // Check cache health
      const cacheStats = HighPerformanceCache.getCacheStats();
      const cacheHealthy = cacheStats.memoryEntries >= 0; // Basic check
      
      // Check connection pool health
      const connectionStats = HighPerformanceHttpClient.getConnectionStats();
      const connectionsHealthy = 
        connectionStats.https.sockets >= 0 && 
        connectionStats.http.sockets >= 0;
      
      const overall = cacheHealthy && connectionsHealthy;
      
      return {
        cache: cacheHealthy,
        connections: connectionsHealthy,
        overall,
      };
    } catch (error) {
      logger.cache.error('Health check failed:', error);
      return {
        cache: false,
        connections: false,
        overall: false,
      };
    }
  }

  /**
   * Get optimization metrics for monitoring
   */
  static getMetrics(): {
    cache: any;
    connections: any;
    uptime: number;
  } {
    try {
      return {
        cache: HighPerformanceCache.getCacheStats(),
        connections: HighPerformanceHttpClient.getConnectionStats(),
        uptime: process.uptime(),
      };
    } catch (error) {
      logger.cache.error('Failed to get metrics:', error);
      return {
        cache: {},
        connections: {},
        uptime: 0,
      };
    }
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  StartupOptimizer.shutdown();
});

process.on('SIGINT', () => {
  StartupOptimizer.shutdown();
});

export default StartupOptimizer;
