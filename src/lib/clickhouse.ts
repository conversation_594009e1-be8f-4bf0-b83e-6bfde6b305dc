import { createClient } from '@clickhouse/client';

// Optimized connection pool for high QPS RTB workload
const client = createClient({
  url: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
  username: process.env.CLICKHOUSE_USERNAME || 'default',
  password: process.env.CLICKHOUSE_PASSWORD || 'gamuschdb171288',
  database: process.env.CLICKHOUSE_DATABASE || 'global_ads_media',
  // Optimized connection pooling for 18 PM2 instances
  max_open_connections: 10, // Reduced to 10 connections per instance (18 * 10 = 180 total)
  keep_alive: {
    enabled: true,
    idle_socket_ttl: 30000, // Increased to 30s for better connection reuse
  },
  request_timeout: 3000, // Reduced to 3s for faster failures
  compression: {
    response: false, // Disabled for speed (you have fast local network)
    request: false, // Disabled for speed
  }
});

export default client;

// Database initialization schemas
export const initializeDatabase = async () => {
  try {
    // Create database if not exists
    await client.command({
      query: `CREATE DATABASE IF NOT EXISTS ${process.env.CLICKHOUSE_DATABASE || 'global_ads_media'}`,
    });

    // Users table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS users (
          id UInt32,
          email String,
          password String,
          full_name String,
          address String,
          city String,
          zip String,
          state String,
          country String,
          role Enum8('advertiser' = 1, 'publisher' = 2, 'admin' = 3, 'dsp' = 4, 'ssp' = 5),
          status Enum8('active' = 1, 'inactive' = 2, 'pending' = 3, 'pending_verification' = 4),
          balance Decimal(18, 6) DEFAULT 0,
          email_verified UInt8 DEFAULT 0,
          email_verification_token String DEFAULT '',
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY id
      `,
    });

    // Note: All columns are now included directly in CREATE TABLE statements

    // Campaigns table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS campaigns (
          id UInt32,
          user_id UInt32,
          name String,
          type Enum8('banner' = 1, 'native' = 2, 'in_page_push' = 3, 'popup' = 4),
          status Enum8('active' = 1, 'paused' = 2, 'pending' = 3, 'rejected' = 4, 'archived' = 5),
          cpm_bid Decimal(18, 6),
          daily_budget Decimal(18, 6),
          total_budget Decimal(18, 6),
          daily_spent Decimal(18, 6) DEFAULT 0,
          total_spent Decimal(18, 6) DEFAULT 0,

          -- Creative fields
          banner_size String DEFAULT '',
          creative_type Enum8('image' = 1, 'js' = 2, 'native' = 3, 'push' = 4, 'popup' = 5) DEFAULT 1,
          banner_image_url String DEFAULT '',
          landing_url String DEFAULT '',
          js_tag String DEFAULT '',

          -- Native ad fields
          native_title String DEFAULT '',
          native_description String DEFAULT '',
          native_icon_url String DEFAULT '',
          native_image_url String DEFAULT '',

          -- In-page push fields
          push_title String DEFAULT '',
          push_description String DEFAULT '',
          push_image_url String DEFAULT '',

          -- Targeting fields
          targeting_countries Array(String) DEFAULT [],
          targeting_states Array(String) DEFAULT [],
          targeting_devices Array(String) DEFAULT [],
          targeting_os Array(String) DEFAULT [],
          targeting_browsers Array(String) DEFAULT [],
          targeting_connection_types Array(String) DEFAULT [],

          -- Schedule fields
          start_date DateTime,
          end_date DateTime DEFAULT NULL,
          daily_schedule Array(String) DEFAULT [],
          hourly_schedule Array(UInt8) DEFAULT [],

          -- Frequency cap fields
          frequency_cap_value UInt32 DEFAULT 0,
          frequency_cap_period Enum8('hour' = 1, 'day' = 2, 'week' = 3) DEFAULT 2,

          -- Whitelist/Blacklist fields
          whitelist_publishers String DEFAULT '',
          whitelist_websites String DEFAULT '',
          whitelist_zones String DEFAULT '',
          blacklist_publishers String DEFAULT '',
          blacklist_websites String DEFAULT '',
          blacklist_zones String DEFAULT '',

          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY id
      `,
    });



    // Websites table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS websites (
          id UInt32,
          user_id UInt32,
          name String,
          url String,
          category String,
          status Enum8('pending' = 1, 'approved' = 2, 'rejected' = 3),
          verification_method Enum8('dns' = 1, 'html_file' = 2, 'meta_tag' = 3),
          verification_code String,
          verification_status Enum8('pending' = 1, 'verified' = 2, 'failed' = 3),
          verified_at Nullable(DateTime),
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY id
      `,
    });

    // Ad Zones table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS ad_zones (
          id UInt32,
          website_id UInt32,
          name String,
          ad_format Enum8('banner' = 1, 'native' = 2, 'in_page_push' = 3, 'popup' = 4),
          size String,
          config String DEFAULT '{}',
          status Enum8('active' = 1, 'inactive' = 2),
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY id
      `,
    });

    // Note: All new columns are now included directly in CREATE TABLE statements

    // Impressions table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS impressions (
          id UInt64,
          campaign_id UInt32 DEFAULT 0,
          dsp_partner_id UInt32 DEFAULT 0,
          ssp_partner_id UInt32 DEFAULT 0,
          website_id UInt32,
          zone_id UInt32,
          user_agent String,
          ip_address String,
          country String,
          state String,
          city String,
          device_type String,
          os String,
          browser String,
          cost_deducted Decimal(18,6) DEFAULT 0,
          publisher_revenue Decimal(18,6) DEFAULT 0,
          bid_type Enum8('cpm' = 1, 'cpv' = 2) DEFAULT 1,
          source_type Enum8('local' = 1, 'dsp' = 2, 'ssp_inbound' = 3) DEFAULT 1,
          timestamp DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY timestamp
      `,
    });

    // Clicks table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS clicks (
          id UInt64,
          impression_id UInt64,
          campaign_id UInt32,
          partner_endpoint_id UInt32 DEFAULT 0,
          website_id UInt32,
          zone_id UInt32,
          user_agent String,
          ip_address String,
          country String,
          state String,
          city String,
          device_type String,
          os String,
          browser String,
          source_type Enum8('local' = 1, 'dsp' = 2, 'ssp_inbound' = 3) DEFAULT 1,
          timestamp DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY timestamp
      `,
    });

    // Transactions table (enhanced with ad revenue/spend types)
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS transactions (
          id UInt64,
          user_id UInt32,
          type Enum8('deposit' = 1, 'withdrawal' = 2, 'adjustment' = 3, 'ad_spend' = 4, 'ad_revenue' = 5, 'publisher_earnings' = 6, 'refund' = 7, 'bonus' = 8, 'penalty' = 9),
          amount Decimal(18, 6),
          is_credit UInt8 DEFAULT 1,
          status Enum8('pending' = 1, 'completed' = 2, 'failed' = 3, 'cancelled' = 4),
          reference_id String DEFAULT '',
          reference_type String DEFAULT '',
          payment_method String DEFAULT '',
          payment_reference String DEFAULT '',
          description String DEFAULT '',
          admin_notes String DEFAULT '',
          fee_amount Decimal(18, 6) DEFAULT 0,
          processed_by String DEFAULT '',
          processed_at Nullable(DateTime),
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY created_at
      `,
    });

    // Platform Settings table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS platform_settings (
          id UInt64,
          setting_key String,
          setting_value String,
          description String DEFAULT '',
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY id
      `,
    });

    // Win Notifications table (for detailed win tracking from all sources)
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS win_notifications (
          id UInt64,
          win_type Enum8('publisher_direct' = 1, 'dsp_rtb' = 2, 'ssp_inventory' = 3) DEFAULT 1,
          campaign_id UInt32 DEFAULT 0,
          inventory_id String DEFAULT '',
          winner_id UInt32,
          supplier_id UInt32,
          website_id UInt32 DEFAULT 0,
          zone_id UInt32 DEFAULT 0,
          partner_endpoint_id UInt32 DEFAULT 0,
          win_price Decimal(18,6),
          platform_revenue Decimal(18,6),
          supplier_revenue Decimal(18,6),
          ssp_impression_id String DEFAULT '', -- New field for SSP impression ID
          timestamp DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY timestamp
      `,
    });

    // Add ssp_impression_id column if it doesn't exist (for existing tables)
    await client.command({
      query: `
        ALTER TABLE win_notifications ADD COLUMN IF NOT EXISTS ssp_impression_id String DEFAULT ''
      `,
    });

    // Migrate impressions table: rename partner_endpoint_id to dsp_partner_id and add ssp_partner_id
    try {
      // Add new columns if they don't exist
      await client.command({
        query: `
          ALTER TABLE impressions ADD COLUMN IF NOT EXISTS dsp_partner_id UInt32 DEFAULT 0
        `,
      });

      await client.command({
        query: `
          ALTER TABLE impressions ADD COLUMN IF NOT EXISTS ssp_partner_id UInt32 DEFAULT 0
        `,
      });

      // Copy data from old column to new column (if old column exists)
      try {
        await client.command({
          query: `
            ALTER TABLE impressions UPDATE dsp_partner_id = partner_endpoint_id WHERE partner_endpoint_id > 0
          `,
        });
      } catch (error) {
        // Column might not exist in new installations, that's fine
        console.log('Note: partner_endpoint_id column not found (expected for new installations)');
      }

      // Drop old column if it exists (only after data migration)
      try {
        await client.command({
          query: `
            ALTER TABLE impressions DROP COLUMN IF EXISTS partner_endpoint_id
          `,
        });
      } catch (error) {
        // Column might not exist, that's fine
        console.log('Note: partner_endpoint_id column already removed or never existed');
      }
    } catch (error) {
      console.error('Error migrating impressions table:', error);
      // Don't throw - let the app continue even if migration fails
    }

    // Health Monitoring Tables
    console.log('Creating health monitoring tables...');

    // System Health Metrics Table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS system_health_metrics (
          id UInt64,
          timestamp DateTime DEFAULT now(),
          server_id String DEFAULT 'rtb-server-1',

          -- CPU Metrics
          cpu_usage_percent Float32,
          cpu_load_1min Float32,
          cpu_load_5min Float32,
          cpu_load_15min Float32,
          cpu_cores UInt8,
          cpu_temperature Float32 DEFAULT 0,

          -- Memory Metrics
          memory_total_gb Float32,
          memory_used_gb Float32,
          memory_free_gb Float32,
          memory_usage_percent Float32,
          memory_available_gb Float32,
          swap_total_gb Float32,
          swap_used_gb Float32,

          -- Disk Metrics
          disk_total_gb Float32,
          disk_used_gb Float32,
          disk_free_gb Float32,
          disk_usage_percent Float32,
          disk_io_read_mb Float32,
          disk_io_write_mb Float32,
          disk_iops_read UInt32,
          disk_iops_write UInt32,

          -- Network Metrics
          network_rx_mb Float32,
          network_tx_mb Float32,
          network_connections_active UInt32,
          network_connections_total UInt32,

          -- Process Metrics
          pm2_instances UInt8,
          pm2_memory_usage_mb Float32,
          pm2_cpu_usage_percent Float32,
          pm2_uptime_seconds UInt32,
          pm2_restarts UInt16,

          -- Application Metrics
          app_version String,
          node_version String,
          npm_version String,

          -- Health Status
          overall_health Enum8('healthy' = 1, 'warning' = 2, 'critical' = 3, 'down' = 4),
          alerts_count UInt16 DEFAULT 0,

          INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
          INDEX idx_server_id server_id TYPE bloom_filter GRANULARITY 1
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(timestamp)
        ORDER BY (timestamp, server_id)
        TTL timestamp + INTERVAL 30 DAY
      `,
    });

    // Application Health Metrics Table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS app_health_metrics (
          id UInt64,
          timestamp DateTime DEFAULT now(),
          server_id String DEFAULT 'rtb-server-1',

          -- RTB Performance Metrics
          qps_current UInt32,
          qps_peak UInt32,
          qps_average UInt32,
          response_time_avg_ms Float32,
          response_time_p95_ms Float32,
          response_time_p99_ms Float32,

          -- Queue Metrics
          queue_impressions_waiting UInt32,
          queue_impressions_active UInt32,
          queue_impressions_completed UInt32,
          queue_impressions_failed UInt32,
          queue_cost_waiting UInt32,
          queue_cost_active UInt32,
          queue_cost_completed UInt32,
          queue_cost_failed UInt32,
          queue_stats_waiting UInt32,
          queue_stats_active UInt32,
          queue_stats_completed UInt32,
          queue_stats_failed UInt32,

          -- Database Metrics
          clickhouse_connections UInt16,
          clickhouse_queries_per_sec UInt32,
          clickhouse_slow_queries UInt16,
          clickhouse_errors UInt16,
          redis_connections UInt16,
          redis_memory_usage_mb Float32,
          redis_ops_per_sec UInt32,
          redis_hit_rate_percent Float32,

          -- Business Metrics
          impressions_per_minute UInt32,
          revenue_per_minute Float32,
          cost_per_minute Float32,
          profit_per_minute Float32,
          active_campaigns UInt16,
          active_dsps UInt8,
          active_ssps UInt8,

          -- Error Metrics
          error_rate_percent Float32,
          timeout_rate_percent Float32,
          fallback_mode Boolean DEFAULT false,
          fallback_jobs_pending UInt32,

          INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
          INDEX idx_server_id server_id TYPE bloom_filter GRANULARITY 1
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(timestamp)
        ORDER BY (timestamp, server_id)
        TTL timestamp + INTERVAL 30 DAY
      `,
    });

    // Service Health Status Table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS service_health_status (
          id UInt64,
          timestamp DateTime DEFAULT now(),
          server_id String DEFAULT 'rtb-server-1',

          -- Service Status
          service_name String,
          service_status Enum8('up' = 1, 'down' = 2, 'degraded' = 3, 'maintenance' = 4),
          response_time_ms Float32,
          last_check DateTime,
          uptime_seconds UInt32,

          -- Service Details
          version String DEFAULT '',
          port UInt16 DEFAULT 0,
          endpoint String DEFAULT '',
          health_check_url String DEFAULT '',

          -- Dependencies
          dependencies_healthy Boolean DEFAULT true,
          dependency_count UInt8 DEFAULT 0,

          INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
          INDEX idx_service service_name TYPE bloom_filter GRANULARITY 1
        ) ENGINE = ReplacingMergeTree()
        PARTITION BY toYYYYMM(timestamp)
        ORDER BY (service_name, server_id, timestamp)
        TTL timestamp + INTERVAL 7 DAY
      `,
    });

    // Health Alerts Table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS health_alerts (
          id UInt64,
          timestamp DateTime DEFAULT now(),
          server_id String DEFAULT 'rtb-server-1',

          -- Alert Details
          alert_type Enum8('info' = 1, 'warning' = 2, 'error' = 3, 'critical' = 4),
          alert_category String, -- 'system', 'application', 'business', 'security'
          alert_title String,
          alert_message String,
          alert_source String, -- 'cpu', 'memory', 'disk', 'network', 'queue', 'database'

          -- Alert Status
          status Enum8('active' = 1, 'acknowledged' = 2, 'resolved' = 3, 'suppressed' = 4),
          severity UInt8, -- 1-10 scale

          -- Metrics
          metric_name String DEFAULT '',
          metric_value Float32 DEFAULT 0,
          threshold_value Float32 DEFAULT 0,

          -- Resolution
          resolved_at DateTime DEFAULT '1970-01-01 00:00:00',
          resolved_by String DEFAULT '',
          resolution_notes String DEFAULT '',

          INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
          INDEX idx_status status TYPE bloom_filter GRANULARITY 1,
          INDEX idx_alert_type alert_type TYPE bloom_filter GRANULARITY 1
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(timestamp)
        ORDER BY (timestamp, alert_type, status)
        TTL timestamp + INTERVAL 90 DAY
      `,
    });

    console.log('✅ Health monitoring tables created successfully');

    // API Keys Table for DSP/SSP Stats API
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS api_keys (
          id UInt64,
          api_key String,
          api_secret String,
          user_id UInt32,
          partner_id UInt32 DEFAULT 0,
          key_name String,
          key_type Enum8('dsp' = 1, 'ssp' = 2, 'admin' = 3),
          permissions Array(String), -- ['stats:read', 'campaigns:read', 'reports:read']

          -- Rate limiting
          rate_limit_per_hour UInt32 DEFAULT 1000,
          rate_limit_per_day UInt32 DEFAULT 10000,

          -- Usage tracking
          total_requests UInt64 DEFAULT 0,
          last_used_at DateTime DEFAULT '1970-01-01 00:00:00',

          -- IP restrictions
          allowed_ips Array(String), -- Empty array means no restrictions
          allowed_domains Array(String), -- Empty array means no restrictions

          -- Status and metadata
          status Enum8('active' = 1, 'suspended' = 2, 'revoked' = 3),
          expires_at DateTime DEFAULT '2099-12-31 23:59:59',
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now(),

          -- Additional metadata
          description String DEFAULT '',
          last_ip String DEFAULT '',
          user_agent String DEFAULT '',

          INDEX idx_api_key api_key TYPE bloom_filter GRANULARITY 1,
          INDEX idx_user_id user_id TYPE bloom_filter GRANULARITY 1,
          INDEX idx_partner_id partner_id TYPE bloom_filter GRANULARITY 1,
          INDEX idx_status status TYPE bloom_filter GRANULARITY 1
        ) ENGINE = ReplacingMergeTree()
        PARTITION BY toYYYYMM(created_at)
        ORDER BY (api_key, user_id, created_at)
        TTL expires_at + INTERVAL 30 DAY
      `,
    });

    // API Usage Logs Table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS api_usage_logs (
          id UInt64,
          api_key String,
          user_id UInt32,
          partner_id UInt32 DEFAULT 0,

          -- Request details
          endpoint String,
          method Enum8('GET' = 1, 'POST' = 2, 'PUT' = 3, 'DELETE' = 4),
          request_path String,
          query_params String DEFAULT '',

          -- Response details
          status_code UInt16,
          response_time_ms UInt32,
          response_size_bytes UInt32 DEFAULT 0,

          -- Client information
          ip_address String,
          user_agent String DEFAULT '',
          referer String DEFAULT '',

          -- Timestamps
          timestamp DateTime DEFAULT now(),
          date Date DEFAULT today(),

          -- Error tracking
          error_message String DEFAULT '',
          error_code String DEFAULT '',

          INDEX idx_api_key api_key TYPE bloom_filter GRANULARITY 1,
          INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
          INDEX idx_endpoint endpoint TYPE bloom_filter GRANULARITY 1,
          INDEX idx_status_code status_code TYPE bloom_filter GRANULARITY 1
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(timestamp)
        ORDER BY (timestamp, api_key, endpoint)
        TTL timestamp + INTERVAL 90 DAY
      `,
    });

    console.log('✅ API keys and usage tracking tables created successfully');

    // Admin Actions table for audit trail
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS admin_actions (
          id UInt64,
          admin_id UInt32,
          action_type String,
          target_id UInt32,
          target_type String,
          reason String,
          timestamp DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (timestamp, admin_id)
      `,
    });

    // Password Reset Tokens table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS password_reset_tokens (
          id UInt64,
          user_id UInt32,
          token String,
          expires_at DateTime,
          created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (expires_at, user_id)
      `,
    });

    // Conversions table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS conversions (
          id UInt64,
          click_id UInt64,
          campaign_id UInt32,
          conversion_value Decimal(10,2) DEFAULT 0,
          conversion_type String DEFAULT 'sale',
          timestamp DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (timestamp, campaign_id)
      `,
    });

    // Postback Configurations table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS postback_configs (
          id UInt64,
          campaign_id UInt32,
          postback_url String,
          method Enum8('GET' = 1, 'POST' = 2),
          parameters String DEFAULT '{}',
          is_active UInt8 DEFAULT 1,
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (campaign_id, id)
      `,
    });

    // Partner Endpoints table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS partner_endpoints (
          id UInt32,
          name String,
          type String,
          user_id UInt32 DEFAULT 0,
          endpoint_url String,
          status String DEFAULT 'active',
          targeting String DEFAULT '{}',
          protocol String DEFAULT 'openrtb',
          openrtb_version String DEFAULT '2.5',
          api_key String DEFAULT '',
          timeout_ms UInt32 DEFAULT 5000,
          qps_limit UInt32 DEFAULT 100,
          revenue_share Decimal(5,2) DEFAULT 0,
          seat_id String DEFAULT '',
          test_mode UInt8 DEFAULT 0,
          auth_type String DEFAULT 'none',
          auth_credentials String DEFAULT '{}',
          auction_type Enum('first_price', 'second_price') DEFAULT 'first_price',
          bid_price_format Enum('cpm', 'cpv', 'cpc', 'ecpm') DEFAULT 'cpm',
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (id)
      `,
    });

    // Note: Transactions table already defined above with enhanced schema

    // Payout Methods table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS payout_methods (
          id UInt64,
          user_id UInt32,
          type String,
          details String,
          is_default UInt8 DEFAULT 0,
          status String DEFAULT 'active',
          created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (user_id, created_at)
      `,
    });

    // Payout Requests table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS payout_requests (
          id UInt64,
          user_id UInt32,
          method_id UInt64,
          amount Decimal(10,2),
          status String DEFAULT 'pending',
          requested_at DateTime DEFAULT now(),
          processed_at DateTime DEFAULT NULL
        ) ENGINE = MergeTree()
        ORDER BY (requested_at, user_id)
      `,
    });

    // Support Tickets table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS support_tickets (
          id UInt64,
          user_id UInt32,
          subject String,
          message String,
          priority String DEFAULT 'medium',
          status String DEFAULT 'open',
          category String DEFAULT 'general',
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (created_at, user_id)
      `,
    });

    // Support Ticket Replies table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS support_ticket_replies (
          id UInt64,
          ticket_id UInt64,
          user_id UInt32,
          message String,
          is_admin UInt8 DEFAULT 0,
          created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (created_at, ticket_id)
      `,
    });

    // Email Settings table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS email_settings (
          id UInt64,
          host String,
          port UInt16,
          username String,
          password String,
          security String,
          from_email String,
          from_name String,
          created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (id)
      `,
    });

    // Email Templates table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS email_templates (
          id UInt64,
          name String,
          type String,
          subject String,
          body String,
          variables String,
          event String DEFAULT '',
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (type, id)
      `,
    });

    // Website Verification Logs table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS website_verification_logs (
          id UInt64,
          website_id UInt32,
          user_id UInt32,
          method String,
          success UInt8,
          details String,
          attempted_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (attempted_at, website_id)
      `,
    });

    // Ad Monitoring table with full geographic information
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS ad_monitoring (
          id UInt64,
          campaign_id UInt32,
          country String DEFAULT '',
          state String DEFAULT '',
          city String DEFAULT '',
          status String DEFAULT 'pending',
          issues_found String DEFAULT '',
          screenshot_url String DEFAULT '',
          landing_url String DEFAULT '',
          checked_at DateTime DEFAULT now(),
          created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (created_at, campaign_id)
      `,
    });

    // DSP/SSP Configuration table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS dsp_ssp_configs (
          id UInt64,
          user_id UInt32,
          name String,
          type Enum8('dsp' = 1, 'ssp' = 2),
          bid_type Enum8('cpm' = 1, 'cpv' = 2) DEFAULT 1,
          endpoint_url String,
          api_key String DEFAULT '',
          revenue_share Decimal(5,2) DEFAULT 0.00,
          status Enum8('active' = 1, 'inactive' = 2) DEFAULT 1,
          created_at DateTime DEFAULT now(),
          updated_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (user_id, id)
      `,
    });

    // SSP Inventory table for temporary storage of external SSP inventory
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS ssp_inventory (
          id UInt64,
          ssp_ UInt32,
          ssp_inventory_id String,
          format Enum8('banner' = 1, 'native' = 2, 'video' = 3, 'popup' = 4),
          width UInt16 DEFAULT 0,
          height UInt16 DEFAULT 0,
          floor_price Decimal(18,6),
          currency String DEFAULT 'USD',
          targeting String DEFAULT '{}',
          available_until DateTime,
          publisher_id String DEFAULT '',
          zone_id String DEFAULT '',
          status Enum8('available' = 1, 'sold' = 2, 'expired' = 3) DEFAULT 1,
          created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (available_until, ssp_)
        TTL available_until + INTERVAL 1 HOUR DELETE
      `,
    });

    // Unified Request Stats table for all traffic sources (space-efficient)
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS request_stats (
          id UInt64,
          date Date,
          hour UInt8,
          source_type Enum8(
            'publisher_incoming' = 1, 'publisher_outgoing' = 2,
            'advertiser_incoming' = 3, 'advertiser_outgoing' = 4,
            'ssp_incoming' = 5, 'ssp_outgoing' = 6,
            'dsp_incoming' = 7, 'dsp_outgoing' = 8
          ),
          partner_endpoint_id UInt32 DEFAULT 0,
          campaign_id UInt32 DEFAULT 0,
          website_id UInt32 DEFAULT 0,
          dsp_id UInt32 DEFAULT 0,
          ssp_id UInt32 DEFAULT 0,
          advertiser_id UInt32 DEFAULT 0,
          publisher_id UInt32 DEFAULT 0,
          total_requests UInt64 DEFAULT 0,
          total_wins UInt64 DEFAULT 0,
          timestamp DateTime DEFAULT now()
        ) ENGINE = SummingMergeTree()
        ORDER BY (date, hour, source_type, partner_endpoint_id, campaign_id, website_id)
      `,
    });

    // Note: Summary tables removed - using materialized views directly instead
    // This eliminates redundancy and simplifies the aggregation system

    // Fraud Monitoring Logs table with 30-day TTL
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS fraud_monitoring_logs (
          id UInt64,
          timestamp DateTime,
          ip_address String,
          user_agent String,
          source_type Enum8('publisher_direct' = 1, 'dsp_inbound' = 2, 'ssp_inbound' = 3),
          publisher_id UInt32 DEFAULT 0,
          website_id UInt32 DEFAULT 0,
          zone_id UInt32 DEFAULT 0,
          partner_id UInt32 DEFAULT 0,
          fraud_reason String,
          risk_score UInt8,
          country String DEFAULT '',
          device_type String DEFAULT '',
          is_excluded UInt8 DEFAULT 0,
          excluded_by UInt32 DEFAULT 0,
          excluded_at DateTime DEFAULT NULL
        ) ENGINE = MergeTree()
        ORDER BY (timestamp, source_type, publisher_id)
        TTL timestamp + INTERVAL 30 DAY DELETE
      `,
    });

    // Note: Fraud monitoring materialized view will be created by cleanup script
    // Using consistent _mv naming convention

    // Publisher Fraud Exclusions table
    await client.command({
      query: `
        CREATE TABLE IF NOT EXISTS publisher_fraud_exclusions (
          id UInt64,
          publisher_id UInt32,
          excluded_by UInt32,
          excluded_at DateTime,
          is_active UInt8 DEFAULT 1,
          reason String DEFAULT 'Manual exclusion'
        ) ENGINE = MergeTree()
        ORDER BY (publisher_id, excluded_at)
      `,
    });

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};
