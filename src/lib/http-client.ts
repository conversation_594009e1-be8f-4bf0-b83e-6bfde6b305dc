import https from 'https';
import http from 'http';
import * as zlib from 'zlib';
import { URL } from 'url';

/**
 * High-Performance HTTP Client with Connection Pooling
 * Optimized for RTB DSP/SSP requests with keep-alive connections
 */

// Global HTTP agents with connection pooling
const httpsAgent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 30000,     // 30 seconds keep-alive
  maxSockets: 50,            // Max 50 concurrent connections per host
  maxFreeSockets: 10,        // Keep 10 free sockets per host
  timeout: 60000,            // 60 seconds socket timeout
  freeSocketTimeout: 30000,  // 30 seconds free socket timeout
  scheduling: 'fifo',        // First-in-first-out scheduling
});

const httpAgent = new http.Agent({
  keepAlive: true,
  keepAliveMsecs: 30000,
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 60000,
  freeSocketTimeout: 30000,
  scheduling: 'fifo',
});

export interface HttpRequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: { [key: string]: string };
  body?: string | Buffer;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export interface HttpResponse {
  status: number;
  statusText: string;
  headers: { [key: string]: string };
  body: string;
  responseTime: number;
}

export class HighPerformanceHttpClient {
  /**
   * Make HTTP request with connection pooling and keep-alive
   */
  static async request(options: HttpRequestOptions): Promise<HttpResponse> {
    const startTime = Date.now();
    const url = new URL(options.url);
    const isHttps = url.protocol === 'https:';
    
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'RTB-Platform/1.0',
        'Connection': 'keep-alive',
        'Accept-Encoding': 'gzip, deflate',
        ...options.headers,
      },
      agent: isHttps ? httpsAgent : httpAgent,
      timeout: options.timeout || 5000, // 5 second default timeout for RTB
    };

    // Add Content-Length for POST requests
    if (options.body && (options.method === 'POST' || options.method === 'PUT')) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(options.body);
      if (!requestOptions.headers['Content-Type']) {
        requestOptions.headers['Content-Type'] = 'application/json';
      }
    }

    return new Promise((resolve, reject) => {
      const client = isHttps ? https : http;
      
      const req = client.request(requestOptions, (res) => {
        let responseStream = res;

        // Handle compressed responses
        const encoding = res.headers['content-encoding'];
        if (encoding === 'gzip') {
          responseStream = res.pipe(zlib.createGunzip());
        } else if (encoding === 'deflate') {
          responseStream = res.pipe(zlib.createInflate());
        }

        let body = '';

        responseStream.on('data', (chunk) => {
          body += chunk;
        });

        responseStream.on('end', () => {
          const responseTime = Date.now() - startTime;

          resolve({
            status: res.statusCode || 0,
            statusText: res.statusMessage || '',
            headers: res.headers as { [key: string]: string },
            body,
            responseTime,
          });
        });

        responseStream.on('error', (error) => {
          reject(new Error(`Response decompression failed: ${error.message}`));
        });
      });

      req.on('error', (error) => {
        reject(new Error(`HTTP request failed: ${error.message}`));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error(`HTTP request timeout after ${options.timeout || 5000}ms`));
      });

      // Write body for POST/PUT requests
      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }

  /**
   * Make request with automatic retries
   */
  static async requestWithRetry(options: HttpRequestOptions): Promise<HttpResponse> {
    const maxRetries = options.retries || 2;
    const retryDelay = options.retryDelay || 100; // 100ms delay between retries
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.request(options);
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }
        
        // Wait before retrying (exponential backoff)
        const delay = retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError || new Error('Request failed after retries');
  }

  /**
   * Make multiple parallel requests with connection reuse
   */
  static async requestBatch(requests: HttpRequestOptions[]): Promise<HttpResponse[]> {
    const promises = requests.map(options => 
      this.requestWithRetry(options).catch(error => ({
        status: 0,
        statusText: 'Error',
        headers: {},
        body: error.message,
        responseTime: 0,
      }))
    );
    
    return Promise.all(promises);
  }

  /**
   * Optimized DSP bid request
   */
  static async sendDspBidRequest(
    endpoint: string,
    bidRequest: any,
    timeoutMs: number = 100
  ): Promise<HttpResponse> {
    return this.requestWithRetry({
      url: endpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-RTB-Version': '2.5',
        'Accept': 'application/json',
      },
      body: JSON.stringify(bidRequest),
      timeout: timeoutMs,
      retries: 1, // Single retry for DSP requests
      retryDelay: 10, // Very fast retry
    });
  }

  /**
   * Optimized SSP win notification
   */
  static async sendSspWinNotification(
    endpoint: string,
    winNotification: any,
    timeoutMs: number = 200
  ): Promise<HttpResponse> {
    return this.requestWithRetry({
      url: endpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-RTB-Version': '2.5',
      },
      body: JSON.stringify(winNotification),
      timeout: timeoutMs,
      retries: 2, // More retries for win notifications
      retryDelay: 50,
    });
  }

  /**
   * Get connection pool statistics
   */
  static getConnectionStats(): {
    https: {
      sockets: number;
      freeSockets: number;
      requests: number;
    };
    http: {
      sockets: number;
      freeSockets: number;
      requests: number;
    };
  } {
    return {
      https: {
        sockets: Object.keys(httpsAgent.sockets).length,
        freeSockets: Object.keys(httpsAgent.freeSockets).length,
        requests: Object.keys(httpsAgent.requests).length,
      },
      http: {
        sockets: Object.keys(httpAgent.sockets).length,
        freeSockets: Object.keys(httpAgent.freeSockets).length,
        requests: Object.keys(httpAgent.requests).length,
      },
    };
  }

  /**
   * Warm up connections to frequently used endpoints
   */
  static async warmUpConnections(endpoints: string[]): Promise<void> {
    const warmUpRequests = endpoints.map(endpoint => ({
      url: endpoint,
      method: 'HEAD' as const,
      timeout: 1000,
      retries: 0,
    }));

    try {
      await this.requestBatch(warmUpRequests);
    } catch (error) {
      // Ignore warm-up errors
    }
  }

  /**
   * Close all connections (for graceful shutdown)
   */
  static closeAllConnections(): void {
    httpsAgent.destroy();
    httpAgent.destroy();
  }
}

// Graceful shutdown handling
process.on('SIGTERM', () => {
  HighPerformanceHttpClient.closeAllConnections();
});

process.on('SIGINT', () => {
  HighPerformanceHttpClient.closeAllConnections();
});

export default HighPerformanceHttpClient;
