import nodemailer from 'nodemailer';
import clickhouse from './clickhouse';

export class EmailService {
  private static async getEmailSettings() {
    const emailSettingsResult = await clickhouse.query({
      query: 'SELECT * FROM email_settings ORDER BY id DESC LIMIT 1',
    });

    const emailSettings = await emailSettingsResult.json();
    if (emailSettings.data.length === 0) {
      throw new Error('Email settings not configured');
    }

    return emailSettings.data[0];
  }

  private static async createTransporter() {
    const settings = await this.getEmailSettings();

    return nodemailer.createTransport({
      host: settings.host,
      port: settings.port,
      secure: settings.security === 'ssl',
      auth: {
        user: settings.username,
        pass: settings.password,
      },
    });
  }

  private static async getEmailTemplate(type: string) {
    try {
      const templateResult = await clickhouse.query({
        query: 'SELECT * FROM email_templates WHERE type = {type:String} LIMIT 1',
        query_params: { type },
      });

      const templateData = await templateResult.json();
      return templateData.data.length > 0 ? templateData.data[0] : null;
    } catch (error) {
      console.error('Error fetching email template:', error);
      return null;
    }
  }

  private static replacePlaceholders(text: string, variables: any) {
    let processedText = text;

    // Replace all placeholders
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      processedText = processedText.replace(new RegExp(placeholder, 'g'), value);
    });

    return processedText;
  }

  private static generateEmailHtml(variables: any) {
    const baseUrl = process.env.PLATFORM_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${variables.email_type || 'Platform Notification'}</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header -->
          <div style="background: #111827; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <img src="${baseUrl}/logo-white.png" alt="Global Ads Media" style="height: 40px; margin-bottom: 10px; display: block; margin-left: auto; margin-right: auto;" />
            <p style="color: #9CA3AF; margin: 10px 0 0 0; font-size: 14px;">${variables.email_type || 'Platform Notification'}</p>
          </div>

          <div style="padding: 20px; background: white; border-radius: 8px; border: 1px solid #e9ecef;">
            ${variables.content || ''}

            ${variables.action_button && variables.action_url ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${variables.action_url}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 500;">${variables.action_button}</a>
              </div>
            ` : ''}
          </div>

          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #e9ecef;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
              © ${new Date().getFullYear()} Global Ads Media. All rights reserved.
            </p>
            <p style="margin: 10px 0 0 0; color: #6c757d; font-size: 12px;">
              This email was sent to ${variables.user_email || 'your email address'}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }



  private static async sendEmail(to: string, subject: string, htmlContent: string) {
    const transporter = await this.createTransporter();
    const settings = await this.getEmailSettings();

    await transporter.sendMail({
      from: `"${settings.from_name}" <${settings.from_email}>`,
      to,
      subject,
      html: htmlContent,
    });
  }



  // Email verification
  static async sendVerificationEmail(user: any) {
    try {
      const template = await this.getEmailTemplate('email_verification');
      if (!template) {
        console.error('Email verification template not found');
        return;
      }

      const baseUrl = process.env.PLATFORM_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const verificationUrl = `${baseUrl}/api/auth/verify-email?token=${user.email_verification_token}`;

      const variables = {
        user_name: user.full_name,
        user_email: user.email,
        platform_name: 'Global Ads Media',
        verification_url: verificationUrl
      };

      const processedSubject = this.replacePlaceholders(template.subject, variables);
      const processedBody = this.replacePlaceholders(template.body, variables);

      const htmlContent = this.generateEmailHtml({
        user_name: user.full_name,
        user_email: user.email,
        email_type: 'Email Verification',
        content: processedBody,
        action_button: 'Verify Email Address',
        action_url: verificationUrl
      });

      await this.sendEmail(user.email, processedSubject, htmlContent);
    } catch (error) {
      console.error('Error sending verification email:', error);
    }
  }

  // Welcome emails
  static async sendWelcomeEmail(user: any) {
    try {
      const templateType = user.role === 'advertiser' ? 'welcome_advertiser' : 'welcome_publisher';
      const template = await this.getEmailTemplate(templateType);

      if (!template) {
        console.error(`Welcome template not found for role: ${user.role}`);
        return;
      }

      const baseUrl = process.env.PLATFORM_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const dashboardUrl = `${baseUrl}/${user.role}/dashboard`;

      const variables = {
        user_name: user.full_name,
        user_email: user.email,
        platform_name: 'Global Ads Media',
        dashboard_url: dashboardUrl,
        current_date: new Date().toLocaleDateString()
      };

      const processedSubject = this.replacePlaceholders(template.subject, variables);
      const processedBody = this.replacePlaceholders(template.body, variables);

      const htmlContent = this.generateEmailHtml({
        user_name: user.full_name,
        user_email: user.email,
        email_type: 'Welcome to Platform',
        content: processedBody,
        action_button: 'Go to Dashboard',
        action_url: dashboardUrl
      });

      await this.sendEmail(user.email, processedSubject, htmlContent);
    } catch (error) {
      console.error('Error sending welcome email:', error);
    }
  }

  // DSP/SSP account creation
  static async sendDspSspAccountEmail(user: any, password: string) {
    try {
      const template = await this.getEmailTemplate('account_created');

      if (!template) {
        console.error('Account created template not found');
        return;
      }

      const baseUrl = process.env.PLATFORM_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const loginUrl = `${baseUrl}/auth/signin`;

      const variables = {
        user_name: user.full_name,
        user_email: user.email,
        user_role: user.role.toUpperCase(),
        password: password,
        login_url: loginUrl,
        platform_name: 'Global Ads Media'
      };

      const processedSubject = this.replacePlaceholders(template.subject, variables);
      const processedBody = this.replacePlaceholders(template.body, variables);

      const htmlContent = this.generateEmailHtml({
        user_name: user.full_name,
        user_email: user.email,
        email_type: 'Account Created',
        content: processedBody,
        action_button: 'Login to Platform',
        action_url: loginUrl
      });

      await this.sendEmail(user.email, processedSubject, htmlContent);
    } catch (error) {
      console.error('Error sending DSP/SSP account email:', error);
    }
  }

  // Event notifications
  static async sendEventNotification(user: any, eventType: string, eventData: any) {
    try {
      const template = await this.getEmailTemplate(eventType);

      if (!template) {
        console.error(`Event template not found: ${eventType}`);
        return;
      }

      const baseUrl = process.env.PLATFORM_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';

      // Prepare variables based on event type
      const variables: any = {
        user_name: user.full_name,
        user_email: user.email,
        platform_name: 'Global Ads Media',
        ...eventData
      };

      // Add action URLs based on event type
      const actionUrls: any = {
        campaign_approved: `${baseUrl}/advertiser/campaigns`,
        campaign_rejected: `${baseUrl}/advertiser/campaigns`,
        website_approved: `${baseUrl}/publisher/websites`,
        website_rejected: `${baseUrl}/publisher/websites`,
        funds_deposited: `${baseUrl}/advertiser/billing`,
        payout_processed: `${baseUrl}/publisher/payouts`,
        funds_withdrawal: `${baseUrl}/advertiser/billing`
      };

      const actionButtons: any = {
        campaign_approved: 'View Campaign',
        campaign_rejected: 'Edit Campaign',
        website_approved: 'Create Ad Zones',
        website_rejected: 'Edit Website',
        funds_deposited: 'View Balance',
        payout_processed: 'View Payouts',
        funds_withdrawal: 'View Transactions'
      };

      const processedSubject = this.replacePlaceholders(template.subject, variables);
      const processedBody = this.replacePlaceholders(template.body, variables);

      const htmlContent = this.generateEmailHtml({
        user_name: user.full_name,
        user_email: user.email,
        email_type: 'Account Notification',
        content: processedBody,
        action_button: actionButtons[eventType],
        action_url: actionUrls[eventType]
      });

      await this.sendEmail(user.email, processedSubject, htmlContent);
    } catch (error) {
      console.error('Error sending event notification:', error);
    }
  }

  // Helper function to send notifications when campaigns are approved/rejected
  static async sendCampaignStatusNotification(campaignId: number, status: string, reason?: string) {
    try {
      // Get campaign and user details
      const campaignResult = await clickhouse.query({
        query: `
          SELECT c.name as campaign_name, u.id, u.email, u.full_name
          FROM campaigns c
          JOIN users u ON c.user_id = u.id
          WHERE c.id = {campaignId:UInt32}
        `,
        query_params: { campaignId },
      });

      const campaignData = await campaignResult.json();
      if (campaignData.data.length === 0) return;

      const data = campaignData.data[0];
      const eventType = status === 'active' ? 'campaign_approved' : 'campaign_rejected';

      await this.sendEventNotification(
        { id: data.id, email: data.email, full_name: data.full_name },
        eventType,
        { campaign_name: data.campaign_name, reason }
      );
    } catch (error) {
      console.error('Error sending campaign status notification:', error);
    }
  }

  // Helper function to send notifications when websites are approved/rejected
  static async sendWebsiteStatusNotification(websiteId: number, status: string, reason?: string) {
    try {
      // Get website and user details
      const websiteResult = await clickhouse.query({
        query: `
          SELECT w.name as website_name, u.id, u.email, u.full_name
          FROM websites w
          JOIN users u ON w.user_id = u.id
          WHERE w.id = {websiteId:UInt32}
        `,
        query_params: { websiteId },
      });

      const websiteData = await websiteResult.json();
      if (websiteData.data.length === 0) return;

      const data = websiteData.data[0];
      const eventType = status === 'approved' ? 'website_approved' : 'website_rejected';

      await this.sendEventNotification(
        { id: data.id, email: data.email, full_name: data.full_name },
        eventType,
        { website_name: data.website_name, reason }
      );
    } catch (error) {
      console.error('Error sending website status notification:', error);
    }
  }

  // Helper function to send fund deposit notifications
  static async sendFundDepositNotification(userId: number, amount: number) {
    try {
      // Get user details
      const userResult = await clickhouse.query({
        query: 'SELECT id, email, full_name FROM users WHERE id = {userId:UInt32}',
        query_params: { userId },
      });

      const userData = await userResult.json();
      if (userData.data.length === 0) return;

      const user = userData.data[0];

      await this.sendEventNotification(
        user,
        'funds_deposited',
        { amount: amount.toFixed(2) }
      );
    } catch (error) {
      console.error('Error sending fund deposit notification:', error);
    }
  }

  // Helper function to send payout notifications
  static async sendPayoutNotification(userId: number, amount: number) {
    try {
      // Get user details
      const userResult = await clickhouse.query({
        query: 'SELECT id, email, full_name FROM users WHERE id = {userId:UInt32}',
        query_params: { userId },
      });

      const userData = await userResult.json();
      if (userData.data.length === 0) return;

      const user = userData.data[0];

      await this.sendEventNotification(
        user,
        'payout_processed',
        { amount: amount.toFixed(2) }
      );
    } catch (error) {
      console.error('Error sending payout notification:', error);
    }
  }
}
