import { RabbitMQManager } from './queue-manager';
import FallbackManager from './fallback-manager';
import logger from './logger';
import { cache } from './cache';

interface HealthMetrics {
  timestamp: number;
  queueHealth: boolean;
  redisHealth: boolean;
  processingRates: {
    impressions: number;
    costs: number;
    requests: number;
  };
  errorRates: {
    impressions: number;
    costs: number;
    requests: number;
  };
  fallbackStatus: {
    bufferedJobs: number;
    fallbackFiles: number;
    processingFallback: boolean;
  };
}

export class QueueHealthMonitor {
  private static metrics: HealthMetrics[] = [];
  private static alertThresholds = {
    maxErrorRate: 0.1,        // 10% error rate
    maxQueueBacklog: 1000,    // 1000 jobs waiting
    maxResponseTime: 5000,    // 5 seconds
    maxFallbackFiles: 10,     // 10 fallback files
  };

  /**
   * Collect comprehensive health metrics
   */
  static async collectMetrics(): Promise<HealthMetrics> {
    try {
      // Get queue statistics
      const queueStats = await RabbitMQManager.getQueueStats();
      
      // Get fallback status
      const fallbackStatus = await FallbackManager.getStatus();
      
      // Check Redis health
      const redisHealth = await this.checkRedisHealth();
      
      // Calculate processing rates (jobs per minute)
      const processingRates = {
        impressions: this.calculateRate(queueStats, 'impression-tracking'),
        costs: this.calculateRate(queueStats, 'cost-processing'),
        requests: this.calculateRate(queueStats, 'request-stats'),
      };

      // Calculate error rates
      const errorRates = {
        impressions: this.calculateErrorRate(queueStats, 'impression-tracking'),
        costs: this.calculateErrorRate(queueStats, 'cost-processing'),
        requests: this.calculateErrorRate(queueStats, 'request-stats'),
      };

      const metrics: HealthMetrics = {
        timestamp: Date.now(),
        queueHealth: fallbackStatus.queueHealthy,
        redisHealth,
        processingRates,
        errorRates,
        fallbackStatus: {
          bufferedJobs: fallbackStatus.bufferedJobs,
          fallbackFiles: fallbackStatus.fallbackFiles,
          processingFallback: fallbackStatus.processingFallback,
        },
      };

      // Store metrics (keep last 100 entries)
      this.metrics.push(metrics);
      if (this.metrics.length > 100) {
        this.metrics.shift();
      }

      // Cache metrics for API access
      await cache.set('queue_health_metrics', metrics, 60);

      return metrics;

    } catch (error) {
      logger.queue.error('Failed to collect health metrics:', error);
      throw error;
    }
  }

  /**
   * Check Redis connectivity
   */
  private static async checkRedisHealth(): Promise<boolean> {
    try {
      await cache.get('health_check');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Calculate processing rate for a queue (RabbitMQ-based)
   */
  private static calculateRate(queueStats: any[], queueName: string): number {
    const queue = queueStats.find(q => q.name === queueName);
    if (!queue) return 0;

    // Use consumer count as processing rate indicator
    return queue.consumerCount || 0;
  }

  /**
   * Calculate error rate for a queue (RabbitMQ-based)
   */
  private static calculateErrorRate(queueStats: any[], queueName: string): number {
    const queue = queueStats.find(q => q.name === queueName);
    if (!queue) return 0;

    // If there's an error, return 1 (100% error rate), otherwise 0
    return queue.error ? 1 : 0;
  }

  /**
   * Check if system is healthy and generate alerts
   */
  static async performHealthCheck(): Promise<{
    healthy: boolean;
    alerts: string[];
    recommendations: string[];
  }> {
    try {
      const metrics = await this.collectMetrics();
      const alerts: string[] = [];
      const recommendations: string[] = [];
      let healthy = true;

      // Check queue health
      if (!metrics.queueHealth) {
        healthy = false;
        alerts.push('🚨 Queue system is unhealthy - using fallback mode');
        recommendations.push('Check Redis connectivity and restart queue processors');
      }

      // Check Redis health
      if (!metrics.redisHealth) {
        healthy = false;
        alerts.push('🚨 Redis is unreachable');
        recommendations.push('Check Redis server status and network connectivity');
      }

      // Check error rates
      Object.entries(metrics.errorRates).forEach(([queue, rate]) => {
        if (rate > this.alertThresholds.maxErrorRate) {
          healthy = false;
          alerts.push(`🚨 High error rate in ${queue}: ${(rate * 100).toFixed(1)}%`);
          recommendations.push(`Investigate ${queue} processing errors and increase retry attempts`);
        }
      });

      // Check fallback status
      if (metrics.fallbackStatus.fallbackFiles > this.alertThresholds.maxFallbackFiles) {
        alerts.push(`⚠️ High number of fallback files: ${metrics.fallbackStatus.fallbackFiles}`);
        recommendations.push('Process fallback files when queue system recovers');
      }

      if (metrics.fallbackStatus.bufferedJobs > 100) {
        alerts.push(`⚠️ High number of buffered jobs: ${metrics.fallbackStatus.bufferedJobs}`);
        recommendations.push('Flush fallback buffer and check queue processing');
      }

      // Performance recommendations
      if (healthy && alerts.length === 0) {
        recommendations.push('✅ All systems operating normally');
        
        // Check for optimization opportunities
        const totalRate = Object.values(metrics.processingRates).reduce((sum, rate) => sum + rate, 0);
        if (totalRate > 1000) {
          recommendations.push('💡 Consider increasing queue concurrency for higher throughput');
        }
      }

      return { healthy, alerts, recommendations };

    } catch (error) {
      logger.queue.error('Health check failed:', error);
      return {
        healthy: false,
        alerts: ['🚨 Health check system failure'],
        recommendations: ['Check monitoring system and logs'],
      };
    }
  }

  /**
   * Get historical metrics
   */
  static getHistoricalMetrics(minutes: number = 60): HealthMetrics[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.metrics.filter(m => m.timestamp > cutoff);
  }

  /**
   * Generate health report
   */
  static async generateHealthReport(): Promise<{
    current: HealthMetrics;
    trends: {
      avgProcessingRate: number;
      avgErrorRate: number;
      uptimePercentage: number;
    };
    alerts: string[];
    recommendations: string[];
  }> {
    const current = await this.collectMetrics();
    const historical = this.getHistoricalMetrics(60); // Last hour
    const healthCheck = await this.performHealthCheck();

    // Calculate trends
    const avgProcessingRate = historical.length > 0
      ? historical.reduce((sum, m) => 
          sum + Object.values(m.processingRates).reduce((s, r) => s + r, 0), 0
        ) / historical.length
      : 0;

    const avgErrorRate = historical.length > 0
      ? historical.reduce((sum, m) => 
          sum + Object.values(m.errorRates).reduce((s, r) => s + r, 0) / 3, 0
        ) / historical.length
      : 0;

    const uptimePercentage = historical.length > 0
      ? (historical.filter(m => m.queueHealth).length / historical.length) * 100
      : 100;

    return {
      current,
      trends: {
        avgProcessingRate,
        avgErrorRate,
        uptimePercentage,
      },
      alerts: healthCheck.alerts,
      recommendations: healthCheck.recommendations,
    };
  }
}

// Start health monitoring
setInterval(async () => {
  try {
    await QueueHealthMonitor.collectMetrics();
    const healthCheck = await QueueHealthMonitor.performHealthCheck();
    
    // Log critical alerts
    if (!healthCheck.healthy) {
      logger.queue.error('🚨 Queue Health Alert:', {
        alerts: healthCheck.alerts,
        recommendations: healthCheck.recommendations,
      });
    }
  } catch (error) {
    logger.queue.error('Health monitoring error:', error);
  }
}, 30000); // Every 30 seconds

export default QueueHealthMonitor;
