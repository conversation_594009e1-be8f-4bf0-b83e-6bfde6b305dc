import { createClient } from 'redis';

class CacheManager {
  private static instance: CacheManager;
  private redis: any;
  private isConnected = false;

  private constructor() {
    // Build Redis configuration dynamically
    const redisConfig = this.buildRedisConfig();

    this.redis = createClient(redisConfig);

    this.redis.on('error', (err: any) => {
      console.error('Redis Client Error', err);
      this.isConnected = false;
    });

    this.redis.on('connect', () => {
      console.log('Redis Client Connected');
      this.isConnected = true;
    });

    this.redis.on('ready', () => {
      console.log('Redis Client Ready');
      this.isConnected = true;
    });

    // Connect immediately
    this.connect().catch(err => {
      console.error('Initial Redis connection failed:', err);
    });
  }

  /**
   * Build Redis configuration based on environment variables
   */
  private buildRedisConfig() {
    const redisHost = process.env.REDIS_HOST || 'localhost';
    const redisPort = parseInt(process.env.REDIS_PORT || '6379');
    const redisPassword = process.env.REDIS_PASSWORD || '';
    const redisUrl = process.env.REDIS_URL;

    // If REDIS_URL is provided, use it (it may include password)
    if (redisUrl) {
      console.log('Using REDIS_URL configuration');
      return { url: redisUrl };
    }

    // Build configuration optimized for high QPS RTB workloads
    const config: any = {
      socket: {
        host: redisHost,
        port: redisPort,
        reconnectStrategy: (retries: number) => {
          if (retries > 5) {
            console.error('Redis reconnection failed after 5 attempts');
            return false;
          }
          return Math.min(retries * 25, 500); // Very fast retry for RTB
        },
        connectTimeout: 500, // Ultra-fast timeout for RTB
        commandTimeout: 100, // 100ms command timeout
        keepAlive: 30000, // 30s keep-alive
        noDelay: true, // Disable Nagle's algorithm for low latency
        family: 4, // Force IPv4 for faster resolution
      },
      commandsQueueMaxLength: 10000, // Higher queue for burst traffic
      maxRetriesPerRequest: 1, // Single retry for RTB speed
      enableReadyCheck: false, // Disable ready check for performance
      enableOfflineQueue: false, // Don't queue commands when disconnected
      lazyConnect: false, // Connect immediately
      retryDelayOnFailover: 50, // Fast failover
      retryDelayOnClusterDown: 100,
    };

    // Add password only if it's provided and not empty
    if (redisPassword && redisPassword.trim() !== '') {
      config.password = redisPassword.trim();
      console.log('Redis configured with password authentication');
    } else {
      console.log('Redis configured without password authentication');
    }

    return config;
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  async connect() {
    if (!this.isConnected) {
      try {
        await this.redis.connect();
        console.log('Redis connection established successfully');
      } catch (error) {
        console.error('Failed to connect to Redis:', error);
        this.isConnected = false;
        // Don't throw error - let the app continue without cache
      }
    }
  }

  /**
   * Test Redis connection and authentication
   */
  async testConnection(): Promise<{ connected: boolean; error?: string; config: any }> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      // Test with a simple ping
      const result = await this.redis.ping();

      return {
        connected: result === 'PONG',
        config: {
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || '6379',
          hasPassword: !!(process.env.REDIS_PASSWORD && process.env.REDIS_PASSWORD.trim() !== ''),
          hasUrl: !!process.env.REDIS_URL,
        }
      };
    } catch (error) {
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        config: {
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || '6379',
          hasPassword: !!(process.env.REDIS_PASSWORD && process.env.REDIS_PASSWORD.trim() !== ''),
          hasUrl: !!process.env.REDIS_URL,
        }
      };
    }
  }

  async get(key: string): Promise<any> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      // Use MULTI to ensure atomic operation
      const value = await this.redis.get(key).catch(() => null);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds: number = 300): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      // Use pipeline for better performance
      const pipeline = this.redis.multi();
      pipeline.setEx(key, ttlSeconds, JSON.stringify(value));
      await pipeline.exec().catch(() => {});
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  // Batch get operation for RTB
  async mget(keys: string[]): Promise<any[]> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      const values = await this.redis.mGet(keys).catch(() => []);
      return values.map((value: string | null) => value ? JSON.parse(value) : null);
    } catch (error) {
      console.error('Cache mget error:', error);
      return new Array(keys.length).fill(null);
    }
  }

  // Batch set operation for RTB
  async mset(entries: { key: string; value: any; ttl?: number }[]): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      const pipeline = this.redis.multi();
      for (const entry of entries) {
        pipeline.setEx(
          entry.key,
          entry.ttl || 300,
          JSON.stringify(entry.value)
        );
      }
      await pipeline.exec().catch(() => {});
    } catch (error) {
      console.error('Cache mset error:', error);
    }
  }

  async del(key: string | string[]): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      const pipeline = this.redis.multi();
      if (Array.isArray(key)) {
        // Batch delete for multiple keys
        pipeline.del(...key);
      } else {
        pipeline.del(key);
      }
      await pipeline.exec().catch(() => {});
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      if (!this.isConnected) {
        await this.connect();
      }
      const pipeline = this.redis.multi();
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        // Use batch delete for better performance
        pipeline.del(...keys);
        await pipeline.exec().catch(() => {});
      }
    } catch (error) {
      console.error('Cache invalidate error:', error);
    }
  }
}

export const cache = CacheManager.getInstance();

// Cache keys for consistent naming
export const CACHE_KEYS = {
  ACTIVE_CAMPAIGNS: (format: string) => `campaigns:active:${format}`,
  PARTNER_ENDPOINTS: (type: string) => `partners:${type}:active`,
  PARTNER_ENDPOINT: (id: number) => `partner:${id}`,
  PLATFORM_SETTINGS: 'settings:platform',
  PUBLISHER_REVENUE_SHARE: 'settings:publisher_revenue_share',
  SSP_INVENTORY: (zoneId: number, format: string) => `ssp:inventory:${zoneId}:${format}`,
  USER_BALANCE: (userId: number) => `user:${userId}:balance`,
  CAMPAIGN_BUDGET: (campaignId: number) => `campaign:${campaignId}:budget`,
};

// Optimized Cache TTL for High QPS RTB
export const CACHE_TTL = {
  CAMPAIGNS: 30, // Reduced to 30s for faster campaign updates
  PARTNERS: 600, // Increased to 10 minutes - partners rarely change
  SETTINGS: 3600, // 1 hour - unchanged
  INVENTORY: 10, // Reduced to 10s for more dynamic inventory
  USER_DATA: 60, // Reduced to 1 minute for faster balance updates
  BUDGET: 30, // Reduced to 30s for faster budget tracking
  AUCTION_RESULT: 0.1, // Reduced to 100ms for faster auctions
};

// Cache helper functions
export class CacheHelper {
  /**
   * Get or set cache with fallback to database
   */
  static async getOrSet<T>(
    key: string,
    fallbackFn: () => Promise<T>,
    ttlSeconds: number = 300
  ): Promise<T> {
    // Try to get from cache first
    const cached = await cache.get(key);
    if (cached !== null) {
      return cached;
    }

    // Cache miss - get from database
    const value = await fallbackFn();

    // Store in cache for next time
    await cache.set(key, value, ttlSeconds);

    return value;
  }

  /**
   * Invalidate related cache entries when data changes
   */
  static async invalidateRelated(patterns: string[]): Promise<void> {
    for (const pattern of patterns) {
      await cache.invalidatePattern(pattern);
    }
  }
}
