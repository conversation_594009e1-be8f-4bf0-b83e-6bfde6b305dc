import crypto from 'crypto';
import clickhouse from './clickhouse';
import { clickHouseBatchWriter } from './clickhouse-batch-writer';
import logger from './logger';

export interface ApiKey {
  id: number;
  apiKey: string;
  apiSecret: string;
  userId: number;
  partnerId: number;
  keyName: string;
  keyType: 'dsp' | 'ssp' | 'admin';
  permissions: string[];
  rateLimitPerHour: number;
  rateLimitPerDay: number;
  totalRequests: number;
  lastUsedAt: string;
  allowedIps: string[];
  allowedDomains: string[];
  status: 'active' | 'suspended' | 'revoked';
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
  description: string;
  lastIp: string;
  userAgent: string;
}

export interface ApiUsageLog {
  id: number;
  apiKey: string;
  userId: number;
  partnerId: number;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  requestPath: string;
  queryParams: string;
  statusCode: number;
  responseTimeMs: number;
  responseSizeBytes: number;
  ipAddress: string;
  userAgent: string;
  referer: string;
  timestamp: string;
  errorMessage: string;
  errorCode: string;
}

export class ApiKeyManager {
  /**
   * Generate a new API key and secret
   */
  static generateApiCredentials(): { apiKey: string; apiSecret: string } {
    const apiKey = 'ak_' + crypto.randomBytes(16).toString('hex');
    const apiSecret = 'sk_' + crypto.randomBytes(32).toString('hex');
    return { apiKey, apiSecret };
  }

  /**
   * Create a new API key
   */
  static async createApiKey(params: {
    userId: number;
    partnerId?: number;
    keyName: string;
    keyType: 'dsp' | 'ssp' | 'admin';
    permissions: string[];
    rateLimitPerHour?: number;
    rateLimitPerDay?: number;
    allowedIps?: string[];
    allowedDomains?: string[];
    expiresAt?: string;
    description?: string;
  }): Promise<ApiKey> {
    try {
      const { apiKey, apiSecret } = this.generateApiCredentials();
      const keyId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
      
      const apiKeyData = {
        id: keyId,
        api_key: apiKey,
        api_secret: apiSecret,
        user_id: params.userId,
        partner_id: params.partnerId || 0,
        key_name: params.keyName,
        key_type: params.keyType,
        permissions: params.permissions,
        rate_limit_per_hour: params.rateLimitPerHour || 1000,
        rate_limit_per_day: params.rateLimitPerDay || 10000,
        total_requests: 0,
        last_used_at: '1970-01-01 00:00:00',
        allowed_ips: params.allowedIps || [],
        allowed_domains: params.allowedDomains || [],
        status: 'active',
        expires_at: params.expiresAt || '2099-12-31 23:59:59',
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        description: params.description || '',
        last_ip: '',
        user_agent: '',
      };

      await clickHouseBatchWriter.write('api_keys', apiKeyData);

      logger.api.info(`API key created: ${apiKey} for user ${params.userId}`);

      return {
        id: keyId,
        apiKey,
        apiSecret,
        userId: params.userId,
        partnerId: params.partnerId || 0,
        keyName: params.keyName,
        keyType: params.keyType,
        permissions: params.permissions,
        rateLimitPerHour: params.rateLimitPerHour || 1000,
        rateLimitPerDay: params.rateLimitPerDay || 10000,
        totalRequests: 0,
        lastUsedAt: '1970-01-01 00:00:00',
        allowedIps: params.allowedIps || [],
        allowedDomains: params.allowedDomains || [],
        status: 'active',
        expiresAt: params.expiresAt || '2099-12-31 23:59:59',
        createdAt: apiKeyData.created_at,
        updatedAt: apiKeyData.updated_at,
        description: params.description || '',
        lastIp: '',
        userAgent: '',
      };
    } catch (error) {
      logger.api.error('Failed to create API key:', error);
      throw error;
    }
  }

  /**
   * Validate API key and return key details
   */
  static async validateApiKey(apiKey: string): Promise<ApiKey | null> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT 
            id, api_key, api_secret, user_id, partner_id, key_name, key_type,
            permissions, rate_limit_per_hour, rate_limit_per_day, total_requests,
            last_used_at, allowed_ips, allowed_domains, status, expires_at,
            created_at, updated_at, description, last_ip, user_agent
          FROM api_keys 
          WHERE api_key = {apiKey:String} 
            AND status = 'active' 
            AND expires_at > now()
          ORDER BY created_at DESC
          LIMIT 1
        `,
        query_params: { apiKey },
      });

      const data = await result.json();
      if (data.data.length === 0) {
        return null;
      }

      const keyData = data.data[0];
      return {
        id: keyData.id,
        apiKey: keyData.api_key,
        apiSecret: keyData.api_secret,
        userId: keyData.user_id,
        partnerId: keyData.partner_id,
        keyName: keyData.key_name,
        keyType: keyData.key_type,
        permissions: keyData.permissions,
        rateLimitPerHour: keyData.rate_limit_per_hour,
        rateLimitPerDay: keyData.rate_limit_per_day,
        totalRequests: keyData.total_requests,
        lastUsedAt: keyData.last_used_at,
        allowedIps: keyData.allowed_ips,
        allowedDomains: keyData.allowed_domains,
        status: keyData.status,
        expiresAt: keyData.expires_at,
        createdAt: keyData.created_at,
        updatedAt: keyData.updated_at,
        description: keyData.description,
        lastIp: keyData.last_ip,
        userAgent: keyData.user_agent,
      };
    } catch (error) {
      logger.api.error('Failed to validate API key:', error);
      return null;
    }
  }

  /**
   * Check rate limits for API key
   */
  static async checkRateLimit(apiKey: string): Promise<{
    allowed: boolean;
    hourlyUsage: number;
    dailyUsage: number;
    hourlyLimit: number;
    dailyLimit: number;
  }> {
    try {
      const keyData = await this.validateApiKey(apiKey);
      if (!keyData) {
        return { allowed: false, hourlyUsage: 0, dailyUsage: 0, hourlyLimit: 0, dailyLimit: 0 };
      }

      // Get usage for last hour and last day
      const result = await clickhouse.query({
        query: `
          SELECT 
            countIf(timestamp >= now() - INTERVAL 1 HOUR) as hourly_usage,
            countIf(timestamp >= now() - INTERVAL 1 DAY) as daily_usage
          FROM api_usage_logs 
          WHERE api_key = {apiKey:String}
        `,
        query_params: { apiKey },
      });

      const data = await result.json();
      const usage = data.data[0] || { hourly_usage: 0, daily_usage: 0 };

      const hourlyUsage = usage.hourly_usage;
      const dailyUsage = usage.daily_usage;
      const hourlyLimit = keyData.rateLimitPerHour;
      const dailyLimit = keyData.rateLimitPerDay;

      const allowed = hourlyUsage < hourlyLimit && dailyUsage < dailyLimit;

      return {
        allowed,
        hourlyUsage,
        dailyUsage,
        hourlyLimit,
        dailyLimit,
      };
    } catch (error) {
      logger.api.error('Failed to check rate limit:', error);
      return { allowed: false, hourlyUsage: 0, dailyUsage: 0, hourlyLimit: 0, dailyLimit: 0 };
    }
  }

  /**
   * Log API usage
   */
  static async logApiUsage(params: {
    apiKey: string;
    userId: number;
    partnerId: number;
    endpoint: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    requestPath: string;
    queryParams?: string;
    statusCode: number;
    responseTimeMs: number;
    responseSizeBytes?: number;
    ipAddress: string;
    userAgent?: string;
    referer?: string;
    errorMessage?: string;
    errorCode?: string;
  }): Promise<void> {
    try {
      const logId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
      
      await clickHouseBatchWriter.write('api_usage_logs', {
        id: logId,
        api_key: params.apiKey,
        user_id: params.userId,
        partner_id: params.partnerId,
        endpoint: params.endpoint,
        method: params.method,
        request_path: params.requestPath,
        query_params: params.queryParams || '',
        status_code: params.statusCode,
        response_time_ms: params.responseTimeMs,
        response_size_bytes: params.responseSizeBytes || 0,
        ip_address: params.ipAddress,
        user_agent: params.userAgent || '',
        referer: params.referer || '',
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        date: new Date().toISOString().slice(0, 10),
        error_message: params.errorMessage || '',
        error_code: params.errorCode || '',
      });

      // Update last used timestamp for API key
      await clickhouse.command({
        query: `
          ALTER TABLE api_keys 
          UPDATE 
            last_used_at = now(),
            total_requests = total_requests + 1,
            last_ip = {ipAddress:String},
            user_agent = {userAgent:String},
            updated_at = now()
          WHERE api_key = {apiKey:String}
        `,
        query_params: {
          apiKey: params.apiKey,
          ipAddress: params.ipAddress,
          userAgent: params.userAgent || '',
        },
      });

    } catch (error) {
      logger.api.error('Failed to log API usage:', error);
    }
  }

  /**
   * Get API keys for a user
   */
  static async getUserApiKeys(userId: number, keyType?: 'dsp' | 'ssp' | 'admin'): Promise<ApiKey[]> {
    try {
      const whereClause = keyType 
        ? `WHERE user_id = {userId:UInt32} AND key_type = {keyType:String}`
        : `WHERE user_id = {userId:UInt32}`;

      const result = await clickhouse.query({
        query: `
          SELECT 
            id, api_key, api_secret, user_id, partner_id, key_name, key_type,
            permissions, rate_limit_per_hour, rate_limit_per_day, total_requests,
            last_used_at, allowed_ips, allowed_domains, status, expires_at,
            created_at, updated_at, description, last_ip, user_agent
          FROM api_keys 
          ${whereClause}
          ORDER BY created_at DESC
        `,
        query_params: keyType ? { userId, keyType } : { userId },
      });

      const data = await result.json();
      return data.data.map((keyData: any) => ({
        id: keyData.id,
        apiKey: keyData.api_key,
        apiSecret: keyData.api_secret,
        userId: keyData.user_id,
        partnerId: keyData.partner_id,
        keyName: keyData.key_name,
        keyType: keyData.key_type,
        permissions: keyData.permissions,
        rateLimitPerHour: keyData.rate_limit_per_hour,
        rateLimitPerDay: keyData.rate_limit_per_day,
        totalRequests: keyData.total_requests,
        lastUsedAt: keyData.last_used_at,
        allowedIps: keyData.allowed_ips,
        allowedDomains: keyData.allowed_domains,
        status: keyData.status,
        expiresAt: keyData.expires_at,
        createdAt: keyData.created_at,
        updatedAt: keyData.updated_at,
        description: keyData.description,
        lastIp: keyData.last_ip,
        userAgent: keyData.user_agent,
      }));
    } catch (error) {
      logger.api.error('Failed to get user API keys:', error);
      return [];
    }
  }

  /**
   * Revoke API key
   */
  static async revokeApiKey(apiKey: string, userId: number): Promise<boolean> {
    try {
      await clickhouse.command({
        query: `
          ALTER TABLE api_keys 
          UPDATE status = 'revoked', updated_at = now()
          WHERE api_key = {apiKey:String} AND user_id = {userId:UInt32}
        `,
        query_params: { apiKey, userId },
      });

      logger.api.info(`API key revoked: ${apiKey} by user ${userId}`);
      return true;
    } catch (error) {
      logger.api.error('Failed to revoke API key:', error);
      return false;
    }
  }
}

export default ApiKeyManager;
