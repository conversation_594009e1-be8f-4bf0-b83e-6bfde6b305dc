import logger from '@/lib/logger';

interface CircuitBreakerOptions {
  failureThreshold?: number; // Number of consecutive failures before opening the circuit
  resetTimeout?: number;    // Time in milliseconds to wait before attempting to close the circuit (half-open state)
  fallbackFn?: (...args: any[]) => any; // Optional fallback function to call when circuit is open
}

export class CircuitBreaker {
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private options: Required<CircuitBreakerOptions>;

  constructor(options?: CircuitBreakerOptions) {
    this.options = {
      failureThreshold: 3,
      resetTimeout: 30000, // 30 seconds
      fallbackFn: () => {
        logger.circuitBreaker.warn('Circuit breaker fallback triggered: No fallback function provided.');
        return Promise.resolve([]); // Default to empty array for bid requests
      },
      ...options,
    };

    // Bind methods to the instance to ensure `this` context is correct
    this.call = this.call.bind(this);
  }

  public async call<T>(fn: (...args: any[]) => Promise<T>, fallbackFn?: (...args: any[]) => Promise<T>, ...args: any[]): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() > this.lastFailureTime + this.options.resetTimeout) {
        this.halfOpen();
      } else {
        logger.circuitBreaker.warn(`Circuit is OPEN. Skipping call to function. Time until half-open: ${((this.lastFailureTime + this.options.resetTimeout - Date.now()) / 1000).toFixed(1)}s`);
        return fallbackFn ? await fallbackFn(...args) : await this.options.fallbackFn(...args);
      }
    }

    try {
      const result = await fn(...args);
      this.success();
      return result;
    } catch (error) {
      logger.circuitBreaker.error(`Function call failed. Error:`, error);
      this.fail();
      if (fallbackFn) {
        return await fallbackFn(...args);
      } else if (this.options.fallbackFn) {
        return await this.options.fallbackFn(...args);
      } else {
        throw error; // If no fallback and no global fallback, rethrow the original error
      }
    }
  }

  private success(): void {
    this.state = 'closed';
    this.failureCount = 0;
    logger.circuitBreaker.info('Circuit is CLOSED. Resetting failure count.');
  }

  private fail(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    if (this.failureCount >= this.options.failureThreshold) {
      this.open();
    } else {
      logger.circuitBreaker.warn(`Circuit is still CLOSED. Failure count: ${this.failureCount}/${this.options.failureThreshold}`);
    }
  }

  private open(): void {
    this.state = 'open';
    logger.circuitBreaker.warn(`Circuit is OPEN after ${this.options.failureThreshold} failures.`);
  }

  private halfOpen(): void {
    this.state = 'half-open';
    logger.circuitBreaker.info('Circuit is HALF-OPEN. Probing for recovery.');
    this.failureCount = 0; // Reset count for the probe attempt
  }

  public getState(): 'closed' | 'open' | 'half-open' {
    return this.state;
  }

  public getFailureCount(): number {
    return this.failureCount;
  }

  public getLastFailureTime(): number {
    return this.lastFailureTime;
  }
} 