import clickhouse from './clickhouse';
import { cache, CACHE_KEYS, CACHE_TTL, CacheHelper } from './cache';

// --- NEW: QPS Tracking Map ---
const dspQpsCounters: Map<number, number[]> = new Map(); // Stores timestamps of last requests for each DSP
// --- END NEW ---

interface DSPBidRequest {
  id: string;
  imp: Array<{
    id: string;
    banner?: { w: number; h: number };
    native?: { request: string };
    bidfloor?: number;
  }>;
  site?: {
    id: string;
    domain?: string;
    page?: string;
  };
  device?: {
    ua: string;
    ip: string;
    geo?: {
      country: string;
      region?: string;
      city?: string;
    };
  };
}

interface DSPPartner {
  id: number;
  name: string;
  endpoint_url: string;
  protocol: string;
  timeout_ms: number;
  bid_price_format: string;
  api_key?: string;
  qps_limit?: number;
}

interface DSPBidResponse {
  partnerId: number;
  partnerName: string;
  bidPrice: number;
  adMarkup: string;
  landingUrl: string;
  winUrl?: string;
  success: boolean;
  responseTime: number;
}

export class AsyncDSPService {
  /**
   * Get active DSP partners with caching
   */
  static async getActiveDSPPartners(): Promise<DSPPartner[]> {
    return await CacheHelper.getOrSet(
      CACHE_KEYS.PARTNER_ENDPOINTS('dsp'),
      async () => {
        const partnersResult = await clickhouse.query({
          query: `
            SELECT id, name, endpoint_url, protocol, timeout_ms, bid_price_format, api_key, qps_limit
            FROM partner_endpoints
            WHERE type = 'dsp' AND status = 'active'
            ORDER BY id
          `,
        });
        const result = await partnersResult.json();
        return result.data as DSPPartner[];
      },
      CACHE_TTL.PARTNERS
    );
  }

  /**
   * Send bid requests to all DSPs in parallel
   */
  static async requestBidsFromAllDSPs(
    bidRequest: DSPBidRequest,
    timeoutMs: number = 150
  ): Promise<DSPBidResponse[]> {
    const dspPartners = await this.getActiveDSPPartners();
    
    if (dspPartners.length === 0) {
      return [];
    }

    const dspPromises = dspPartners.map(async partner => {
      // Check QPS limit before sending request
      if (partner.qps_limit && !AsyncDSPService.checkAndIncrementQPS(partner.id, partner.qps_limit)) {
        console.warn(`DSP ${partner.name} (ID: ${partner.id}) is over QPS limit of ${partner.qps_limit}. Skipping bid request.`);
        return {
          partnerId: partner.id,
          partnerName: partner.name,
          bidPrice: 0,
          adMarkup: '',
          landingUrl: '',
          success: false,
          responseTime: 0,
        } as DSPBidResponse; // Return a no-bid response for skipped DSPs
      }
      // If within QPS limit, send the request
      return this.requestBidFromDSP(partner, bidRequest, timeoutMs);
    });

    // Wait for all DSPs with timeout
    const results = await Promise.allSettled(dspPromises);
    
    // Filter successful responses
    const successfulBids: DSPBidResponse[] = [];
    
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const partner = dspPartners[i];
      
      if (result.status === 'fulfilled' && result.value.success) {
        successfulBids.push(result.value);
      } else {
        // Log failed DSP for monitoring
        console.warn(`DSP ${partner.name} failed:`, 
          result.status === 'rejected' ? result.reason : 'No bid');
      }
    }

    return successfulBids;
  }

  /**
   * Send bid request to a single DSP with timeout
   */
  private static async requestBidFromDSP(
    partner: DSPPartner,
    bidRequest: DSPBidRequest,
    timeoutMs: number
  ): Promise<DSPBidResponse> {
    const startTime = Date.now();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await this.sendHTTPRequest(partner, bidRequest, controller.signal);
      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;

      if (response && response.seatbid && response.seatbid.length > 0) {
        const bid = response.seatbid[0].bid[0];
        
        let normalizedBidPrice = bid.price;
        if (partner.bid_price_format === 'cpv' || partner.bid_price_format === 'cpc') {
          normalizedBidPrice = bid.price * 1000;
        }

        return {
          partnerId: partner.id,
          partnerName: partner.name,
          bidPrice: normalizedBidPrice,
          adMarkup: bid.adm || '',
          landingUrl: bid.adomain?.[0] || '',
          winUrl: bid.nurl || '',
          success: true,
          responseTime,
        };
      }

      return {
        partnerId: partner.id,
        partnerName: partner.name,
        bidPrice: 0,
        adMarkup: '',
        landingUrl: '',
        success: false,
        responseTime,
      };

    } catch (error: any) {
      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      if (error.name === 'AbortError') {
        console.warn(`DSP ${partner.name} timed out after ${responseTime}ms`);
      } else {
        console.error(`DSP ${partner.name} request failed:`, error.message);
      }

      return {
        partnerId: partner.id,
        partnerName: partner.name,
        bidPrice: 0,
        adMarkup: '',
        landingUrl: '',
        success: false,
        responseTime,
      };
    }
  }

  /**
   * Send HTTP request to DSP
   */
  private static async sendHTTPRequest(
    partner: DSPPartner,
    bidRequest: DSPBidRequest,
    signal: AbortSignal
  ): Promise<any> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'GlobalAdsMedia-RTB/1.0',
    };

    if (partner.api_key) {
      headers['Authorization'] = `Bearer ${partner.api_key}`;
    }

    const response = await fetch(partner.endpoint_url, {
      method: 'POST',
      headers,
      body: JSON.stringify(bidRequest),
      signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    return await response.json();
  }

  /**
   * Convert bid request format for DSP compatibility
   */
  static createDSPBidRequest(originalRequest: any, imp: any): DSPBidRequest {
    return {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      imp: [{
        id: imp.id,
        banner: imp.banner,
        native: imp.native,
        bidfloor: imp.bidfloor || 0,
      }],
      site: originalRequest.site,
      device: originalRequest.device,
    };
  }

  /**
   * Helper to check and increment QPS counter for a given DSP
   * Returns true if within limit, false otherwise
   */
  private static checkAndIncrementQPS(partnerId: number, qpsLimit: number): boolean {
    const now = Date.now();
    let timestamps = dspQpsCounters.get(partnerId) || [];

    // Filter out timestamps older than 1 second (1000 ms)
    timestamps = timestamps.filter(ts => now - ts < 1000);

    if (timestamps.length < qpsLimit) {
      timestamps.push(now);
      dspQpsCounters.set(partnerId, timestamps);
      return true;
    } else {
      return false;
    }
  }
}
