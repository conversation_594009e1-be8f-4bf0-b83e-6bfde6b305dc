import { SmartImpressionTracker, ImpressionData } from './smart-impression-tracker';
import { CostProcessor } from './cost-processor';
import { RequestTracker } from './request-tracker';
import { clickHouseBatchWriter } from './clickhouse-batch-writer';
import clickhouse from './clickhouse';
import logger from './logger';
import fs from 'fs/promises';
import path from 'path';

// Fallback storage for when queues are unavailable
const FALLBACK_DIR = './fallback-storage';
const MAX_FALLBACK_FILES = 100;
const FALLBACK_BATCH_SIZE = 1000;

interface FallbackJob {
  id: string;
  type: 'track-impression' | 'process-cost' | 'track-request' | 'process-win' | 'log-transaction';
  data: any;
  timestamp: number;
  attempts: number;
  priority: number;
}

export class FallbackManager {
  private static isQueueHealthy = true;
  private static fallbackBuffer: FallbackJob[] = [];
  private static processingFallback = false;

  /**
   * Check if queues are healthy and available
   */
  static async checkQueueHealth(): Promise<boolean> {
    try {
      // Try to connect to Redis
      const Redis = require('ioredis');
      const testRedis = new Redis({
        host: process.env.REDIS_HOST || '127.0.0.1',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD || '',
        connectTimeout: 2000,
        lazyConnect: true,
      });

      await testRedis.ping();
      await testRedis.disconnect();
      
      this.isQueueHealthy = true;
      return true;
    } catch (error) {
      logger.queue.error('Queue health check failed:', error);
      this.isQueueHealthy = false;
      return false;
    }
  }

  /**
   * Execute job with automatic fallback to direct database
   */
  static async executeWithFallback(
    jobType: FallbackJob['type'],
    jobData: any,
    priority: number = 5
  ): Promise<boolean> {
    // First, try queue if healthy
    if (this.isQueueHealthy) {
      try {
        const success = await this.tryQueueExecution(jobType, jobData, priority);
        if (success) return true;
      } catch (error) {
        logger.queue.warn(`Queue execution failed for ${jobType}, falling back to direct processing:`, error);
        this.isQueueHealthy = false;
      }
    }

    // Fallback to direct database execution
    return await this.executeDirectly(jobType, jobData);
  }

  /**
   * Try to execute via queue system
   */
  private static async tryQueueExecution(
    jobType: FallbackJob['type'],
    jobData: any,
    priority: number
  ): Promise<boolean> {
    try {
      const { 
        impressionQueue, 
        costProcessingQueue, 
        requestStatsQueue, 
        winNotificationQueue, 
        transactionQueue 
      } = await import('./queue-manager');

      const jobOptions = {
        priority,
        attempts: 3,
        removeOnComplete: 100,
        removeOnFail: 50,
      };

      switch (jobType) {
        case 'track-impression':
          await impressionQueue.add('track-impression', jobData, jobOptions);
          break;
        case 'process-cost':
          await costProcessingQueue.add('process-cost', jobData, jobOptions);
          break;
        case 'track-request':
          await requestStatsQueue.add('track-request', jobData, jobOptions);
          break;
        case 'process-win':
          await winNotificationQueue.add('process-win', jobData, jobOptions);
          break;
        case 'log-transaction':
          await transactionQueue.add('log-transaction', jobData, jobOptions);
          break;
        default:
          throw new Error(`Unknown job type: ${jobType}`);
      }

      return true;
    } catch (error) {
      logger.queue.error(`Failed to queue ${jobType}:`, error);
      return false;
    }
  }

  /**
   * Execute job directly without queue (fallback mode)
   */
  private static async executeDirectly(
    jobType: FallbackJob['type'],
    jobData: any
  ): Promise<boolean> {
    try {
      logger.queue.info(`🔄 Executing ${jobType} directly (fallback mode)`);

      switch (jobType) {
        case 'track-impression':
          await SmartImpressionTracker.trackImpression(jobData as ImpressionData);
          break;

        case 'process-cost':
          await CostProcessor.processImpressionCost(jobData);
          break;

        case 'track-request':
          await RequestTracker.trackRequest(jobData);
          break;

        case 'process-win':
          // Store win notification record directly
          const winId = Date.now() * 1000 + Math.floor(Math.random() * 1000);
          await clickHouseBatchWriter.write('win_notifications', {
            id: winId,
            ssp_partner_id: jobData.sspPartnerId || 0,
            ssp_impression_id: jobData.sspImpressionId || '',
            bid_price: jobData.bidPrice || 0,
            gross_bid_cpm: jobData.grossBidCpm || 0,
            winner_source: jobData.winnerSource || '',
            winner_id: jobData.winnerId || 0,
            timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
          });
          break;

        case 'log-transaction':
          // Log transaction directly
          await clickHouseBatchWriter.write('transactions', {
            id: jobData.id || Date.now() * 1000 + Math.floor(Math.random() * 1000),
            user_id: jobData.userId,
            type: jobData.type,
            amount: jobData.amount,
            status: jobData.status || 'completed',
            payment_method: jobData.paymentMethod || '',
            payment_reference: jobData.paymentReference || '',
            description: jobData.description,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          });
          break;

        default:
          throw new Error(`Unknown job type: ${jobType}`);
      }

      logger.queue.info(`✅ Direct execution completed for ${jobType}`);
      return true;

    } catch (error) {
      logger.queue.error(`❌ Direct execution failed for ${jobType}:`, error);
      
      // If direct execution fails, store to file for later processing
      await this.storeToFallbackFile({
        id: `${Date.now()}-${Math.random()}`,
        type: jobType,
        data: jobData,
        timestamp: Date.now(),
        attempts: 0,
        priority: 5,
      });

      return false;
    }
  }

  /**
   * Store failed jobs to disk for later processing
   */
  private static async storeToFallbackFile(job: FallbackJob): Promise<void> {
    try {
      // Ensure fallback directory exists
      await fs.mkdir(FALLBACK_DIR, { recursive: true });

      // Add to buffer
      this.fallbackBuffer.push(job);

      // Write to file when buffer is full or periodically
      if (this.fallbackBuffer.length >= FALLBACK_BATCH_SIZE) {
        await this.flushFallbackBuffer();
      }

      logger.queue.warn(`📁 Stored job ${job.type} to fallback file`);
    } catch (error) {
      logger.queue.error('Failed to store fallback job:', error);
    }
  }

  /**
   * Flush fallback buffer to disk
   */
  private static async flushFallbackBuffer(): Promise<void> {
    if (this.fallbackBuffer.length === 0) return;

    try {
      const filename = `fallback-${Date.now()}.json`;
      const filepath = path.join(FALLBACK_DIR, filename);
      
      await fs.writeFile(filepath, JSON.stringify(this.fallbackBuffer, null, 2));
      
      logger.queue.info(`💾 Flushed ${this.fallbackBuffer.length} jobs to ${filename}`);
      this.fallbackBuffer = [];

      // Clean up old fallback files
      await this.cleanupOldFallbackFiles();
    } catch (error) {
      logger.queue.error('Failed to flush fallback buffer:', error);
    }
  }

  /**
   * Process stored fallback jobs when system recovers
   */
  static async processFallbackJobs(): Promise<void> {
    if (this.processingFallback) return;
    this.processingFallback = true;

    try {
      // Check if queues are healthy again
      const isHealthy = await this.checkQueueHealth();
      if (!isHealthy) {
        logger.queue.warn('Queues still unhealthy, skipping fallback processing');
        return;
      }

      // Process any buffered jobs first
      if (this.fallbackBuffer.length > 0) {
        await this.flushFallbackBuffer();
      }

      // Process stored fallback files
      const files = await fs.readdir(FALLBACK_DIR).catch(() => []);
      const fallbackFiles = files.filter(f => f.startsWith('fallback-') && f.endsWith('.json'));

      for (const filename of fallbackFiles) {
        await this.processFallbackFile(filename);
      }

      logger.queue.info(`🔄 Processed ${fallbackFiles.length} fallback files`);
    } catch (error) {
      logger.queue.error('Error processing fallback jobs:', error);
    } finally {
      this.processingFallback = false;
    }
  }

  /**
   * Process a single fallback file
   */
  private static async processFallbackFile(filename: string): Promise<void> {
    try {
      const filepath = path.join(FALLBACK_DIR, filename);
      const content = await fs.readFile(filepath, 'utf-8');
      const jobs: FallbackJob[] = JSON.parse(content);

      let processed = 0;
      let failed = 0;

      for (const job of jobs) {
        try {
          const success = await this.executeWithFallback(job.type, job.data, job.priority);
          if (success) {
            processed++;
          } else {
            failed++;
          }
        } catch (error) {
          logger.queue.error(`Failed to process fallback job ${job.id}:`, error);
          failed++;
        }
      }

      // Remove file if all jobs processed successfully
      if (failed === 0) {
        await fs.unlink(filepath);
        logger.queue.info(`✅ Processed and removed fallback file ${filename}: ${processed} jobs`);
      } else {
        logger.queue.warn(`⚠️ Fallback file ${filename}: ${processed} processed, ${failed} failed`);
      }

    } catch (error) {
      logger.queue.error(`Error processing fallback file ${filename}:`, error);
    }
  }

  /**
   * Clean up old fallback files
   */
  private static async cleanupOldFallbackFiles(): Promise<void> {
    try {
      const files = await fs.readdir(FALLBACK_DIR).catch(() => []);
      const fallbackFiles = files
        .filter(f => f.startsWith('fallback-') && f.endsWith('.json'))
        .sort()
        .reverse(); // Newest first

      // Remove excess files
      if (fallbackFiles.length > MAX_FALLBACK_FILES) {
        const filesToRemove = fallbackFiles.slice(MAX_FALLBACK_FILES);
        for (const filename of filesToRemove) {
          await fs.unlink(path.join(FALLBACK_DIR, filename));
        }
        logger.queue.info(`🧹 Cleaned up ${filesToRemove.length} old fallback files`);
      }
    } catch (error) {
      logger.queue.error('Error cleaning up fallback files:', error);
    }
  }

  /**
   * Get fallback system status
   */
  static async getStatus() {
    const files = await fs.readdir(FALLBACK_DIR).catch(() => []);
    const fallbackFiles = files.filter(f => f.startsWith('fallback-') && f.endsWith('.json'));
    
    return {
      queueHealthy: this.isQueueHealthy,
      bufferedJobs: this.fallbackBuffer.length,
      fallbackFiles: fallbackFiles.length,
      processingFallback: this.processingFallback,
    };
  }
}

// Periodic health checks and fallback processing
setInterval(async () => {
  await FallbackManager.checkQueueHealth();
  if (FallbackManager['isQueueHealthy']) {
    await FallbackManager.processFallbackJobs();
  }
}, 30000); // Check every 30 seconds

// Flush buffer periodically
setInterval(async () => {
  if (FallbackManager['fallbackBuffer'].length > 0) {
    await FallbackManager['flushFallbackBuffer']();
  }
}, 10000); // Flush every 10 seconds

// Graceful shutdown
process.on('SIGTERM', async () => {
  await FallbackManager['flushFallbackBuffer']();
});

process.on('SIGINT', async () => {
  await FallbackManager['flushFallbackBuffer']();
});

export default FallbackManager;
