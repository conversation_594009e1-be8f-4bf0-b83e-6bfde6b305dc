import { NextRequest, NextResponse } from 'next/server';
import ApiKeyManager from './api-key-manager';
import logger from './logger';

export interface AuthenticatedRequest extends NextRequest {
  apiKey?: {
    id: number;
    apiKey: string;
    userId: number;
    partnerId: number;
    keyType: 'dsp' | 'ssp' | 'admin';
    permissions: string[];
  };
}

export interface ApiAuthResult {
  success: boolean;
  error?: string;
  statusCode?: number;
  apiKey?: {
    id: number;
    apiKey: string;
    userId: number;
    partnerId: number;
    keyType: 'dsp' | 'ssp' | 'admin';
    permissions: string[];
  };
}

export class ApiAuthMiddleware {
  /**
   * Authenticate API request using API key
   */
  static async authenticate(request: NextRequest): Promise<ApiAuthResult> {
    const startTime = Date.now();
    
    try {
      // Extract API key from headers
      const apiKey = this.extractApiKey(request);
      if (!apiKey) {
        return {
          success: false,
          error: 'API key is required. Provide it in Authorization header as "Bearer YOUR_API_KEY" or X-API-Key header.',
          statusCode: 401,
        };
      }

      // Validate API key
      const keyData = await ApiKeyManager.validateApiKey(apiKey);
      if (!keyData) {
        await this.logFailedAuth(request, apiKey, 'Invalid API key', startTime);
        return {
          success: false,
          error: 'Invalid or expired API key',
          statusCode: 401,
        };
      }

      // Check IP restrictions
      if (keyData.allowedIps.length > 0) {
        const clientIp = this.getClientIp(request);
        if (!keyData.allowedIps.includes(clientIp)) {
          await this.logFailedAuth(request, apiKey, 'IP not allowed', startTime);
          return {
            success: false,
            error: 'Access denied: IP address not allowed',
            statusCode: 403,
          };
        }
      }

      // Check domain restrictions
      if (keyData.allowedDomains.length > 0) {
        const referer = request.headers.get('referer') || '';
        const domain = this.extractDomain(referer);
        if (domain && !keyData.allowedDomains.includes(domain)) {
          await this.logFailedAuth(request, apiKey, 'Domain not allowed', startTime);
          return {
            success: false,
            error: 'Access denied: Domain not allowed',
            statusCode: 403,
          };
        }
      }

      // Check rate limits
      const rateLimitResult = await ApiKeyManager.checkRateLimit(apiKey);
      if (!rateLimitResult.allowed) {
        await this.logFailedAuth(request, apiKey, 'Rate limit exceeded', startTime);
        return {
          success: false,
          error: `Rate limit exceeded. Hourly: ${rateLimitResult.hourlyUsage}/${rateLimitResult.hourlyLimit}, Daily: ${rateLimitResult.dailyUsage}/${rateLimitResult.dailyLimit}`,
          statusCode: 429,
        };
      }

      // Log successful authentication
      await this.logSuccessfulAuth(request, keyData, startTime);

      return {
        success: true,
        apiKey: {
          id: keyData.id,
          apiKey: keyData.apiKey,
          userId: keyData.userId,
          partnerId: keyData.partnerId,
          keyType: keyData.keyType,
          permissions: keyData.permissions,
        },
      };

    } catch (error) {
      logger.api.error('API authentication error:', error);
      return {
        success: false,
        error: 'Internal authentication error',
        statusCode: 500,
      };
    }
  }

  /**
   * Check if API key has required permission
   */
  static hasPermission(apiKey: { permissions: string[] }, requiredPermission: string): boolean {
    return apiKey.permissions.includes(requiredPermission) || apiKey.permissions.includes('*');
  }

  /**
   * Middleware wrapper for API routes
   */
  static withAuth(
    handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
    options: {
      requiredPermission?: string;
      allowedKeyTypes?: ('dsp' | 'ssp' | 'admin')[];
    } = {}
  ) {
    return async (request: NextRequest): Promise<NextResponse> => {
      const authResult = await this.authenticate(request);
      
      if (!authResult.success) {
        return NextResponse.json(
          { 
            error: authResult.error,
            code: 'AUTHENTICATION_FAILED',
            timestamp: new Date().toISOString(),
          },
          { status: authResult.statusCode || 401 }
        );
      }

      // Check required permission
      if (options.requiredPermission && !this.hasPermission(authResult.apiKey!, options.requiredPermission)) {
        return NextResponse.json(
          { 
            error: `Permission denied. Required permission: ${options.requiredPermission}`,
            code: 'INSUFFICIENT_PERMISSIONS',
            timestamp: new Date().toISOString(),
          },
          { status: 403 }
        );
      }

      // Check allowed key types
      if (options.allowedKeyTypes && !options.allowedKeyTypes.includes(authResult.apiKey!.keyType)) {
        return NextResponse.json(
          { 
            error: `Access denied. Allowed key types: ${options.allowedKeyTypes.join(', ')}`,
            code: 'INVALID_KEY_TYPE',
            timestamp: new Date().toISOString(),
          },
          { status: 403 }
        );
      }

      // Add API key data to request
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.apiKey = authResult.apiKey;

      return handler(authenticatedRequest);
    };
  }

  /**
   * Extract API key from request headers
   */
  private static extractApiKey(request: NextRequest): string | null {
    // Try Authorization header (Bearer token)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Try X-API-Key header
    const apiKeyHeader = request.headers.get('x-api-key');
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // Try query parameter (less secure, for testing only)
    const url = new URL(request.url);
    const apiKeyParam = url.searchParams.get('api_key');
    if (apiKeyParam) {
      return apiKeyParam;
    }

    return null;
  }

  /**
   * Get client IP address
   */
  private static getClientIp(request: NextRequest): string {
    // Check various headers for real IP
    const xForwardedFor = request.headers.get('x-forwarded-for');
    if (xForwardedFor) {
      return xForwardedFor.split(',')[0].trim();
    }

    const xRealIp = request.headers.get('x-real-ip');
    if (xRealIp) {
      return xRealIp;
    }

    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    if (cfConnectingIp) {
      return cfConnectingIp;
    }

    // Fallback to request IP
    return request.ip || 'unknown';
  }

  /**
   * Extract domain from URL
   */
  private static extractDomain(url: string): string | null {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.hostname;
    } catch {
      return null;
    }
  }

  /**
   * Log successful authentication
   */
  private static async logSuccessfulAuth(
    request: NextRequest,
    keyData: any,
    startTime: number
  ): Promise<void> {
    const responseTime = Date.now() - startTime;
    const url = new URL(request.url);
    
    await ApiKeyManager.logApiUsage({
      apiKey: keyData.apiKey,
      userId: keyData.userId,
      partnerId: keyData.partnerId,
      endpoint: url.pathname,
      method: request.method as any,
      requestPath: url.pathname + url.search,
      queryParams: url.search,
      statusCode: 200,
      responseTimeMs: responseTime,
      ipAddress: this.getClientIp(request),
      userAgent: request.headers.get('user-agent') || '',
      referer: request.headers.get('referer') || '',
    });
  }

  /**
   * Log failed authentication
   */
  private static async logFailedAuth(
    request: NextRequest,
    apiKey: string,
    reason: string,
    startTime: number
  ): Promise<void> {
    const responseTime = Date.now() - startTime;
    const url = new URL(request.url);
    
    await ApiKeyManager.logApiUsage({
      apiKey,
      userId: 0,
      partnerId: 0,
      endpoint: url.pathname,
      method: request.method as any,
      requestPath: url.pathname + url.search,
      queryParams: url.search,
      statusCode: 401,
      responseTimeMs: responseTime,
      ipAddress: this.getClientIp(request),
      userAgent: request.headers.get('user-agent') || '',
      referer: request.headers.get('referer') || '',
      errorMessage: reason,
      errorCode: 'AUTH_FAILED',
    });

    logger.api.warn(`API authentication failed: ${reason} for key ${apiKey} from IP ${this.getClientIp(request)}`);
  }
}

export default ApiAuthMiddleware;
