import clickhouse from '@/lib/clickhouse';

export interface ImpressionData {
  auctionId: number; // Required: auction ID to match impression to auction
  campaignId: number;
  dspPartnerId?: number; // For DSP partner tracking (when impression comes from DSP)
  sspPartnerId?: number; // For SSP partner tracking (when impression comes from SSP)
  websiteId: number;
  zoneId?: number;
  userAgent?: string;
  ipAddress?: string;
  country: string;
  region?: string;
  city?: string;
  deviceType: string;
  os: string;
  browser: string;
  costDeducted: number;
  publisherRevenue: number;
  sourceType?: 'local' | 'dsp' | 'ssp_inbound'; // Track source type
  isClick?: boolean;
  isConversion?: boolean;
}

export class SmartImpressionTracker {
  /**
   * Track impression with targeting data aggregation (SPACE-EFFICIENT)
   * Aggregates by hour + targeting dimensions instead of storing individual records
   */
  static async trackImpression(data: ImpressionData): Promise<number> {
    try {
      console.log(`📊 SmartImpressionTracker: Tracking impression for campaign ${data.campaignId}`);
      console.log(`📊 SmartImpressionTracker: Data:`, JSON.stringify(data, null, 2));

      // Save individual impression record to impressions table (let ClickHouse auto-generate ID)
      await clickhouse.command({
        query: `
          INSERT INTO impressions (
            auction_id, campaign_id, dsp_partner_id, ssp_partner_id, website_id, zone_id, user_agent, ip_address,
            country, state, city, device_type, os, browser,
            cost_deducted, publisher_revenue, bid_type, source_type
          )
          VALUES (
            {auctionId:UInt64}, {campaignId:UInt32}, {dspPartnerId:UInt32}, {sspPartnerId:UInt32}, {websiteId:UInt32}, {zoneId:UInt32},
            {userAgent:String}, {ipAddress:String}, {country:String}, {state:String}, {city:String},
            {deviceType:String}, {os:String}, {browser:String},
            {costDeducted:Decimal(18,6)}, {publisherRevenue:Decimal(18,6)}, {bidType:UInt8}, {sourceType:UInt8}
          )
        `,
        query_params: {
          auctionId: data.auctionId,
          campaignId: data.campaignId,
          dspPartnerId: data.dspPartnerId || 0, // Track DSP partner ID for DSP wins
          sspPartnerId: data.sspPartnerId || 0, // Track SSP partner ID for SSP requests
          websiteId: data.websiteId,
          zoneId: data.zoneId || 0,
          userAgent: data.userAgent || 'Unknown',
          ipAddress: data.ipAddress || 'Unknown',
          country: data.country,
          state: data.region || 'Unknown',
          city: data.city || 'Unknown',
          deviceType: data.deviceType,
          os: data.os,
          browser: data.browser,
          costDeducted: data.costDeducted,
          publisherRevenue: data.publisherRevenue,
          bidType: 1, // CPM = 1, CPV = 2
          sourceType: data.sourceType === 'dsp' ? 2 : data.sourceType === 'ssp_inbound' ? 3 : 1, // local = 1, dsp = 2, ssp_inbound = 3
        },
      });

      // Get the auto-generated ID from the last insert
      const result = await clickhouse.query({
        query: 'SELECT MAX(id) as last_id FROM impressions'
      });
      const resultData = await result.json();
      const generatedId = (resultData.data[0] as any)?.last_id || 0;

      console.log(`✅ SmartImpressionTracker: Successfully tracked impression with auto-generated ID ${generatedId}`);

      return generatedId;

    } catch (error) {
      console.error('❌ SmartImpressionTracker: Error tracking impression:', error);
      console.error('❌ SmartImpressionTracker: Failed data:', JSON.stringify(data, null, 2));
      // Don't throw - tracking failures shouldn't break ad serving
      return 0;
    }
  }

  /**
   * Get targeting performance data for campaign optimization
   */
  static async getTargetingStats(
    campaignId: number,
    period: 'today' | 'week' | 'month' = 'today'
  ): Promise<any[]> {
    try {
      let dateFilter = '';
      switch (period) {
        case 'week':
          dateFilter = 'date >= today() - 7';
          break;
        case 'month':
          dateFilter = 'date >= today() - 30';
          break;
        default:
          dateFilter = 'date = today()';
      }

      const result = await clickhouse.query({
        query: `
          SELECT
            country,
            device_type,
            os,
            browser,
            sum(total_impressions) as impressions,
            sum(total_clicks) as clicks,
            sum(total_conversions) as conversions,
            sum(total_cost) as cost,
            round(sum(total_clicks) / sum(total_impressions) * 100, 2) as ctr,
            round(sum(total_conversions) / sum(total_impressions) * 100, 2) as cvr,
            round(sum(total_cost) / sum(total_impressions) * 1000, 2) as cpm
          FROM targeting_stats
          WHERE campaign_id = {campaignId:UInt32} AND ${dateFilter}
          GROUP BY country, device_type, os, browser
          ORDER BY impressions DESC
        `,
        query_params: { campaignId },
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting targeting stats:', error);
      return [];
    }
  }

  /**
   * Get top performing targeting combinations
   */
  static async getTopPerformingTargets(
    campaignId: number,
    metric: 'impressions' | 'clicks' | 'conversions' | 'ctr' | 'cvr' = 'impressions',
    limit: number = 10
  ): Promise<any[]> {
    try {
      const orderBy = metric === 'ctr'
        ? 'round(sum(total_clicks) / sum(total_impressions) * 100, 2) DESC'
        : metric === 'cvr'
        ? 'round(sum(total_conversions) / sum(total_impressions) * 100, 2) DESC'
        : `sum(total_${metric}) DESC`;

      const result = await clickhouse.query({
        query: `
          SELECT
            country,
            device_type,
            os,
            browser,
            sum(total_impressions) as impressions,
            sum(total_clicks) as clicks,
            sum(total_conversions) as conversions,
            round(sum(total_clicks) / sum(total_impressions) * 100, 2) as ctr,
            round(sum(total_conversions) / sum(total_impressions) * 100, 2) as cvr
          FROM targeting_stats
          WHERE campaign_id = {campaignId:UInt32} AND date >= today() - 7
          GROUP BY country, device_type, os, browser
          HAVING impressions >= 100  -- Only show statistically significant data
          ORDER BY ${orderBy}
          LIMIT {limit:UInt32}
        `,
        query_params: { campaignId, limit },
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting top performing targets:', error);
      return [];
    }
  }

  /**
   * Get hourly performance breakdown
   */
  static async getHourlyBreakdown(
    campaignId: number,
    date: string = new Date().toISOString().slice(0, 10)
  ): Promise<any[]> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT
            hour,
            sum(total_impressions) as impressions,
            sum(total_clicks) as clicks,
            sum(total_conversions) as conversions,
            sum(total_cost) as cost,
            round(sum(total_clicks) / sum(total_impressions) * 100, 2) as ctr
          FROM targeting_stats
          WHERE campaign_id = {campaignId:UInt32} AND date = {date:Date}
          GROUP BY hour
          ORDER BY hour
        `,
        query_params: { campaignId, date },
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting hourly breakdown:', error);
      return [];
    }
  }
}
