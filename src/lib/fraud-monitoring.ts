import clickhouse from '@/lib/clickhouse';

export interface FraudLogData {
  ipAddress: string;
  userAgent: string;
  sourceType: 'publisher_direct' | 'dsp_inbound' | 'ssp_inbound';
  publisherId?: number;
  websiteId?: number;
  zoneId?: number;
  partnerId?: number;
  fraudReason: string;
  riskScore: number;
  country?: string;
  deviceType?: string;
}

export interface FraudExclusion {
  id: number;
  ipAddress: string;
  userAgent: string;
  sourceType: string;
  publisherId: number;
  websiteId: number;
  zoneId: number;
  partnerId: number;
  fraudReason: string;
  riskScore: number;
  country: string;
  deviceType: string;
  isExcluded: boolean;
  excludedBy: number;
  excludedAt: string;
  timestamp: string;
}

export class FraudMonitoring {
  /**
   * Log fraud detection event
   */
  static async logFraudDetection(data: FraudLogData): Promise<void> {
    try {
      const fraudId = Math.floor(Math.random() * 1000000000000);
      const now = new Date();

      await clickhouse.command({
        query: `
          INSERT INTO fraud_monitoring_logs (
            id, timestamp, ip_address, user_agent, source_type,
            publisher_id, website_id, zone_id, partner_id,
            fraud_reason, risk_score, country, device_type
          )
          VALUES (
            {id:UInt64}, {timestamp:DateTime}, {ipAddress:String}, {userAgent:String}, {sourceType:String},
            {publisherId:UInt32}, {websiteId:UInt32}, {zoneId:UInt32}, {partnerId:UInt32},
            {fraudReason:String}, {riskScore:UInt8}, {country:String}, {deviceType:String}
          )
        `,
        query_params: {
          id: fraudId,
          timestamp: now.toISOString().slice(0, 19).replace('T', ' '),
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          sourceType: data.sourceType,
          publisherId: data.publisherId || 0,
          websiteId: data.websiteId || 0,
          zoneId: data.zoneId || 0,
          partnerId: data.partnerId || 0,
          fraudReason: data.fraudReason,
          riskScore: data.riskScore,
          country: data.country || '',
          deviceType: data.deviceType || '',
        },
      });
    } catch (error) {
      console.error('Error logging fraud detection:', error);
    }
  }

  /**
   * Get recent fraud logs with pagination
   */
  static async getRecentFraudLogs(
    limit: number = 50,
    offset: number = 0,
    sourceType?: string,
    publisherId?: number
  ): Promise<FraudExclusion[]> {
    try {
      let whereClause = 'WHERE 1=1';
      const params: any = { limit, offset };

      if (sourceType) {
        whereClause += ' AND source_type = {sourceType:String}';
        params.sourceType = sourceType;
      }

      if (publisherId) {
        whereClause += ' AND publisher_id = {publisherId:UInt32}';
        params.publisherId = publisherId;
      }

      const result = await clickhouse.query({
        query: `
          SELECT
            id,
            timestamp,
            ip_address,
            user_agent,
            source_type,
            publisher_id,
            website_id,
            zone_id,
            partner_id,
            fraud_reason,
            risk_score,
            country,
            device_type,
            is_excluded,
            excluded_by,
            excluded_at
          FROM fraud_monitoring_logs
          ${whereClause}
          ORDER BY timestamp DESC
          LIMIT {limit:UInt32}
          OFFSET {offset:UInt32}
        `,
        query_params: params,
      });

      const data = await result.json();
      return (data.data || []) as FraudExclusion[];
    } catch (error) {
      console.error('Error getting fraud logs:', error);
      return [];
    }
  }

  /**
   * Get fraud statistics for dashboard (using main table directly)
   */
  static async getFraudStatistics(days: number = 7): Promise<any> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT
            source_type,
            fraud_reason,
            count() as total_blocked,
            avg(risk_score) as avg_risk_score,
            uniq(publisher_id) as affected_publishers,
            uniq(ip_address) as unique_ips
          FROM fraud_monitoring_logs
          WHERE timestamp >= now() - INTERVAL {days:UInt32} DAY
          GROUP BY source_type, fraud_reason
          ORDER BY total_blocked DESC
        `,
        query_params: { days },
      });

      const data = await result.json();

      // Return the data directly since we now get unique_ips in the same query
      return data.data || [];
    } catch (error) {
      console.error('Error getting fraud statistics:', error);
      return [];
    }
  }

  /**
   * Exclude fraud detection for specific criteria
   */
  static async excludeFraudDetection(
    fraudId: number,
    excludedBy: number,
    reason: string = 'Manual exclusion'
  ): Promise<boolean> {
    try {
      const now = new Date();

      await clickhouse.command({
        query: `
          ALTER TABLE fraud_monitoring_logs
          UPDATE
            is_excluded = 1,
            excluded_by = {excludedBy:UInt32},
            excluded_at = {excludedAt:DateTime}
          WHERE id = {fraudId:UInt64}
        `,
        query_params: {
          fraudId,
          excludedBy,
          excludedAt: now.toISOString().slice(0, 19).replace('T', ' '),
        },
      });

      return true;
    } catch (error) {
      console.error('Error excluding fraud detection:', error);
      return false;
    }
  }

  /**
   * Check if publisher should be excluded from fraud detection
   */
  static async isExcluded(
    ipAddress: string,
    publisherId?: number,
    websiteId?: number,
    zoneId?: number
  ): Promise<boolean> {
    try {
      // First check if publisher is globally excluded
      if (publisherId) {
        try {
          const publisherExclusionResult = await clickhouse.query({
            query: `
              SELECT count() as excluded_count
              FROM publisher_fraud_exclusions
              WHERE publisher_id = {publisherId:UInt32} AND is_active = 1
              LIMIT 1
            `,
            query_params: { publisherId },
          });

          const publisherExclusionData = await publisherExclusionResult.json();
          if (((publisherExclusionData.data[0] as any)?.excluded_count || 0) > 0) {
            return true; // Publisher is globally excluded
          }
        } catch (error) {
          // Table might not exist yet, continue with fraud detection
          console.log('publisher_fraud_exclusions table does not exist yet');
        }
      }

      // Then check if specific IP/criteria was manually excluded
      let whereClause = 'WHERE is_excluded = 1 AND ip_address = {ipAddress:String}';
      const params: any = { ipAddress };

      if (publisherId) {
        whereClause += ' AND publisher_id = {publisherId:UInt32}';
        params.publisherId = publisherId;
      }

      if (websiteId) {
        whereClause += ' AND website_id = {websiteId:UInt32}';
        params.websiteId = websiteId;
      }

      if (zoneId) {
        whereClause += ' AND zone_id = {zoneId:UInt32}';
        params.zoneId = zoneId;
      }

      const result = await clickhouse.query({
        query: `
          SELECT count() as excluded_count
          FROM fraud_monitoring_logs
          ${whereClause}
          LIMIT 1
        `,
        query_params: params,
      });

      const data = await result.json();
      return ((data.data[0] as any)?.excluded_count || 0) > 0;
    } catch (error) {
      console.error('Error checking fraud exclusion:', error);
      return false;
    }
  }

  /**
   * Get fraud statistics for publisher
   */
  static async getPublisherFraudStats(
    publisherId: number,
    days: number = 30
  ): Promise<any> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT
            fraud_reason,
            count() as total_blocked,
            avg(risk_score) as avg_risk_score,
            uniq(ip_address) as unique_ips,
            toDate(timestamp) as date
          FROM fraud_monitoring_logs
          WHERE publisher_id = {publisherId:UInt32}
            AND timestamp >= now() - INTERVAL {days:UInt32} DAY
          GROUP BY fraud_reason, date
          ORDER BY date DESC, total_blocked DESC
        `,
        query_params: { publisherId, days },
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting publisher fraud stats:', error);
      return [];
    }
  }

  /**
   * Get top fraud reasons
   */
  static async getTopFraudReasons(days: number = 7): Promise<any> {
    try {
      const result = await clickhouse.query({
        query: `
          SELECT
            fraud_reason,
            count() as total_blocked,
            avg(risk_score) as avg_risk_score,
            uniq(ip_address) as unique_ips
          FROM fraud_monitoring_logs
          WHERE timestamp >= now() - INTERVAL {days:UInt32} DAY
          GROUP BY fraud_reason
          ORDER BY total_blocked DESC
          LIMIT 10
        `,
        query_params: { days },
      });

      const data = await result.json();
      return data.data || [];
    } catch (error) {
      console.error('Error getting top fraud reasons:', error);
      return [];
    }
  }
}
