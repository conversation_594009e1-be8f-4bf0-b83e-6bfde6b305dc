import maxmind, { CityResponse, CountryResponse } from 'maxmind';
import { UAParser } from 'ua-parser-js';
import path from 'path';

interface DeviceInfo {
  device_type: string;
  os: string;
  browser: string;
  country: string;
  state: string;
  region: string; // Keep for backward compatibility
  city: string;
  connection_type: string;
}

class DeviceGeoDetector {
  private cityLookup: maxmind.Reader<CityResponse> | null = null;
  private countryLookup: maxmind.Reader<CountryResponse> | null = null;
  private initialized = false;

  async initialize() {
    if (this.initialized) return;

    try {
      // Initialize MaxMind databases
      const dataPath = path.join(process.cwd(), 'data');

      this.cityLookup = await maxmind.open<CityResponse>(
        path.join(dataPath, 'GeoLite2-City.mmdb')
      );

      this.countryLookup = await maxmind.open<CountryResponse>(
        path.join(dataPath, 'GeoLite2-Country.mmdb')
      );

      this.initialized = true;
      console.log('✅ MaxMind databases initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize MaxMind databases:', error);
      // Continue without geo data rather than failing
    }
  }

  private getDeviceType(userAgent: string): string {
    const ua = new UAParser(userAgent);
    const device = ua.getDevice();

    if (device.type === 'mobile') return 'Mobile';
    if (device.type === 'tablet') return 'Tablet';
    if (device.type === 'smarttv') return 'Smart TV';
    if (device.type === 'wearable') return 'Wearable';
    if (device.type === 'console') return 'Console';

    // Check for mobile indicators in user agent
    const mobileRegex = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    if (mobileRegex.test(userAgent)) return 'Mobile';

    return 'Desktop';
  }

  private getOS(userAgent: string): string {
    const ua = new UAParser(userAgent);
    const os = ua.getOS();

    if (os.name && os.version) {
      return `${os.name} ${os.version}`;
    } else if (os.name) {
      return os.name;
    }

    return 'Unknown';
  }

  private getBrowser(userAgent: string): string {
    const ua = new UAParser(userAgent);
    const browser = ua.getBrowser();

    if (browser.name && browser.version) {
      // Get major version only
      const majorVersion = browser.version.split('.')[0];
      return `${browser.name} ${majorVersion}`;
    } else if (browser.name) {
      return browser.name;
    }

    return 'Unknown';
  }

  private getGeoLocation(ip: string): { country: string; region: string; city: string } {
    const defaultGeo = { country: 'Unknown', region: 'Unknown', city: 'Unknown' };

    // Skip local/private IPs
    if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
      return defaultGeo;
    }

    try {
      // Try city lookup first (more detailed)
      if (this.cityLookup) {
        const cityData = this.cityLookup.get(ip);
        if (cityData) {
          return {
            country: cityData.country?.iso_code || 'Unknown',
            region: cityData.subdivisions?.[0]?.names?.en || 'Unknown',
            city: cityData.city?.names?.en || 'Unknown'
          };
        }
      }

      // Fallback to country lookup
      if (this.countryLookup) {
        const countryData = this.countryLookup.get(ip);
        if (countryData) {
          return {
            country: countryData.country?.iso_code || 'Unknown',
            region: 'Unknown',
            city: 'Unknown'
          };
        }
      }
    } catch (error) {
      console.error('Geo lookup error for IP', ip, ':', error);
    }

    return defaultGeo;
  }

  async detectDeviceAndGeo(ip: string, userAgent: string): Promise<DeviceInfo> {
    // Ensure databases are initialized
    await this.initialize();

    // Get device info from user agent
    const device_type = this.getDeviceType(userAgent);
    const os = this.getOS(userAgent);
    const browser = this.getBrowser(userAgent);

    // Get geo info from IP
    const { country, region, city } = this.getGeoLocation(ip);

    return {
      device_type,
      os,
      browser,
      country,
      state: region, // Use region as state (primary field)
      region, // Keep for backward compatibility
      city,
      connection_type: this.getConnectionType(userAgent)
    };
  }

  // Method to get connection type based on user agent and other factors
  getConnectionType(userAgent: string): string {
    // Basic connection type detection
    if (/Mobile|Android|iPhone|iPad/i.test(userAgent)) {
      return 'Mobile';
    }
    return 'Broadband';
  }

  // Method to detect if it's a bot/crawler
  isBot(userAgent: string): boolean {
    const botRegex = /bot|crawler|spider|scraper|facebookexternalhit|twitterbot|linkedinbot|whatsapp/i;
    return botRegex.test(userAgent);
  }
}

// Create singleton instance
const deviceGeoDetector = new DeviceGeoDetector();

export default deviceGeoDetector;
