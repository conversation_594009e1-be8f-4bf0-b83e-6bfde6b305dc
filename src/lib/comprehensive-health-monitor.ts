import os from 'os';
import fs from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';
import clickhouse from './clickhouse';
import { clickHouseBatchWriter } from './clickhouse-batch-writer';
import { cache } from './cache';
import { RabbitMQManager } from './queue-manager';
import FallbackManager from './fallback-manager';
import logger from './logger';

const execAsync = promisify(exec);

interface SystemMetrics {
  cpu: {
    usage: number;
    load: [number, number, number];
    cores: number;
    temperature: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    available: number;
    usage: number;
    swap: { total: number; used: number };
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
    io: { read: number; write: number; readOps: number; writeOps: number };
  };
  network: {
    rx: number;
    tx: number;
    connections: { active: number; total: number };
  };
  processes: {
    pm2: {
      instances: number;
      memory: number;
      cpu: number;
      uptime: number;
      restarts: number;
    };
  };
}

interface ApplicationMetrics {
  performance: {
    qps: { current: number; peak: number; average: number };
    responseTime: { avg: number; p95: number; p99: number };
  };
  queues: {
    impressions: { waiting: number; active: number; completed: number; failed: number };
    cost: { waiting: number; active: number; completed: number; failed: number };
    stats: { waiting: number; active: number; completed: number; failed: number };
  };
  database: {
    clickhouse: { connections: number; qps: number; slowQueries: number; errors: number };
    redis: { connections: number; memory: number; ops: number; hitRate: number };
  };
  business: {
    impressions: number;
    revenue: number;
    cost: number;
    profit: number;
    activeCampaigns: number;
    activeDsps: number;
    activeSsps: number;
  };
  errors: {
    errorRate: number;
    timeoutRate: number;
    fallbackMode: boolean;
    fallbackJobs: number;
  };
}

interface ServiceStatus {
  name: string;
  status: 'up' | 'down' | 'degraded' | 'maintenance';
  responseTime: number;
  uptime: number;
  version: string;
  port: number;
  endpoint: string;
  dependencies: boolean;
}

interface HealthAlert {
  type: 'info' | 'warning' | 'error' | 'critical';
  category: 'system' | 'application' | 'business' | 'security';
  title: string;
  message: string;
  source: string;
  severity: number;
  metricName?: string;
  metricValue?: number;
  thresholdValue?: number;
}

export class ComprehensiveHealthMonitor {
  private static serverId = process.env.SERVER_ID || 'rtb-server-1';
  private static metricsHistory: { system: SystemMetrics[]; app: ApplicationMetrics[] } = {
    system: [],
    app: [],
  };
  private static alerts: HealthAlert[] = [];
  private static isCollecting = false;

  /**
   * Collect comprehensive system metrics
   */
  static async collectSystemMetrics(): Promise<SystemMetrics> {
    try {
      const [cpuMetrics, memoryMetrics, diskMetrics, networkMetrics, processMetrics] = await Promise.all([
        this.getCpuMetrics(),
        this.getMemoryMetrics(),
        this.getDiskMetrics(),
        this.getNetworkMetrics(),
        this.getProcessMetrics(),
      ]);

      return {
        cpu: cpuMetrics,
        memory: memoryMetrics,
        disk: diskMetrics,
        network: networkMetrics,
        processes: processMetrics,
      };
    } catch (error) {
      logger.health.error('Failed to collect system metrics:', error);
      throw error;
    }
  }

  /**
   * Get CPU metrics
   */
  private static async getCpuMetrics() {
    const cpus = os.cpus();
    const load = os.loadavg();
    
    // Calculate CPU usage
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    });
    
    const usage = 100 - (totalIdle / totalTick) * 100;
    
    // Get CPU temperature (Linux only)
    let temperature = 0;
    try {
      const tempData = await fs.readFile('/sys/class/thermal/thermal_zone0/temp', 'utf8');
      temperature = parseInt(tempData) / 1000; // Convert from millidegrees
    } catch (error) {
      // Temperature not available on this system
    }

    return {
      usage: Math.round(usage * 100) / 100,
      load: load as [number, number, number],
      cores: cpus.length,
      temperature,
    };
  }

  /**
   * Get memory metrics
   */
  private static async getMemoryMetrics() {
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    
    // Get more detailed memory info (Linux)
    let availableMem = freeMem;
    let swapTotal = 0;
    let swapUsed = 0;
    
    try {
      const meminfo = await fs.readFile('/proc/meminfo', 'utf8');
      const lines = meminfo.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('MemAvailable:')) {
          availableMem = parseInt(line.split(/\s+/)[1]) * 1024; // Convert from KB
        } else if (line.startsWith('SwapTotal:')) {
          swapTotal = parseInt(line.split(/\s+/)[1]) * 1024;
        } else if (line.startsWith('SwapFree:')) {
          const swapFree = parseInt(line.split(/\s+/)[1]) * 1024;
          swapUsed = swapTotal - swapFree;
        }
      }
    } catch (error) {
      // Fallback for non-Linux systems
    }

    return {
      total: Math.round(totalMem / 1024 / 1024 / 1024 * 100) / 100, // GB
      used: Math.round(usedMem / 1024 / 1024 / 1024 * 100) / 100,
      free: Math.round(freeMem / 1024 / 1024 / 1024 * 100) / 100,
      available: Math.round(availableMem / 1024 / 1024 / 1024 * 100) / 100,
      usage: Math.round((usedMem / totalMem) * 100 * 100) / 100,
      swap: {
        total: Math.round(swapTotal / 1024 / 1024 / 1024 * 100) / 100,
        used: Math.round(swapUsed / 1024 / 1024 / 1024 * 100) / 100,
      },
    };
  }

  /**
   * Get disk metrics
   */
  private static async getDiskMetrics() {
    let diskStats = {
      total: 0,
      used: 0,
      free: 0,
      usage: 0,
      io: { read: 0, write: 0, readOps: 0, writeOps: 0 },
    };

    try {
      // Get disk usage
      const { stdout: dfOutput } = await execAsync('df -h / | tail -1');
      const dfParts = dfOutput.trim().split(/\s+/);
      
      if (dfParts.length >= 6) {
        diskStats.total = this.parseSize(dfParts[1]);
        diskStats.used = this.parseSize(dfParts[2]);
        diskStats.free = this.parseSize(dfParts[3]);
        diskStats.usage = parseInt(dfParts[4].replace('%', ''));
      }

      // Get disk I/O stats (Linux)
      try {
        const { stdout: iostatOutput } = await execAsync('iostat -d 1 2 | tail -n +4 | tail -1');
        const iostatParts = iostatOutput.trim().split(/\s+/);
        
        if (iostatParts.length >= 6) {
          diskStats.io = {
            read: parseFloat(iostatParts[2]) || 0,
            write: parseFloat(iostatParts[3]) || 0,
            readOps: parseFloat(iostatParts[4]) || 0,
            writeOps: parseFloat(iostatParts[5]) || 0,
          };
        }
      } catch (error) {
        // iostat not available
      }
    } catch (error) {
      logger.health.warn('Could not get disk metrics:', error);
    }

    return diskStats;
  }

  /**
   * Parse size string (e.g., "100G" -> 100)
   */
  private static parseSize(sizeStr: string): number {
    const size = parseFloat(sizeStr);
    const unit = sizeStr.slice(-1).toLowerCase();
    
    switch (unit) {
      case 'k': return size / 1024 / 1024; // Convert to GB
      case 'm': return size / 1024;
      case 'g': return size;
      case 't': return size * 1024;
      default: return size / 1024 / 1024 / 1024; // Assume bytes
    }
  }

  /**
   * Get network metrics
   */
  private static async getNetworkMetrics() {
    let networkStats = {
      rx: 0,
      tx: 0,
      connections: { active: 0, total: 0 },
    };

    try {
      // Get network I/O (Linux)
      const { stdout: netOutput } = await execAsync('cat /proc/net/dev | grep -E "(eth|ens|enp)" | head -1');
      const netParts = netOutput.trim().split(/\s+/);
      
      if (netParts.length >= 10) {
        networkStats.rx = Math.round(parseInt(netParts[1]) / 1024 / 1024 * 100) / 100; // MB
        networkStats.tx = Math.round(parseInt(netParts[9]) / 1024 / 1024 * 100) / 100; // MB
      }

      // Get connection count
      const { stdout: connOutput } = await execAsync('ss -s | grep TCP | head -1');
      const connMatch = connOutput.match(/(\d+)/g);
      if (connMatch && connMatch.length >= 2) {
        networkStats.connections.total = parseInt(connMatch[0]);
        networkStats.connections.active = parseInt(connMatch[1]);
      }
    } catch (error) {
      logger.health.warn('Could not get network metrics:', error);
    }

    return networkStats;
  }

  /**
   * Get PM2 process metrics
   */
  private static async getProcessMetrics() {
    let pm2Stats = {
      pm2: {
        instances: 0,
        memory: 0,
        cpu: 0,
        uptime: 0,
        restarts: 0,
      },
    };

    try {
      const { stdout: pm2Output } = await execAsync('pm2 jlist');
      const pm2Data = JSON.parse(pm2Output);
      
      if (Array.isArray(pm2Data)) {
        pm2Stats.pm2.instances = pm2Data.length;
        
        pm2Data.forEach((process: any) => {
          if (process.monit) {
            pm2Stats.pm2.memory += process.monit.memory || 0;
            pm2Stats.pm2.cpu += process.monit.cpu || 0;
          }
          if (process.pm2_env) {
            pm2Stats.pm2.uptime = Math.max(pm2Stats.pm2.uptime, process.pm2_env.pm_uptime || 0);
            pm2Stats.pm2.restarts += process.pm2_env.restart_time || 0;
          }
        });
        
        pm2Stats.pm2.memory = Math.round(pm2Stats.pm2.memory / 1024 / 1024 * 100) / 100; // MB
        pm2Stats.pm2.cpu = Math.round(pm2Stats.pm2.cpu * 100) / 100;
        pm2Stats.pm2.uptime = Math.round((Date.now() - pm2Stats.pm2.uptime) / 1000);
      }
    } catch (error) {
      logger.health.warn('Could not get PM2 metrics:', error);
    }

    return pm2Stats;
  }

  /**
   * Collect application-specific metrics
   */
  static async collectApplicationMetrics(): Promise<ApplicationMetrics> {
    try {
      const [queueStats, businessMetrics, errorMetrics] = await Promise.all([
        this.getQueueMetrics(),
        this.getBusinessMetrics(),
        this.getErrorMetrics(),
      ]);

      // Get performance metrics from cache
      const performanceMetrics = await this.getPerformanceMetrics();
      const databaseMetrics = await this.getDatabaseMetrics();

      return {
        performance: performanceMetrics,
        queues: queueStats,
        database: databaseMetrics,
        business: businessMetrics,
        errors: errorMetrics,
      };
    } catch (error) {
      logger.health.error('Failed to collect application metrics:', error);
      throw error;
    }
  }

  /**
   * Get queue metrics
   */
  private static async getQueueMetrics() {
    try {
      const queueStats = await RabbitMQManager.getQueueStats();

      const getQueueData = (name: string) => {
        const queue = queueStats.find(q => q.name === name);
        return queue ? {
          waiting: queue.messageCount || 0,
          active: queue.consumerCount || 0,
          completed: 0, // RabbitMQ doesn't track completed jobs
          failed: 0,    // RabbitMQ doesn't track failed jobs
        } : { waiting: 0, active: 0, completed: 0, failed: 0 };
      };

      return {
        impressions: getQueueData('impression-tracking'),
        cost: getQueueData('cost-processing'),
        stats: getQueueData('request-stats'),
      };
    } catch (error) {
      return {
        impressions: { waiting: 0, active: 0, completed: 0, failed: 0 },
        cost: { waiting: 0, active: 0, completed: 0, failed: 0 },
        stats: { waiting: 0, active: 0, completed: 0, failed: 0 },
      };
    }
  }

  /**
   * Get performance metrics
   */
  private static async getPerformanceMetrics() {
    const cached = await cache.get('performance_metrics');
    return cached || {
      qps: { current: 0, peak: 0, average: 0 },
      responseTime: { avg: 0, p95: 0, p99: 0 },
    };
  }

  /**
   * Get database metrics
   */
  private static async getDatabaseMetrics() {
    try {
      // ClickHouse metrics
      const chResult = await clickhouse.query({
        query: `
          SELECT
            count() as connections,
            0 as qps,
            0 as slow_queries,
            0 as errors
          FROM system.processes
        `,
      });
      const chData = await chResult.json();
      const chMetrics = chData.data[0] || { connections: 0, qps: 0, slow_queries: 0, errors: 0 };

      // Redis metrics
      let redisMetrics = { connections: 0, memory: 0, ops: 0, hitRate: 0 };
      try {
        const redisInfo = await cache.get('redis_info');
        redisMetrics = redisInfo || redisMetrics;
      } catch (error) {
        // Redis not available
      }

      return {
        clickhouse: {
          connections: chMetrics.connections,
          qps: chMetrics.qps,
          slowQueries: chMetrics.slow_queries,
          errors: chMetrics.errors,
        },
        redis: redisMetrics,
      };
    } catch (error) {
      return {
        clickhouse: { connections: 0, qps: 0, slowQueries: 0, errors: 0 },
        redis: { connections: 0, memory: 0, ops: 0, hitRate: 0 },
      };
    }
  }

  /**
   * Get business metrics
   */
  private static async getBusinessMetrics() {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);

      const result = await clickhouse.query({
        query: `
          SELECT
            count() as impressions,
            sum(cost_deducted) as cost,
            sum(publisher_revenue) as revenue,
            (sum(cost_deducted) - sum(publisher_revenue)) as profit
          FROM impressions
          WHERE timestamp >= {start:DateTime}
        `,
        query_params: {
          start: oneMinuteAgo.toISOString().slice(0, 19).replace('T', ' '),
        },
      });

      const data = await result.json();
      const metrics = data.data[0] || { impressions: 0, cost: 0, revenue: 0, profit: 0 };

      // Get active counts
      const countsResult = await clickhouse.query({
        query: `
          SELECT
            (SELECT count() FROM campaigns WHERE status = 'active') as campaigns,
            (SELECT count() FROM partner_endpoints WHERE type = 'dsp' AND status = 'active') as dsps,
            (SELECT count() FROM partner_endpoints WHERE type = 'ssp' AND status = 'active') as ssps
        `,
      });

      const countsData = await countsResult.json();
      const counts = countsData.data[0] || { campaigns: 0, dsps: 0, ssps: 0 };

      return {
        impressions: metrics.impressions,
        revenue: metrics.revenue || 0,
        cost: metrics.cost || 0,
        profit: metrics.profit || 0,
        activeCampaigns: counts.campaigns,
        activeDsps: counts.dsps,
        activeSsps: counts.ssps,
      };
    } catch (error) {
      return {
        impressions: 0,
        revenue: 0,
        cost: 0,
        profit: 0,
        activeCampaigns: 0,
        activeDsps: 0,
        activeSsps: 0,
      };
    }
  }

  /**
   * Get error metrics
   */
  private static async getErrorMetrics() {
    try {
      const fallbackStatus = await FallbackManager.getStatus();

      return {
        errorRate: 0, // TODO: Calculate from logs
        timeoutRate: 0, // TODO: Calculate from logs
        fallbackMode: !fallbackStatus.queueHealthy,
        fallbackJobs: fallbackStatus.bufferedJobs + fallbackStatus.fallbackFiles,
      };
    } catch (error) {
      return {
        errorRate: 0,
        timeoutRate: 0,
        fallbackMode: false,
        fallbackJobs: 0,
      };
    }
  }

  /**
   * Store metrics to database
   */
  static async storeMetrics(systemMetrics: SystemMetrics, appMetrics: ApplicationMetrics): Promise<void> {
    try {
      const timestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');
      const metricId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

      // Store system metrics
      await clickHouseBatchWriter.write('system_health_metrics', {
        id: metricId,
        timestamp,
        server_id: this.serverId,

        // CPU
        cpu_usage_percent: systemMetrics.cpu.usage,
        cpu_load_1min: systemMetrics.cpu.load[0],
        cpu_load_5min: systemMetrics.cpu.load[1],
        cpu_load_15min: systemMetrics.cpu.load[2],
        cpu_cores: systemMetrics.cpu.cores,
        cpu_temperature: systemMetrics.cpu.temperature,

        // Memory
        memory_total_gb: systemMetrics.memory.total,
        memory_used_gb: systemMetrics.memory.used,
        memory_free_gb: systemMetrics.memory.free,
        memory_usage_percent: systemMetrics.memory.usage,
        memory_available_gb: systemMetrics.memory.available,
        swap_total_gb: systemMetrics.memory.swap.total,
        swap_used_gb: systemMetrics.memory.swap.used,

        // Disk
        disk_total_gb: systemMetrics.disk.total,
        disk_used_gb: systemMetrics.disk.used,
        disk_free_gb: systemMetrics.disk.free,
        disk_usage_percent: systemMetrics.disk.usage,
        disk_io_read_mb: systemMetrics.disk.io.read,
        disk_io_write_mb: systemMetrics.disk.io.write,
        disk_iops_read: systemMetrics.disk.io.readOps,
        disk_iops_write: systemMetrics.disk.io.writeOps,

        // Network
        network_rx_mb: systemMetrics.network.rx,
        network_tx_mb: systemMetrics.network.tx,
        network_connections_active: systemMetrics.network.connections.active,
        network_connections_total: systemMetrics.network.connections.total,

        // PM2
        pm2_instances: systemMetrics.processes.pm2.instances,
        pm2_memory_usage_mb: systemMetrics.processes.pm2.memory,
        pm2_cpu_usage_percent: systemMetrics.processes.pm2.cpu,
        pm2_uptime_seconds: systemMetrics.processes.pm2.uptime,
        pm2_restarts: systemMetrics.processes.pm2.restarts,

        // App info
        app_version: process.env.npm_package_version || '1.0.0',
        node_version: process.version,
        npm_version: process.env.npm_version || 'unknown',

        // Health
        overall_health: this.calculateOverallHealth(systemMetrics, appMetrics),
        alerts_count: this.alerts.length,
      });

      // Store application metrics
      await clickHouseBatchWriter.write('app_health_metrics', {
        id: metricId + 1,
        timestamp,
        server_id: this.serverId,

        // Performance
        qps_current: appMetrics.performance.qps.current,
        qps_peak: appMetrics.performance.qps.peak,
        qps_average: appMetrics.performance.qps.average,
        response_time_avg_ms: appMetrics.performance.responseTime.avg,
        response_time_p95_ms: appMetrics.performance.responseTime.p95,
        response_time_p99_ms: appMetrics.performance.responseTime.p99,

        // Queues
        queue_impressions_waiting: appMetrics.queues.impressions.waiting,
        queue_impressions_active: appMetrics.queues.impressions.active,
        queue_impressions_completed: appMetrics.queues.impressions.completed,
        queue_impressions_failed: appMetrics.queues.impressions.failed,
        queue_cost_waiting: appMetrics.queues.cost.waiting,
        queue_cost_active: appMetrics.queues.cost.active,
        queue_cost_completed: appMetrics.queues.cost.completed,
        queue_cost_failed: appMetrics.queues.cost.failed,
        queue_stats_waiting: appMetrics.queues.stats.waiting,
        queue_stats_active: appMetrics.queues.stats.active,
        queue_stats_completed: appMetrics.queues.stats.completed,
        queue_stats_failed: appMetrics.queues.stats.failed,

        // Database
        clickhouse_connections: appMetrics.database.clickhouse.connections,
        clickhouse_queries_per_sec: appMetrics.database.clickhouse.qps,
        clickhouse_slow_queries: appMetrics.database.clickhouse.slowQueries,
        clickhouse_errors: appMetrics.database.clickhouse.errors,
        redis_connections: appMetrics.database.redis.connections,
        redis_memory_usage_mb: appMetrics.database.redis.memory,
        redis_ops_per_sec: appMetrics.database.redis.ops,
        redis_hit_rate_percent: appMetrics.database.redis.hitRate,

        // Business
        impressions_per_minute: appMetrics.business.impressions,
        revenue_per_minute: appMetrics.business.revenue,
        cost_per_minute: appMetrics.business.cost,
        profit_per_minute: appMetrics.business.profit,
        active_campaigns: appMetrics.business.activeCampaigns,
        active_dsps: appMetrics.business.activeDsps,
        active_ssps: appMetrics.business.activeSsps,

        // Errors
        error_rate_percent: appMetrics.errors.errorRate,
        timeout_rate_percent: appMetrics.errors.timeoutRate,
        fallback_mode: appMetrics.errors.fallbackMode,
        fallback_jobs_pending: appMetrics.errors.fallbackJobs,
      });

    } catch (error) {
      logger.health.error('Failed to store health metrics:', error);
    }
  }

  /**
   * Calculate overall health status
   */
  private static calculateOverallHealth(systemMetrics: SystemMetrics, appMetrics: ApplicationMetrics): number {
    let score = 1; // Start with healthy

    // Check critical thresholds
    if (systemMetrics.cpu.usage > 90) score = Math.max(score, 3); // Critical
    if (systemMetrics.memory.usage > 95) score = Math.max(score, 3); // Critical
    if (systemMetrics.disk.usage > 95) score = Math.max(score, 3); // Critical
    if (appMetrics.errors.fallbackMode) score = Math.max(score, 3); // Critical

    // Check warning thresholds
    if (systemMetrics.cpu.usage > 80) score = Math.max(score, 2); // Warning
    if (systemMetrics.memory.usage > 85) score = Math.max(score, 2); // Warning
    if (systemMetrics.disk.usage > 85) score = Math.max(score, 2); // Warning
    if (appMetrics.errors.errorRate > 5) score = Math.max(score, 2); // Warning

    return score;
  }

  /**
   * Start continuous monitoring
   */
  static startMonitoring(intervalMs: number = 30000): void {
    if (this.isCollecting) return;

    this.isCollecting = true;
    logger.health.info(`🏥 Starting comprehensive health monitoring (${intervalMs}ms interval)`);

    const collectAndStore = async () => {
      try {
        const [systemMetrics, appMetrics] = await Promise.all([
          this.collectSystemMetrics(),
          this.collectApplicationMetrics(),
        ]);

        // Store to database
        await this.storeMetrics(systemMetrics, appMetrics);

        // Store in memory for quick access
        this.metricsHistory.system.push(systemMetrics);
        this.metricsHistory.app.push(appMetrics);

        // Keep only last 100 entries
        if (this.metricsHistory.system.length > 100) {
          this.metricsHistory.system.shift();
          this.metricsHistory.app.shift();
        }

        // Cache latest metrics
        await cache.set('latest_health_metrics', {
          system: systemMetrics,
          app: appMetrics,
          timestamp: new Date().toISOString(),
        }, 60);

        // Generate alerts
        await this.generateAlerts(systemMetrics, appMetrics);

      } catch (error) {
        logger.health.error('Health monitoring collection failed:', error);
      }
    };

    // Initial collection
    collectAndStore();

    // Set up interval
    setInterval(collectAndStore, intervalMs);
  }

  /**
   * Generate health alerts
   */
  private static async generateAlerts(systemMetrics: SystemMetrics, appMetrics: ApplicationMetrics): Promise<void> {
    const newAlerts: HealthAlert[] = [];

    // System alerts
    if (systemMetrics.cpu.usage > 90) {
      newAlerts.push({
        type: 'critical',
        category: 'system',
        title: 'Critical CPU Usage',
        message: `CPU usage is at ${systemMetrics.cpu.usage.toFixed(1)}%`,
        source: 'cpu',
        severity: 9,
        metricName: 'cpu_usage',
        metricValue: systemMetrics.cpu.usage,
        thresholdValue: 90,
      });
    }

    if (systemMetrics.memory.usage > 95) {
      newAlerts.push({
        type: 'critical',
        category: 'system',
        title: 'Critical Memory Usage',
        message: `Memory usage is at ${systemMetrics.memory.usage.toFixed(1)}%`,
        source: 'memory',
        severity: 9,
        metricName: 'memory_usage',
        metricValue: systemMetrics.memory.usage,
        thresholdValue: 95,
      });
    }

    if (systemMetrics.disk.usage > 95) {
      newAlerts.push({
        type: 'critical',
        category: 'system',
        title: 'Critical Disk Usage',
        message: `Disk usage is at ${systemMetrics.disk.usage}%`,
        source: 'disk',
        severity: 9,
        metricName: 'disk_usage',
        metricValue: systemMetrics.disk.usage,
        thresholdValue: 95,
      });
    }

    // Application alerts
    if (appMetrics.errors.fallbackMode) {
      newAlerts.push({
        type: 'critical',
        category: 'application',
        title: 'Fallback Mode Active',
        message: 'Queue system is down, using fallback processing',
        source: 'queue',
        severity: 8,
      });
    }

    if (appMetrics.errors.errorRate > 10) {
      newAlerts.push({
        type: 'error',
        category: 'application',
        title: 'High Error Rate',
        message: `Error rate is ${appMetrics.errors.errorRate.toFixed(1)}%`,
        source: 'application',
        severity: 7,
        metricName: 'error_rate',
        metricValue: appMetrics.errors.errorRate,
        thresholdValue: 10,
      });
    }

    // Store new alerts
    for (const alert of newAlerts) {
      await this.storeAlert(alert);
    }

    this.alerts = [...this.alerts, ...newAlerts].slice(-50); // Keep last 50 alerts
  }

  /**
   * Store alert to database
   */
  private static async storeAlert(alert: HealthAlert): Promise<void> {
    try {
      const alertId = Date.now() * 1000 + Math.floor(Math.random() * 1000);

      await clickHouseBatchWriter.write('health_alerts', {
        id: alertId,
        timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '),
        server_id: this.serverId,
        alert_type: alert.type,
        alert_category: alert.category,
        alert_title: alert.title,
        alert_message: alert.message,
        alert_source: alert.source,
        status: 'active',
        severity: alert.severity,
        metric_name: alert.metricName || '',
        metric_value: alert.metricValue || 0,
        threshold_value: alert.thresholdValue || 0,
      });

      logger.health.warn(`🚨 Health Alert: ${alert.title} - ${alert.message}`);
    } catch (error) {
      logger.health.error('Failed to store alert:', error);
    }
  }

  /**
   * Get current health status
   */
  static async getCurrentHealth(): Promise<{
    system: SystemMetrics;
    app: ApplicationMetrics;
    alerts: HealthAlert[];
    overall: 'healthy' | 'warning' | 'critical' | 'down';
  }> {
    try {
      const cached = await cache.get('latest_health_metrics');

      if (cached) {
        return {
          system: cached.system,
          app: cached.app,
          alerts: this.alerts,
          overall: this.getOverallStatus(cached.system, cached.app),
        };
      }

      // Fallback to fresh collection
      const [systemMetrics, appMetrics] = await Promise.all([
        this.collectSystemMetrics(),
        this.collectApplicationMetrics(),
      ]);

      return {
        system: systemMetrics,
        app: appMetrics,
        alerts: this.alerts,
        overall: this.getOverallStatus(systemMetrics, appMetrics),
      };
    } catch (error) {
      logger.health.error('Failed to get current health:', error);
      throw error;
    }
  }

  /**
   * Get overall status string
   */
  private static getOverallStatus(systemMetrics: SystemMetrics, appMetrics: ApplicationMetrics): 'healthy' | 'warning' | 'critical' | 'down' {
    const healthScore = this.calculateOverallHealth(systemMetrics, appMetrics);

    switch (healthScore) {
      case 1: return 'healthy';
      case 2: return 'warning';
      case 3: return 'critical';
      case 4: return 'down';
      default: return 'healthy';
    }
  }
}

export default ComprehensiveHealthMonitor;
