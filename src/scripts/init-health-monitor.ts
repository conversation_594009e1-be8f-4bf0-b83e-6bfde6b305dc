#!/usr/bin/env tsx

/**
 * Initialize Comprehensive Health Monitoring System
 * Sets up monitoring, creates initial data, and starts collection
 */

import ComprehensiveHealthMonitor from '../lib/comprehensive-health-monitor';
import clickhouse from '../lib/clickhouse';
import logger from '../lib/logger';

async function initializeHealthMonitor() {
  try {
    console.log('🏥 Initializing Comprehensive Health Monitoring System...');

    // Test database connectivity
    console.log('📊 Testing database connectivity...');
    try {
      await clickhouse.ping();
      console.log('✅ ClickHouse connection successful');
    } catch (error) {
      console.error('❌ ClickHouse connection failed:', error);
      throw error;
    }

    // Collect initial metrics
    console.log('📈 Collecting initial health metrics...');
    const [systemMetrics, appMetrics] = await Promise.all([
      ComprehensiveHealthMonitor.collectSystemMetrics(),
      ComprehensiveHealthMonitor.collectApplicationMetrics(),
    ]);

    console.log('📊 Initial System Metrics:');
    console.log(`  CPU Usage: ${systemMetrics.cpu.usage.toFixed(1)}%`);
    console.log(`  Memory Usage: ${systemMetrics.memory.usage.toFixed(1)}% (${systemMetrics.memory.used.toFixed(2)}GB / ${systemMetrics.memory.total.toFixed(2)}GB)`);
    console.log(`  Disk Usage: ${systemMetrics.disk.usage}% (${systemMetrics.disk.used.toFixed(2)}GB / ${systemMetrics.disk.total.toFixed(2)}GB)`);
    console.log(`  Network: RX ${systemMetrics.network.rx.toFixed(2)}MB, TX ${systemMetrics.network.tx.toFixed(2)}MB`);
    console.log(`  PM2 Instances: ${systemMetrics.processes.pm2.instances}`);

    console.log('\n📱 Initial Application Metrics:');
    console.log(`  Current QPS: ${appMetrics.performance.qps.current}`);
    console.log(`  Response Time: ${appMetrics.performance.responseTime.avg.toFixed(1)}ms`);
    console.log(`  Queue Status:`);
    console.log(`    Impressions: ${appMetrics.queues.impressions.waiting}W/${appMetrics.queues.impressions.active}A/${appMetrics.queues.impressions.completed}C/${appMetrics.queues.impressions.failed}F`);
    console.log(`    Cost: ${appMetrics.queues.cost.waiting}W/${appMetrics.queues.cost.active}A/${appMetrics.queues.cost.completed}C/${appMetrics.queues.cost.failed}F`);
    console.log(`    Stats: ${appMetrics.queues.stats.waiting}W/${appMetrics.queues.stats.active}A/${appMetrics.queues.stats.completed}C/${appMetrics.queues.stats.failed}F`);
    console.log(`  Business Metrics:`);
    console.log(`    Impressions/min: ${appMetrics.business.impressions}`);
    console.log(`    Revenue/min: $${appMetrics.business.revenue.toFixed(4)}`);
    console.log(`    Active Campaigns: ${appMetrics.business.activeCampaigns}`);
    console.log(`    Active DSPs: ${appMetrics.business.activeDsps}`);
    console.log(`    Active SSPs: ${appMetrics.business.activeSsps}`);

    // Store initial metrics
    console.log('\n💾 Storing initial metrics to database...');
    await ComprehensiveHealthMonitor.storeMetrics(systemMetrics, appMetrics);
    console.log('✅ Initial metrics stored successfully');

    // Start continuous monitoring
    console.log('\n🔄 Starting continuous health monitoring...');
    ComprehensiveHealthMonitor.startMonitoring(30000); // 30 second intervals
    console.log('✅ Continuous monitoring started (30s intervals)');

    // Test health status endpoint
    console.log('\n🔍 Testing health status...');
    const currentHealth = await ComprehensiveHealthMonitor.getCurrentHealth();
    console.log(`📊 Overall Health Status: ${currentHealth.overall.toUpperCase()}`);
    console.log(`🚨 Active Alerts: ${currentHealth.alerts.length}`);

    if (currentHealth.alerts.length > 0) {
      console.log('\n⚠️ Current Alerts:');
      currentHealth.alerts.forEach((alert, index) => {
        console.log(`  ${index + 1}. [${alert.type.toUpperCase()}] ${alert.title}: ${alert.message}`);
      });
    }

    // Display monitoring URLs
    console.log('\n🌐 Health Monitoring URLs:');
    console.log(`  Admin Dashboard: http://localhost:3102/admin/health-monitor`);
    console.log(`  API Endpoints:`);
    console.log(`    Current Health: http://localhost:3102/api/admin/health-monitor?type=current`);
    console.log(`    Historical Data: http://localhost:3102/api/admin/health-monitor?type=historical&timeframe=1h`);
    console.log(`    Active Alerts: http://localhost:3102/api/admin/health-monitor?type=alerts`);
    console.log(`    Service Status: http://localhost:3102/api/admin/health-monitor?type=services`);
    console.log(`    Health Summary: http://localhost:3102/api/admin/health-monitor?type=summary`);

    // Display thresholds and recommendations
    console.log('\n📋 Monitoring Thresholds:');
    console.log(`  🔴 Critical: CPU >90%, Memory >95%, Disk >95%, Error Rate >10%`);
    console.log(`  🟡 Warning: CPU >80%, Memory >85%, Disk >85%, Error Rate >5%`);
    console.log(`  🟢 Healthy: All metrics below warning thresholds`);

    console.log('\n💡 Recommendations:');
    console.log(`  • Monitor the dashboard regularly for system health`);
    console.log(`  • Set up alerts for critical thresholds`);
    console.log(`  • Review historical data for performance trends`);
    console.log(`  • Check queue status for processing bottlenecks`);
    console.log(`  • Monitor business metrics for revenue optimization`);

    console.log('\n🎯 Monitoring Features:');
    console.log(`  ✅ Real-time system metrics (CPU, Memory, Disk, Network)`);
    console.log(`  ✅ Application performance metrics (QPS, Response Time)`);
    console.log(`  ✅ Queue health monitoring (Impressions, Cost, Stats)`);
    console.log(`  ✅ Business metrics tracking (Revenue, Impressions, Profit)`);
    console.log(`  ✅ Service status monitoring (API, ClickHouse, Redis, Queues)`);
    console.log(`  ✅ Automated alerting system with severity levels`);
    console.log(`  ✅ Historical data storage with 30-day retention`);
    console.log(`  ✅ Web-based admin dashboard with auto-refresh`);
    console.log(`  ✅ RESTful API for external monitoring integration`);

    console.log('\n🚀 Health Monitoring System initialized successfully!');
    console.log('📊 Data collection running every 30 seconds');
    console.log('🌐 Access the dashboard at: http://localhost:3102/admin/health-monitor');

  } catch (error) {
    console.error('❌ Failed to initialize health monitoring:', error);
    process.exit(1);
  }
}

// Command line options
const args = process.argv.slice(2);
const options = {
  verbose: args.includes('--verbose'),
  test: args.includes('--test'),
  interval: args.includes('--fast') ? 10000 : 30000, // 10s or 30s intervals
};

if (options.test) {
  console.log('🧪 Test mode - will collect metrics once and exit');
}

if (options.verbose) {
  console.log('📝 Verbose mode enabled');
}

// Run if called directly
if (require.main === module) {
  if (options.test) {
    // Test mode - collect once and exit
    (async () => {
      try {
        const [systemMetrics, appMetrics] = await Promise.all([
          ComprehensiveHealthMonitor.collectSystemMetrics(),
          ComprehensiveHealthMonitor.collectApplicationMetrics(),
        ]);
        
        console.log('🧪 Test collection successful:');
        console.log('System:', JSON.stringify(systemMetrics, null, 2));
        console.log('Application:', JSON.stringify(appMetrics, null, 2));
        process.exit(0);
      } catch (error) {
        console.error('🧪 Test collection failed:', error);
        process.exit(1);
      }
    })();
  } else {
    // Normal initialization
    initializeHealthMonitor()
      .then(() => {
        console.log('✅ Health monitoring initialization complete!');
        // Keep process running for continuous monitoring
        console.log('🔄 Monitoring will continue running...');
        console.log('Press Ctrl+C to stop monitoring');
      })
      .catch((error) => {
        console.error('💥 Health monitoring initialization failed:', error);
        process.exit(1);
      });
  }
}

export default initializeHealthMonitor;
