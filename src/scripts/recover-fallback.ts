#!/usr/bin/env tsx

/**
 * Fallback Recovery Script
 * Processes stored fallback jobs when the queue system recovers
 */

import FallbackManager from '../lib/fallback-manager';
import QueueHealthMonitor from '../lib/queue-health-monitor';
import logger from '../lib/logger';

async function recoverFallbackJobs() {
  try {
    console.log('🔄 Starting fallback recovery process...');

    // Check current system health
    const healthCheck = await QueueHealthMonitor.performHealthCheck();
    console.log('📊 Current System Health:');
    console.log(`  Queue Health: ${healthCheck.healthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    
    if (healthCheck.alerts.length > 0) {
      console.log('🚨 Active Alerts:');
      healthCheck.alerts.forEach(alert => console.log(`  ${alert}`));
    }

    if (healthCheck.recommendations.length > 0) {
      console.log('💡 Recommendations:');
      healthCheck.recommendations.forEach(rec => console.log(`  ${rec}`));
    }

    // Get fallback status
    const fallbackStatus = await FallbackManager.getStatus();
    console.log('\n📁 Fallback Status:');
    console.log(`  Queue Healthy: ${fallbackStatus.queueHealthy ? '✅' : '❌'}`);
    console.log(`  Buffered Jobs: ${fallbackStatus.bufferedJobs}`);
    console.log(`  Fallback Files: ${fallbackStatus.fallbackFiles}`);
    console.log(`  Processing: ${fallbackStatus.processingFallback ? '🔄' : '⏸️'}`);

    if (!fallbackStatus.queueHealthy) {
      console.log('\n⚠️ Queue system is still unhealthy. Attempting to restore...');
      
      // Force health check
      const isHealthy = await FallbackManager.checkQueueHealth();
      if (!isHealthy) {
        console.log('❌ Queue system could not be restored. Manual intervention required.');
        console.log('\n🔧 Troubleshooting Steps:');
        console.log('  1. Check Redis server status: redis-cli ping');
        console.log('  2. Check Redis connectivity: telnet localhost 6379');
        console.log('  3. Restart Redis service: sudo systemctl restart redis');
        console.log('  4. Check Redis logs: sudo journalctl -u redis');
        console.log('  5. Restart PM2 processes: npm run reload');
        process.exit(1);
      }
    }

    // Process fallback jobs if any exist
    if (fallbackStatus.bufferedJobs > 0 || fallbackStatus.fallbackFiles > 0) {
      console.log('\n🔄 Processing fallback jobs...');
      await FallbackManager.processFallbackJobs();
      
      // Check status after processing
      const newStatus = await FallbackManager.getStatus();
      console.log('\n✅ Fallback processing completed:');
      console.log(`  Buffered Jobs: ${fallbackStatus.bufferedJobs} → ${newStatus.bufferedJobs}`);
      console.log(`  Fallback Files: ${fallbackStatus.fallbackFiles} → ${newStatus.fallbackFiles}`);
      
      if (newStatus.bufferedJobs === 0 && newStatus.fallbackFiles === 0) {
        console.log('🎉 All fallback jobs processed successfully!');
      } else {
        console.log('⚠️ Some fallback jobs remain. Check logs for errors.');
      }
    } else {
      console.log('\n✅ No fallback jobs to process.');
    }

    // Generate final health report
    console.log('\n📊 Final Health Report:');
    const finalReport = await QueueHealthMonitor.generateHealthReport();
    
    console.log(`  System Status: ${finalReport.alerts.length === 0 ? '✅ Healthy' : '⚠️ Degraded'}`);
    console.log(`  Uptime: ${finalReport.trends.uptimePercentage.toFixed(1)}%`);
    console.log(`  Avg Processing Rate: ${finalReport.trends.avgProcessingRate.toFixed(0)} jobs/min`);
    console.log(`  Avg Error Rate: ${(finalReport.trends.avgErrorRate * 100).toFixed(2)}%`);

    if (finalReport.alerts.length > 0) {
      console.log('\n🚨 Remaining Alerts:');
      finalReport.alerts.forEach(alert => console.log(`  ${alert}`));
    }

    if (finalReport.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      finalReport.recommendations.forEach(rec => console.log(`  ${rec}`));
    }

    console.log('\n🎯 Recovery process completed successfully!');

  } catch (error) {
    console.error('❌ Fallback recovery failed:', error);
    process.exit(1);
  }
}

// Command line options
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force'),
  verbose: args.includes('--verbose'),
  dryRun: args.includes('--dry-run'),
};

if (options.dryRun) {
  console.log('🔍 Dry run mode - no actual processing will occur');
}

if (options.verbose) {
  console.log('📝 Verbose mode enabled');
}

// Run if called directly
if (require.main === module) {
  recoverFallbackJobs()
    .then(() => {
      console.log('✅ Fallback recovery completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fallback recovery failed:', error);
      process.exit(1);
    });
}

export default recoverFallbackJobs;
