#!/usr/bin/env tsx

/**
 * Initialize RabbitMQ-style queues using Bull for RTB platform
 * This script sets up all the necessary queues and processors
 */

import '../lib/queue-processors'; // Initialize all processors
import { QueueManager } from '../lib/queue-manager';
import logger from '../lib/logger';

async function initializeQueues() {
  try {
    console.log('🚀 Initializing RTB Queue System...');

    // Get initial queue stats
    const stats = await QueueManager.getQueueStats();
    console.log('📊 Initial Queue Statistics:');
    stats.forEach(stat => {
      console.log(`  ${stat.name}: ${stat.waiting} waiting, ${stat.active} active, ${stat.completed} completed, ${stat.failed} failed`);
    });

    // Clean old completed jobs
    await QueueManager.cleanAllQueues();
    console.log('🧹 Cleaned old queue jobs');

    // Resume all queues (in case they were paused)
    await QueueManager.resumeAllQueues();
    console.log('▶️ All queues resumed and ready');

    console.log('✅ RTB Queue System initialized successfully!');
    console.log('');
    console.log('📋 Queue Priorities:');
    console.log('  🔥 Priority 10: Impression Tracking & Cost Processing');
    console.log('  🎯 Priority 7-9: Win Notifications');
    console.log('  📊 Priority 5-6: Request Stats & Transactions');
    console.log('  📦 Priority 1-3: Batch Processing & Cleanup');
    console.log('');
    console.log('🎯 Expected QPS Improvement: 5-8x increase');
    console.log('💡 Monitor queues with: npm run monitor:queues');

  } catch (error) {
    console.error('❌ Failed to initialize queues:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initializeQueues()
    .then(() => {
      console.log('🎉 Queue initialization complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Queue initialization failed:', error);
      process.exit(1);
    });
}

export default initializeQueues;
