#!/usr/bin/env tsx

/**
 * Test script for the enhanced logger system
 * Tests hourly rotation and file management
 */

import logger from '../lib/logger';

async function testLogger() {
  console.log('🧪 Testing Enhanced Logger System');
  console.log('==================================');
  
  // Test all logger categories
  const categories = [
    'auction', 'dsp', 'ssp', 'admin', 'app', 'api', 'db',
    'fraud', 'campaign', 'user', 'tracking', 'cache', 
    'queue', 'health', 'circuitBreaker', 'test'
  ];
  
  console.log(`📝 Testing ${categories.length} logger categories...`);
  
  // Write test messages to all loggers
  categories.forEach(category => {
    const categoryLogger = (logger as any)[category];
    if (categoryLogger && typeof categoryLogger.info === 'function') {
      categoryLogger.info(`Test message for ${category} logger`, { 
        timestamp: new Date().toISOString(),
        testData: { category, messageId: Math.random() }
      });
      categoryLogger.warn(`Test warning for ${category} logger`);
      categoryLogger.error(`Test error for ${category} logger`);
      categoryLogger.debug(`Test debug for ${category} logger`);
    }
  });
  
  console.log('✅ Test messages written to all loggers');
  
  // Get log statistics
  const stats = logger.getLogStats();
  console.log('\n📊 Current Log Statistics:');
  console.log('===========================');
  
  Object.entries(stats).forEach(([category, stat]) => {
    const ageMinutes = Math.floor(stat.age / (60 * 1000));
    const ageHours = Math.floor(ageMinutes / 60);
    const sizeKB = Math.round(stat.size / 1024);
    
    console.log(`${category}.log: ${sizeKB}KB, Age: ${ageHours}h ${ageMinutes % 60}m`);
  });
  
  // Test manual rotation
  console.log('\n🔄 Testing manual rotation...');
  logger.rotateAllLogs();
  console.log('✅ Manual rotation completed');
  
  // Get updated statistics
  const newStats = logger.getLogStats();
  console.log('\n📊 Post-Rotation Statistics:');
  console.log('=============================');
  
  Object.entries(newStats).forEach(([category, stat]) => {
    const sizeKB = Math.round(stat.size / 1024);
    console.log(`${category}.log: ${sizeKB}KB (should be 0KB after rotation)`);
  });
  
  console.log('\n🎯 Logger Test Summary:');
  console.log('=======================');
  console.log(`✅ Log Directory: ${logger.logDir}`);
  console.log(`✅ Rotation Interval: ${logger.rotationInterval / (60 * 60 * 1000)} hours`);
  console.log(`✅ Active Loggers: ${Object.keys(stats).length}`);
  console.log(`✅ Hourly rotation system: Active`);
  console.log(`✅ File truncation: Working`);
  
  console.log('\n🚀 Enhanced Logger System Test Complete!');
}

if (require.main === module) {
  testLogger().catch(console.error);
}

export default testLogger;
