#!/usr/bin/env tsx

/**
 * Test script to verify environment variable loading
 */

// Load environment variables from multiple sources (same as ecosystem.config.js)
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });
require('dotenv').config(); // Load from system environment

console.log('🔧 Environment Variable Test');
console.log('============================');

// Core settings
console.log('\n📋 Core Application Settings:');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`PORT: ${process.env.PORT}`);

// Database settings
console.log('\n🗄️ Database Settings:');
console.log(`CLICKHOUSE_HOST: ${process.env.CLICKHOUSE_HOST}`);
console.log(`CLICKHOUSE_USER: ${process.env.CLICKHOUSE_USER}`);
console.log(`CLICKHOUSE_PASSWORD: ${process.env.CLICKHOUSE_PASSWORD ? '***SET***' : 'NOT SET'}`);
console.log(`CLICKHOUSE_DATABASE: ${process.env.CLICKHOUSE_DATABASE}`);

// Redis settings
console.log('\n🔴 Redis Settings:');
console.log(`REDIS_HOST: ${process.env.REDIS_HOST}`);
console.log(`REDIS_PORT: ${process.env.REDIS_PORT}`);
console.log(`REDIS_PASSWORD: ${process.env.REDIS_PASSWORD ? '***SET***' : 'NOT SET'}`);

// RabbitMQ settings
console.log('\n🐰 RabbitMQ Settings:');
console.log(`RABBITMQ_URL: ${process.env.RABBITMQ_URL}`);
console.log(`RABBITMQ_HOST: ${process.env.RABBITMQ_HOST}`);
console.log(`RABBITMQ_PORT: ${process.env.RABBITMQ_PORT}`);
console.log(`RABBITMQ_USERNAME: ${process.env.RABBITMQ_USERNAME || process.env.RABBITMQ_USER}`);
console.log(`RABBITMQ_PASSWORD: ${process.env.RABBITMQ_PASSWORD ? '***SET***' : 'NOT SET'}`);
console.log(`RABBITMQ_VHOST: ${process.env.RABBITMQ_VHOST}`);
console.log(`RABBITMQ_HEARTBEAT: ${process.env.RABBITMQ_HEARTBEAT}`);
console.log(`RABBITMQ_CONNECTION_TIMEOUT: ${process.env.RABBITMQ_CONNECTION_TIMEOUT}`);

// Performance settings
console.log('\n⚡ Performance Settings:');
console.log(`UV_THREADPOOL_SIZE: ${process.env.UV_THREADPOOL_SIZE}`);
console.log(`NODE_OPTIONS: ${process.env.NODE_OPTIONS}`);
console.log(`MAX_CONCURRENT_REQUESTS: ${process.env.MAX_CONCURRENT_REQUESTS}`);
console.log(`KEEP_ALIVE_TIMEOUT: ${process.env.KEEP_ALIVE_TIMEOUT}`);

// RTB settings
console.log('\n🎯 RTB Settings:');
console.log(`RTB_TIMEOUT: ${process.env.RTB_TIMEOUT}`);
console.log(`SSP_TIMEOUT: ${process.env.SSP_TIMEOUT}`);
console.log(`DSP_TIMEOUT: ${process.env.DSP_TIMEOUT}`);

// Optimization flags
console.log('\n🚀 Optimization Flags:');
console.log(`ENABLE_HIGH_PERFORMANCE_CACHE: ${process.env.ENABLE_HIGH_PERFORMANCE_CACHE}`);
console.log(`ENABLE_CONNECTION_POOLING: ${process.env.ENABLE_CONNECTION_POOLING}`);
console.log(`ENABLE_QUEUE_PROCESSING: ${process.env.ENABLE_QUEUE_PROCESSING}`);
console.log(`ENABLE_HOURLY_LOG_ROTATION: ${process.env.ENABLE_HOURLY_LOG_ROTATION}`);

// Health monitoring
console.log('\n🏥 Health Monitoring:');
console.log(`HEALTH_MONITOR_ENABLED: ${process.env.HEALTH_MONITOR_ENABLED}`);
console.log(`HEALTH_MONITOR_INTERVAL: ${process.env.HEALTH_MONITOR_INTERVAL}`);
console.log(`HEALTH_ALERT_THRESHOLDS: ${process.env.HEALTH_ALERT_THRESHOLDS}`);

// NextAuth settings
console.log('\n🔐 Authentication Settings:');
console.log(`NEXTAUTH_URL: ${process.env.NEXTAUTH_URL}`);
console.log(`NEXTAUTH_SECRET: ${process.env.NEXTAUTH_SECRET ? '***SET***' : 'NOT SET'}`);

// Payment settings
console.log('\n💳 Payment Settings:');
console.log(`STRIPE_SECRET_KEY: ${process.env.STRIPE_SECRET_KEY ? '***SET***' : 'NOT SET'}`);
console.log(`STRIPE_PUBLISHABLE_KEY: ${process.env.STRIPE_PUBLISHABLE_KEY ? '***SET***' : 'NOT SET'}`);

console.log('\n✅ Environment Variable Test Complete!');
console.log('\n💡 Tips:');
console.log('- Make sure .env.local exists with your local settings');
console.log('- Check that sensitive values are properly set');
console.log('- Verify performance settings match your server specs');
