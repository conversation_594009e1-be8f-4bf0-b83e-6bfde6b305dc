import clickhouse from '@/lib/clickhouse';

async function seedProductionData() {
  try {
    console.log('🚀 Setting up production environment...');

    // Create admin user
    console.log('📝 Creating admin user...');
    await clickhouse.insert({
      table: 'users',
      values: [{
        id: 2,
        email: '<EMAIL>',
        password: '$2a$12$fxUSuiNwLUfNK0q2NB/cVOoH2jXUPV2JbcTfO9BGP757WrAS3IzKG', // password123
        full_name: 'Global Ads Media',
        address: '66 West Flagler Street',
        city: 'Miami',
        zip: '33130',
        state: 'FL',
        country: 'US',
        role: 'admin',
        status: 'active',
        balance: 0,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      }],
      format: 'JSONEachRow',
    });

    console.log('✅ Admin user created: <EMAIL> / password123');

    // Seed email templates
    console.log('📧 Setting up email templates...');
    await seedEmailTemplates();

    console.log('🎉 Production setup completed successfully!');

  } catch (error) {
    console.error('❌ Failed to setup production environment:', error);
  }
}

async function seedEmailTemplates() {
  try {
    const templates = [
      {
        id: Date.now() * 1000 + 1,
        name: 'Email Verification',
        type: 'email_verification',
        subject: `Verify Your Email Address - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Thank you for signing up with {{platform_name}}! To complete your registration and activate your account, please verify your email address.</p>
          <p>Click the button below to verify your email:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{verification_url}}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 500;">Verify Email Address</a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="{{verification_url}}">{{verification_url}}</a></p>
          <p>This verification link will expire in 24 hours for security reasons.</p>
          <p>If you didn't create an account with us, please ignore this email.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{verification_url}}"]),
        event: 'user_email_verification',
      },
      {
        id: Date.now() * 1000 + 2,
        name: 'Welcome - Advertiser',
        type: 'welcome_advertiser',
        subject: `Welcome to {{platform_name}} - Start Advertising Today!`,
        body: `<p>Hello {{user_name}},</p>
          <p>Welcome to {{platform_name}}! Your advertiser account has been successfully verified and activated.</p>
          <p><strong>What you can do now:</strong></p>
          <ul>
            <li>Create and manage advertising campaigns</li>
            <li>Target specific audiences with precision</li>
            <li>Track performance with detailed analytics</li>
            <li>Add funds to start advertising immediately</li>
          </ul>
          <p>Ready to get started? Log in to your dashboard and create your first campaign!</p>
          <p>If you have any questions, our support team is here to help you succeed.</p>
          <p>Welcome aboard!</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}"]),
        event: 'user_welcome_advertiser',
      },
      {
        id: Date.now() * 1000 + 3,
        name: 'Welcome - Publisher',
        type: 'welcome_publisher',
        subject: `Welcome to {{platform_name}} - Start Monetizing Today!`,
        body: `<p>Hello {{user_name}},</p>
          <p>Welcome to {{platform_name}}! Your publisher account has been successfully verified and activated.</p>
          <p><strong>What you can do now:</strong></p>
          <ul>
            <li>Add your websites for monetization</li>
            <li>Create ad zones for different formats</li>
            <li>Monitor earnings with real-time statistics</li>
            <li>Request payouts when you reach minimum threshold</li>
          </ul>
          <p>Ready to start earning? Log in to your dashboard and add your first website!</p>
          <p>If you have any questions, our support team is here to help you maximize your earnings.</p>
          <p>Welcome aboard!</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}"]),
        event: 'user_welcome_publisher',
      },
      {
        id: Date.now() * 1000 + 4,
        name: 'DSP/SSP Account Created',
        type: 'account_created',
        subject: `Your {{user_role}} Account - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Your {{user_role}} account has been created by our admin team.</p>
          <p><strong>Your login credentials:</strong></p>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <p style="margin: 5px 0;"><strong>Email:</strong> {{user_email}}</p>
            <p style="margin: 5px 0;"><strong>Password:</strong> {{password}}</p>
            <p style="margin: 5px 0;"><strong>Role:</strong> {{user_role}}</p>
          </div>
          <p><strong>Important:</strong> Please change your password after your first login for security.</p>
          <p>Click the button below to access your account:</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{user_role}}","{{platform_name}}","{{user_name}}","{{user_email}}","{{password}}","{{login_url}}"]),
        event: 'admin_user_created',
      },
      {
        id: Date.now() * 1000 + 5,
        name: 'Campaign Approved',
        type: 'campaign_approved',
        subject: `Campaign Approved - {{campaign_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Great news! Your campaign "<strong>{{campaign_name}}</strong>" has been approved and is now live on our platform.</p>
          <p><strong>What happens next:</strong></p>
          <ul>
            <li>Your ads will start showing to your target audience</li>
            <li>You can monitor performance in your dashboard</li>
            <li>Real-time statistics will be available shortly</li>
          </ul>
          <p>You can view your campaign performance and make adjustments anytime through your advertiser dashboard.</p>
          <p>Thank you for choosing {{platform_name}} for your advertising needs!</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{campaign_name}}"]),
        event: 'campaign_approved',
      },
      {
        id: Date.now() * 1000 + 6,
        name: 'Campaign Rejected',
        type: 'campaign_rejected',
        subject: `Campaign Requires Review - {{campaign_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>We've reviewed your campaign "<strong>{{campaign_name}}</strong>" and it requires some modifications before it can be approved.</p>
          <p><strong>Reason for review:</strong></p>
          <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #dc2626;">{{reason}}</p>
          </div>
          <p><strong>Next steps:</strong></p>
          <ul>
            <li>Review the feedback above</li>
            <li>Edit your campaign to address the issues</li>
            <li>Your campaign will be automatically resubmitted for review</li>
          </ul>
          <p>If you have any questions about the review feedback, please don't hesitate to contact our support team.</p>
          <p>Thank you for your understanding.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{campaign_name}}","{{reason}}"]),
        event: 'campaign_rejected',
      },
      {
        id: Date.now() * 1000 + 7,
        name: 'Funds Deposited',
        type: 'funds_deposited',
        subject: `Funds Added - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Your account has been credited with <strong>\${{amount}}</strong>.</p>
          <p><strong>Transaction Details:</strong></p>
          <ul>
            <li>Amount: \${{amount}}</li>
            <li>Date: {{current_date}}</li>
            <li>Status: Completed</li>
          </ul>
          <p>You can now use these funds for your advertising campaigns.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{amount}}","{{account_balance}}"]),
        event: 'funds_deposited',
      },
      {
        id: Date.now() * 1000 + 8,
        name: 'Payout Processed',
        type: 'payout_processed',
        subject: `Payout Processed - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Your payout of <strong>\${{amount}}</strong> has been processed successfully.</p>
          <p><strong>Payout Details:</strong></p>
          <ul>
            <li>Amount: \${{amount}}</li>
            <li>Processing Date: {{current_date}}</li>
            <li>Status: Processed</li>
          </ul>
          <p>The funds should appear in your account within 2-5 business days depending on your payout method.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{amount}}"]),
        event: 'payout_processed',
      },
      {
        id: Date.now() * 1000 + 9,
        name: 'Website Approved',
        type: 'website_approved',
        subject: `Website Approved - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Excellent! Your website "<strong>{{website_name}}</strong>" has been approved for monetization.</p>
          <p><strong>What you can do now:</strong></p>
          <ul>
            <li>Create ad zones for different ad formats</li>
            <li>Generate ad tags for your website</li>
            <li>Start earning revenue from your traffic</li>
            <li>Monitor performance in real-time</li>
          </ul>
          <p>You can now create ad zones and start earning revenue from your website traffic.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{website_name}}"]),
        event: 'website_approved',
      },
      {
        id: Date.now() * 1000 + 10,
        name: 'Website Rejected',
        type: 'website_rejected',
        subject: `Website Requires Review - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Your website "<strong>{{website_name}}</strong>" requires review before approval.</p>
          <p><strong>Reason for review:</strong></p>
          <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #dc2626;">{{reason}}</p>
          </div>
          <p><strong>Next steps:</strong></p>
          <ul>
            <li>Review the feedback above</li>
            <li>Make the necessary changes to your website</li>
            <li>Update your website information in your dashboard</li>
            <li>Resubmit for review</li>
          </ul>
          <p>Please make the necessary changes and resubmit your website for approval.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{website_name}}","{{reason}}"]),
        event: 'website_rejected',
      },
      {
        id: Date.now() * 1000 + 11,
        name: 'Funds Withdrawal',
        type: 'funds_withdrawal',
        subject: `Withdrawal Request Processed - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Your withdrawal request of <strong>\${{amount}}</strong> has been processed successfully.</p>
          <p><strong>Withdrawal Details:</strong></p>
          <ul>
            <li>Amount: \${{amount}}</li>
            <li>Processing Date: {{current_date}}</li>
            <li>Status: Processed</li>
            <li>Method: {{method}}</li>
          </ul>
          <p>The funds should appear in your account within 2-5 business days depending on your withdrawal method.</p>
          <p>If you have any questions about this withdrawal, please contact our support team.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{amount}}"]),
        event: 'funds_withdrawal',
      },
      {
        id: Date.now() * 1000 + 12,
        name: 'Password Reset',
        type: 'password_reset',
        subject: `Password Reset Request - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>We received a request to reset your password for your {{platform_name}} account.</p>
          <p>Click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{reset_url}}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 500;">Reset Password</a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="{{reset_url}}">{{reset_url}}</a></p>
          <p><strong>Important:</strong> This password reset link will expire in 1 hour for security reasons.</p>
          <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{reset_link}}"]),
        event: 'password_reset_requested',
      },
      {
        id: Date.now() * 1000 + 13,
        name: 'Payment Reminder',
        type: 'payment_reminder',
        subject: `Payment Reminder - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>This is a friendly reminder about your account balance and recent activity.</p>
          <p><strong>Account Summary:</strong></p>
          <ul>
            <li>Current Balance: \${{current_balance}}</li>
            <li>Recent Spend: \${{recent_spend}}</li>
            <li>Account Status: {{account_status}}</li>
          </ul>
          <p>{{reminder_message}}</p>
          <p>To add funds to your account or review your billing information, please visit your dashboard.</p>
          <p>If you have any questions about your account or billing, please don't hesitate to contact our support team.</p>
          <p>Best regards,<br>{{platform_name}} Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}"]),
        event: 'payment_reminder',
      },
      {
        id: Date.now() * 1000 + 14,
        name: 'Support Ticket Created',
        type: 'support_ticket_created',
        subject: `Support Ticket Created - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>Your support ticket has been created successfully. We've received your request and our team will review it shortly.</p>
          <p><strong>Ticket Details:</strong></p>
          <ul>
            <li>Ticket ID: #{{ticket_id}}</li>
            <li>Subject: {{ticket_subject}}</li>
            <li>Priority: {{priority}}</li>
            <li>Created: {{created_date}}</li>
          </ul>
          <p><strong>Your Message:</strong></p>
          <div style="background-color: #f8f9fa; border-left: 4px solid #3b82f6; padding: 12px; margin: 16px 0;">
            <p style="margin: 0;">{{ticket_message}}</p>
          </div>
          <p>Our support team typically responds within 24 hours during business days. You'll receive an email notification when we reply to your ticket.</p>
          <p>Thank you for contacting us!</p>
          <p>Best regards,<br>{{platform_name}} Support Team</p>`,
        variables: JSON.stringify(["{{platform_name}}","{{user_name}}","{{ticket_id}}","{{subject}}"]),
        event: 'support_ticket_created',
      },
      {
        id: Date.now() * 1000 + 15,
        name: 'Support Ticket Replied',
        type: 'support_ticket_replied',
        subject: `Support Ticket Reply - {{platform_name}}`,
        body: `<p>Hello {{user_name}},</p>
          <p>We've replied to your support ticket. Here are the details:</p>
          <p><strong>Ticket Details:</strong></p>
          <ul>
            <li>Ticket ID: #{{ticket_id}}</li>
            <li>Subject: {{ticket_subject}}</li>
            <li>Reply Date: {{reply_date}}</li>
            <li>Replied by: {{agent_name}}</li>
          </ul>
          <p><strong>Our Reply:</strong></p>
          <div style="background-color: #f0f9ff; border-left: 4px solid #3b82f6; padding: 12px; margin: 16px 0;">
            <p style="margin: 0;">{{reply_message}}</p>
          </div>
          <p>If you need further assistance, you can reply to this ticket through your dashboard or by replying to this email.</p>
          <p>Thank you for your patience!</p>
          <p>Best regards,<br>{{platform_name}} Support Team</p>`,
        variables: JSON.stringify(["{{user_name}}","{{ticket_id}}","{{reply_message}}","{{platform_name}}"]),
        event: 'support_ticket_replied',
      }
    ];

    // Insert templates
    for (const template of templates) {
      // Check if template already exists
      const existingResult = await clickhouse.query({
        query: 'SELECT id FROM email_templates WHERE type = {type:String}',
        query_params: { type: template.type },
      });

      const existing = await existingResult.json();
      if (existing.data.length === 0) {
        await clickhouse.insert({
          table: 'email_templates',
          values: [{
            ...template,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });
        console.log(`✓ Created template: ${template.name}`);
      } else {
        console.log(`- Template already exists: ${template.name}`);
      }
    }

    console.log('📧 Email templates setup completed!');

  } catch (error) {
    console.error('❌ Error seeding email templates:', error);
  }
}

seedProductionData();
