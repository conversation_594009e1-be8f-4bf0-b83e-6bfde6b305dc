import clickhouse from '@/lib/clickhouse';

async function seedEmailTemplates() {
  try {
    console.log('Seeding email templates...');

    const templates = [
      {
        id: Date.now() * 1000 + 1,
        name: 'Email Verification',
        type: 'email_verification',
        subject: 'Verify Your Email - {{platform_name}}',
        body: `
          <p>Hello {{user_name}},</p>
          <p>Thank you for registering with {{platform_name}}! To complete your registration, please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{verification_url}}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Verify Email Address</a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p><a href="{{verification_url}}">{{verification_url}}</a></p>
          <p>This verification link will expire in 24 hours for security reasons.</p>
          <p>If you didn't create an account with us, please ignore this email.</p>
        `,
        variables: JSON.stringify(['{{user_name}}', '{{platform_name}}', '{{verification_url}}']),
        event: 'user_registration',
      },
      {
        id: Date.now() * 1000 + 2,
        name: 'Campaign Approved',
        type: 'campaign_approved',
        subject: 'Campaign Approved - {{campaign_name}}',
        body: `
          <p>Hello {{user_name}},</p>
          <p>Great news! Your campaign "<strong>{{campaign_name}}</strong>" has been approved and is now live on our platform.</p>
          <p><strong>What happens next:</strong></p>
          <ul>
            <li>Your ads will start showing to your target audience</li>
            <li>You can monitor performance in your dashboard</li>
            <li>Real-time statistics will be available shortly</li>
          </ul>
          <p>You can view your campaign performance and make adjustments anytime through your advertiser dashboard.</p>
          <p>Thank you for choosing {{platform_name}} for your advertising needs!</p>
        `,
        variables: JSON.stringify(['{{user_name}}', '{{campaign_name}}', '{{platform_name}}']),
        event: 'campaign_approved',
      },
      {
        id: Date.now() * 1000 + 3,
        name: 'Campaign Rejected',
        type: 'campaign_rejected',
        subject: 'Campaign Requires Review - {{campaign_name}}',
        body: `
          <p>Hello {{user_name}},</p>
          <p>We've reviewed your campaign "<strong>{{campaign_name}}</strong>" and it requires some modifications before it can be approved.</p>
          <p><strong>Reason for review:</strong></p>
          <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 12px; margin: 16px 0;">
            <p style="margin: 0; color: #dc2626;">{{reason}}</p>
          </div>
          <p><strong>Next steps:</strong></p>
          <ul>
            <li>Review the feedback above</li>
            <li>Edit your campaign to address the issues</li>
            <li>Your campaign will be automatically resubmitted for review</li>
          </ul>
          <p>If you have any questions about the review feedback, please don't hesitate to contact our support team.</p>
          <p>Thank you for your understanding.</p>
        `,
        variables: JSON.stringify(['{{user_name}}', '{{campaign_name}}', '{{reason}}', '{{platform_name}}']),
        event: 'campaign_rejected',
      },
      {
        id: Date.now() * 1000 + 4,
        name: 'Welcome Advertiser',
        type: 'welcome_advertiser',
        subject: 'Welcome to {{platform_name}} - Start Advertising Today!',
        body: `
          <p>Hello {{user_name}},</p>
          <p>Welcome to {{platform_name}}! Your advertiser account has been successfully verified and activated.</p>
          <p><strong>What you can do now:</strong></p>
          <ul>
            <li>Create and manage advertising campaigns</li>
            <li>Target specific audiences with precision</li>
            <li>Track performance with detailed analytics</li>
            <li>Add funds to start advertising immediately</li>
          </ul>
          <p>Ready to get started? Log in to your dashboard and create your first campaign!</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboard_url}}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Go to Dashboard</a>
          </div>
          <p>If you have any questions, our support team is here to help you succeed.</p>
          <p>Welcome aboard!</p>
        `,
        variables: JSON.stringify(['{{user_name}}', '{{platform_name}}', '{{dashboard_url}}']),
        event: 'email_verified',
      },
      {
        id: Date.now() * 1000 + 5,
        name: 'Welcome Publisher',
        type: 'welcome_publisher',
        subject: 'Welcome to {{platform_name}} - Start Monetizing Today!',
        body: `
          <p>Hello {{user_name}},</p>
          <p>Welcome to {{platform_name}}! Your publisher account has been successfully verified and activated.</p>
          <p><strong>What you can do now:</strong></p>
          <ul>
            <li>Add your websites for monetization</li>
            <li>Create ad zones for different formats</li>
            <li>Monitor earnings with real-time statistics</li>
            <li>Request payouts when you reach minimum threshold</li>
          </ul>
          <p>Ready to start earning? Log in to your dashboard and add your first website!</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboard_url}}" style="background-color: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Go to Dashboard</a>
          </div>
          <p>If you have any questions, our support team is here to help you maximize your earnings.</p>
          <p>Welcome aboard!</p>
        `,
        variables: JSON.stringify(['{{user_name}}', '{{platform_name}}', '{{dashboard_url}}']),
        event: 'email_verified',
      }
    ];

    // Insert templates
    for (const template of templates) {
      // Check if template already exists
      const existingResult = await clickhouse.query({
        query: 'SELECT id FROM email_templates WHERE type = {type:String}',
        query_params: { type: template.type },
      });

      const existing = await existingResult.json();
      if (existing.data.length === 0) {
        await clickhouse.insert({
          table: 'email_templates',
          values: [{
            ...template,
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
          }],
          format: 'JSONEachRow',
        });
        console.log(`✓ Created template: ${template.name}`);
      } else {
        console.log(`- Template already exists: ${template.name}`);
      }
    }

    console.log('Email templates seeding completed!');

  } catch (error) {
    console.error('Error seeding email templates:', error);
  }
}

// Run if called directly
if (require.main === module) {
  seedEmailTemplates().then(() => process.exit(0));
}

export default seedEmailTemplates;
