#!/usr/bin/env tsx

/**
 * Test script for RabbitMQ connection and queue setup
 */

import { RabbitMQManager, publishToImpressionQueue, publishToCostProcessingQueue } from '../lib/queue-manager';
import logger from '../lib/logger';

async function testRabbitMQ() {
  console.log('🐰 Testing RabbitMQ Connection and Queue Setup');
  console.log('==============================================');
  
  try {
    // Test 1: Initialize RabbitMQ
    console.log('\n📋 Step 1: Initializing RabbitMQ...');
    await RabbitMQManager.initialize();
    console.log('✅ RabbitMQ initialized successfully');
    
    // Test 2: Get queue statistics
    console.log('\n📊 Step 2: Getting queue statistics...');
    const stats = await RabbitMQManager.getQueueStats();
    console.log('Queue Statistics:');
    stats.forEach(stat => {
      console.log(`  - ${stat.name}: ${stat.messageCount} messages, ${stat.consumerCount} consumers`);
    });
    
    // Test 3: Publish test messages
    console.log('\n📤 Step 3: Publishing test messages...');
    
    // Test impression tracking message
    const impressionResult = await publishToImpressionQueue({
      impressionId: Date.now(),
      campaignId: 1,
      dspPartnerId: 0,
      sspPartnerId: 0,
      websiteId: 1,
      zoneId: 1,
      userAgent: 'Test User Agent',
      ipAddress: '127.0.0.1',
      country: 'US',
      region: 'CA',
      city: 'San Francisco',
      deviceType: 'desktop',
      os: 'Windows',
      browser: 'Chrome',
      costDeducted: 0.001,
      publisherRevenue: 0.0002,
      sourceType: 'local',
    });
    
    console.log(`✅ Impression message published: ${impressionResult}`);
    
    // Test cost processing message
    const costResult = await publishToCostProcessingQueue({
      campaignId: 1,
      websiteId: 1,
      zoneId: 1,
      impressionId: Date.now(),
      bidType: 'cpm',
      bidAmount: 5.0,
      publisherCpm: 1.0,
      isWinConfirmation: true,
    });
    
    console.log(`✅ Cost processing message published: ${costResult}`);
    
    // Test 4: Check updated queue statistics
    console.log('\n📊 Step 4: Checking updated queue statistics...');
    const updatedStats = await RabbitMQManager.getQueueStats();
    console.log('Updated Queue Statistics:');
    updatedStats.forEach(stat => {
      console.log(`  - ${stat.name}: ${stat.messageCount} messages, ${stat.consumerCount} consumers`);
    });
    
    console.log('\n🎯 RabbitMQ Test Summary:');
    console.log('=========================');
    console.log('✅ Connection: Successful');
    console.log('✅ Queue Initialization: Successful');
    console.log('✅ Message Publishing: Successful');
    console.log('✅ Queue Statistics: Working');
    console.log('\n🚀 RabbitMQ is ready for RTB platform optimization!');
    
  } catch (error) {
    console.error('❌ RabbitMQ test failed:', error);
    process.exit(1);
  }
}

async function testEnvironmentVariables() {
  console.log('\n🔧 Environment Variables Check:');
  console.log('===============================');
  
  const requiredVars = [
    'RABBITMQ_URL',
    'RABBITMQ_HOST', 
    'RABBITMQ_PORT',
    'RABBITMQ_USERNAME',
    'RABBITMQ_PASSWORD'
  ];
  
  let allSet = true;
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      if (varName.includes('PASSWORD')) {
        console.log(`✅ ${varName}: ***SET***`);
      } else {
        console.log(`✅ ${varName}: ${value}`);
      }
    } else {
      console.log(`❌ ${varName}: NOT SET`);
      allSet = false;
    }
  });
  
  if (!allSet) {
    console.log('\n⚠️  Warning: Some RabbitMQ environment variables are not set.');
    console.log('   The system will use default values, but this may cause connection issues.');
    console.log('   Please check your .env.local file.');
  }
  
  return allSet;
}

if (require.main === module) {
  (async () => {
    await testEnvironmentVariables();
    await testRabbitMQ();
    process.exit(0);
  })().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

export { testRabbitMQ, testEnvironmentVariables };
