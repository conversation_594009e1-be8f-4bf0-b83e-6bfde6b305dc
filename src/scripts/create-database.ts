import { createClient } from '@clickhouse/client';

async function createDatabase() {
  console.log('Creating ClickHouse database...');
  
  try {
    // Connect to ClickHouse without specifying a database
    const client = createClient({
      url: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
      username: process.env.CLICKHOUSE_USERNAME || 'default',
      password: process.env.CLICKHOUSE_PASSWORD || 'gamuschdb171288',
    });

    // Create the database
    const databaseName = process.env.CLICKHOUSE_DATABASE || 'global_ads_media';
    
    await client.command({
      query: `CREATE DATABASE IF NOT EXISTS ${databaseName}`,
    });

    console.log(`Database '${databaseName}' created successfully!`);
    
    await client.close();
    
  } catch (error) {
    console.error('Error creating database:', error);
    throw error;
  }
}

// Run the script
createDatabase()
  .then(() => {
    console.log('Database creation completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Failed to create database:', error);
    process.exit(1);
  });
