'use client';

import { useState, useEffect } from 'react';
import { formatNumber, formatCurrency } from '@/lib/format-utils';

interface DSPStats {
  totalBids: number;
  wonBids: number;
  impressions: number;
  totalSpend: number;
}

export default function DSPStatsCards() {
  const [stats, setStats] = useState<DSPStats>({
    totalBids: 0,
    wonBids: 0,
    impressions: 0,
    totalSpend: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDSPStats();
  }, []);

  const fetchDSPStats = async () => {
    try {
      // Fetch DSP statistics from correct endpoint
      const response = await fetch('/api/dsp/statistics?group_by=day');
      if (response.ok) {
        const result = await response.json();
        const data = result.data || {};
        const totals = data.totals || {};

        // Debug: Log the API response
        console.log('🔍 DSP Stats API Response:', {
          totals,
          daily_breakdown: data.daily_breakdown,
          wins_breakdown: data.wins_breakdown
        });

        // Calculate totals from detailed breakdown if totals are 0
        let totalRequests = parseInt(totals.total_requests) || 0;
        let totalWins = parseInt(totals.total_wins) || 0;
        let totalRevenue = parseFloat(totals.total_revenue) || 0;

        console.log('📊 Initial totals:', { totalRequests, totalWins, totalRevenue });

        // If totals are 0, sum from daily breakdown (which shows real data)
        if (totalWins === 0 && data.daily_breakdown && data.daily_breakdown.length > 0) {
          console.log('📊 Using daily breakdown fallback');
          totalRequests = data.daily_breakdown.reduce((sum, day) => sum + (day.total_requests || 0), 0);
          totalWins = data.daily_breakdown.reduce((sum, day) => sum + (day.total_wins || 0), 0);
          totalRevenue = data.daily_breakdown.reduce((sum, day) => sum + (day.total_revenue || 0), 0);
          console.log('📊 Daily breakdown totals:', { totalRequests, totalWins, totalRevenue });
        }

        // If still 0, try wins breakdown
        if (totalWins === 0 && data.wins_breakdown && data.wins_breakdown.length > 0) {
          console.log('📊 Using wins breakdown fallback');
          totalWins = data.wins_breakdown.reduce((sum, day) => sum + (day.wins || 0), 0);
          totalRevenue = data.wins_breakdown.reduce((sum, day) => sum + (day.total_spend || 0), 0);
          totalRequests = totalWins; // For DSP, if we have wins, assume same number of requests
          console.log('📊 Wins breakdown totals:', { totalRequests, totalWins, totalRevenue });
        }

        console.log('📊 Final stats being set:', { totalRequests, totalWins, totalRevenue });

        setStats({
          totalBids: totalRequests,
          wonBids: totalWins,
          impressions: totalWins, // For DSP, wins = impressions delivered
          totalSpend: totalRevenue,
        });
      } else {
        console.error('Failed to fetch DSP stats:', response.status);
      }
    } catch (error) {
      console.error('Failed to fetch DSP stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 rounded-md"></div>
                <div className="ml-5 flex-1">
                  <div className="h-4 bg-gray-300 rounded w-20 mb-2"></div>
                  <div className="h-6 bg-gray-300 rounded w-16"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Bids</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.totalBids)}</dd>
            </dl>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Won Bids</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.wonBids)}</dd>
            </dl>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Impressions</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.impressions)}</dd>
            </dl>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Spend</dt>
              <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.totalSpend)}</dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
}
