'use client';

import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import {
  HomeIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  LifebuoyIcon,
  CogIcon,
  UsersIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  BellIcon,
  ClockIcon,
  EnvelopeIcon,
  EyeIcon,
  UserGroupIcon,
  BanknotesIcon,
  ArrowTrendingUpIcon,
  ComputerDesktopIcon,
  TagIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CursorArrowRaysIcon,
  ShieldExclamationIcon,
  HeartIcon,
  CodeBracketIcon,
  RocketLaunchIcon,
} from '@heroicons/react/24/outline';

interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

export default function Sidebar() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const getNavItems = (): NavItem[] => {
    if (!session) return [];

    const role = session.user?.role;

    switch (role) {
      case 'advertiser':
        return [
          { href: '/advertiser/dashboard', label: 'Dashboard', icon: HomeIcon },
          { href: '/advertiser/campaigns', label: 'Campaigns', icon: TagIcon },
          { href: '/advertiser/tracking', label: 'Tracking', icon: CursorArrowRaysIcon },
          { href: '/advertiser/statistics', label: 'Statistics', icon: ChartBarIcon },
          { href: '/advertiser/billing', label: 'Billing', icon: CurrencyDollarIcon },
          { href: '/advertiser/support', label: 'Support', icon: LifebuoyIcon },
        ];
      case 'publisher':
        return [
          { href: '/publisher/dashboard', label: 'Dashboard', icon: HomeIcon },
          { href: '/publisher/websites', label: 'Websites', icon: GlobeAltIcon },
          { href: '/publisher/zones', label: 'Ad Zones', icon: ComputerDesktopIcon },
          { href: '/publisher/statistics', label: 'Statistics', icon: ChartBarIcon },
          { href: '/publisher/payouts', label: 'Payouts', icon: BanknotesIcon },
          { href: '/publisher/support', label: 'Support', icon: LifebuoyIcon },
        ];
      case 'admin':
        return [
          { href: '/admin/dashboard', label: 'Dashboard', icon: HomeIcon },
          { href: '/admin/users', label: 'Users', icon: UsersIcon },
          { href: '/admin/campaigns', label: 'Campaigns', icon: TagIcon },
          { href: '/admin/websites', label: 'Websites', icon: GlobeAltIcon },
          { href: '/admin/partners', label: 'Partners', icon: UserGroupIcon },
          { href: '/admin/payouts', label: 'Payouts', icon: BanknotesIcon },
          { href: '/admin/transactions', label: 'Transactions', icon: CurrencyDollarIcon },
          { href: '/admin/support', label: 'Support', icon: LifebuoyIcon },
          { href: '/admin/ad-monitoring', label: 'Ad Monitor', icon: EyeIcon },
          { href: '/admin/fraud-monitoring', label: 'Fraud Monitor', icon: ShieldExclamationIcon },
          { href: '/admin/health-monitor', label: 'Health Monitor', icon: HeartIcon },
          { href: '/admin/optimization', label: 'Optimization', icon: RocketLaunchIcon },
          { href: '/admin/logs', label: 'Log Management', icon: DocumentTextIcon },
          { href: '/admin/realtime', label: 'Real-time', icon: ClockIcon },
          { href: '/admin/statistics', label: 'Statistics', icon: ChartBarIcon },
          { href: '/admin/email-settings', label: 'Email', icon: EnvelopeIcon },
          { href: '/admin/bulk-email', label: 'Bulk Email', icon: EnvelopeIcon },
          { href: '/admin/settings', label: 'Settings', icon: CogIcon },
        ];
      case 'dsp':
        return [
          { href: '/dsp/dashboard', label: 'Dashboard', icon: HomeIcon },
          { href: '/dsp/statistics', label: 'Statistics', icon: ChartBarIcon },
          { href: '/dsp/stats-api', label: 'Stats API', icon: CodeBracketIcon },
        ];
      case 'ssp':
        return [
          { href: '/ssp/dashboard', label: 'Dashboard', icon: HomeIcon },
          { href: '/ssp/statistics', label: 'Statistics', icon: ChartBarIcon },
          { href: '/ssp/stats-api', label: 'Stats API', icon: CodeBracketIcon },
        ];
      default:
        return [];
    }
  };

  const navItems = getNavItems();

  if (!session || navItems.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gray-900 text-white transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'} min-h-screen flex flex-col`}>
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h2 className="text-lg font-semibold text-blue-400">
                {session.user?.role?.charAt(0).toUpperCase() + session.user?.role?.slice(1)} Panel
              </h2>
              <p className="text-sm text-gray-400 truncate">{session.user?.name}</p>
            </div>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 rounded-lg hover:bg-gray-700 transition-colors"
          >
            {isCollapsed ? (
              <ChevronRightIcon className="w-5 h-5" />
            ) : (
              <ChevronLeftIcon className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className={`flex-1 ${isCollapsed ? 'p-2' : 'p-4'}`}>
        <ul className="space-y-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={`flex items-center ${isCollapsed ? 'justify-center px-2 mx-1' : 'px-3'} py-2 rounded-lg transition-colors group ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                  title={isCollapsed ? item.label : undefined}
                >
                  <Icon className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5'} flex-shrink-0`} />
                  {!isCollapsed && (
                    <span className="ml-3 text-sm font-medium">{item.label}</span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Sidebar Footer */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-700">
          <div className="text-xs text-gray-400">
            <p>Global Ads Media</p>
            <p>v1.0.0</p>
          </div>
        </div>
      )}
    </div>
  );
}
