import { useState } from 'react';

export default function MacroInfo() {
  const [isOpen, setIsOpen] = useState(false);

  const macros = [
    {
      macro: '{click_id}',
      description: 'Unique click identifier for tracking',
      example: 'https://example.com/landing?click_id={click_id}'
    },
    {
      macro: '{campaign_id}',
      description: 'Campaign ID',
      example: 'https://example.com/landing?cid={campaign_id}'
    },

    {
      macro: '{zone_id}',
      description: 'Publisher zone ID',
      example: 'https://example.com/landing?zone={zone_id}'
    },
    {
      macro: '{website_id}',
      description: 'Publisher website ID',
      example: 'https://example.com/landing?site={website_id}'
    },
    {
      macro: '{country}',
      description: 'User country code (ISO 2-letter)',
      example: 'https://example.com/landing?country={country}'
    },
    {
      macro: '{region}',
      description: 'User region/state',
      example: 'https://example.com/landing?region={region}'
    },
    {
      macro: '{city}',
      description: 'User city',
      example: 'https://example.com/landing?city={city}'
    },
    {
      macro: '{device_type}',
      description: 'Device type (desktop, mobile, tablet)',
      example: 'https://example.com/landing?device={device_type}'
    },
    {
      macro: '{os}',
      description: 'Operating system',
      example: 'https://example.com/landing?os={os}'
    },
    {
      macro: '{browser}',
      description: 'Browser name',
      example: 'https://example.com/landing?browser={browser}'
    },
    {
      macro: '{ip_address}',
      description: 'User IP address',
      example: 'https://example.com/landing?ip={ip_address}'
    },
    {
      macro: '{timestamp}',
      description: 'Click timestamp (Unix)',
      example: 'https://example.com/landing?ts={timestamp}'
    },
    {
      macro: '{user_agent}',
      description: 'User agent string (URL encoded)',
      example: 'https://example.com/landing?ua={user_agent}'
    },
    {
      macro: '{user_lang}',
      description: 'User language code (e.g., en, es, fr)',
      example: 'https://example.com/landing?lang={user_lang}'
    },
    {
      macro: '{ad_format}',
      description: 'Ad format type (banner, popup, native, etc.)',
      example: 'https://example.com/landing?format={ad_format}'
    },
    {
      macro: '{page_url}',
      description: 'Referring page URL (URL encoded)',
      example: 'https://example.com/landing?ref={page_url}'
    },
    {
      macro: '{os_version}',
      description: 'Operating system version',
      example: 'https://example.com/landing?os_ver={os_version}'
    }
  ];

  return (
    <div className="mt-2">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
      >
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Available Macros
        <svg className={`w-4 h-4 ml-1 transform transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="mt-3 bg-gray-50 border border-gray-200 rounded-md p-4 max-h-64 overflow-y-auto">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Tracking Macros</h4>
          <p className="text-xs text-gray-600 mb-3">
            Use these macros in your landing URLs to receive tracking data. They will be automatically replaced with actual values when users click your ads.
          </p>

          <div className="space-y-3">
            {macros.map((macro, index) => (
              <div key={index} className="border-b border-gray-200 pb-2 last:border-b-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <code className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded font-mono">
                      {macro.macro}
                    </code>
                    <p className="text-xs text-gray-600 mt-1">{macro.description}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => navigator.clipboard.writeText(macro.macro)}
                    className="ml-2 text-xs text-gray-500 hover:text-gray-700"
                    title="Copy macro"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>
                <div className="mt-1">
                  <p className="text-xs text-gray-500">
                    <span className="font-medium">Example:</span> {macro.example}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-xs text-yellow-800">
              <strong>Note:</strong> Macros are case-sensitive and must be used exactly as shown.
              They will only work in landing URLs and tracking pixels.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
