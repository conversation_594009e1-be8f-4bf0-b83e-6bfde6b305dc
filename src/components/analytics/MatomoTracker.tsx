'use client';

import Script from 'next/script';

interface MatomoTrackerProps {
  matomoUrl?: string;
  siteId?: string;
  enabled?: boolean;
}

export default function MatomoTracker({
  matomoUrl = "//stats.globaladsmedia.us/",
  siteId = "16",
  enabled = true
}: MatomoTrackerProps = {}) {
  // Don't render anything if tracking is disabled
  if (!enabled) {
    return null;
  }

  return (
    <>
      <Script
        id="matomo-tracker"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            var _paq = window._paq = window._paq || [];
            /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
            _paq.push(['trackPageView']);
            _paq.push(['enableLinkTracking']);
            (function() {
              var u="${matomoUrl}";
              _paq.push(['setTrackerUrl', u+'matomo.php']);
              _paq.push(['setSiteId', '${siteId}']);
              var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
              g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
            })();
          `,
        }}
      />
      {/* Noscript fallback for users with JavaScript disabled */}
      <noscript>
        <p>
          <img
            src={`${matomoUrl}matomo.php?idsite=${siteId}&amp;rec=1`}
            style={{ border: 0 }}
            alt=""
          />
        </p>
      </noscript>
    </>
  );
}
