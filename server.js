const next = require('next');
const http = require('http');

require('dotenv').config({ path: '.env.local' });

const dev = process.env.NODE_ENV !== 'production';

// PORT will be supplied by PM2 (e.g., 3102 for all instances)
const defaultAppUrl = `http://localhost:${parseInt(process.env.PORT, 10) || 3102}`;
const appUrl = new URL(process.env.NEXTAUTH_URL || defaultAppUrl);
const port = parseInt(process.env.PORT, 10) || parseInt(appUrl.port, 10) || 3102;

const appHostnameForNext = appUrl.hostname; // Next.js can know its public hostname
const listenHostname = '0.0.0.0';         // Node.js server will listen on all interfaces

// Initialize Next.js
const app = next({ dev, hostname: appHostnameForNext, port });
const handle = app.getRequestHandler();

// Desired server settings from environment variables or defaults
const desiredKeepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT, 10) || 65000;
const desiredHeadersTimeout = parseInt(process.env.HEADERS_TIMEOUT, 10) || 66000;
const desiredServerTimeout = 10000; // General server socket inactivity timeout (10s)

// RTB/SSP/DSP specific timeouts from environment or defaults
const rtbRequestTimeout = parseInt(process.env.RTB_TIMEOUT, 10) || 100;
const sspDspRequestTimeout = parseInt(process.env.SSP_TIMEOUT, 10) || 200;

app.prepare().then(() => {
  const server = http.createServer(async (req, res) => {
    try {
      if (req.url.startsWith('/api/rtb/')) {
        req.setTimeout(rtbRequestTimeout);
      }
      else if (req.url.match(/^\/api\/(ssp|dsp)\//)) {
        req.setTimeout(sspDspRequestTimeout);
      }
      await handle(req, res);
    } catch (err) {
      if (!res.headersSent) {
        res.statusCode = 500;
        res.setHeader('Content-Type', 'text/plain');
        res.end('Internal Server Error');
      }
      console.error(`Error handling ${req.method} ${req.url} - PID: ${process.pid}`, err);
    }
  });

  server.keepAliveTimeout = desiredKeepAliveTimeout;
  server.headersTimeout = desiredHeadersTimeout;
  server.timeout = desiredServerTimeout;

  server.on('connection', (socket) => {
    socket.setNoDelay(true);
    socket.setKeepAlive(true, desiredKeepAliveTimeout);
  });

  const shutdown = () => {
    console.log(`PID: ${process.pid} - SIGTERM/SIGINT received, shutting down gracefully...`);
    server.close(() => {
      console.log(`PID: ${process.pid} - Server closed. Exiting.`);
      setTimeout(() => process.exit(0), 100);
    });
    setTimeout(() => {
      console.error(`PID: ${process.pid} - Could not close connections in time, forcefully shutting down`);
      process.exit(1);
    }, 10000);
  };

  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);

  server.listen(port, listenHostname, (err) => { // Use listenHostname here
    if (err) {
      console.error(`PID: ${process.pid} - Failed to listen on ${listenHostname}:${port}`, err);
      process.exit(1);
    }
    console.log(`> PID: ${process.pid}, Instance: ${process.env.INSTANCE_ID || 'N/A'} - Server listening on ${listenHostname}:${port}, publicly accessible (via Nginx) at http://${appHostnameForNext}${(port === 80 || port === 443) ? '' : ':' + port} (env.PORT: ${process.env.PORT})`);

    if (process.send) {
      process.send('ready');
    }
  });

}).catch(err => {
  console.error(`PID: ${process.pid} - Next.js app preparation failed:`, err);
  process.exit(1);
});